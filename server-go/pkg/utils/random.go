package utils

import (
	"crypto/rand"
	"math/big"
)

const (
	// 字符集，包含数字和字母，排除容易混淆的字符如 0, O, I, l
	charset = "**********************************************************"
)

// GenerateRandomString 生成指定长度的随机字符串
func GenerateRandomString(length int) (string, error) {
	result := make([]byte, length)
	charsetLen := big.NewInt(int64(len(charset)))

	for i := 0; i < length; i++ {
		randomIndex, err := rand.Int(rand.Reader, charsetLen)
		if err != nil {
			return "", err
		}
		result[i] = charset[randomIndex.Int64()]
	}

	return string(result), nil
}

// GenerateCacheVersion 生成10位缓存版本号
func GenerateCacheVersion() (string, error) {
	return GenerateRandomString(10)
}
