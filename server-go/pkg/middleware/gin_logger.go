package middleware

import (
	"io"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// GinLogger 自定义 Gin 日志中间件
func GinLogger(logger *logrus.Logger) gin.HandlerFunc {
	// 在生产环境禁用 Gin 的默认输出
	if os.Getenv("APP_MODE") == "production" {
		gin.SetMode(gin.ReleaseMode)
		gin.DefaultWriter = io.Discard
		gin.DefaultErrorWriter = io.Discard
	}

	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		// 使用自定义日志格式记录到文件
		logger.WithFields(logrus.Fields{
			"status":     param.StatusCode,
			"method":     param.Method,
			"path":       param.Path,
			"ip":         param.ClientIP,
			"user_agent": param.Request.UserAgent(),
			"latency":    param.Latency.String(),
			"time":       param.TimeStamp.Format(time.RFC3339),
		}).Info("HTTP Request")

		// 返回空字符串，避免重复输出
		return ""
	})
}

// GinRecovery 自定义 Gin 恢复中间件
func GinRecovery(logger *logrus.Logger) gin.HandlerFunc {
	return gin.RecoveryWithWriter(io.Discard, func(c *gin.Context, recovered interface{}) {
		// 记录 panic 错误到日志文件
		logger.WithFields(logrus.Fields{
			"error":  recovered,
			"method": c.Request.Method,
			"path":   c.Request.URL.Path,
			"ip":     c.ClientIP(),
		}).Error("Panic recovered")

		c.AbortWithStatus(500)
	})
}
