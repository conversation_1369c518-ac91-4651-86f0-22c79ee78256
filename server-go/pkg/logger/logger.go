package logger

import (
	"io"
	"os"
	"path/filepath"
	"weishi-server/internal/config"

	"github.com/sirupsen/logrus"
)

var Logger *logrus.Logger

func Init(cfg config.LogConfig) {
	Logger = logrus.New()

	// 设置日志级别
	level, err := logrus.ParseLevel(cfg.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	Logger.SetLevel(level)

	// 设置日志格式
	Logger.SetFormatter(&logrus.JSONFormatter{
		TimestampFormat: "2006-01-02 15:04:05",
	})

	// 创建日志目录
	if err := os.MkdirAll(cfg.Path, 0755); err != nil {
		Logger.Warn("Failed to create log directory:", err)
		Logger.SetOutput(os.Stdout)
		return
	}

	// 创建日志文件
	logFile := filepath.Join(cfg.Path, "app.log")
	file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		Logger.Warn("Failed to open log file:", err)
		Logger.SetOutput(os.Stdout)
		return
	}

	// 根据环境决定输出位置
	appMode := os.Getenv("APP_MODE")
	if appMode == "production" {
		// 生产环境只输出到文件
		Logger.SetOutput(file)
	} else {
		// 开发环境同时输出到文件和控制台
		Logger.SetOutput(io.MultiWriter(file, os.Stdout))
	}
}

// Info 信息日志
func Info(args ...interface{}) {
	Logger.Info(args...)
}

// Warn 警告日志
func Warn(args ...interface{}) {
	Logger.Warn(args...)
}

// Error 错误日志
func Error(args ...interface{}) {
	Logger.Error(args...)
}

// Debug 调试日志
func Debug(args ...interface{}) {
	Logger.Debug(args...)
}

// Fatal 致命错误日志
func Fatal(args ...interface{}) {
	Logger.Fatal(args...)
}

// WithField 带字段的日志
func WithField(key string, value interface{}) *logrus.Entry {
	return Logger.WithField(key, value)
}

// WithFields 带多个字段的日志
func WithFields(fields logrus.Fields) *logrus.Entry {
	return Logger.WithFields(fields)
}
