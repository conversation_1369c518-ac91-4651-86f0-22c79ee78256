package cos

import (
	"context"
	"crypto/rand"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"path/filepath"
	"strings"
	"time"

	"github.com/tencentyun/cos-go-sdk-v5"
)

type Config struct {
	SecretID  string
	SecretKey string
	Region    string
	Bucket    string
	Domain    string
}

type Client struct {
	cosClient *cos.Client
	config    *Config
}

type UploadResult struct {
	Key      string `json:"key"`      // 文件路径
	URL      string `json:"url"`      // 访问URL
	Size     int64  `json:"size"`     // 文件大小
	MimeType string `json:"mimeType"` // 文件类型
}

// NewClient 创建COS客户端
func NewClient(config *Config) (*Client, error) {
	if config.SecretID == "" || config.SecretKey == "" || config.Bucket == "" {
		return nil, fmt.Errorf("COS配置不完整")
	}

	// 构建bucket URL
	bucketURL := fmt.Sprintf("https://%s.cos.%s.myqcloud.com", config.Bucket, config.Region)
	u, err := url.Parse(bucketURL)
	if err != nil {
		return nil, fmt.Errorf("解析bucket URL失败: %v", err)
	}

	baseURL := &cos.BaseURL{BucketURL: u}

	cosClient := cos.NewClient(baseURL, &http.Client{
		Timeout: 100 * time.Second,
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.SecretID,
			SecretKey: config.SecretKey,
		},
	})

	return &Client{
		cosClient: cosClient,
		config:    config,
	}, nil
}

// UploadFile 上传文件
func (c *Client) UploadFile(file multipart.File, header *multipart.FileHeader, module string) (*UploadResult, error) {
	// 生成唯一文件名
	fileName := c.generateFileName(header.Filename)

	// 构建文件路径
	key := fmt.Sprintf("%s/%s", module, fileName)

	// 获取文件内容
	fileContent, err := io.ReadAll(file)
	if err != nil {
		return nil, fmt.Errorf("读取文件内容失败: %v", err)
	}

	// 重置文件指针
	file.Seek(0, 0)

	// 上传到COS
	_, err = c.cosClient.Object.Put(context.Background(), key, strings.NewReader(string(fileContent)), nil)
	if err != nil {
		return nil, fmt.Errorf("上传文件到COS失败: %v", err)
	}

	// 构建访问URL
	accessURL := c.getAccessURL(key)

	return &UploadResult{
		Key:      key,
		URL:      accessURL,
		Size:     header.Size,
		MimeType: header.Header.Get("Content-Type"),
	}, nil
}

// DeleteFile 删除文件
func (c *Client) DeleteFile(key string) error {
	_, err := c.cosClient.Object.Delete(context.Background(), key)
	if err != nil {
		return fmt.Errorf("删除文件失败: %v", err)
	}
	return nil
}

// GetFileURL 获取文件访问URL
func (c *Client) GetFileURL(key string) string {
	return c.getAccessURL(key)
}

// IsFileExists 检查文件是否存在
func (c *Client) IsFileExists(key string) (bool, error) {
	_, err := c.cosClient.Object.Head(context.Background(), key, nil)
	if err != nil {
		// 如果是404错误，说明文件不存在
		if cos.IsNotFoundError(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// generateFileName 生成唯一文件名
// 规则：时间戳 + 上传的文件名 + 随机数字(8位)
func (c *Client) generateFileName(originalName string) string {
	// 获取文件扩展名
	ext := filepath.Ext(originalName)

	// 获取不含扩展名的文件名
	nameWithoutExt := strings.TrimSuffix(originalName, ext)

	// 清理文件名，移除特殊字符，只保留字母、数字、中文、下划线和横线
	cleanName := strings.ReplaceAll(nameWithoutExt, " ", "_")
	// 移除其他可能有问题的字符
	cleanName = strings.ReplaceAll(cleanName, "/", "_")
	cleanName = strings.ReplaceAll(cleanName, "\\", "_")
	cleanName = strings.ReplaceAll(cleanName, "?", "_")
	cleanName = strings.ReplaceAll(cleanName, "*", "_")
	cleanName = strings.ReplaceAll(cleanName, ":", "_")
	cleanName = strings.ReplaceAll(cleanName, "|", "_")
	cleanName = strings.ReplaceAll(cleanName, "<", "_")
	cleanName = strings.ReplaceAll(cleanName, ">", "_")
	cleanName = strings.ReplaceAll(cleanName, "\"", "_")

	// 限制文件名长度，避免过长
	if len(cleanName) > 50 {
		cleanName = cleanName[:50]
	}

	// 生成8位随机数字 (10000000 到 99999999)
	randomBytes := make([]byte, 4)
	rand.Read(randomBytes)
	// 确保生成的数字在8位范围内
	randomNum := (uint32(randomBytes[0])<<24|
		uint32(randomBytes[1])<<16|
		uint32(randomBytes[2])<<8|
		uint32(randomBytes[3]))%90000000 + 10000000

	// 组合时间戳、文件名和随机数字
	timestamp := time.Now().Unix()

	return fmt.Sprintf("%d_%s_%08d%s", timestamp, cleanName, randomNum, ext)
}

// getAccessURL 获取文件访问URL
func (c *Client) getAccessURL(key string) string {
	if c.config.Domain != "" {
		// 使用自定义域名
		return fmt.Sprintf("https://%s/%s", c.config.Domain, key)
	}

	// 使用默认域名
	return fmt.Sprintf("https://%s.cos.%s.myqcloud.com/%s",
		c.config.Bucket, c.config.Region, key)
}

// ValidateFileType 验证文件类型
func ValidateFileType(header *multipart.FileHeader, allowedTypes []string) bool {
	if len(allowedTypes) == 0 {
		return true
	}

	contentType := header.Header.Get("Content-Type")
	if contentType == "" {
		// 根据文件扩展名判断
		ext := strings.ToLower(filepath.Ext(header.Filename))
		for _, allowedType := range allowedTypes {
			if strings.Contains(allowedType, ext[1:]) {
				return true
			}
		}
		return false
	}

	for _, allowedType := range allowedTypes {
		if strings.Contains(contentType, allowedType) {
			return true
		}
	}

	return false
}

// ValidateFileSize 验证文件大小
func ValidateFileSize(header *multipart.FileHeader, maxSize int64) bool {
	return header.Size <= maxSize
}

// GetConfig 获取配置信息
func (c *Client) GetConfig() *Config {
	return c.config
}
