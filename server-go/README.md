# 蔚之领域智能科技 - Go 服务端

基于 Go + Gin + GORM 的企业官网后端服务，替换原有的 NestJS 版本。

## 技术栈

- **Web 框架**: Gin
- **ORM**: GORM
- **数据库**: MySQL
- **认证**: JWT
- **配置管理**: Viper
- **日志**: Logrus
- **文档**: Swagger
- **开发工具**: Air (热重载)

## 项目结构

```
server-go/
├── cmd/                 # 应用入口
│   └── main.go
├── internal/            # 内部包
│   ├── config/         # 配置管理
│   ├── database/       # 数据库连接
│   ├── handler/        # HTTP 处理器
│   ├── middleware/     # 中间件
│   ├── model/          # 数据模型
│   ├── repository/     # 数据访问层
│   ├── router/         # 路由配置
│   └── service/        # 业务逻辑层
├── pkg/                # 公共包
│   ├── jwt/           # JWT 工具
│   ├── logger/        # 日志工具
│   └── response/      # 响应格式
├── docs/              # 📚 项目文档（已迁移到根目录docs/）
│   └── table_naming_examples.go       # 命名规范示例
├── scripts/           # 构建脚本
├── bin/              # 编译输出
├── tmp/              # 临时文件（Air 热重载）
├── logs/             # 日志文件
├── .air.toml         # Air 配置文件
├── config.yaml       # 应用配置
├── env.example       # 环境变量示例
├── Makefile          # 构建管理
├── Dockerfile        # Docker 配置
├── go.mod           # Go 模块定义
├── go.sum           # 依赖校验
└── README.md        # 项目说明
```

## 快速开始

### 环境要求

- Go 1.23+
- MySQL 5.7+

### 安装依赖

```bash
go mod download
```

### 配置数据库

1. 创建数据库：
```sql
CREATE DATABASE weishi_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 修改配置文件 `config.yaml` 中的数据库连接信息

### 运行应用

#### 开发模式（推荐使用 Air 热重载）

```bash
# 使用 Air 热重载开发（推荐）
make dev

# 传统开发模式
go run cmd/main.go
```

#### 生产模式

```bash
# 构建并运行
make build
make run

# 或者手动构建
go build -o main cmd/main.go
./main
```

### Docker 部署

```bash
# 构建镜像
docker build -t weishi-server .

# 运行容器
docker run -p 3000:3000 weishi-server
```


## 数据库迁移（使用 make）

- 环境切换:
  - ENV=dev（默认）本地开发；使用本地 Docker MySQL（容器名默认 weishi_mysql_local，可在 scripts/local/migrate.sh 中修改 DOCKER_CONTAINER）
  - ENV=prod 生产环境；使用生产迁移脚本（scripts/production/migrate-docker.sh）

- 常用命令:
  - 列出迁移: make migrate-list ENV=dev
  - 查看状态: make migrate-status ENV=dev
  - 备份数据库: make migrate-backup ENV=dev
  - 执行升级: make migrate-up VERSION=002 ENV=dev
  - 回滚版本: make migrate-down VERSION=002 ENV=dev

- 本地测试升级 002 示例:
```bash
# 1) 备份（可选，推荐）
make migrate-backup ENV=dev

# 2) 执行升级到 002
make migrate-up VERSION=002 ENV=dev

# 3) 回滚（如需）
make migrate-down VERSION=002 ENV=dev
```

- 生产环境（示例）:
```bash
# 查看迁移列表
make migrate-list ENV=prod

# 执行升级 002
make migrate-up VERSION=002 ENV=prod
```

## API 文档

启动服务后访问：http://localhost:3000/api/docs/index.html

## 主要功能模块

- ✅ 用户管理
- 🚧 管理员系统
- 🚧 轮播图管理
- 🚧 新闻管理
- 🚧 服务管理
- 🚧 项目案例
- 🚧 合作伙伴
- 🚧 友情链接
- 🚧 招聘信息
- 🚧 平台管理

## 环境变量

可以通过环境变量覆盖配置文件中的设置：

```bash
# 应用配置
export WEISHI_APP_PORT=3000
export WEISHI_APP_MODE=production

# 数据库配置
export WEISHI_DATABASE_HOST=localhost
export WEISHI_DATABASE_PORT=3306
export WEISHI_DATABASE_USERNAME=root
export WEISHI_DATABASE_PASSWORD=your_password
export WEISHI_DATABASE_DATABASE=weishi_db

# JWT 配置
export WEISHI_JWT_SECRET=your_jwt_secret
export WEISHI_JWT_EXPIRE_TIME=7200

# 日志配置
export WEISHI_LOG_LEVEL=info
export WEISHI_LOG_PATH=./logs
```

## 与 NestJS 版本的对比

| 功能 | NestJS 版本 | Go 版本 |
|------|-------------|----------|
| Web 框架 | NestJS | Gin |
| ORM | Drizzle | GORM |
| 语言 | TypeScript | Go |
| 认证 | JWT | JWT |
| 文档 | Swagger | Swagger |
| 数据库 | MySQL | MySQL |
| 性能 | 中等 | 高 |
| 内存占用 | 较高 | 较低 |

## 开发指南

### 开发工具

- **Air 热重载** - 详见 [开发工具指南](../docs/development/03-development-tools.md)
- **Makefile** - 统一的项目管理命令
- **完整文档** - 查看 [文档中心](../docs/readme.md)

```bash
make help          # 查看所有可用命令
make dev           # 开发模式（热重载）
make build         # 构建应用
make run           # 运行应用
make setup-db      # 设置数据库
make kill-port     # 释放端口
```

### 添加新模块

1. 在 `internal/model/` 添加数据模型
2. 在 `internal/repository/` 添加数据访问层
3. 在 `internal/service/` 添加业务逻辑层
4. 在 `internal/handler/` 添加控制器
5. 在 `internal/router/router.go` 中注册路由

### 生成 Swagger 文档

```bash
# 安装 swag
go install github.com/swaggo/swag/cmd/swag@latest

# 生成文档
swag init -g cmd/main.go
```