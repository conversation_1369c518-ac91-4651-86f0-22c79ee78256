-- MySQL dump 10.13  Distrib 8.0.41, for Linux (aarch64)
--
-- Host: localhost    Database: weizhi
-- ------------------------------------------------------
-- Server version	8.0.41

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `swipers`
--

DROP TABLE IF EXISTS `swipers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `swipers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `order` bigint DEFAULT '0',
  `deleted_at` datetime(3) DEFAULT NULL,
  `status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'active',
  PRIMARY KEY (`id`),
  KEY `idx_swipers_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `swipers`
--

LOCK TABLES `swipers` WRITE;
/*!40000 ALTER TABLE `swipers` DISABLE KEYS */;
INSERT INTO `swipers` VALUES (1,'2025-06-15 15:38:40.000','2025-07-20 22:23:51.867','https://images-1305444037.cos.ap-nanjing.myqcloud.com/swiper/1.jpg','轮播图1',1,NULL,'active'),(2,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','https://images-1305444037.cos.ap-nanjing.myqcloud.com/swiper/2.jpg','轮播图2',2,NULL,'active'),(3,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','https://images-1305444037.cos.ap-nanjing.myqcloud.com/swiper/3.jpg','轮播图3',3,NULL,'active'),(4,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','https://images-1305444037.cos.ap-nanjing.myqcloud.com/swiper/4.jpg','轮播图4',4,NULL,'active');
/*!40000 ALTER TABLE `swipers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `friend_links`
--

DROP TABLE IF EXISTS `friend_links`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `friend_links` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `order` bigint DEFAULT '0',
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_friend_links_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `friend_links`
--

LOCK TABLES `friend_links` WRITE;
/*!40000 ALTER TABLE `friend_links` DISABLE KEYS */;
INSERT INTO `friend_links` VALUES (2,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','中华人民共和国生态环境部','https://www.mee.gov.cn/',2,NULL),(3,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','中国汽车技术研究中心有限公司','https://www.catarc.ac.cn/',3,NULL),(4,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','SMVIC','https://www.smvic.com.cn/pages/index.html',4,NULL),(5,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','California Air Resources Board','https://ww2.arb.ca.gov/homepage',5,NULL),(6,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','瀚海检测','http://www.hhtesting.cn/',6,NULL),(7,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','江苏东方汽车装饰件有限公司','http://www.dongfang-js.com/',7,NULL),(8,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','泸州北方化学工业有限公司','http://lzbfhg.norincogroup.com.cn/',8,NULL),(9,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','扬州长运塑料技术股份有限公司','http://www.cyjsgf.com/',9,NULL),(10,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','扬州良诚汽车部件有限公司','http://www.yzlc.com.cn/',10,NULL);
/*!40000 ALTER TABLE `friend_links` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `partners`
--

DROP TABLE IF EXISTS `partners`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `partners` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `logo` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_partners_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `partners`
--

LOCK TABLES `partners` WRITE;
/*!40000 ALTER TABLE `partners` DISABLE KEYS */;
INSERT INTO `partners` VALUES (3,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','3','https://images-1305444037.cos.ap-nanjing.myqcloud.com/partners/3.png',NULL),(4,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','4','https://images-1305444037.cos.ap-nanjing.myqcloud.com/partners/4.png',NULL),(8,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','8','https://images-1305444037.cos.ap-nanjing.myqcloud.com/partners/8.png',NULL),(14,'2025-07-19 17:16:25.752','2025-07-19 17:16:25.752','尼威动力','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752916573_尼威动力_23417857.png',NULL),(15,'2025-07-19 17:16:25.752','2025-07-19 17:16:25.752','1','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752917509_HydroTech_99087545.png',NULL),(16,'2025-07-19 17:16:25.752','2025-07-19 17:16:25.752','2','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752917519_ARGOHYTOS_13864275.png',NULL),(17,'2025-07-19 17:16:25.752','2025-07-19 17:16:25.752','3','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752917527_TECHGOAL_19945045.png',NULL),(18,'2025-07-19 17:16:25.752','2025-07-19 17:16:25.752','5','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752917539_扬州长运_94669250.png',NULL),(19,'2025-07-19 17:16:25.752','2025-07-19 17:16:25.752','6','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752917548_ParkOhio_99426720.png',NULL),(20,'2025-07-19 17:16:25.752','2025-07-19 17:16:25.752','7','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752917573_FD_56142273.png',NULL),(21,'2025-07-19 17:16:25.752','2025-07-19 17:16:25.752','9','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752917584_SHYECHAN_92704341.png',NULL);
/*!40000 ALTER TABLE `partners` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `part_platform`
--

DROP TABLE IF EXISTS `part_platform`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `part_platform` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `parameters` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `applications` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `order` bigint DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_part_platform_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `part_platform`
--

LOCK TABLES `part_platform` WRITE;
/*!40000 ALTER TABLE `part_platform` DISABLE KEYS */;
INSERT INTO `part_platform` VALUES (7,'2025-06-15 15:38:39.000','2025-07-20 22:03:33.783','用于燃油系统的各类阀门GVV/FLVV/FTIV等','https://images-1305444037.cos.ap-nanjing.myqcloud.com/part-platform/1.png','用于燃油系统的各类阀门GVV/FLVV/FTIV等','技术参数详情','应用于各类燃油系统',NULL,0),(8,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','用于燃油系统的各类炭罐总成','https://images-1305444037.cos.ap-nanjing.myqcloud.com/part-platform/2.png','用于燃油系统的各类炭罐总成','技术参数详情','应用于各类燃油系统',NULL,0),(9,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','用于燃油系统的各类油泵总成','https://images-1305444037.cos.ap-nanjing.myqcloud.com/part-platform/3.png','用于燃油系统的各类油泵总成','技术参数详情','应用于各类燃油系统',NULL,0),(10,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','各类管路，油管、气管、胶管等','https://images-1305444037.cos.ap-nanjing.myqcloud.com/part-platform/4.png','各类管路，油管、气管、胶管等','技术参数详情','应用于各类燃油系统',NULL,0),(11,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','用于燃油系统的各类气液分离器LVS','https://images-1305444037.cos.ap-nanjing.myqcloud.com/part-platform/5.png','用于燃油系统的各类气液分离器LVS','技术参数详情','应用于各类燃油系统',NULL,0),(12,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','各类快插接头QC、连接接头等','https://images-1305444037.cos.ap-nanjing.myqcloud.com/part-platform/6.png','各类快插接头QC、连接接头等','技术参数详情','应用于各类燃油系统',NULL,0),(13,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','用于燃油系统的各类锁盖','https://images-1305444037.cos.ap-nanjing.myqcloud.com/part-platform/7.png','用于燃油系统的各类锁盖','技术参数详情','应用于各类燃油系统',NULL,0),(14,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','用于燃油系统的各类加油管总成等','https://images-1305444037.cos.ap-nanjing.myqcloud.com/part-platform/8.png','用于燃油系统的各类加油管总成等','技术参数详情','应用于各类燃油系统',NULL,0),(18,'2025-07-19 18:11:38.308','2025-07-19 18:12:48.919','工业阀组过滤器','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/platform/1752919887_工业阀组过滤器_81101260.png','工业阀组过滤器','无','无',NULL,0),(19,'2025-07-19 18:12:30.363','2025-07-19 18:12:44.144','燃油箱总成','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/platform/1752919928_燃油箱总成_99957873.png','无','无','无',NULL,0);
/*!40000 ALTER TABLE `part_platform` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `project_cases`
--

DROP TABLE IF EXISTS `project_cases`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `project_cases` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `title` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `sort` bigint DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_project_cases_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `project_cases`
--

LOCK TABLES `project_cases` WRITE;
/*!40000 ALTER TABLE `project_cases` DISABLE KEYS */;
INSERT INTO `project_cases` VALUES (1,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/design-and-development/swiper/1.png',NULL,NULL,NULL,0),(2,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/design-and-development/swiper/2.png',NULL,NULL,NULL,0),(3,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/design-and-development/swiper/3.png',NULL,NULL,NULL,0),(4,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/design-and-development/swiper/4.png',NULL,NULL,NULL,0),(5,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/design-and-development/swiper/5.png',NULL,NULL,NULL,0);
/*!40000 ALTER TABLE `project_cases` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `recruitments`
--

DROP TABLE IF EXISTS `recruitments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `recruitments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `location` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `position` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `department` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `requirement` text COLLATE utf8mb4_unicode_ci,
  `order` bigint DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `idx_recruitments_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `recruitments`
--

LOCK TABLES `recruitments` WRITE;
/*!40000 ALTER TABLE `recruitments` DISABLE KEYS */;
INSERT INTO `recruitments` VALUES (6,'2025-07-19 10:30:43.000','2025-07-20 22:11:14.570','高级机械设计工程师','扬州','负责汽车动力系统机械部件的设计开发，包括燃油系统、冷却系统等关键部件的结构设计与优化。',NULL,'高级工程师','研发部','参与新产品开发项目，负责机械设计方案制定、3D建模、工程图纸绘制等工作。','1. 机械设计或相关专业本科及以上学历\n2. 5年以上汽车零部件设计经验\n3. 熟练使用UG、CATIA、AutoCAD等设计软件\n4. 具备良好的沟通协调能力',0,1),(7,'2025-07-19 10:30:43.000','2025-07-19 10:30:43.000','CAE仿真工程师','扬州','负责汽车动力系统的仿真分析工作，包括结构分析、流体分析、热分析等，为产品设计提供技术支持。',NULL,'工程师','技术部','运用CAE软件进行产品性能仿真分析，优化产品设计方案，提升产品性能。','1. 力学、机械或相关专业硕士及以上学历\n2. 3年以上CAE仿真分析经验\n3. 熟练使用ANSYS、ABAQUS、FLUENT等仿真软件\n4. 具备扎实的理论基础和实践经验',0,1),(8,'2025-07-19 10:30:43.000','2025-07-19 10:30:43.000','电气工程师','扬州','负责测试设备的电气系统设计、调试和维护，确保设备正常运行和测试精度。',NULL,'工程师','工程部','设计测试台架的电气控制系统，编写PLC程序，调试设备电气系统。','1. 电气工程或自动化专业本科及以上学历\n2. 3年以上电气设计经验\n3. 熟练使用PLC编程、电气设计软件\n4. 具备良好的动手能力和问题解决能力',0,1),(9,'2025-07-19 10:30:43.000','2025-07-19 10:30:43.000','项目经理','扬州','负责项目的整体规划、执行和管理，协调各部门资源，确保项目按时按质完成。',NULL,'项目经理','项目部','制定项目计划，跟踪项目进度，管理项目风险，协调内外部资源。','1. 工程管理或相关专业本科及以上学历\n2. 5年以上项目管理经验\n3. 具备PMP证书优先\n4. 优秀的沟通协调和团队管理能力',0,1);
/*!40000 ALTER TABLE `recruitments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `news`
--

DROP TABLE IF EXISTS `news`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `news` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_home_page` tinyint(1) DEFAULT '0',
  `deleted_at` datetime(3) DEFAULT NULL,
  `summary` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `cover_image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `author` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `publish_time` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_published` tinyint(1) DEFAULT '0',
  `view_count` bigint DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_news_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `news`
--

LOCK TABLES `news` WRITE;
/*!40000 ALTER TABLE `news` DISABLE KEYS */;
INSERT INTO `news` VALUES (1,'2025-06-23 23:50:39.944','2025-07-05 23:51:02.592','公司成立','蔚之领域智能科技有限公司正式成立，致力于为客户提供专业的智能科技解决方案。','https://picsum.photos/800/400?random=201',1,NULL,'公司成立',NULL,NULL,NULL,1,0),(2,'2025-06-23 23:50:39.946','2025-07-05 23:51:03.595','技术团队扩建','为了更好地服务客户，公司技术团队进一步扩建，引入更多优秀的技术人才。','https://picsum.photos/800/400?random=202',1,NULL,'技术团队扩建',NULL,NULL,NULL,1,0),(3,'2025-06-30 00:55:41.315','2025-07-05 23:51:04.388','测试新闻API完整功能','这是一条测试新闻，用于验证新闻管理功能的完整性。包括分页获取、创建、更新、删除以及首页显示设置等功能。通过前后端API的完整实现，实现了新闻内容的全生命周期管理。','https://picsum.photos/800/400?random=888',1,NULL,NULL,NULL,NULL,NULL,0,0),(4,'2025-01-15 10:30:00.000','2025-01-15 10:30:00.000','蔚之领域获得ISO9001质量管理体系认证','经过严格的审核和评估，江苏蔚之领域智能科技有限公司正式获得ISO9001:2015质量管理体系认证。这标志着公司在质量管理方面达到了国际先进水平，为客户提供更加可靠的产品和服务奠定了坚实基础。公司将继续秉承\"质量第一、客户至上\"的理念，不断提升产品质量和服务水平。','https://picsum.photos/800/400?random=101',1,NULL,'ISO9001质量管理体系认证',NULL,NULL,NULL,1,0),(5,'2025-01-10 14:20:00.000','2025-07-20 22:25:00.500','新一代EDS测试台架正式投入使用','公司自主研发的新一代EDS（电驱动系统）测试台架经过数月的调试和优化，正式投入使用。该测试台架采用先进的控制系统和高精度传感器，能够对电机性能进行全面、精确的测试和分析。新设备的投入使用将大幅提升公司的测试能力和服务质量，为新能源汽车行业的发展贡献力量。','https://picsum.photos/800/400?random=102',0,NULL,'新一代EDS测试台架投入使用',NULL,NULL,NULL,1,0),(6,'2025-01-05 09:15:00.000','2025-07-20 22:25:03.458','与知名汽车制造商达成战略合作','蔚之领域与国内知名汽车制造商正式签署战略合作协议，双方将在汽车动力系统测试、产品开发等领域展开深度合作。此次合作将充分发挥双方的技术优势和资源优势，共同推动汽车行业的技术创新和产业升级。合作项目预计将在未来三年内为公司带来显著的业务增长。','https://picsum.photos/800/400?random=103',0,NULL,'与知名汽车制造商达成战略合作',NULL,NULL,NULL,1,0),(7,'2024-12-28 16:45:00.000','2025-07-20 22:25:06.475','公司荣获\"高新技术企业\"称号','经过严格的评审程序，江苏蔚之领域智能科技有限公司被认定为\"高新技术企业\"。这一荣誉的获得，充分体现了公司在技术创新、研发投入和知识产权等方面的突出表现。公司将以此为契机，继续加大研发投入，提升技术创新能力，为行业发展贡献更多的智慧和力量。','https://picsum.photos/800/400?random=104',0,NULL,'荣获高新技术企业称号',NULL,NULL,NULL,1,0);
/*!40000 ALTER TABLE `news` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `services`
--

DROP TABLE IF EXISTS `services`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `services` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `icon` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `order` bigint DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_services_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `services`
--

LOCK TABLES `services` WRITE;
/*!40000 ALTER TABLE `services` DISABLE KEYS */;
INSERT INTO `services` VALUES (1,'2025-06-23 23:50:39.938','2025-06-23 23:50:39.938',NULL,'软件开发','提供专业的软件开发服务，包括Web应用、移动应用等',NULL,1),(2,'2025-06-23 23:50:39.940','2025-06-23 23:50:39.940',NULL,'系统集成','为企业提供完整的系统集成解决方案',NULL,2),(3,'2025-06-23 23:50:39.941','2025-06-23 23:50:39.941',NULL,'技术咨询','提供专业的技术咨询和架构设计服务',NULL,3),(4,'2025-06-23 23:50:39.943','2025-06-23 23:50:39.943',NULL,'运维服务','7x24小时专业运维服务，保障系统稳定运行',NULL,4);
/*!40000 ALTER TABLE `services` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-08 15:24:18
