# 生产环境迁移脚本

## 📋 概述

本目录包含生产环境的数据库迁移脚本，专门用于Docker容器环境的数据库操作。

## 📁 文件说明

- `migrate-docker.sh` - Docker容器环境数据库迁移脚本

## 🚀 使用方法

### 在production目录下执行

```bash
cd server-go/scripts/production

# 查看帮助
./migrate-docker.sh --help

# 执行迁移
./migrate-docker.sh up 002

# 完整升级流程
./migrate-docker.sh full-upgrade
```

### 通过统一管理器执行（推荐）

```bash
cd server-go/scripts

# 强制使用生产环境
./migrate-manager.sh production up 002
```

## ⚙️ 配置说明

### Docker容器配置

在 `migrate-docker.sh` 中的配置：

```bash
COMPOSE_FILE="../../../deployment/docker-compose.prod.yml"
ENV_FILE="../../../deployment/production.env"
MYSQL_CONTAINER="weishi-mysql-prod"
DB_NAME="weizhi"
DB_USER="root"
DB_PASS="Ydb3344%"
```

### 迁移文件路径

脚本会自动查找 `../migrations/` 目录中的迁移文件。

## 🛡️ 安全特性

### 自动备份
- 每次迁移前自动备份数据库
- 备份文件命名：`backup_docker_YYYYMMDD_HHMMSS.sql`
- 失败时自动尝试恢复

### 容器检查
- 自动检查MySQL容器运行状态
- 验证数据库连接
- 检查必要文件存在

### 服务管理
- 迁移前自动停止应用服务
- 迁移后自动启动应用服务
- 最终状态检查

## 🔧 执行流程

### 完整升级流程

```bash
./migrate-docker.sh full-upgrade
```

执行步骤：
1. 检查容器状态
2. 测试数据库连接
3. 停止应用服务
4. 备份数据库
5. 执行所有迁移
6. 验证升级结果
7. 启动应用服务
8. 最终检查

### 单独执行迁移

```bash
# 执行指定迁移
./migrate-docker.sh up 002

# 回滚迁移
./migrate-docker.sh down 002

# 仅备份
./migrate-docker.sh backup
```

## 🔍 故障排除

### 常见问题

1. **容器未运行**
   ```
   [ERROR] MySQL容器 weishi-mysql-prod 未运行
   ```
   **解决**: 
   ```bash
   docker-compose -f ../../../deployment/docker-compose.prod.yml --env-file ../../../deployment/production.env up -d
   ```

2. **权限不足**
   ```
   permission denied: ./migrate-docker.sh
   ```
   **解决**: `chmod +x migrate-docker.sh`

3. **文件不存在**
   ```
   [ERROR] 迁移文件不存在
   ```
   **解决**: 检查migrations目录和文件

### 检查命令

```bash
# 检查容器状态
docker ps | grep weishi

# 检查迁移文件
ls -la ../migrations/

# 测试数据库连接
docker-compose -f ../../../deployment/docker-compose.prod.yml --env-file ../../../deployment/production.env exec mysql mysql -u root -p"Ydb3344%" weizhi -e "SELECT 1;"
```

## ⚠️ 重要提醒

1. **生产环境专用**：仅在生产环境使用
2. **备份重要**：执行前确保有完整备份
3. **停机时间**：迁移期间服务会短暂停止
4. **监控日志**：密切关注执行日志

## 📚 相关文档

- `../migrations/README.md` - 迁移文件说明
- `../../../deployment/README.md` - 部署环境说明
- `../README.md` - 脚本总体说明
