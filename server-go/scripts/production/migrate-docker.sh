#!/bin/bash

# 容器环境数据库升级脚本
# 用法: ./migrate-docker.sh [up|down] [migration_number]

set -e

# 容器配置
COMPOSE_FILE="../../../deployment/docker-compose.prod.yml"
ENV_FILE="../../../deployment/production.env"
MYSQL_CONTAINER="weishi-mysql-prod"
DB_NAME="weizhi"
DB_USER="root"
DB_PASS="Ydb3344%"
MIGRATIONS_DIR="../migrations"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查容器状态
check_container_status() {
    log_step "检查容器状态..."
    
    if ! docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps | grep -q "$MYSQL_CONTAINER.*Up"; then
        log_error "MySQL容器 $MYSQL_CONTAINER 未运行"
        log_info "请先启动容器：docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d"
        exit 1
    fi
    
    log_info "✅ 容器状态正常"
}

# 备份数据库
backup_database() {
    log_step "备份数据库..."
    
    local backup_file="backup_docker_$(date +%Y%m%d_%H%M%S).sql"
    
    if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T "$MYSQL_CONTAINER" mysqldump -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" > "$backup_file"; then
        log_info "✅ 数据库备份完成: $backup_file"
        echo "$backup_file" > .last_backup
    else
        log_error "❌ 数据库备份失败"
        exit 1
    fi
}

# 测试数据库连接
test_database_connection() {
    log_step "测试数据库连接..."
    
    if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec "$MYSQL_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASS" -e "USE $DB_NAME; SELECT 1;" 2>/dev/null; then
        log_info "✅ 数据库连接正常"
    else
        log_error "❌ 数据库连接失败"
        exit 1
    fi
}

# 获取迁移文件名
get_migration_filename() {
    local direction=$1
    local migration_number=$2

    case "$migration_number" in
        "001")
            if [ "$direction" = "up" ]; then
                echo "${migration_number}_optimize_table_structure.sql"
            else
                echo "${migration_number}_optimize_table_structure_rollback.sql"
            fi
            ;;
        "002")
            if [ "$direction" = "up" ]; then
                echo "${migration_number}_drop_services_table.sql"
            else
                echo "${migration_number}_drop_services_table_rollback.sql"
            fi
            ;;
        *)
            log_error "未知的迁移版本号: $migration_number"
            log_info "可用的迁移版本: 001, 002"
            exit 1
            ;;
    esac
}

# 执行迁移
run_migration() {
    local direction=$1
    local migration_number=$2

    if [ -z "$migration_number" ]; then
        log_error "请指定迁移版本号"
        log_info "可用的迁移版本: 001, 002"
        exit 1
    fi

    local migration_filename=$(get_migration_filename "$direction" "$migration_number")
    local migration_file="$MIGRATIONS_DIR/$migration_filename"

    if [ ! -f "$migration_file" ]; then
        log_error "迁移文件不存在: $migration_file"
        exit 1
    fi

    log_step "执行迁移: $migration_file"
    
    if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T "$MYSQL_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$migration_file"; then
        log_info "✅ 迁移执行成功"
    else
        log_error "❌ 迁移执行失败"
        log_info "正在尝试回滚..."
        if [ -f ".last_backup" ]; then
            local backup_file=$(cat .last_backup)
            restore_database "$backup_file"
        fi
        exit 1
    fi
}

# 恢复数据库
restore_database() {
    local backup_file=$1
    
    if [ ! -f "$backup_file" ]; then
        log_error "备份文件不存在: $backup_file"
        exit 1
    fi
    
    log_step "恢复数据库从: $backup_file"
    
    if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T "$MYSQL_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$backup_file"; then
        log_info "✅ 数据库恢复成功"
    else
        log_error "❌ 数据库恢复失败"
        exit 1
    fi
}

# 停止应用服务
stop_app_services() {
    log_step "停止应用服务..."
    
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" stop web admin server
    
    log_info "✅ 应用服务已停止"
}

# 启动应用服务
start_app_services() {
    log_step "启动应用服务..."
    
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d web admin server
    
    log_info "✅ 应用服务已启动"
}

# 显示帮助
show_help() {
    echo "容器环境数据库迁移工具"
    echo ""
    echo "用法:"
    echo "  $0 up <migration_number>     执行迁移"
    echo "  $0 down <migration_number>   回滚迁移"
    echo "  $0 backup                    仅备份数据库"
    echo "  $0 restore <backup_file>     恢复数据库"
    echo "  $0 list                      列出可用的迁移"
    echo "  $0 full-upgrade              执行完整升级流程"
    echo ""
    echo "可用的迁移版本:"
    echo "  001 - 优化表结构（删除冗余字段）"
    echo "  002 - 删除services表"
    echo ""
    echo "示例:"
    echo "  $0 up 001                    执行001号迁移"
    echo "  $0 up 002                    执行002号迁移"
    echo "  $0 full-upgrade              执行完整升级流程"
    echo "  $0 backup                    备份当前数据库"
}

# 列出可用的迁移
list_migrations() {
    echo "可用的迁移文件:"
    echo ""
    echo "📁 $MIGRATIONS_DIR/"

    if [ -f "$MIGRATIONS_DIR/001_optimize_table_structure.sql" ]; then
        echo "  ✅ 001_optimize_table_structure.sql"
        echo "     📝 优化表结构（删除冗余字段）"
    fi

    if [ -f "$MIGRATIONS_DIR/002_drop_services_table.sql" ]; then
        echo "  ✅ 002_drop_services_table.sql"
        echo "     📝 删除services表"
    fi

    echo ""
    echo "回滚文件:"

    if [ -f "$MIGRATIONS_DIR/001_optimize_table_structure_rollback.sql" ]; then
        echo "  ↩️  001_optimize_table_structure_rollback.sql"
    fi

    if [ -f "$MIGRATIONS_DIR/002_drop_services_table_rollback.sql" ]; then
        echo "  ↩️  002_drop_services_table_rollback.sql"
    fi
}

# 完整升级流程
full_upgrade() {
    log_info "开始执行完整升级流程..."
    
    # 1. 检查状态
    check_container_status
    test_database_connection
    
    # 2. 停止应用服务
    stop_app_services
    
    # 3. 备份数据库
    backup_database
    
    # 4. 执行迁移
    run_migration "up" "001"
    run_migration "up" "002"
    
    # 5. 验证升级
    test_database_connection
    
    # 6. 启动应用服务
    start_app_services
    
    # 7. 最终检查
    log_step "执行最终检查..."
    sleep 10
    ./check-services.sh
    
    log_info "🎉 完整升级流程执行成功！"
}

# 主函数
main() {
    local command=$1
    local arg=$2

    case "$command" in
        "up")
            check_container_status
            test_database_connection
            backup_database
            run_migration "up" "$arg"
            ;;
        "down")
            check_container_status
            test_database_connection
            backup_database
            run_migration "down" "$arg"
            ;;
        "backup")
            check_container_status
            test_database_connection
            backup_database
            ;;
        "restore")
            check_container_status
            test_database_connection
            restore_database "$arg"
            ;;
        "list")
            list_migrations
            ;;
        "full-upgrade")
            full_upgrade
            ;;
        *)
            show_help
            exit 1
            ;;
    esac
}

# 检查参数
if [ $# -eq 0 ]; then
    show_help
    exit 1
fi

# 检查必要文件
if [ ! -f "$COMPOSE_FILE" ]; then
    log_error "Docker Compose文件不存在: $COMPOSE_FILE"
    log_info "请确保在deployment目录下执行此脚本"
    exit 1
fi

if [ ! -f "$ENV_FILE" ]; then
    log_error "环境变量文件不存在: $ENV_FILE"
    log_info "请确保在deployment目录下执行此脚本"
    exit 1
fi

# 执行主函数
main "$@"