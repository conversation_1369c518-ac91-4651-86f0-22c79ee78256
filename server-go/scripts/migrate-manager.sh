#!/bin/bash

# 统一迁移管理脚本
# 支持本地开发环境和Docker容器环境的数据库迁移

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示标题
show_header() {
    echo "=================================================="
    echo "🔧 统一数据库迁移管理器"
    echo "=================================================="
    echo ""
}

# 检测环境类型
detect_environment() {
    # 优先使用ENV环境变量
    if [ -n "$ENV" ]; then
        echo "$ENV"
        return
    fi

    # 检查APP_MODE环境变量
    if [ "$APP_MODE" = "production" ]; then
        echo "prod"
        return
    fi

    # 检查是否有生产环境配置文件
    if [ -f "../../deployment/docker-compose.prod.yml" ]; then
        echo "prod"
    else
        echo "dev"
    fi
}

# 显示环境信息
show_environment_info() {
    local env_type=$1
    
    echo "🌍 检测到的环境类型: $env_type"
    echo ""
    
    case "$env_type" in
        "prod")
            echo "🚀 生产环境（Docker容器）"
            echo "   - 使用 production/migrate-docker.sh"
            echo "   - 数据库: Docker容器中的MySQL"
            echo "   - 配置文件: deployment/docker-compose.prod.yml"
            ;;
        "dev")
            echo "💻 开发环境（本地Docker）"
            echo "   - 使用 local/migrate.sh"
            echo "   - 数据库: 本地Docker MySQL"
            echo "   - 容器: weishi_mysql_local"
            ;;
        *)
            echo "❓ 未知环境: $env_type"
            echo "   - 支持的环境: dev, prod"
            ;;
    esac
    echo ""
}

# 执行迁移
execute_migration() {
    local env_type=$1
    local command=$2
    local arg=$3
    
    case "$env_type" in
        "prod")
            log_step "使用生产环境迁移脚本..."
            if [ -f "./production/migrate-docker.sh" ]; then
                cd production && ./migrate-docker.sh "$command" "$arg"
            else
                log_error "生产环境迁移脚本不存在: ./production/migrate-docker.sh"
                exit 1
            fi
            ;;
        "dev")
            log_step "使用开发环境迁移脚本..."
            if [ -f "./local/migrate.sh" ]; then
                cd local && ./migrate.sh "$command" "$arg"
            else
                log_error "开发环境迁移脚本不存在: ./local/migrate.sh"
                exit 1
            fi
            ;;
        *)
            log_error "未知的环境类型: $env_type (支持: dev, prod)"
            log_info "使用方法: ENV=dev make migrate-up VERSION=002"
            log_info "或者: ENV=prod make migrate-up VERSION=002"
            exit 1
            ;;
    esac
}

# 显示帮助
show_help() {
    echo "统一数据库迁移管理器"
    echo ""
    echo "用法:"
    echo "  $0 [环境] [命令] [参数]"
    echo ""
    echo "环境选项:"
    echo "  auto                     自动检测环境（默认）"
    echo "  production               强制使用生产环境（Docker）"
    echo "  local                    强制使用本地开发环境"
    echo ""
    echo "命令选项:"
    echo "  up <migration_number>    执行迁移"
    echo "  down <migration_number>  回滚迁移"
    echo "  backup                   备份数据库"
    echo "  list                     列出可用迁移"
    echo "  full-upgrade             执行完整升级流程（仅Docker）"
    echo "  status                   显示环境状态"
    echo ""
    echo "可用的迁移版本:"
    echo "  001 - 优化表结构（删除冗余字段）"
    echo "  002 - 删除services表"
    echo ""
    echo "示例:"
    echo "  $0 up 002                自动检测环境并执行002号迁移"
    echo "  $0 production up 002     强制使用生产环境执行迁移"
    echo "  $0 local backup          强制使用本地环境备份数据库"
    echo "  $0 status                显示当前环境状态"
    echo "  $0 list                  列出可用迁移"
}

# 显示状态
show_status() {
    local env_type=$1
    
    echo "📊 环境状态检查"
    echo "=================="
    echo ""
    
    case "$env_type" in
        "production")
            echo "🚀 生产环境状态:"
            if [ -f "../../deployment/docker-compose.prod.yml" ]; then
                echo "   ✅ Docker Compose文件存在"
            else
                echo "   ❌ Docker Compose文件不存在"
            fi

            if [ -f "../../deployment/production.env" ]; then
                echo "   ✅ 环境变量文件存在"
            else
                echo "   ❌ 环境变量文件不存在"
            fi

            if [ -f "./production/migrate-docker.sh" ]; then
                echo "   ✅ 生产环境迁移脚本存在"
            else
                echo "   ❌ 生产环境迁移脚本不存在"
            fi
            ;;
        "local")
            echo "💻 本地开发环境状态:"
            if [ -f "./local/migrate.sh" ]; then
                echo "   ✅ 本地迁移脚本存在"
            else
                echo "   ❌ 本地迁移脚本不存在"
            fi
            ;;
    esac
    
    echo ""
    echo "📁 迁移文件状态:"
    if [ -d "./migrations" ]; then
        echo "   ✅ 迁移目录存在"
        local count=$(ls -1 ./migrations/*.sql 2>/dev/null | wc -l)
        echo "   📄 迁移文件数量: $count"
    else
        echo "   ❌ 迁移目录不存在"
    fi
}

# 主函数
main() {
    show_header
    
    # 解析参数
    local env_arg=""
    local command=""
    local migration_arg=""
    
    # 检查第一个参数是否是环境类型
    case "$1" in
        "production"|"local"|"auto")
            env_arg="$1"
            command="$2"
            migration_arg="$3"
            ;;
        *)
            env_arg="auto"
            command="$1"
            migration_arg="$2"
            ;;
    esac
    
    # 确定环境类型
    local env_type
    if [ "$env_arg" = "auto" ]; then
        env_type=$(detect_environment)
    else
        env_type="$env_arg"
    fi
    
    # 显示环境信息
    show_environment_info "$env_type"
    
    # 执行命令
    case "$command" in
        "up"|"down"|"backup"|"list"|"full-upgrade")
            execute_migration "$env_type" "$command" "$migration_arg"
            ;;
        "status")
            show_status "$env_type"
            ;;
        "help"|"--help"|"-h"|"")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 检查是否在正确的目录
if [ ! -d "./migrations" ]; then
    log_error "请在 server-go/scripts 目录下执行此脚本"
    echo ""
    echo "正确的执行方式:"
    echo "  cd server-go/scripts"
    echo "  ./migrate-manager.sh [命令]"
    exit 1
fi

# 执行主函数
main "$@"
