# SQL文件目录

## 📋 概述

本目录包含项目中使用的各种SQL文件，包括数据库初始化、数据导入、结构定义等。

## 📁 文件分类

### 数据库初始化
- `create_database.sql` - 创建数据库脚本
- `init_database.sql` - 数据库初始化脚本
- `database_structure.sql` - 数据库结构定义

### 数据管理
- `database_data.sql` - 基础数据导入
- `database_full.sql` - 完整数据库备份
- `create_file_uploads_table.sql` - 文件上传表创建

### 特定操作
- `drop_services_table.sql` - 删除services表脚本

## 🚀 使用方法

### 直接执行SQL文件

```bash
# 本地MySQL
mysql -u root -p weizhi < create_database.sql

# Docker容器
docker-compose -f ../../deployment/docker-compose.prod.yml --env-file ../../deployment/production.env exec -T mysql mysql -u root -p"Ydb3344%" weizhi < init_database.sql
```

### 通过迁移脚本使用

SQL文件通常通过迁移脚本自动执行，不建议直接手动执行。

## 📝 文件说明

### create_database.sql
- **用途**: 创建项目数据库
- **使用场景**: 初次部署时创建数据库

### init_database.sql
- **用途**: 初始化数据库结构和基础数据
- **使用场景**: 新环境部署时的完整初始化

### database_structure.sql
- **用途**: 仅包含数据库表结构
- **使用场景**: 结构对比、文档生成

### database_data.sql
- **用途**: 基础数据导入
- **使用场景**: 测试环境数据准备

### database_full.sql
- **用途**: 完整数据库备份
- **使用场景**: 数据迁移、环境复制

### create_file_uploads_table.sql
- **用途**: 创建文件上传相关表
- **使用场景**: 文件管理功能部署

### drop_services_table.sql
- **用途**: 删除services表
- **使用场景**: 模块清理（已通过迁移脚本执行）

## ⚠️ 使用注意事项

### 安全提醒
1. **备份优先**: 执行任何SQL前先备份数据
2. **环境确认**: 确认在正确的数据库环境中执行
3. **权限检查**: 确保有足够的数据库权限

### 执行顺序
1. `create_database.sql` - 创建数据库
2. `init_database.sql` - 初始化结构和数据
3. 其他特定功能SQL文件

### 版本管理
- SQL文件应与代码版本保持同步
- 重要变更应通过迁移脚本管理
- 避免直接修改生产环境

## 🔧 维护指南

### 添加新SQL文件
1. 按功能分类命名
2. 添加详细注释
3. 更新本README文档

### 文件命名规范
- 使用小写字母和下划线
- 功能描述要清晰
- 包含版本信息（如需要）

### 文档更新
- 每次添加新文件时更新文档
- 说明文件用途和使用场景
- 记录重要的执行注意事项

## 📚 相关文档

- `../migrations/README.md` - 数据库迁移说明
- `../local/README.md` - 本地环境脚本
- `../production/README.md` - 生产环境脚本
- `../../deployment/README.md` - 部署环境说明
