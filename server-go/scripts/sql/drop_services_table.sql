-- 删除services表的SQL脚本
-- 执行前请确保已备份重要数据

-- 检查表是否存在
SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    DATA_LENGTH,
    INDEX_LENGTH
FROM 
    information_schema.TABLES 
WHERE 
    TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'our_services';

-- 如果需要备份数据，可以先执行以下命令：
-- CREATE TABLE our_services_backup AS SELECT * FROM our_services;

-- 删除our_services表
DROP TABLE IF EXISTS our_services;

-- 验证表已被删除
SELECT 
    TABLE_NAME
FROM 
    information_schema.TABLES 
WHERE 
    TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'our_services';

-- 如果上面的查询返回空结果，说明表已成功删除
