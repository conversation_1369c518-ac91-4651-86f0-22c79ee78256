-- 创建文件上传表
CREATE TABLE IF NOT EXISTS `file_uploads` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `file_name` varchar(255) NOT NULL COMMENT '存储文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_size` bigint(20) NOT NULL COMMENT '文件大小（字节）',
  `mime_type` varchar(100) NOT NULL COMMENT '文件类型',
  `bucket_name` varchar(100) NOT NULL COMMENT '存储桶名称',
  `cos_url` varchar(500) NOT NULL COMMENT 'COS访问URL',
  `uploaded_by` bigint(20) unsigned NOT NULL COMMENT '上传用户ID',
  `module` varchar(50) NOT NULL COMMENT '所属模块',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '文件状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_uploaded_by` (`uploaded_by`),
  KEY `idx_module` (`module`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件上传记录表'; 