-- 删除services表迁移脚本
-- 版本: 002
-- 描述: 完全删除services表，该模块已不再需要
-- 创建时间: 2025-01-07

-- 开始事务
START TRANSACTION;

-- 记录删除前的表信息
SELECT 
    'Before deletion' as action,
    TABLE_NAME,
    TABLE_ROWS,
    DATA_LENGTH,
    INDEX_LENGTH,
    CREATE_TIME
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'services';

-- 备份services表数据到临时表（可选，用于紧急恢复）
CREATE TABLE IF NOT EXISTS `services_backup_20250808` AS
SELECT * FROM `services`;

-- 记录备份的数据量
SELECT
    'Backup created' as action,
    COUNT(*) as backup_record_count
FROM `services_backup_20250808`;

-- 删除services表
-- 注意：这将删除表结构和所有数据
DROP TABLE IF EXISTS `services`;

-- 验证表已被删除
SELECT 
    'After deletion' as action,
    CASE
        WHEN COUNT(*) = 0 THEN 'services table successfully deleted'
        ELSE 'services table still exists'
    END as status
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'services';

-- 显示当前数据库中剩余的内容管理表
SELECT 
    'Remaining content tables' as info,
    TABLE_NAME,
    TABLE_ROWS
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('swipers', 'news', 'project_cases', 'partners', 'friend_links', 'recruitments', 'part_platform')
ORDER BY TABLE_NAME;

-- 提交事务
COMMIT;

-- 迁移完成提示
SELECT
    '✅ Migration 002 completed successfully' as status,
    'services table has been dropped' as details,
    'Backup table: services_backup_20250808' as backup_info;
