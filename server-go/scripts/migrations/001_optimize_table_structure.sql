-- 数据库表结构优化迁移脚本
-- 版本: 001
-- 描述: 删除冗余字段，优化表结构
-- 创建时间: 2025-01-04

-- 开始事务
START TRANSACTION;

-- 1. 优化 our_services 表
-- 删除冗余字段：name, type, description, fullDescription, features, equipments, testItems
ALTER TABLE `our_services`
DROP COLUMN `name`,
DROP COLUMN `type`,
DROP COLUMN `description`,
DROP COLUMN `fullDescription`,
DROP COLUMN `features`,
DROP COLUMN `equipments`,
DROP COLUMN `testItems`;

-- 2. 优化 project_cases 表
-- 删除冗余字段：title, description, sort, cover_image, tags, order
ALTER TABLE `project_cases`
DROP COLUMN `title`,
DROP COLUMN `description`,
DROP COLUMN `sort`,
DROP COLUMN `cover_image`,
DROP COLUMN `tags`,
DROP COLUMN `order`;

-- 3. 优化 swipers 表
-- 删除冗余字段：status
ALTER TABLE `swipers`
DROP COLUMN `status`;

-- 4. 优化 part_platform 表
-- 删除冗余字段：order
ALTER TABLE `part_platform`
DROP COLUMN `order`;

-- 5. 优化 recruitments 表
-- 删除冗余字段：is_active, order
ALTER TABLE `recruitments`
DROP COLUMN `is_active`,
DROP COLUMN `order`;

-- 提交事务
COMMIT;

-- 验证表结构
SELECT 'our_services' as table_name, COUNT(*) as column_count 
FROM information_schema.columns 
WHERE table_schema = 'weizhi' AND table_name = 'our_services'
UNION ALL
SELECT 'project_cases', COUNT(*) 
FROM information_schema.columns 
WHERE table_schema = 'weizhi' AND table_name = 'project_cases'
UNION ALL
SELECT 'swipers', COUNT(*) 
FROM information_schema.columns 
WHERE table_schema = 'weizhi' AND table_name = 'swipers'
UNION ALL
SELECT 'part_platform', COUNT(*) 
FROM information_schema.columns 
WHERE table_schema = 'weizhi' AND table_name = 'part_platform'
UNION ALL
SELECT 'recruitments', COUNT(*) 
FROM information_schema.columns 
WHERE table_schema = 'weizhi' AND table_name = 'recruitments';
