-- 数据库表结构优化回滚脚本
-- 版本: 001
-- 描述: 恢复删除的字段（如果需要回滚）
-- 创建时间: 2025-01-04

-- 开始事务
START TRANSACTION;

-- 1. 恢复 our_services 表字段
ALTER TABLE `our_services` 
ADD COLUMN IF NOT EXISTS `name` varchar(255) NOT NULL DEFAULT '' COMMENT '服务名称',
ADD COLUMN IF NOT EXISTS `type` varchar(50) NOT NULL DEFAULT '' COMMENT '服务类型',
ADD COLUMN IF NOT EXISTS `description` text NOT NULL COMMENT '服务描述',
ADD COLUMN IF NOT EXISTS `fullDescription` text NOT NULL COMMENT '详细描述',
ADD COLUMN IF NOT EXISTS `features` text NOT NULL COMMENT '服务特点',
ADD COLUMN IF NOT EXISTS `equipments` text COMMENT '设备信息',
ADD COLUMN IF NOT EXISTS `testItems` text COMMENT '检测项目';

-- 2. 恢复 project_cases 表字段
ALTER TABLE `project_cases`
ADD COLUMN IF NOT EXISTS `title` varchar(100) COMMENT '案例标题',
ADD COLUMN IF NOT EXISTS `description` text NOT NULL DEFAULT '' COMMENT '案例描述',
ADD COLUMN IF NOT EXISTS `sort` bigint DEFAULT 0 COMMENT '排序（旧字段）',
ADD COLUMN IF NOT EXISTS `cover_image` varchar(255) COMMENT '封面图片',
ADD COLUMN IF NOT EXISTS `tags` varchar(255) COMMENT '标签',
ADD COLUMN IF NOT EXISTS `order` bigint DEFAULT 0 COMMENT '排序';

-- 3. 恢复 swipers 表字段
ALTER TABLE `swipers`
ADD COLUMN IF NOT EXISTS `status` varchar(20) DEFAULT 'active' COMMENT '状态';

-- 4. 恢复 part_platform 表字段
ALTER TABLE `part_platform`
ADD COLUMN IF NOT EXISTS `order` bigint DEFAULT 0 COMMENT '排序';

-- 5. 恢复 recruitments 表字段
ALTER TABLE `recruitments`
ADD COLUMN IF NOT EXISTS `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
ADD COLUMN IF NOT EXISTS `order` bigint DEFAULT 0 COMMENT '排序';

-- 提交事务
COMMIT;

-- 验证表结构
SELECT 'Rollback completed' as status;
