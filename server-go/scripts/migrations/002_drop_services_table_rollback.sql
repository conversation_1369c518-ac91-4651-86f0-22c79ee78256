-- 删除services表回滚脚本（修正版）
-- 版本: 002
-- 描述: 恢复services表（如果需要回滚），优先从备份表恢复数据
-- 创建时间: 2025-08-09
-- 警告: 如无备份表，将只恢复表结构

-- 开始事务
START TRANSACTION;

-- 检查备份表是否存在
SELECT
    'Checking backup table' as action,
    CASE
        WHEN COUNT(*) > 0 THEN 'Backup table exists'
        ELSE 'Backup table not found'
    END as status
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'services_backup_20250808';

-- 重新创建 services 表（使用当前结构定义）
-- 结构参考 scripts/sql/database_structure.sql
CREATE TABLE IF NOT EXISTS `services` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `icon` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `order` bigint DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_services_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 如果备份表存在，恢复数据
INSERT INTO `services` (`id`, `created_at`, `updated_at`, `deleted_at`, `title`, `description`, `icon`, `order`)
SELECT `id`, `created_at`, `updated_at`, `deleted_at`, `title`, `description`, `icon`, `order`
FROM `services_backup_20250808`
WHERE EXISTS (
    SELECT 1 FROM information_schema.TABLES
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'services_backup_20250808'
);

-- 验证表恢复情况
SELECT
    'Table restoration' as action,
    TABLE_NAME,
    TABLE_ROWS,
    'Table restored successfully' as status
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'services';

-- 显示恢复的数据量
SELECT
    'Data restoration' as action,
    COUNT(*) as restored_record_count
FROM `services`;

-- 提交事务
COMMIT;

-- 回滚完成提示
SELECT
    '⚠️  Migration 002 rollback completed' as status,
    'services table has been restored' as details,
    'Please verify data integrity manually' as warning;
