-- 测试迁移脚本
-- 版本: 003
-- 描述: 测试迁移系统功能
-- 创建时间: 2025-08-08

-- 开始事务
START TRANSACTION;

-- 创建一个测试表
CREATE TABLE IF NOT EXISTS `migration_test` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `test_field` varchar(255) NOT NULL DEFAULT '',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入测试数据
INSERT INTO `migration_test` (`test_field`) VALUES 
('Migration 003 executed successfully'),
('Test data for migration system');

-- 验证创建结果
SELECT 
    'Migration 003 completed' as status,
    COUNT(*) as test_records
FROM `migration_test`;

-- 提交事务
COMMIT;

-- 迁移完成提示
SELECT 
    '✅ Migration 003 completed successfully' as status,
    'Test table migration_test created' as details;
