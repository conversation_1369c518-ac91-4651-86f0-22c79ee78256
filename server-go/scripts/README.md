# 数据库脚本目录

## 📋 概述

本目录包含项目的数据库相关脚本和工具，按功能和环境进行了分类管理。

## 📁 目录结构

```
server-go/scripts/
├── README.md                    # 📚 总体说明文档
├── migrate-manager.sh           # 🔧 统一迁移管理器
├── local/                       # 💻 本地开发环境
│   ├── migrate.sh              # 本地迁移脚本
│   ├── drop_services_migration.sh # 快速删除脚本
│   └── README.md               # 本地环境说明
├── production/                  # 🚀 生产环境
│   ├── migrate-docker.sh       # Docker迁移脚本
│   └── README.md               # 生产环境说明
├── migrations/                  # 📋 迁移文件目录
│   ├── 001_optimize_table_structure.sql
│   ├── 002_drop_services_table.sql
│   └── README.md               # 迁移文件说明
├── sql/                        # 📄 SQL文件目录
│   ├── init_database.sql      # 数据库初始化
│   ├── database_structure.sql # 数据库结构
│   ├── create_database.sql    # 创建数据库
│   └── README.md               # SQL文件说明

```

## 📂 目录分类说明

### 核心目录

#### local/ - 本地开发环境
- **用途**: 本地开发时的数据库迁移和管理
- **主要文件**: migrate.sh, drop_services_migration.sh
- **使用场景**: 开发者本地环境的数据库操作

#### production/ - 生产环境
- **用途**: Docker容器环境的数据库迁移
- **主要文件**: migrate-docker.sh
- **使用场景**: 生产环境的数据库升级和维护

#### migrations/ - 迁移文件
- **用途**: 版本化的数据库迁移脚本
- **文件格式**: SQL文件和对应的回滚文件
- **使用场景**: 数据库结构升级和回滚

#### sql/ - SQL文件
- **用途**: 各种数据库相关的SQL脚本
- **包含内容**: 初始化脚本、结构定义、数据导入等
- **使用场景**: 数据库初始化和数据管理

### 根目录文件

#### migrate-manager.sh
- **用途**: 统一的迁移管理器
- **功能**: 自动检测环境并选择合适的迁移脚本
- **使用场景**: 简化迁移操作的统一入口

### 1. `setup_database.sh` - 数据库完整设置脚本

这是主要的数据库设置脚本，用于完整地初始化项目数据库。

#### 功能
- 创建数据库
- 初始化数据表结构（通过Go程序的AutoMigrate）
- 导入管理员初始化数据
- 导入业务种子数据

#### 使用方法

```bash
# 完整初始化（推荐）
./scripts/setup_database.sh

# 查看帮助信息
./scripts/setup_database.sh --help

# 跳过某些步骤的初始化
./scripts/setup_database.sh --skip-db-create      # 跳过数据库创建
./scripts/setup_database.sh --skip-table-init    # 跳过表结构初始化
./scripts/setup_database.sh --skip-admin-data    # 跳过管理员数据导入
./scripts/setup_database.sh --skip-seed-data     # 跳过业务数据导入

# 组合使用（只初始化表结构和管理员数据）
./scripts/setup_database.sh --skip-db-create --skip-seed-data
```

#### 执行流程
1. **检查MySQL环境** - 验证MySQL客户端是否安装
2. **读取配置** - 从`config.yaml`读取数据库连接配置
3. **测试连接** - 验证数据库连接是否正常
4. **创建数据库** - 执行`create_database.sql`
5. **初始化表结构** - 通过Go程序执行GORM AutoMigrate
6. **导入管理员数据** - 执行`init_admin.sql`
7. **导入业务数据** - 执行`seed_data.sql`

#### 输出信息
脚本执行成功后会显示：
- 管理员登录信息（用户名: admin, 密码: admin123）
- 管理后台地址（http://localhost:3001/admin）
- 已导入的数据类型列表

### 2. `create_database.sql` - 数据库创建脚本

创建项目数据库的SQL脚本。

```sql
-- 删除已存在的数据库
DROP DATABASE IF EXISTS weishi_db1;

-- 创建新数据库
CREATE DATABASE weishi_db1 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;
```

### 3. `init_admin.sql` - 管理员初始化数据脚本

包含管理员系统的基础数据：

#### 管理员用户
- **用户名**: admin
- **密码**: admin123
- **邮箱**: <EMAIL>
- **角色**: 超级管理员

#### 角色数据
- 超级管理员（super_admin）
- 管理员（admin）
- 编辑员（editor）

#### 权限数据
- 系统管理权限
- 用户管理权限
- 内容管理权限
- 各种API操作权限

#### 关联关系
- 管理员用户与角色的关联
- 角色与权限的关联

### 4. `seed_data.sql` - 业务种子数据脚本

包含业务系统的示例数据：

- **友情链接** - 合作伙伴网站链接
- **合作伙伴** - 合作伙伴Logo和信息
- **零件平台** - 产品零件信息
- **项目案例** - 项目展示案例
- **服务配置** - 公司服务项目
- **轮播图** - 首页轮播图片
- **招聘信息** - 人才招聘信息

## 使用场景

### 1. 全新项目部署
```bash
# 完整初始化所有数据
./scripts/setup_database.sh
```

### 2. 开发环境重置
```bash
# 重新创建数据库和所有数据
./scripts/setup_database.sh
```

### 3. 只需要管理员功能
```bash
# 跳过业务数据，只初始化管理员系统
./scripts/setup_database.sh --skip-seed-data
```

### 4. 只需要业务数据
```bash
# 跳过管理员数据，只初始化业务系统
./scripts/setup_database.sh --skip-admin-data
```

### 5. 表结构已存在，只导入数据
```bash
# 跳过数据库创建和表结构初始化
./scripts/setup_database.sh --skip-db-create --skip-table-init
```

## 注意事项

### 1. 配置文件
确保项目根目录存在`config.yaml`文件，包含正确的数据库配置：

```yaml
database:
  host: "localhost"
  port: "3306"
  username: "root"
  password: "your_password"
  database: "weishi_db1"
  charset: "utf8mb4"
```

### 2. MySQL权限
确保MySQL用户具有以下权限：
- CREATE DATABASE
- DROP DATABASE
- CREATE TABLE
- INSERT, UPDATE, DELETE, SELECT

### 3. 数据备份
脚本会删除已存在的数据库，请在执行前备份重要数据。

### 4. 网络连接
脚本会自动检测并适配MySQL的SSL配置，支持：
- SSL禁用模式
- SSL跳过模式
- 默认SSL模式

## 故障排除

### 1. MySQL连接失败
- 检查MySQL服务是否启动
- 验证用户名和密码
- 确认主机和端口配置

### 2. 权限不足
- 确保MySQL用户具有足够权限
- 检查数据库用户的权限设置

### 3. 文件不存在
- 确保在项目根目录执行脚本
- 检查所有SQL文件是否存在

### 4. Go程序编译失败
- 确保Go环境正确安装
- 检查项目依赖是否完整

## 更新记录

### v2.0 (当前版本)
- ✅ 添加管理员初始化数据导入
- ✅ 分离管理员数据和业务数据导入
- ✅ 更新表名为Admin前缀格式
- ✅ 改进脚本参数和帮助信息
- ✅ 优化输出信息和成功提示

### v1.0
- ✅ 基础数据库创建功能
- ✅ 表结构自动迁移
- ✅ 业务种子数据导入

## 使用 Makefile 管理数据库

除了直接使用脚本，您也可以使用 Makefile 命令来管理数据库：

### 基本数据库设置命令

```bash
# 完整设置数据库（推荐）
make setup-database

# 设置数据库但跳过管理员数据
make setup-database-skip-admin

# 仅创建数据库
make create-database

# 仅初始化表结构
make init-tables

# 仅初始化管理员数据
make init-admin

# 仅导入业务数据
make seed-data
```

### 数据库管理命令

```bash
# 检查数据库连接
make check-db

# 重置管理员数据
make reset-admin

# 重置业务数据
make reset-business

# 重置所有数据
make reset-db

# 备份数据库
make backup-db

# 删除数据库（危险操作）
make drop-database
```

### 快速开始

```bash
# 一键启动整个项目（包含完整数据库设置）
make quick-start
```

这个命令会依次执行：
1. 安装依赖 (`make deps`)
2. 编译项目 (`make build`)
3. 完整设置数据库 (`make setup-database`)
4. 启动服务器 (`make start`)

### Makefile vs 脚本的区别

| 特性 | Makefile | 脚本 |
|------|----------|------|
| 使用方式 | `make setup-database` | `./scripts/setup_database.sh` |
| 参数支持 | 通过不同命令名 | 通过命令行参数 |
| 集成度 | 与项目构建流程集成 | 独立脚本 |
| 灵活性 | 固定流程 | 支持参数化配置 |

### 推荐使用场景

- **开发环境快速搭建**: 使用 `make quick-start`
- **生产环境部署**: 使用 `make setup-database`
- **日常开发**: 使用各种单独的 make 命令
- **自定义需求**: 使用脚本文件并传递参数

## 数据库导出脚本 🆕

### 5. `export_database.sh` - 功能完整的数据库导出脚本

支持多种导出选项的完整导出脚本（Linux/macOS）。

#### 功能特点
- ✅ 自动排除 admin 用户数据
- ✅ 保留角色权限体系完整性
- ✅ 支持结构和数据分离导出
- ✅ 支持 Docker 容器导出
- ✅ 灵活的命令行参数配置

#### 使用方法

```bash
# 显示帮助信息
./export_database.sh --help

# 使用 Docker 容器导出（推荐）
./export_database.sh --docker

# 仅导出结构
./export_database.sh --structure-only

# 仅导出数据
./export_database.sh --data-only

# 包含 admin 用户数据
./export_database.sh --include-admin

# 指定输出目录
./export_database.sh -o /backup/exports

# 指定数据库连接参数
./export_database.sh -H localhost -P 3307 -u root -p "password"
```

### 6. `quick_export.sh` - 快速数据库导出脚本

简化版的快速导出脚本，适合日常使用。

#### 功能
- 🚀 一键快速导出
- 📋 自动生成时间戳文件名
- 🗄️ 同时生成结构、数据、完整三种文件
- ⚠️ 自动排除 admin 用户数据

#### 使用方法

```bash
# 快速导出（最简单）
./quick_export.sh
```

#### 输出文件
```
exports/
├── structure_20250722_001702.sql      # 数据库结构
├── data_no_admin_20250722_001702.sql  # 数据（排除admin）
└── full_no_admin_20250722_001702.sql  # 完整导出
```

### 7. `export_database.bat` - Windows 数据库导出脚本

Windows 用户专用的批处理导出脚本。

#### 使用方法

```cmd
# 双击运行或命令行执行
export_database.bat
```

### 导出脚本使用场景

#### 1. 开发环境数据备份
```bash
# 快速备份当前开发数据
./quick_export.sh
```

#### 2. 生产环境部署准备
```bash
# 导出用于生产部署的数据（自动排除admin用户）
./export_database.sh --docker
```

#### 3. 数据迁移
```bash
# 导出完整数据用于迁移
./export_database.sh --docker -o /backup/migration
```

#### 4. 测试环境初始化
```bash
# 仅导出结构用于测试环境
./export_database.sh --structure-only
```

### 导出数据的安全特性

#### 自动排除敏感数据
- **默认排除 admin 用户**：防止生产环境中的管理员密码泄露
- **保留角色权限结构**：确保权限系统完整性
- **清理操作日志**：避免敏感操作记录泄露

#### 部署友好
- 导出的数据可以安全地用于生产环境部署
- admin 用户将通过密码初始化功能自动创建
- 保持数据库结构和权限体系的完整性

### 导入导出的数据

```bash
# 导入结构
mysql -h localhost -P 3307 -u root -p weizhi < structure_20250722_001702.sql

# 导入数据
mysql -h localhost -P 3307 -u root -p weizhi < data_no_admin_20250722_001702.sql

# 导入完整数据库
mysql -h localhost -P 3307 -u root -p weizhi < full_no_admin_20250722_001702.sql

# 使用 Docker 导入
docker cp full_no_admin_20250722_001702.sql weishi-mysql:/tmp/
docker exec weishi-mysql mysql -u root -p"Ydb3344%" weizhi < /tmp/full_no_admin_20250722_001702.sql
```

## 相关文档

- [数据库导出详细指南](../../docs/database-export.md)
- [管理员密码初始化](../../docs/admin-password-init.md)
