# 本地开发环境迁移脚本

## 📋 概述

本目录包含本地开发环境的数据库迁移脚本，适用于开发者在本地机器上进行数据库操作。

## 📁 文件说明

- `migrate.sh` - 本地环境数据库迁移脚本
- `drop_services_migration.sh` - 快速删除Services表脚本

## 🚀 使用方法

### 在local目录下执行

```bash
cd server-go/scripts/local

# 查看帮助
./migrate.sh --help

# 执行迁移
./migrate.sh up 002

# 快速删除Services表
./drop_services_migration.sh
```

### 通过统一管理器执行

```bash
cd server-go/scripts

# 强制使用本地环境
./migrate-manager.sh local up 002
```

## ⚙️ 配置说明

### 数据库连接配置

在 `migrate.sh` 中修改以下配置：

```bash
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="weizhi"
DB_USER="root"
DB_PASS="Ydb3344%"
```

### 迁移文件路径

脚本会自动查找 `../migrations/` 目录中的迁移文件。

## 🛡️ 安全注意事项

1. **仅用于开发环境**：这些脚本仅适用于本地开发环境
2. **数据备份**：执行前会自动备份数据库
3. **权限检查**：确保数据库用户有足够权限

## 🔧 故障排除

### 常见问题

1. **连接失败**
   ```
   ERROR 2003: Can't connect to MySQL server
   ```
   **解决**: 检查MySQL服务是否启动，配置是否正确

2. **权限不足**
   ```
   ERROR 1142: DROP command denied
   ```
   **解决**: 确保数据库用户有DROP权限

3. **文件不存在**
   ```
   迁移文件不存在
   ```
   **解决**: 检查migrations目录是否存在

## 📚 相关文档

- `../migrations/README.md` - 迁移文件说明
- `../README.md` - 脚本总体说明
