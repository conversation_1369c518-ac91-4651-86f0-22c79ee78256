#!/bin/bash

# 删除Services表的快速迁移脚本
# 这是migration 002的快速执行版本

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示标题
show_header() {
    echo "=================================================="
    echo "🗑️  删除Services表迁移脚本 (Migration 002)"
    echo "=================================================="
    echo ""
}

# 确认操作
confirm_operation() {
    echo "⚠️  警告: 此操作将永久删除 our_services 表及其所有数据！"
    echo ""
    echo "📋 操作内容:"
    echo "   1. 备份当前数据库"
    echo "   2. 创建 our_services 表的备份副本"
    echo "   3. 删除 our_services 表"
    echo "   4. 验证删除结果"
    echo ""
    
    read -p "❓ 确定要继续吗？(输入 'YES' 确认): " confirmation
    
    if [ "$confirmation" != "YES" ]; then
        log_warn "操作已取消"
        exit 0
    fi
}

# 检查前置条件
check_prerequisites() {
    log_step "检查前置条件..."
    
    # 检查migrate.sh是否存在
    if [ ! -f "./migrate.sh" ]; then
        log_error "migrate.sh 脚本不存在，请确保在 server-go/scripts 目录下执行"
        exit 1
    fi
    
    # 检查迁移文件是否存在
    if [ ! -f "./migrations/002_drop_services_table.sql" ]; then
        log_error "迁移文件不存在: ./migrations/002_drop_services_table.sql"
        exit 1
    fi
    
    log_info "前置条件检查通过"
}

# 执行迁移
execute_migration() {
    log_step "执行迁移..."
    
    # 执行002号迁移
    if ./migrate.sh up 002; then
        log_info "迁移执行成功！"
        return 0
    else
        log_error "迁移执行失败！"
        return 1
    fi
}

# 验证结果
verify_result() {
    log_step "验证删除结果..."
    
    # 这里可以添加验证逻辑
    # 由于我们没有直接的MySQL访问，让用户手动验证
    echo ""
    echo "🔍 请手动验证删除结果:"
    echo "   mysql -u root -p weizhi -e \"SHOW TABLES LIKE 'our_services';\""
    echo ""
    echo "✅ 如果查询结果为空，说明表已成功删除"
    echo "📦 备份表名称: our_services_backup_20250107"
}

# 显示后续步骤
show_next_steps() {
    echo ""
    echo "=================================================="
    echo "🎉 迁移完成！"
    echo "=================================================="
    echo ""
    echo "📝 后续步骤:"
    echo "   1. 重启应用服务"
    echo "   2. 验证应用功能正常"
    echo "   3. 确认前端页面无异常"
    echo "   4. 监控系统运行状态"
    echo ""
    echo "🔄 如需回滚:"
    echo "   ./migrate.sh down 002"
    echo ""
    echo "📚 相关文档:"
    echo "   - docs/fixes/services-module-complete-removal.md"
    echo "   - scripts/migrations/README.md"
}

# 主函数
main() {
    show_header
    confirm_operation
    check_prerequisites
    
    if execute_migration; then
        verify_result
        show_next_steps
    else
        echo ""
        log_error "迁移失败，请检查错误信息并重试"
        echo ""
        echo "🔧 故障排除:"
        echo "   1. 检查数据库连接配置"
        echo "   2. 确认数据库用户权限"
        echo "   3. 查看详细错误日志"
        echo "   4. 参考 scripts/migrations/README.md"
        exit 1
    fi
}

# 检查参数
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "删除Services表迁移脚本"
    echo ""
    echo "用法:"
    echo "  $0                执行删除Services表迁移"
    echo "  $0 --help         显示帮助信息"
    echo ""
    echo "注意:"
    echo "  - 请在 server-go/scripts 目录下执行"
    echo "  - 执行前会自动备份数据库"
    echo "  - 操作不可逆，请谨慎执行"
    exit 0
fi

# 检查是否在正确的目录
if [ ! -f "./migrate.sh" ]; then
    log_error "请在 server-go/scripts 目录下执行此脚本"
    echo "正确的执行方式:"
    echo "  cd server-go/scripts"
    echo "  ./drop_services_migration.sh"
    exit 1
fi

# 执行主函数
main
