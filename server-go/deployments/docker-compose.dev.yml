version: '3.8'

services:
  # 开发环境MySQL数据库
  mysql:
    image: docker.cnb.cool/yuandongbin/docker-sync/mysql:8.0
    container_name: weishi-mysql-dev
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: Ydb3344%
      MYSQL_DATABASE: weizhi
      MYSQL_USER: weizhi
      MYSQL_PASSWORD: Ydb3344%
      TZ: Asia/Shanghai
    ports:
      - "3307:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./scripts/sql:/docker-entrypoint-initdb.d:ro
    networks:
      - weishi-dev-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-pYdb3344%"]
      timeout: 20s
      retries: 10
      interval: 10s
      start_period: 40s
    command: --default-authentication-plugin=mysql_native_password

volumes:
  mysql_dev_data:
    driver: local

networks:
  weishi-dev-network:
    driver: bridge
