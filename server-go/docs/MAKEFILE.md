# 🔧 Makefile 使用指南

## 📋 概述

简化的Makefile，专注于核心开发需求：
1. 服务启动、停止、重启
2. 默认使用Docker数据库
3. 数据导出（全数据、内容数据、管理员数据）
4. 数据导入（支持3种类型）
5. 数据库升级操作（迁移管理）

## 🚀 服务管理

### 开发模式
```bash
# 开发模式启动（自动重启）
make dev

# 需要先安装air
go install github.com/cosmtrek/air@latest
```

### 生产模式
```bash
# 编译并后台启动
make start

# 停止服务
make stop

# 重启服务
make restart

# 查看服务状态
make status

# 清理编译文件
make clean
```

## 🐳 Docker容器管理

### 容器操作
```bash
# 启动Docker容器
make container-up

# 停止Docker容器
make container-down

# 查看容器状态
make container-status

# 连接到MySQL容器
make docker-shell
```

### 数据库连接
```bash
# 检查数据库连接
make check-db
```

## 📦 数据导出

### 导出类型

#### 1. 完整数据库
```bash
make export-full
```
- 导出整个数据库
- 文件名：`exports/full_YYYYMMDD_HHMMSS.sql`

#### 2. 内容数据（业务数据）
```bash
make export-content
```
- 导出业务相关表：
  - `swipers` - 轮播图
  - `friend_links` - 友情链接
  - `partners` - 合作伙伴
  - `part_platform` - 平台信息
  - `project_cases` - 项目案例
  - `recruitments` - 招聘信息
- 文件名：`exports/content_YYYYMMDD_HHMMSS.sql`

#### 3. 管理员数据
```bash
make export-admin
```
- 导出管理相关表：
  - `admin_users` - 管理员用户
  - `roles` - 角色
  - `permissions` - 权限
  - `admin_user_roles` - 用户角色关联
  - `role_permissions` - 角色权限关联
  - `admin_logs` - 管理员日志
- 文件名：`exports/admin_YYYYMMDD_HHMMSS.sql`

## 📥 数据导入

### 导入语法
```bash
make import-sql FILE=文件路径 TYPE=类型
```

### 导入类型

#### 1. 完整数据库导入
```bash
make import-sql FILE=backup.sql TYPE=full
```

#### 2. 内容数据导入
```bash
make import-sql FILE=content_backup.sql TYPE=content
```

#### 3. 管理员数据导入
```bash
make import-sql FILE=admin_backup.sql TYPE=admin
```

### 导入示例
```bash
# 导入完整备份
make import-sql FILE=exports/full_20250107_143022.sql TYPE=full

# 导入内容数据
make import-sql FILE=exports/content_20250107_143022.sql TYPE=content

# 导入管理员数据
make import-sql FILE=exports/admin_20250107_143022.sql TYPE=admin

# 导入外部SQL文件
make import-sql FILE=/path/to/external.sql TYPE=full
```

## � 数据库升级操作

### 迁移管理

#### 查看可用迁移
```bash
# 列出所有可用的迁移文件
make migrate-list

# 查看迁移环境状态
make migrate-status
```

#### 执行迁移
```bash
# 执行指定版本的迁移
make migrate-up VERSION=002

# 执行完整升级流程（所有迁移）
make upgrade-full
```

#### 回滚迁移
```bash
# 回滚指定版本的迁移
make migrate-down VERSION=002
```

#### 备份操作
```bash
# 仅备份数据库（不执行迁移）
make migrate-backup
```

### 快速操作

#### 删除Services表
```bash
# 快速删除Services表（执行002号迁移）
make drop-services
```

### 升级流程示例

#### 1. 标准升级流程
```bash
# 1. 查看当前状态
make migrate-status

# 2. 列出可用迁移
make migrate-list

# 3. 备份数据库
make migrate-backup

# 4. 执行完整升级
make upgrade-full
```

#### 2. 单步升级流程
```bash
# 1. 查看可用迁移
make migrate-list

# 2. 执行指定迁移
make migrate-up VERSION=001
make migrate-up VERSION=002

# 3. 验证结果
make migrate-status
```

#### 3. 紧急回滚流程
```bash
# 1. 停止服务
make stop

# 2. 回滚迁移
make migrate-down VERSION=002

# 3. 重启服务
make start
```

### 迁移版本说明

#### 当前可用迁移
- **001**: 优化表结构（删除冗余字段）
- **002**: 删除services表

#### 迁移文件位置
```
server-go/scripts/migrations/
├── 001_optimize_table_structure.sql
├── 001_optimize_table_structure_rollback.sql
├── 002_drop_services_table.sql
└── 002_drop_services_table_rollback.sql
```

## �🔧 配置说明

### Docker配置
```makefile
DOCKER_MYSQL_CONTAINER=weishi-mysql-prod
DOCKER_COMPOSE_FILE=../deployment/docker-compose.prod.yml
DOCKER_ENV_FILE=../deployment/production.env
```

### 数据库配置
```makefile
DB_NAME=weizhi
DB_USER=root
DB_PASS=Ydb3344%
```

## 📊 使用场景

### 日常开发
```bash
# 1. 启动开发环境
make dev

# 2. 导出测试数据
make export-content

# 3. 执行数据库升级
make upgrade-full

# 4. 重置开发数据
make import-sql FILE=test_data.sql TYPE=content
```

### 数据备份
```bash
# 1. 导出完整备份
make export-full

# 2. 分类导出
make export-admin
make export-content

# 3. 验证备份
make import-sql FILE=backup.sql TYPE=full
```

### 环境迁移
```bash
# 源环境导出
make export-full

# 目标环境导入
make import-sql FILE=source_backup.sql TYPE=full

# 执行数据库升级
make upgrade-full
```

### 生产环境升级
```bash
# 1. 备份当前数据
make migrate-backup

# 2. 查看升级计划
make migrate-list

# 3. 执行完整升级
make upgrade-full

# 4. 验证升级结果
make migrate-status
```

## ⚠️ 注意事项

### 数据安全
1. **导入前备份**: 导入数据前先备份当前数据
2. **类型匹配**: 确保导入类型与SQL文件内容匹配
3. **文件检查**: 导入前检查SQL文件完整性

### 容器依赖
1. **容器运行**: 确保Docker容器正在运行
2. **网络连接**: 检查容器网络连接正常
3. **权限配置**: 确保数据库用户权限正确

### 文件管理
1. **导出目录**: 自动创建`exports/`目录
2. **文件命名**: 使用时间戳避免文件覆盖
3. **空间管理**: 定期清理旧的导出文件

## 🔍 故障排除

### 常见问题

#### 1. 容器连接失败
```bash
# 检查容器状态
make container-status

# 启动容器
make container-up

# 检查数据库连接
make check-db
```

#### 2. 导入失败
```bash
# 检查文件是否存在
ls -la exports/

# 检查文件权限
chmod 644 backup.sql

# 手动连接测试
make docker-shell
```

#### 3. 权限问题
```bash
# 检查Docker权限
docker ps

# 检查文件权限
ls -la exports/
```

## 📚 相关文档

- `../deployment/README.md` - Docker部署说明
- `scripts/README.md` - 数据库脚本说明
- `MIGRATION.md` - 数据库迁移指南
