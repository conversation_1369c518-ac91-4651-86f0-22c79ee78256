package docs

// 这个文件展示了GORM中自定义表名的各种方法
// 注意：这只是示例代码，不会被实际使用

import (
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

// ============== 方法一：实现 TableName() 方法（推荐） ==============

type User struct {
	ID   uint   `gorm:"primarykey"`
	Name string `gorm:"size:100"`
}

// TableName 自定义表名
func (User) TableName() string {
	return "sys_users" // 表名将是 sys_users 而不是 users
}

// ============== 方法二：使用动态表名 ==============

type Order struct {
	ID     uint `gorm:"primarykey"`
	Amount float64
}

// TableName 根据条件动态生成表名
func (o Order) TableName() string {
	// 可以根据业务逻辑动态返回不同的表名
	// 例如：按月分表
	return "orders_202501" // 动态表名
}

// ============== 方法三：带前缀的表名 ==============

type Product struct {
	ID   uint `gorm:"primarykey"`
	Name string
}

func (Product) TableName() string {
	return "shop_products" // 带业务前缀
}

// ============== 方法四：全局命名策略 ==============

// 在数据库初始化时配置全局命名策略
func setupNamingStrategy() *gorm.DB {
	db, _ := gorm.Open(nil, &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   "t_",  // 所有表名添加前缀 t_
			SingularTable: true,  // 使用单数表名，不自动复数化
			NoLowerCase:   false, // 转换为小写
		},
	})
	return db
}

// ============== 方法五：临时指定表名 ==============

type Article struct {
	ID    uint `gorm:"primarykey"`
	Title string
}

// 在查询时临时指定表名
func queryWithCustomTable(db *gorm.DB) {
	var articles []Article

	// 临时使用不同的表名
	db.Table("archived_articles").Find(&articles)

	// 或者使用 Scopes
	db.Scopes(func(d *gorm.DB) *gorm.DB {
		return d.Table("archived_articles")
	}).Find(&articles)
}

// ============== 方法六：环境相关的表名 ==============

type Log struct {
	ID      uint `gorm:"primarykey"`
	Message string
}

func (Log) TableName() string {
	// 根据环境变量决定表名
	env := "dev" // 从环境变量获取
	return env + "_logs"
}

// ============== 实际项目中的命名建议 ==============

// 1. 系统表：使用 sys_ 前缀
type SysUser struct {
	ID   uint `gorm:"primarykey"`
	Name string
}

func (SysUser) TableName() string {
	return "sys_users"
}

// 2. 业务表：使用业务模块前缀
type ShopOrder struct {
	ID     uint `gorm:"primarykey"`
	Amount float64
}

func (ShopOrder) TableName() string {
	return "shop_orders"
}

// 3. 日志表：使用 log_ 前缀
type LogOperation struct {
	ID     uint `gorm:"primarykey"`
	Action string
}

func (LogOperation) TableName() string {
	return "log_operations"
}

// 4. 配置表：使用 config_ 前缀
type ConfigSetting struct {
	ID    uint `gorm:"primarykey"`
	Key   string
	Value string
}

func (ConfigSetting) TableName() string {
	return "config_settings"
}

// ============== 批量设置表名前缀 ==============

// 为所有模型统一添加前缀的工具函数
func withPrefix(tableName string) string {
	return "weishi_" + tableName
}

type Category struct {
	ID   uint `gorm:"primarykey"`
	Name string
}

func (Category) TableName() string {
	return withPrefix("categories") // 结果：weishi_categories
}
