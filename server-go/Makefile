# 蔚之领域 Go 后端服务器 Makefile

# 项目配置
APP_NAME=weishi-server
BINARY_PATH=./build/main

# 环境配置 - 支持开发和生产环境
ENV ?= dev

# 数据库配置
DB_NAME=weizhi
DB_USER=root
DB_PASS=Ydb3344%
DEV_DB_PORT=3307
PROD_DB_PORT=3306

# 开发环境配置 - 本地Docker MySQL
DEV_MYSQL_CONTAINER=weishi-mysql-dev
DEV_COMPOSE_FILE=deployments/docker-compose.dev.yml
DEV_MYSQL_CMD=docker-compose -f $(DEV_COMPOSE_FILE) exec -T mysql mysql -u $(DB_USER) -p'$(DB_PASS)' $(DB_NAME)
DEV_MYSQLDUMP_CMD=docker-compose -f $(DEV_COMPOSE_FILE) exec -T mysql mysqldump -u $(DB_USER) -p'$(DB_PASS)' $(DB_NAME)

# 生产环境配置
PROD_MYSQL_CONTAINER=weishi-mysql-prod
PROD_COMPOSE_FILE=../deployment/docker-compose.prod.yml
PROD_ENV_FILE=../deployment/production.env
PROD_MYSQL_CMD=docker-compose -f $(PROD_COMPOSE_FILE) --env-file $(PROD_ENV_FILE) exec -T mysql mysql -u $(DB_USER) -p'$(DB_PASS)' $(DB_NAME)
PROD_MYSQLDUMP_CMD=docker-compose -f $(PROD_COMPOSE_FILE) --env-file $(PROD_ENV_FILE) exec -T mysql mysqldump -u $(DB_USER) -p'$(DB_PASS)' $(DB_NAME)

# 根据环境选择配置
ifeq ($(ENV),prod)
    MYSQL_CONTAINER=$(PROD_MYSQL_CONTAINER)
    COMPOSE_FILE=$(PROD_COMPOSE_FILE)
    ENV_FILE=$(PROD_ENV_FILE)
    MYSQL_CMD=$(PROD_MYSQL_CMD)
    MYSQLDUMP_CMD=$(PROD_MYSQLDUMP_CMD)
else
    MYSQL_CONTAINER=$(DEV_MYSQL_CONTAINER)
    COMPOSE_FILE=$(DEV_COMPOSE_FILE)
    ENV_FILE=
    MYSQL_CMD=$(DEV_MYSQL_CMD)
    MYSQLDUMP_CMD=$(DEV_MYSQLDUMP_CMD)
endif

.PHONY: help dev start stop restart status export-full export-content export-admin import-sql clean build migrate-up migrate-down migrate-list migrate-status migrate-backup upgrade-full drop-services dev-db-up dev-db-down dev-db-restart dev-db-logs dev-db-shell dev-db-init

# 默认目标
help: ## 显示帮助信息
	@echo "🚀 蔚之领域 Go 后端服务器"
	@echo ""
	@echo "📋 可用命令:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'
	@echo ""
	@echo "💡 使用示例:"
	@echo "  make dev                    # 开发模式启动"
	@echo "  make dev-db-up              # 启动开发环境MySQL"
	@echo "  make export-content         # 导出内容数据"
	@echo "  make export-content ENV=prod # 导出生产环境数据"
	@echo "  make import-sql TYPE=admin  # 导入管理员数据"
	@echo ""
	@echo "🔄 数据库迁移示例:"
	@echo "  make migrate-up VERSION=002 ENV=dev  # 开发环境迁移"
	@echo "  make migrate-up VERSION=002 ENV=prod # 生产环境迁移"
	@echo "  make migrate-down VERSION=002        # 回滚迁移"
	@echo ""
	@echo "🌍 环境切换:"
	@echo "  ENV=dev  (默认) - 使用本地Docker MySQL"
	@echo "  ENV=prod         - 使用生产环境MySQL"

# ==================== 服务管理 ====================

dev: ## 开发模式启动 (自动重启)
	@echo "🔧 开发模式启动..."
	@which air > /dev/null || (echo "❌ 请先安装air: go install github.com/cosmtrek/air@latest" && exit 1)
	@air

build: ## 编译项目
	@echo "🔨 编译 $(APP_NAME)..."
	@go build -o $(BINARY_PATH) ./cmd
	@echo "✅ 编译完成: $(BINARY_PATH)"

start: build ## 后台启动服务器
	@echo "🚀 后台启动 $(APP_NAME) 服务器..."
	@nohup ./$(BINARY_PATH) > server.log 2>&1 &
	@echo "✅ 服务器已启动，日志文件: server.log"
	@echo "🌐 服务地址: http://localhost:3000"

stop: ## 停止服务器
	@echo "🛑 停止 $(APP_NAME) 服务器..."
	@pkill -f "./$(BINARY_PATH)" || echo "ℹ️  服务器未运行"
	@lsof -ti:3000 | xargs kill -9 2>/dev/null || echo "ℹ️  3000端口未被占用"

restart: stop start ## 重启服务器

status: ## 查看服务器状态
	@echo "📊 检查 $(APP_NAME) 服务器状态..."
	@ps aux | grep "./$(BINARY_PATH)" | grep -v grep || echo "❌ 服务器未运行"
	@echo ""
	@echo "🔍 健康检查:"
	@curl -s http://localhost:3000/api/health || echo "❌ 服务器无响应"

clean: ## 清理编译文件
	@echo "🧹 清理编译文件..."
	@rm -f $(BINARY_PATH)
	@rm -f server.log
	@echo "✅ 清理完成"

# ==================== 数据库管理 ====================

check-db: ## 检查Docker数据库连接
	@echo "🔍 检查Docker数据库连接 ($(ENV)环境)..."
	@$(MYSQL_CMD) -e "SELECT 'Database connection successful!' as status;"

# ==================== 开发环境数据库 ====================

dev-db-up: ## 启动开发环境MySQL容器
	@echo "🚀 启动开发环境MySQL容器..."
	@docker-compose -f $(DEV_COMPOSE_FILE) up -d mysql
	@echo "⏳ 等待MySQL启动..."
	@sleep 10
	@echo "✅ 开发环境MySQL已启动"

dev-db-down: ## 停止开发环境MySQL容器
	@echo "🛑 停止开发环境MySQL容器..."
	@docker-compose -f $(DEV_COMPOSE_FILE) down
	@echo "✅ 开发环境MySQL已停止"

dev-db-restart: dev-db-down dev-db-up ## 重启开发环境MySQL

dev-db-logs: ## 查看开发环境MySQL日志
	@echo "📋 开发环境MySQL日志:"
	@docker-compose -f $(DEV_COMPOSE_FILE) logs -f mysql

dev-db-shell: ## 连接到开发环境MySQL
	@echo "🐳 连接到开发环境MySQL..."
	@docker-compose -f $(DEV_COMPOSE_FILE) exec mysql mysql -u $(DB_USER) -p'$(DB_PASS)' $(DB_NAME)

dev-db-init: ## 初始化开发环境数据库
	@echo "🔧 初始化开发环境数据库..."
	@$(DEV_MYSQL_CMD) < scripts/sql/init_database.sql
	@echo "✅ 开发环境数据库初始化完成"

# ==================== 数据导出 ====================

export-full: ## 导出完整数据库
	@echo "📦 导出完整数据库 ($(ENV)环境)..."
	@mkdir -p exports
	@$(MYSQLDUMP_CMD) > exports/full_$(ENV)_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "✅ 完整数据库导出完成: exports/full_$(ENV)_$(shell date +%Y%m%d_%H%M%S).sql"

export-content: ## 导出内容数据 (业务数据)
	@echo "📄 导出内容数据 ($(ENV)环境)..."
	@mkdir -p exports
	@$(MYSQLDUMP_CMD) --where="1" \
		swipers friend_links partners part_platform project_cases recruitments \
		> exports/content_$(ENV)_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "✅ 内容数据导出完成: exports/content_$(ENV)_$(shell date +%Y%m%d_%H%M%S).sql"

export-admin: ## 导出管理员数据
	@echo "👤 导出管理员数据 ($(ENV)环境)..."
	@mkdir -p exports
	@$(MYSQLDUMP_CMD) --where="1" \
		admin_users roles permissions admin_user_roles role_permissions admin_logs \
		> exports/admin_$(ENV)_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "✅ 管理员数据导出完成: exports/admin_$(ENV)_$(shell date +%Y%m%d_%H%M%S).sql"

# ==================== 数据导入 ====================

import-sql: ## 导入指定SQL文件 (用法: make import-sql FILE=xxx.sql TYPE=full|content|admin)
	@echo "📥 导入SQL文件..."
	@if [ -z "$(FILE)" ]; then \
		echo "❌ 错误: 请指定SQL文件，例如: make import-sql FILE=backup.sql TYPE=full"; \
		exit 1; \
	fi
	@if [ ! -f "$(FILE)" ]; then \
		echo "❌ 错误: 文件不存在: $(FILE)"; \
		exit 1; \
	fi
	@if [ -z "$(TYPE)" ]; then \
		echo "❌ 错误: 请指定导入类型 TYPE=full|content|admin"; \
		exit 1; \
	fi
	@case "$(TYPE)" in \
		full) \
			echo "🔄 导入完整数据库到$(ENV)环境..."; \
			$(MYSQL_CMD) < $(FILE); \
			echo "✅ 完整数据库导入完成"; \
			;; \
		content) \
			echo "🔄 导入内容数据到$(ENV)环境..."; \
			$(MYSQL_CMD) < $(FILE); \
			echo "✅ 内容数据导入完成"; \
			;; \
		admin) \
			echo "🔄 导入管理员数据到$(ENV)环境..."; \
			$(MYSQL_CMD) < $(FILE); \
			echo "✅ 管理员数据导入完成"; \
			;; \
		*) \
			echo "❌ 错误: 无效的类型 $(TYPE)，请使用 full|content|admin"; \
			exit 1; \
			;; \
	esac

docker-shell: ## 连接到Docker MySQL容器
	@echo "🐳 连接到Docker MySQL容器..."
	@docker-compose -f $(DOCKER_COMPOSE_FILE) --env-file $(DOCKER_ENV_FILE) exec mysql mysql -u $(DB_USER) -p'$(DB_PASS)' $(DB_NAME)

# ==================== 容器管理 ====================

container-up: ## 启动Docker容器
	@echo "🐳 启动Docker容器..."
	@docker-compose -f $(DOCKER_COMPOSE_FILE) --env-file $(DOCKER_ENV_FILE) up -d
	@echo "✅ Docker容器已启动"

container-down: ## 停止Docker容器
	@echo "🛑 停止Docker容器..."
	@docker-compose -f $(DOCKER_COMPOSE_FILE) --env-file $(DOCKER_ENV_FILE) down
	@echo "✅ Docker容器已停止"

container-status: ## 查看容器状态
	@echo "📊 Docker容器状态:"
	@docker-compose -f $(DOCKER_COMPOSE_FILE) --env-file $(DOCKER_ENV_FILE) ps

# ==================== 数据库升级 ====================

migrate-up: ## 执行数据库迁移 (用法: make migrate-up VERSION=002)
	@echo "🔄 执行数据库迁移..."
	@if [ -z "$(VERSION)" ]; then \
		echo "❌ 错误: 请指定迁移版本号，例如: make migrate-up VERSION=002"; \
		exit 1; \
	fi
	@cd scripts && ENV=$(ENV) ./migrate-manager.sh up $(VERSION)

migrate-down: ## 回滚数据库迁移 (用法: make migrate-down VERSION=002)
	@echo "🔄 回滚数据库迁移..."
	@if [ -z "$(VERSION)" ]; then \
		echo "❌ 错误: 请指定迁移版本号，例如: make migrate-down VERSION=002"; \
		exit 1; \
	fi
	@cd scripts && ENV=$(ENV) ./migrate-manager.sh down $(VERSION)

migrate-list: ## 列出可用的迁移
	@echo "📋 可用的迁移:"
	@cd scripts && ENV=$(ENV) ./migrate-manager.sh list

migrate-status: ## 查看迁移状态
	@echo "📊 迁移环境状态:"
	@cd scripts && ENV=$(ENV) ./migrate-manager.sh status

migrate-backup: ## 仅备份数据库
	@echo "💾 备份数据库..."
	@cd scripts && ENV=$(ENV) ./migrate-manager.sh backup

upgrade-full: ## 执行完整升级流程
	@echo "🚀 执行完整升级流程..."
	@cd scripts && ./migrate-manager.sh full-upgrade

drop-services: ## 删除Services表 (快速操作)
	@echo "🗑️  删除Services表..."
	@cd scripts && ./migrate-manager.sh up 002