package main

import (
	"log"
	"os"
	"weishi-server/internal/config"
	"weishi-server/pkg/logger"
	"weishi-server/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// 初始化日志系统
	if err := logger.Init(cfg.Log); err != nil {
		log.Fatal("Failed to init logger:", err)
	}

	// 在生产环境设置 Gin 为 release 模式
	if os.Getenv("APP_MODE") == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建 Gin 引擎
	r := gin.New()

	// 使用自定义中间件（不输出到控制台）
	r.Use(middleware.GinLogger(logger.Logger))
	r.Use(middleware.GinRecovery(logger.Logger))

	// 添加路由
	setupRoutes(r)

	// 启动服务器
	logger.Logger.Infof("Server starting on port %s", cfg.App.Port)
	if err := r.Run(":" + cfg.App.Port); err != nil {
		logger.Logger.Fatal("Failed to start server:", err)
	}
}

func setupRoutes(r *gin.Engine) {
	// 健康检查
	r.GET("/api/health", func(c *gin.Context) {
		logger.Logger.Info("Health check requested")
		c.JSON(200, gin.H{"status": "ok"})
	})

	// API 路由组
	api := r.Group("/api/v1")
	{
		api.GET("/test", func(c *gin.Context) {
			logger.Logger.Info("Test endpoint accessed")
			c.JSON(200, gin.H{"message": "test successful"})
		})
	}
}
