# 蔚之领域后端服务配置文件示例
# 复制此文件为 config.yaml 并填入实际配置信息
# 注意：config.yaml 不会被提交到版本控制

# ==================== 应用配置 ====================
app:
  name: "weishi-server"           # 应用名称
  port: "3001"                    # 服务端口
  mode: "debug"                   # 运行模式: debug/release/test

# ==================== 数据库配置 ====================
database:
  host: "localhost"               # 数据库主机地址
  port: "3306"                    # 数据库端口
  username: "root"                # 数据库用户名
  password: "your_password_here"  # 数据库密码 - 请填入实际密码
  database: "weizhi"              # 数据库名称
  charset: "utf8mb4"              # 字符集

# ==================== JWT配置 ====================
jwt:
  secret: "your_jwt_secret_key_here"  # JWT密钥 - 请使用强密钥
  expire_time: 28800                  # 过期时间(秒) - 8小时

# ==================== 日志配置 ====================
log:
  level: "info"                   # 日志级别: debug/info/warn/error
  path: "./logs"                  # 日志文件路径
  filename: "app.log"             # 日志文件名
  max_size: 100                   # 单个日志文件最大大小(MB)
  max_backups: 10                 # 保留的日志文件数量
  max_age: 30                     # 日志文件保留天数
  compress: true                  # 是否压缩旧日志文件
  console: false                  # 是否输出到控制台(生产环境建议false)

# ==================== 腾讯云COS配置 ====================
cos:
  secret_id: "your_cos_secret_id"     # COS SecretId - 请填入实际值
  secret_key: "your_cos_secret_key"   # COS SecretKey - 请填入实际值
  region: "ap-nanjing"                # COS地域
  bucket: "your_bucket_name"          # COS存储桶名称
  domain: "your_custom_domain"        # 自定义域名(可选)

# ==================== 文件上传配置 ====================
file:
  max_file_size: 52428800         # 最大文件大小(字节) - 50MB
  allowed_file_types:             # 允许的文件类型
    - "image/jpeg"
    - "image/png"
    - "image/gif"
    - "image/webp"
    - "application/pdf"
    - "text/plain"

# ==================== 管理员配置 ====================
admin:
  username: "admin"               # 默认管理员用户名
  password: "your_admin_password" # 默认管理员密码 - 请修改为强密码
  init_password: false            # 是否在启动时重置管理员密码

# ==================== 数据导入配置 ====================
data_import:
  import_seed_data: true          # 是否导入种子数据
  import_admin_data: true         # 是否导入管理员数据

# ==================== 开发环境特殊配置 ====================
# 以下配置仅在开发环境生效

# 开发环境数据库配置(使用Docker MySQL)
# database:
#   host: "localhost"
#   port: "3307"                  # 开发环境使用3307端口
#   username: "root"
#   password: "Ydb3344%"
#   database: "weizhi"
#   charset: "utf8mb4"

# 开发环境日志配置
# log:
#   level: "debug"                # 开发环境使用debug级别
#   console: true                 # 开发环境输出到控制台
