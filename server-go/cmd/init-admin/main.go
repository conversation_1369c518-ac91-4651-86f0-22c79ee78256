package main

import (
	"flag"
	"fmt"
	"log"
	"weishi-server/internal/config"
	"weishi-server/internal/database"

	"golang.org/x/crypto/bcrypt"
)

func main() {
	var password = flag.String("password", "admin123", "管理员密码")
	flag.Parse()

	fmt.Println("🚀 蔚之领域管理员初始化工具")
	fmt.Println("==================================")

	// 加载配置
	cfg := config.New()

	// 连接数据库
	_, err := database.New(cfg.Database)
	if err != nil {
		log.Fatalf("❌ 数据库连接失败: %v", err)
	}

	fmt.Println("✅ 数据库连接成功")

	// 生成密码哈希
	hash, err := bcrypt.GenerateFromPassword([]byte(*password), bcrypt.DefaultCost)
	if err != nil {
		log.Fatalf("❌ 密码哈希生成失败: %v", err)
	}

	fmt.Printf("🔑 密码哈希: %s\n", string(hash))

	// 注意：种子数据已通过SQL脚本手动插入，这里不再需要程序化初始化
	fmt.Println("✅ 数据库已包含完整的权限数据")

	fmt.Println("🎉 管理员初始化完成！")
	fmt.Println("==================================")
	fmt.Println("📋 登录信息:")
	fmt.Println("管理员账号: admin")
	fmt.Printf("管理员密码: %s\n", *password)
	fmt.Println("登录地址: http://localhost:3001")
	fmt.Println("==================================")
}
