package main

import (
	"log"
	"os"
	"weishi-server/internal/config"
	"weishi-server/internal/database"
	"weishi-server/internal/repository"
	"weishi-server/internal/router"
	"weishi-server/internal/service"
	"weishi-server/pkg/logger"

	"github.com/gin-gonic/gin"
)

// @title 蔚之领域智能科技 API
// @version 1.0
// @description 江苏蔚之领域智能科技有限公司企业官网后端 API
// @host localhost:3001
// @BasePath /api
// @securityDefinitions.apikey Bearer
// @in header
// @name Authorization
func main() {
	// 初始化配置
	cfg := config.New()

	// 初始化日志
	logger.Init(cfg.Log)

	// 输出环境变量信息（用于调试）
	log.Printf("=== 环境变量信息 ===")
	log.Printf("DB_HOST: %s", os.Getenv("DB_HOST"))
	log.Printf("DB_PORT: %s", os.Getenv("DB_PORT"))
	log.Printf("DB_USERNAME: %s", os.Getenv("DB_USERNAME"))
	dbPassword := os.Getenv("DB_PASSWORD")
	log.Printf("DB_PASSWORD 长度: %d", len(dbPassword))
	log.Printf("DB_PASSWORD 实际值: '%s'", dbPassword)
	log.Printf("DB_DATABASE: %s", os.Getenv("DB_DATABASE"))
	log.Printf("==================")

	// 输出数据库配置信息（用于调试）
	log.Printf("=== 数据库配置信息 ===")
	log.Printf("数据库主机: %s", cfg.Database.Host)
	log.Printf("数据库端口: %s", cfg.Database.Port)
	log.Printf("数据库名称: %s", cfg.Database.Database)
	log.Printf("数据库用户: %s", cfg.Database.Username)
	log.Printf("数据库密码: %s", cfg.Database.Password)
	log.Printf("字符集: %s", cfg.Database.Charset)
	log.Printf("连接字符串: %s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=True&loc=Local",
		cfg.Database.Username,
		cfg.Database.Password,
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.Database,
		cfg.Database.Charset,
	)
	log.Printf("=====================")

	// 初始化数据库
	db, err := database.New(cfg.Database)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// 初始化管理员密码（如果配置启用）
	if cfg.Admin.InitPassword {
		log.Printf("🔧 开始初始化管理员密码...")

		// 创建必要的 repository 和 service
		adminRepo := repository.NewAdminRepository(db)
		roleRepo := repository.NewRoleRepository(db)
		permissionRepo := repository.NewPermissionRepository(db)
		adminLogRepo := repository.NewAdminLogRepository(db)
		adminService := service.NewAdminService(adminRepo, roleRepo, permissionRepo, adminLogRepo)

		// 初始化管理员密码
		if err := adminService.InitializeAdminPassword(cfg.Admin.Username, cfg.Admin.Password); err != nil {
			log.Printf("⚠️ 管理员密码初始化失败: %v", err)
		}
	}

	// 检查并执行数据导入（如果配置启用）
	importFullData := os.Getenv("IMPORT_FULL_DATA")
	if importFullData == "true" {
		log.Printf("🗄️ 开始执行完整数据导入...")

		// 创建数据导入服务
		dataImportService := service.NewDataImportService(db)

		// 执行完整数据导入
		if err := dataImportService.ImportFullData(); err != nil {
			log.Printf("❌ 完整数据导入失败: %v", err)
		} else {
			log.Printf("✅ 完整数据导入成功")
		}
	}

	// 设置 Gin 模式
	if cfg.App.Mode == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 初始化路由
	r := router.New(db, cfg)

	// 启动服务器
	log.Printf("Server is starting on port %s", cfg.App.Port)
	if err := r.Run(":" + cfg.App.Port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
