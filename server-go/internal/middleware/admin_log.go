package middleware

import (
	"bytes"
	"encoding/json"
	"io"
	"strings"
	"time"
	"weishi-server/internal/service"

	"github.com/gin-gonic/gin"
)

// AdminLogMiddleware 管理员操作日志中间件
func AdminLogMiddleware(adminLogService service.AdminLogService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 只记录管理员相关的API操作
		if !strings.HasPrefix(c.Request.URL.Path, "/api/admin") {
			c.Next()
			return
		}

		// 记录开始时间
		startTime := time.Now()

		// 读取请求体
		var requestBody []byte
		if c.Request.Body != nil {
			requestBody, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 创建一个自定义的 ResponseWriter 来捕获响应
		writer := &responseWriter{
			ResponseWriter: c.Writer,
			body:           &bytes.Buffer{},
		}
		c.Writer = writer

		// 处理请求
		c.Next()

		// 计算耗时
		duration := time.Since(startTime)

		// 获取用户信息
		var userID uint
		var username string
		if uid, exists := c.Get("user_id"); exists {
			userID = uid.(uint)
		}
		if uname, exists := c.Get("username"); exists {
			username = uname.(string)
		}

		// 解析模块和操作
		module, action := parseModuleAndAction(c.Request.URL.Path, c.Request.Method)

		// 确定状态
		status := "success"
		errorMsg := ""
		if c.Writer.Status() >= 400 {
			status = "failed"
			// 尝试从响应中提取错误信息
			var response map[string]interface{}
			if err := json.Unmarshal(writer.body.Bytes(), &response); err == nil {
				if msg, ok := response["message"].(string); ok {
					errorMsg = msg
				}
			}
		}

		// 创建日志记录
		logReq := service.CreateAdminLogRequest{
			AdminUserID: userID,
			Username:    username,
			Action:      action,
			Module:      module,
			Method:      c.Request.Method,
			URL:         c.Request.URL.Path,
			IP:          c.ClientIP(),
			UserAgent:   c.Request.UserAgent(),
			Params:      string(requestBody),
			Result:      writer.body.String(),
			Status:      status,
			ErrorMsg:    errorMsg,
			Duration:    int(duration.Milliseconds()),
		}

		// 异步记录日志，避免影响响应性能
		go func() {
			adminLogService.Create(logReq)
		}()
	}
}

// responseWriter 自定义响应写入器，用于捕获响应体
type responseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (r *responseWriter) Write(b []byte) (int, error) {
	r.body.Write(b)
	return r.ResponseWriter.Write(b)
}

// parseModuleAndAction 解析模块和操作
func parseModuleAndAction(path, method string) (string, string) {
	// 移除 /api/admin 前缀
	path = strings.TrimPrefix(path, "/api/admin")
	if path == "" {
		return "admin", method
	}

	parts := strings.Split(strings.Trim(path, "/"), "/")
	if len(parts) == 0 {
		return "admin", method
	}

	module := parts[0]
	action := method

	// 根据路径和方法确定具体操作
	switch {
	case strings.Contains(path, "/login"):
		action = "login"
	case strings.Contains(path, "/logout"):
		action = "logout"
	case strings.Contains(path, "/profile"):
		switch method {
		case "GET":
			action = "view_profile"
		case "PUT", "PATCH":
			action = "update_profile"
		}
	case strings.Contains(path, "/change-password"):
		action = "change_password"
	case strings.Contains(path, "/reset-password"):
		action = "reset_password"
	case strings.Contains(path, "/toggle-status"):
		action = "toggle_status"
	case strings.Contains(path, "/batch-delete"):
		action = "batch_delete"
	case strings.Contains(path, "/permissions"):
		action = "assign_permissions"
	case strings.Contains(path, "/roles"):
		action = "assign_roles"
	default:
		switch method {
		case "GET":
			if strings.Contains(path, "/") && len(parts) > 1 {
				action = "view"
			} else {
				action = "list"
			}
		case "POST":
			action = "create"
		case "PUT", "PATCH":
			action = "update"
		case "DELETE":
			action = "delete"
		}
	}

	return module, action
}
