package middleware

import (
	"strings"
	"weishi-server/internal/config"
	"weishi-server/pkg/jwt"
	"weishi-server/pkg/response"

	"github.com/gin-gonic/gin"
)

// JWTAuth JWT认证中间件
func JWTAuth(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 跳过不需要认证的路径
		skipPaths := []string{
			"/api/admin/login",
			"/api/admin/auth/login",
			"/api/admin/captcha",
			"/api/health",
			"/api/docs",
			"/api/swagger",
		}

		path := c.Request.URL.Path
		for _, skipPath := range skipPaths {
			if strings.HasPrefix(path, skipPath) {
				c.Next()
				return
			}
		}

		// 获取 Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			response.Unauthorized(c, "未提供认证token")
			c.Abort()
			return
		}

		// 检查 Bearer 前缀
		if !strings.HasPrefix(authHeader, "Bearer ") {
			response.Unauthorized(c, "token格式错误")
			c.Abort()
			return
		}

		// 提取token
		tokenString := authHeader[7:] // 去掉 "Bearer " 前缀

		// 解析token
		claims, err := jwt.ParseToken(tokenString, cfg.JWT)
		if err != nil {
			response.Unauthorized(c, "token无效或已过期")
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)

		c.Next()
	}
}

// GetUserID 从上下文中获取用户ID
func GetUserID(c *gin.Context) uint {
	if userID, exists := c.Get("user_id"); exists {
		return userID.(uint)
	}
	return 0
}

// GetUsername 从上下文中获取用户名
func GetUsername(c *gin.Context) string {
	if username, exists := c.Get("username"); exists {
		return username.(string)
	}
	return ""
}
