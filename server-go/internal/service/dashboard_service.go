package service

import (
	"weishi-server/internal/repository"
)

type DashboardService interface {
	GetOverviewStats() (*DashboardOverview, error)
	GetSystemInfo() (*SystemInfo, error)
	GetRecentActivity() ([]ActivityRecord, error)
	GetUserGrowthStats(days int) ([]UserGrowthData, error)
	GetModuleAccessStats() ([]ModuleStats, error)
	GetOperationStats(days int) ([]OperationStats, error)
}

type DashboardOverview struct {
	TotalUsers       int64 `json:"total_users"`
	ActiveUsers      int64 `json:"active_users"`
	TotalRoles       int64 `json:"total_roles"`
	TotalPermissions int64 `json:"total_permissions"`
	TodayLogins      int64 `json:"today_logins"`
	TodayOperations  int64 `json:"today_operations"`
	SystemUptime     int64 `json:"system_uptime"`
	OnlineUsers      int64 `json:"online_users"`
}

type SystemInfo struct {
	Version      string `json:"version"`
	GoVersion    string `json:"go_version"`
	StartTime    string `json:"start_time"`
	DatabaseType string `json:"database_type"`
	ServerOS     string `json:"server_os"`
	ServerArch   string `json:"server_arch"`
	CPUCount     int    `json:"cpu_count"`
	MemoryUsage  string `json:"memory_usage"`
	DiskUsage    string `json:"disk_usage"`
}

type ActivityRecord struct {
	ID        uint   `json:"id"`
	UserID    uint   `json:"user_id"`
	Username  string `json:"username"`
	Action    string `json:"action"`
	Module    string `json:"module"`
	IP        string `json:"ip"`
	CreatedAt string `json:"created_at"`
}

type UserGrowthData struct {
	Date  string `json:"date"`
	Count int64  `json:"count"`
}

type ModuleStats struct {
	Module      string  `json:"module"`
	AccessCount int64   `json:"access_count"`
	Percentage  float64 `json:"percentage"`
}

type OperationStats struct {
	Date    string `json:"date"`
	Success int64  `json:"success"`
	Failed  int64  `json:"failed"`
	Total   int64  `json:"total"`
}

type dashboardService struct {
	adminRepo      repository.AdminRepository
	roleRepo       repository.RoleRepository
	permissionRepo repository.PermissionRepository
	adminLogRepo   repository.AdminLogRepository
}

func NewDashboardService(
	adminRepo repository.AdminRepository,
	roleRepo repository.RoleRepository,
	permissionRepo repository.PermissionRepository,
	adminLogRepo repository.AdminLogRepository,
) DashboardService {
	return &dashboardService{
		adminRepo:      adminRepo,
		roleRepo:       roleRepo,
		permissionRepo: permissionRepo,
		adminLogRepo:   adminLogRepo,
	}
}

func (s *dashboardService) GetOverviewStats() (*DashboardOverview, error) {
	// 获取用户统计
	totalUsers, err := s.adminRepo.CountUsers()
	if err != nil {
		return nil, err
	}

	activeUsers, err := s.adminRepo.CountActiveUsers()
	if err != nil {
		return nil, err
	}

	// 获取角色数量
	totalRoles, err := s.roleRepo.CountRoles()
	if err != nil {
		return nil, err
	}

	// 获取权限数量
	totalPermissions, err := s.permissionRepo.CountPermissions()
	if err != nil {
		return nil, err
	}

	// 获取今日登录次数
	todayLogins, err := s.adminLogRepo.CountTodayLogins()
	if err != nil {
		return nil, err
	}

	// 获取今日操作次数
	todayOperations, err := s.adminLogRepo.CountTodayOperations()
	if err != nil {
		return nil, err
	}

	overview := &DashboardOverview{
		TotalUsers:       totalUsers,
		ActiveUsers:      activeUsers,
		TotalRoles:       totalRoles,
		TotalPermissions: totalPermissions,
		TodayLogins:      todayLogins,
		TodayOperations:  todayOperations,
		SystemUptime:     0, // 可以根据实际需求实现
		OnlineUsers:      0, // 可以根据实际需求实现
	}

	return overview, nil
}

func (s *dashboardService) GetSystemInfo() (*SystemInfo, error) {
	// 这里可以获取系统信息，暂时返回基本信息
	systemInfo := &SystemInfo{
		Version:      "1.0.0",
		GoVersion:    "go1.21",
		StartTime:    "2024-01-01 00:00:00",
		DatabaseType: "MySQL",
		ServerOS:     "Linux",
		ServerArch:   "amd64",
		CPUCount:     4,
		MemoryUsage:  "512MB",
		DiskUsage:    "50%",
	}

	return systemInfo, nil
}

func (s *dashboardService) GetRecentActivity() ([]ActivityRecord, error) {
	logs, err := s.adminLogRepo.GetRecentLogs(20) // 获取最近20条记录
	if err != nil {
		return nil, err
	}

	activities := make([]ActivityRecord, len(logs))
	for i, log := range logs {
		activities[i] = ActivityRecord{
			ID:        log.ID,
			Action:    log.Action,
			Module:    log.Module,
			CreatedAt: log.CreatedAt.Format("2006-01-02 15:04:05"),
		}

		if log.AdminUserID != nil {
			activities[i].UserID = *log.AdminUserID
		}
		if log.Username != nil {
			activities[i].Username = *log.Username
		}
		if log.IP != nil {
			activities[i].IP = *log.IP
		}
	}

	return activities, nil
}

func (s *dashboardService) GetUserGrowthStats(days int) ([]UserGrowthData, error) {
	if days <= 0 {
		days = 7 // 默认7天
	}

	// 这里可以实现用户增长统计
	// 暂时返回模拟数据
	growthData := make([]UserGrowthData, 0)
	return growthData, nil
}

func (s *dashboardService) GetModuleAccessStats() ([]ModuleStats, error) {
	// 获取模块访问统计
	moduleStats, err := s.adminLogRepo.GetModuleStats()
	if err != nil {
		return nil, err
	}

	// 计算总访问量
	var total int64
	for _, stat := range moduleStats {
		total += stat.Count
	}

	// 计算百分比
	result := make([]ModuleStats, len(moduleStats))
	for i, stat := range moduleStats {
		result[i] = ModuleStats{
			Module:      stat.Module,
			AccessCount: stat.Count,
		}
		if total > 0 {
			result[i].Percentage = float64(stat.Count) / float64(total) * 100
		}
	}

	return result, nil
}

func (s *dashboardService) GetOperationStats(days int) ([]OperationStats, error) {
	if days <= 0 {
		days = 7 // 默认7天
	}

	// 获取操作统计
	operationStats, err := s.adminLogRepo.GetOperationStats(days)
	if err != nil {
		return nil, err
	}

	result := make([]OperationStats, len(operationStats))
	for i, stat := range operationStats {
		result[i] = OperationStats{
			Date:    stat.Date,
			Success: stat.Success,
			Failed:  stat.Failed,
			Total:   stat.Success + stat.Failed,
		}
	}

	return result, nil
}
