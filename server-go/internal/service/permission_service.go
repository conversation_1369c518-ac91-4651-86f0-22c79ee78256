package service

import (
	"errors"
	"weishi-server/internal/model"
	"weishi-server/internal/repository"

	"gorm.io/gorm"
)

type PermissionService interface {
	Create(req CreatePermissionRequest) (*model.AdminPermission, error)
	GetByID(id uint) (*model.AdminPermission, error)
	GetByCode(code string) (*model.AdminPermission, error)
	GetAll(req QueryPermissionRequest) ([]model.AdminPermission, int64, error)
	GetAllActive() ([]model.AdminPermission, error)
	GetTree() ([]model.AdminPermission, error)
	GetChildren(parentID uint) ([]model.AdminPermission, error)
	GetMenuPermissions() ([]model.AdminPermission, error)
	Update(id uint, req UpdatePermissionRequest) (*model.AdminPermission, error)
	Delete(id uint) error
	BatchDelete(ids []uint) error
	ToggleStatus(id uint) error
	GetUserPermissions(userID uint) ([]model.AdminPermission, error)
	CountPermissions() (int64, error)
}

type CreatePermissionRequest struct {
	Name        string `json:"name" binding:"required"`
	Code        string `json:"code" binding:"required"`
	Type        string `json:"type" binding:"required,oneof=menu button api"`
	ParentID    uint   `json:"parent_id"`
	Path        string `json:"path"`
	Component   string `json:"component"`
	Icon        string `json:"icon"`
	Method      string `json:"method"`
	Description string `json:"description"`
	Status      string `json:"status"`
	Sort        int    `json:"sort"`
	Remark      string `json:"remark"`
}

type UpdatePermissionRequest struct {
	Name        string `json:"name"`
	Code        string `json:"code"`
	Type        string `json:"type" binding:"omitempty,oneof=menu button api"`
	ParentID    uint   `json:"parent_id"`
	Path        string `json:"path"`
	Component   string `json:"component"`
	Icon        string `json:"icon"`
	Method      string `json:"method"`
	Description string `json:"description"`
	Status      string `json:"status"`
	Sort        int    `json:"sort"`
	Remark      string `json:"remark"`
}

type QueryPermissionRequest struct {
	Page     int    `json:"page" form:"page"`
	PageSize int    `json:"page_size" form:"page_size"`
	Keyword  string `json:"keyword" form:"keyword"`
	Type     string `json:"type" form:"type"`
	Status   string `json:"status" form:"status"`
	ParentID uint   `json:"parent_id" form:"parent_id"`
}

type permissionService struct {
	permissionRepo repository.PermissionRepository
}

func NewPermissionService(permissionRepo repository.PermissionRepository) PermissionService {
	return &permissionService{
		permissionRepo: permissionRepo,
	}
}

func (s *permissionService) Create(req CreatePermissionRequest) (*model.AdminPermission, error) {
	// 检查权限代码是否已存在
	if _, err := s.permissionRepo.GetByCode(req.Code); err == nil {
		return nil, errors.New("权限代码已存在")
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	// 如果有父权限，验证父权限是否存在
	if req.ParentID > 0 {
		if _, err := s.permissionRepo.GetByID(uint(req.ParentID)); err != nil {
			return nil, errors.New("父权限不存在")
		}
	}

	status := req.Status
	if status == "" {
		status = "active"
	}

	permission := &model.AdminPermission{
		Name:     req.Name,
		Code:     req.Code,
		Type:     req.Type,
		ParentID: &req.ParentID,
		Status:   status,
		Sort:     req.Sort,
	}

	if req.Path != "" {
		permission.Path = &req.Path
	}
	if req.Component != "" {
		permission.Component = &req.Component
	}
	if req.Icon != "" {
		permission.Icon = &req.Icon
	}
	if req.Method != "" {
		permission.Method = &req.Method
	}
	if req.Description != "" {
		permission.Description = &req.Description
	}
	if req.Remark != "" {
		permission.Remark = &req.Remark
	}

	if err := s.permissionRepo.Create(permission); err != nil {
		return nil, err
	}

	return permission, nil
}

func (s *permissionService) GetByID(id uint) (*model.AdminPermission, error) {
	return s.permissionRepo.GetByID(id)
}

func (s *permissionService) GetAll(req QueryPermissionRequest) ([]model.AdminPermission, int64, error) {
	// 计算偏移量
	offset := (req.Page - 1) * req.PageSize

	// 处理父权限ID参数
	var parentID *uint
	if req.ParentID > 0 {
		parentID = &req.ParentID
	}

	return s.permissionRepo.GetAll(offset, req.PageSize, req.Keyword, req.Type, req.Status, parentID)
}

func (s *permissionService) GetByCode(code string) (*model.AdminPermission, error) {
	return s.permissionRepo.GetByCode(code)
}

func (s *permissionService) GetAllActive() ([]model.AdminPermission, error) {
	return s.permissionRepo.GetAllActive()
}

func (s *permissionService) GetTree() ([]model.AdminPermission, error) {
	return s.permissionRepo.GetTree()
}

func (s *permissionService) GetChildren(parentID uint) ([]model.AdminPermission, error) {
	return s.permissionRepo.GetChildren(parentID)
}

func (s *permissionService) GetMenuPermissions() ([]model.AdminPermission, error) {
	return s.permissionRepo.GetMenuPermissions()
}

func (s *permissionService) Update(id uint, req UpdatePermissionRequest) (*model.AdminPermission, error) {
	permission, err := s.permissionRepo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// 检查权限代码是否被其他权限使用
	if req.Code != "" && req.Code != permission.Code {
		if existingPermission, err := s.permissionRepo.GetByCode(req.Code); err == nil && existingPermission.ID != id {
			return nil, errors.New("权限代码已被其他权限使用")
		}
	}

	// 如果修改了父权限，验证父权限是否存在且不能是自己或自己的子权限
	if req.ParentID > 0 && (permission.ParentID == nil || req.ParentID != *permission.ParentID) {
		if uint(req.ParentID) == id {
			return nil, errors.New("不能将权限设置为自己的子权限")
		}
		if _, err := s.permissionRepo.GetByID(uint(req.ParentID)); err != nil {
			return nil, errors.New("父权限不存在")
		}
	}

	// 更新字段
	if req.Name != "" {
		permission.Name = req.Name
	}
	if req.Code != "" {
		permission.Code = req.Code
	}
	if req.Type != "" {
		permission.Type = req.Type
	}
	if req.ParentID != 0 {
		permission.ParentID = &req.ParentID
	}
	if req.Path != "" {
		permission.Path = &req.Path
	}
	if req.Component != "" {
		permission.Component = &req.Component
	}
	if req.Icon != "" {
		permission.Icon = &req.Icon
	}
	if req.Method != "" {
		permission.Method = &req.Method
	}
	if req.Description != "" {
		permission.Description = &req.Description
	}
	if req.Status != "" {
		permission.Status = req.Status
	}
	if req.Sort != 0 {
		permission.Sort = req.Sort
	}
	if req.Remark != "" {
		permission.Remark = &req.Remark
	}

	if err := s.permissionRepo.Update(id, permission); err != nil {
		return nil, err
	}

	return s.permissionRepo.GetByID(id)
}

func (s *permissionService) Delete(id uint) error {
	if _, err := s.permissionRepo.GetByID(id); err != nil {
		return err
	}

	// 检查是否有子权限
	children, err := s.permissionRepo.GetChildren(id)
	if err != nil {
		return err
	}
	if len(children) > 0 {
		return errors.New("存在子权限，无法删除")
	}

	return s.permissionRepo.Delete(id)
}

func (s *permissionService) BatchDelete(ids []uint) error {
	// 检查是否有子权限
	for _, id := range ids {
		children, err := s.permissionRepo.GetChildren(id)
		if err != nil {
			return err
		}
		if len(children) > 0 {
			return errors.New("存在子权限，无法删除")
		}
	}

	return s.permissionRepo.BatchDelete(ids)
}

func (s *permissionService) ToggleStatus(id uint) error {
	if err := s.permissionRepo.ToggleStatus(id); err != nil {
		return err
	}
	return nil
}

func (s *permissionService) GetUserPermissions(userID uint) ([]model.AdminPermission, error) {
	return s.permissionRepo.GetUserPermissions(userID)
}

func (s *permissionService) CountPermissions() (int64, error) {
	return s.permissionRepo.CountPermissions()
}
