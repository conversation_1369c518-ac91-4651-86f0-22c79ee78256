package service

import (
	"errors"
	"weishi-server/internal/model"
	"weishi-server/internal/repository"

	"gorm.io/gorm"
)

type RoleService interface {
	Create(req CreateRoleRequest) (*model.AdminRole, error)
	GetByID(id uint) (*model.AdminRole, error)
	GetByCode(code string) (*model.AdminRole, error)
	GetAll(offset, limit int, keyword, status string) ([]model.AdminRole, int64, error)
	GetAllActive() ([]model.AdminRole, error)
	Update(id uint, req UpdateRoleRequest) (*model.AdminRole, error)
	Delete(id uint) error
	BatchDelete(ids []uint) error
	GetRolePermissions(roleID uint) ([]model.AdminPermission, error)
	AssignPermissions(roleID uint, permissionIDs []uint) error
	ToggleStatus(id uint) error
	GetUserRoles(userID uint) ([]model.AdminRole, error)
	AssignRolesToUser(userID uint, roleIDs []uint) error
	CountRoles() (int64, error)
}

type CreateRoleRequest struct {
	Name        string `json:"name" binding:"required"`
	Code        string `json:"code" binding:"required"`
	Description string `json:"description"`
	Status      string `json:"status"`
	Sort        int    `json:"sort"`
	Remark      string `json:"remark"`
}

type UpdateRoleRequest struct {
	Name        string `json:"name"`
	Code        string `json:"code"`
	Description string `json:"description"`
	Status      string `json:"status"`
	Sort        int    `json:"sort"`
	Remark      string `json:"remark"`
}

type QueryRoleRequest struct {
	Page     int    `json:"page" form:"page"`
	PageSize int    `json:"page_size" form:"page_size"`
	Keyword  string `json:"keyword" form:"keyword"`
	Status   string `json:"status" form:"status"`
}

type roleService struct {
	roleRepo       repository.RoleRepository
	permissionRepo repository.PermissionRepository
}

func NewRoleService(roleRepo repository.RoleRepository, permissionRepo repository.PermissionRepository) RoleService {
	return &roleService{
		roleRepo:       roleRepo,
		permissionRepo: permissionRepo,
	}
}

func (s *roleService) Create(req CreateRoleRequest) (*model.AdminRole, error) {
	// 检查角色代码是否已存在
	if _, err := s.roleRepo.GetByCode(req.Code); err == nil {
		return nil, errors.New("角色代码已存在")
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	status := req.Status
	if status == "" {
		status = "active"
	}

	role := &model.AdminRole{
		Name:   req.Name,
		Code:   req.Code,
		Status: status,
		Sort:   req.Sort,
	}

	if req.Description != "" {
		role.Description = &req.Description
	}
	if req.Remark != "" {
		role.Remark = &req.Remark
	}

	if err := s.roleRepo.Create(role); err != nil {
		return nil, err
	}

	return role, nil
}

func (s *roleService) GetByID(id uint) (*model.AdminRole, error) {
	return s.roleRepo.GetByID(id)
}

func (s *roleService) GetAll(offset, limit int, keyword, status string) ([]model.AdminRole, int64, error) {
	return s.roleRepo.GetAll(offset, limit, keyword, status)
}

func (s *roleService) GetByCode(code string) (*model.AdminRole, error) {
	return s.roleRepo.GetByCode(code)
}

func (s *roleService) GetAllActive() ([]model.AdminRole, error) {
	return s.roleRepo.GetAllActive()
}

func (s *roleService) Update(id uint, req UpdateRoleRequest) (*model.AdminRole, error) {
	role, err := s.roleRepo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// 检查角色代码是否被其他角色使用
	if req.Code != "" && req.Code != role.Code {
		if existingRole, err := s.roleRepo.GetByCode(req.Code); err == nil && existingRole.ID != id {
			return nil, errors.New("角色代码已被其他角色使用")
		}
	}

	// 更新字段
	if req.Name != "" {
		role.Name = req.Name
	}
	if req.Code != "" {
		role.Code = req.Code
	}
	if req.Description != "" {
		role.Description = &req.Description
	}
	if req.Status != "" {
		role.Status = req.Status
	}
	if req.Sort != 0 {
		role.Sort = req.Sort
	}
	if req.Remark != "" {
		role.Remark = &req.Remark
	}

	if err := s.roleRepo.Update(id, role); err != nil {
		return nil, err
	}

	return s.roleRepo.GetByID(id)
}

func (s *roleService) Delete(id uint) error {
	if _, err := s.roleRepo.GetByID(id); err != nil {
		return err
	}
	return s.roleRepo.Delete(id)
}

func (s *roleService) BatchDelete(ids []uint) error {
	return s.roleRepo.BatchDelete(ids)
}

func (s *roleService) ToggleStatus(id uint) error {
	return s.roleRepo.ToggleStatus(id)
}

func (s *roleService) GetRolePermissions(roleID uint) ([]model.AdminPermission, error) {
	return s.roleRepo.GetRolePermissions(roleID)
}

func (s *roleService) AssignPermissions(roleID uint, permissionIDs []uint) error {
	// 验证角色是否存在
	if _, err := s.roleRepo.GetByID(roleID); err != nil {
		return err
	}

	// 验证权限是否都存在
	for _, permissionID := range permissionIDs {
		if _, err := s.permissionRepo.GetByID(permissionID); err != nil {
			return errors.New("权限ID不存在: " + string(rune(permissionID)))
		}
	}

	return s.roleRepo.AssignPermissions(roleID, permissionIDs)
}

func (s *roleService) GetUserRoles(userID uint) ([]model.AdminRole, error) {
	return s.roleRepo.GetUserRoles(userID)
}

func (s *roleService) AssignRolesToUser(userID uint, roleIDs []uint) error {
	return s.roleRepo.AssignRolesToUser(userID, roleIDs)
}

func (s *roleService) CountRoles() (int64, error) {
	return s.roleRepo.CountRoles()
}
