package service

import (
	"fmt"
	"mime/multipart"
	"weishi-server/internal/config"
	"weishi-server/internal/model"
	"weishi-server/internal/repository"
	"weishi-server/pkg/cos"
)

type FileService interface {
	UploadFile(file multipart.File, header *multipart.FileHeader, module string, userID uint) (*model.FileUpload, error)
	DeleteFile(id uint, userID uint) error
	GetFileByID(id uint) (*model.FileUpload, error)
	GetFilesByModule(module string) ([]model.FileUpload, error)
	GetFilesByUser(userID uint) ([]model.FileUpload, error)
	ValidateUpload(header *multipart.FileHeader) error
}

type fileService struct {
	contentRepo repository.ContentRepository
	cosClient   *cos.Client
	config      *config.Config
}

func NewFileService(contentRepo repository.ContentRepository, cosClient *cos.Client, cfg *config.Config) FileService {
	return &fileService{
		contentRepo: contentRepo,
		cosClient:   cosClient,
		config:      cfg,
	}
}

// UploadFile 上传文件
func (s *fileService) UploadFile(file multipart.File, header *multipart.FileHeader, module string, userID uint) (*model.FileUpload, error) {
	// 验证文件
	if err := s.ValidateUpload(header); err != nil {
		return nil, err
	}

	// 上传到COS
	result, err := s.cosClient.UploadFile(file, header, module)
	if err != nil {
		return nil, fmt.Errorf("上传文件失败: %v", err)
	}

	// 保存文件记录到数据库
	fileUpload := &model.FileUpload{
		OriginalName: header.Filename,
		FileName:     result.Key,
		FilePath:     result.Key,
		FileSize:     result.Size,
		MimeType:     result.MimeType,
		BucketName:   s.cosClient.GetConfig().Bucket,
		CosURL:       result.URL,
		UploadedBy:   userID,
		Module:       module,
		Status:       "active",
	}

	err = s.contentRepo.CreateFileUpload(fileUpload)
	if err != nil {
		// 如果数据库保存失败，尝试删除COS中的文件
		s.cosClient.DeleteFile(result.Key)
		return nil, fmt.Errorf("保存文件记录失败: %v", err)
	}

	return fileUpload, nil
}

// DeleteFile 删除文件
func (s *fileService) DeleteFile(id uint, userID uint) error {
	// 获取文件信息
	fileUpload, err := s.contentRepo.GetFileUploadByID(id)
	if err != nil {
		return fmt.Errorf("文件不存在: %v", err)
	}

	// 检查权限（只有上传者可以删除文件）
	if fileUpload.UploadedBy != userID {
		return fmt.Errorf("无权限删除此文件")
	}

	// 从COS删除文件
	err = s.cosClient.DeleteFile(fileUpload.FilePath)
	if err != nil {
		// 记录日志，但不阻止数据库删除
		fmt.Printf("删除COS文件失败: %v\n", err)
	}

	// 从数据库删除记录
	err = s.contentRepo.DeleteFileUpload(id)
	if err != nil {
		return fmt.Errorf("删除文件记录失败: %v", err)
	}

	return nil
}

// GetFileByID 根据ID获取文件信息
func (s *fileService) GetFileByID(id uint) (*model.FileUpload, error) {
	return s.contentRepo.GetFileUploadByID(id)
}

// GetFilesByModule 根据模块获取文件列表
func (s *fileService) GetFilesByModule(module string) ([]model.FileUpload, error) {
	return s.contentRepo.GetFileUploadsByModule(module)
}

// GetFilesByUser 根据用户获取文件列表
func (s *fileService) GetFilesByUser(userID uint) ([]model.FileUpload, error) {
	return s.contentRepo.GetFileUploadsByUser(userID)
}

// ValidateUpload 验证文件上传
func (s *fileService) ValidateUpload(header *multipart.FileHeader) error {
	// 从配置获取允许的文件类型
	allowedTypes := s.config.File.AllowedFileTypes
	if len(allowedTypes) == 0 {
		// 如果配置为空，使用默认类型
		allowedTypes = []string{
			"image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp",
			"application/pdf", "text/plain",
		}
	}

	// 验证文件类型
	if !cos.ValidateFileType(header, allowedTypes) {
		return fmt.Errorf("不支持的文件类型，允许的类型: %v", allowedTypes)
	}

	// 从配置获取最大文件大小
	maxSize := s.config.File.MaxFileSize
	if maxSize <= 0 {
		maxSize = 52428800 // 默认50MB
	}

	// 验证文件大小
	if !cos.ValidateFileSize(header, maxSize) {
		return fmt.Errorf("文件大小超出限制（最大%dMB）", maxSize/(1024*1024))
	}

	return nil
}
