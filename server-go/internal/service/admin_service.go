package service

import (
	"crypto/rand"
	"errors"
	"fmt"
	"weishi-server/internal/model"
	"weishi-server/internal/repository"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type AdminService interface {
	Create(req CreateAdminRequest) (*model.AdminUser, error)
	GetByID(id uint) (*model.AdminUser, error)
	GetByIDWithRoles(id uint) (*model.AdminUser, error)
	GetAll(req QueryAdminRequest) ([]model.AdminUser, int64, error)
	Update(id uint, req UpdateAdminRequest) (*model.AdminUser, error)
	Delete(id uint) error
	BatchDelete(ids []uint) error
	Login(req LoginRequest) (*model.AdminUser, error)
	ToggleStatus(id uint) (*model.AdminUser, error)
	ResetPassword(id uint, newPassword string) (string, error)
	ChangePassword(id uint, req ChangePasswordRequest) error
	UpdateProfile(id uint, req UpdateProfileRequest) (*model.AdminUser, error)
	GetUserRoles(userID uint) ([]model.AdminRole, error)
	GetUserPermissions(userID uint) ([]string, error)
	AssignRoles(userID uint, roleIDs []uint) error
	InitializeAdminPassword(username, password string) error
}

type CreateAdminRequest struct {
	Username string `json:"username" binding:"required"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
	RealName string `json:"real_name" binding:"required"`
	Phone    string `json:"phone"`
	Status   string `json:"status"`
	RoleIDs  []uint `json:"role_ids"`
}

type UpdateAdminRequest struct {
	Username string `json:"username"`
	Email    string `json:"email"`
	RealName string `json:"real_name"`
	Phone    string `json:"phone"`
	Status   string `json:"status"`
	Avatar   string `json:"avatar"`
	Remark   string `json:"remark"`
	RoleIDs  []uint `json:"role_ids"`
}

type QueryAdminRequest struct {
	Page      int    `json:"page" form:"page"`
	PageSize  int    `json:"page_size" form:"page_size"`
	Keyword   string `json:"keyword" form:"keyword"`
	Status    string `json:"status" form:"status"`
	RoleID    uint   `json:"role_id" form:"role_id"`
	StartDate string `json:"start_date" form:"start_date"`
	EndDate   string `json:"end_date" form:"end_date"`
}

type LoginRequest struct {
	Username    string `json:"username" binding:"required"`
	Password    string `json:"password" binding:"required"`
	CaptchaID   string `json:"captcha_id"`
	CaptchaCode string `json:"captcha_code"`
}

type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=6"`
}

type UpdateProfileRequest struct {
	Email    string `json:"email" binding:"email"`
	RealName string `json:"real_name"`
	Phone    string `json:"phone"`
	Avatar   string `json:"avatar"`
}

type adminService struct {
	adminRepo      repository.AdminRepository
	roleRepo       repository.RoleRepository
	permissionRepo repository.PermissionRepository
	adminLogRepo   repository.AdminLogRepository
}

func NewAdminService(adminRepo repository.AdminRepository, roleRepo repository.RoleRepository, permissionRepo repository.PermissionRepository, adminLogRepo repository.AdminLogRepository) AdminService {
	return &adminService{
		adminRepo:      adminRepo,
		roleRepo:       roleRepo,
		permissionRepo: permissionRepo,
		adminLogRepo:   adminLogRepo,
	}
}

func (s *adminService) Create(req CreateAdminRequest) (*model.AdminUser, error) {
	// 检查用户名是否已存在
	if _, err := s.adminRepo.GetByUsername(req.Username); err == nil {
		return nil, errors.New("用户名已存在")
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	// 检查邮箱是否已存在
	if _, err := s.adminRepo.GetByEmail(req.Email); err == nil {
		return nil, errors.New("邮箱已存在")
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	status := req.Status
	if status == "" {
		status = "active"
	}

	admin := &model.AdminUser{
		Username: req.Username,
		Email:    req.Email,
		Password: string(hashedPassword),
		RealName: req.RealName,
		Status:   status,
	}

	if req.Phone != "" {
		admin.Phone = &req.Phone
	}

	if err := s.adminRepo.Create(admin); err != nil {
		return nil, err
	}

	// 分配角色
	if len(req.RoleIDs) > 0 {
		if err := s.adminRepo.AssignRoles(admin.ID, req.RoleIDs); err != nil {
			return nil, err
		}
	}

	return admin, nil
}

func (s *adminService) GetByID(id uint) (*model.AdminUser, error) {
	return s.adminRepo.GetByID(id)
}

func (s *adminService) GetByIDWithRoles(id uint) (*model.AdminUser, error) {
	return s.adminRepo.GetByIDWithRoles(id)
}

func (s *adminService) GetAll(req QueryAdminRequest) ([]model.AdminUser, int64, error) {
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 10
	}

	filters := make(map[string]interface{})
	if req.Keyword != "" {
		filters["keyword"] = req.Keyword
	}
	if req.Status != "" {
		filters["status"] = req.Status
	}
	if req.RoleID > 0 {
		filters["role_id"] = req.RoleID
	}
	if req.StartDate != "" {
		filters["start_date"] = req.StartDate
	}
	if req.EndDate != "" {
		filters["end_date"] = req.EndDate
	}

	offset := (req.Page - 1) * req.PageSize
	return s.adminRepo.GetAll(offset, req.PageSize, filters)
}

func (s *adminService) Update(id uint, req UpdateAdminRequest) (*model.AdminUser, error) {
	admin, err := s.adminRepo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// 检查用户名是否被其他用户使用
	if req.Username != "" && req.Username != admin.Username {
		if existingAdmin, err := s.adminRepo.GetByUsername(req.Username); err == nil && existingAdmin.ID != id {
			return nil, errors.New("用户名已被其他用户使用")
		}
	}

	// 检查邮箱是否被其他用户使用
	if req.Email != "" && req.Email != admin.Email {
		if existingAdmin, err := s.adminRepo.GetByEmail(req.Email); err == nil && existingAdmin.ID != id {
			return nil, errors.New("邮箱已被其他用户使用")
		}
	}

	// 更新字段 - 直接设置所有字段，包括空值
	admin.Username = req.Username
	admin.Email = req.Email
	admin.RealName = req.RealName
	if req.Phone != "" {
		admin.Phone = &req.Phone
	} else {
		admin.Phone = nil
	}
	admin.Status = req.Status
	if req.Avatar != "" {
		admin.Avatar = &req.Avatar
	} else {
		admin.Avatar = nil
	}
	if req.Remark != "" {
		admin.Remark = &req.Remark
	} else {
		admin.Remark = nil
	}

	if err := s.adminRepo.Update(id, admin); err != nil {
		return nil, err
	}

	// 分配角色
	if req.RoleIDs != nil {
		if err := s.adminRepo.AssignRoles(id, req.RoleIDs); err != nil {
			return nil, err
		}
	}

	return s.adminRepo.GetByIDWithRoles(id)
}

func (s *adminService) Delete(id uint) error {
	if _, err := s.adminRepo.GetByID(id); err != nil {
		return err
	}
	return s.adminRepo.Delete(id)
}

func (s *adminService) BatchDelete(ids []uint) error {
	return s.adminRepo.BatchDelete(ids)
}

func (s *adminService) Login(req LoginRequest) (*model.AdminUser, error) {
	admin, err := s.adminRepo.GetByUsername(req.Username)
	if err != nil {
		fmt.Printf("DEBUG: 用户查找失败: %v\n", err)
		return nil, errors.New("用户名或密码错误")
	}

	fmt.Printf("DEBUG: 找到用户: %s, 状态: %s, 密码哈希: %s\n", admin.Username, admin.Status, admin.Password)
	if admin.Status != "active" {
		fmt.Printf("DEBUG: 账户状态不是active: %s\n", admin.Status)
		return nil, errors.New("账户已被禁用")
	}

	fmt.Printf("DEBUG: 验证密码: %s\n", req.Password)
	if err := bcrypt.CompareHashAndPassword([]byte(admin.Password), []byte(req.Password)); err != nil {
		fmt.Printf("DEBUG: 密码验证失败: %v\n", err)
		return nil, errors.New("用户名或密码错误")
	}

	fmt.Printf("DEBUG: 登录成功\n")
	return admin, nil
}

func (s *adminService) ToggleStatus(id uint) (*model.AdminUser, error) {
	if err := s.adminRepo.ToggleStatus(id); err != nil {
		return nil, err
	}
	return s.adminRepo.GetByID(id)
}

func (s *adminService) ResetPassword(id uint, newPassword string) (string, error) {
	if _, err := s.adminRepo.GetByID(id); err != nil {
		return "", err
	}

	// 如果没有提供新密码，生成随机密码
	if newPassword == "" {
		newPassword = generateRandomPassword(8)
	}

	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}

	if err := s.adminRepo.ResetPassword(id, string(hashedPassword)); err != nil {
		return "", err
	}

	return newPassword, nil
}

func (s *adminService) ChangePassword(id uint, req ChangePasswordRequest) error {
	admin, err := s.adminRepo.GetByID(id)
	if err != nil {
		return err
	}

	// 验证原密码
	if err := bcrypt.CompareHashAndPassword([]byte(admin.Password), []byte(req.OldPassword)); err != nil {
		return errors.New("原密码错误")
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	return s.adminRepo.UpdatePassword(id, string(hashedPassword))
}

func (s *adminService) UpdateProfile(id uint, req UpdateProfileRequest) (*model.AdminUser, error) {
	admin, err := s.adminRepo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// 检查邮箱是否被其他用户使用
	if req.Email != "" && req.Email != admin.Email {
		if existingAdmin, err := s.adminRepo.GetByEmail(req.Email); err == nil && existingAdmin.ID != id {
			return nil, errors.New("邮箱已被其他用户使用")
		}
	}

	// 更新字段
	if req.Email != "" {
		admin.Email = req.Email
	}
	if req.RealName != "" {
		admin.RealName = req.RealName
	}
	if req.Phone != "" {
		admin.Phone = &req.Phone
	}
	if req.Avatar != "" {
		admin.Avatar = &req.Avatar
	}

	if err := s.adminRepo.Update(id, admin); err != nil {
		return nil, err
	}

	return s.adminRepo.GetByID(id)
}

func (s *adminService) GetUserRoles(userID uint) ([]model.AdminRole, error) {
	return s.adminRepo.GetUserRoles(userID)
}

func (s *adminService) GetUserPermissions(userID uint) ([]string, error) {
	// 获取用户的角色
	roles, err := s.adminRepo.GetUserRoles(userID)
	if err != nil {
		return nil, err
	}

	// 收集所有权限编码
	permissionCodes := make(map[string]bool)

	for _, role := range roles {
		// 获取角色的权限
		permissions, err := s.roleRepo.GetRolePermissions(role.ID)
		if err != nil {
			continue // 忽略错误，继续处理其他角色
		}

		for _, permission := range permissions {
			permissionCodes[permission.Code] = true
		}
	}

	// 转换为字符串切片
	result := make([]string, 0, len(permissionCodes))
	for code := range permissionCodes {
		result = append(result, code)
	}

	return result, nil
}

func (s *adminService) AssignRoles(userID uint, roleIDs []uint) error {
	return s.adminRepo.AssignRoles(userID, roleIDs)
}

// generateRandomPassword 生成随机密码
func generateRandomPassword(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		randomByte := make([]byte, 1)
		rand.Read(randomByte)
		b[i] = charset[randomByte[0]%byte(len(charset))]
	}
	return string(b)
}

// InitializeAdminPassword 初始化管理员密码（如果用户不存在则创建）
func (s *adminService) InitializeAdminPassword(username, password string) error {
	// 查找管理员用户
	admin, err := s.adminRepo.GetByUsername(username)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 用户不存在，创建新用户
			return s.createAdminUser(username, password)
		}
		return fmt.Errorf("查找管理员用户失败: %v", err)
	}

	// 用户存在，更新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("密码加密失败: %v", err)
	}

	if err := s.adminRepo.UpdatePassword(admin.ID, string(hashedPassword)); err != nil {
		return fmt.Errorf("更新管理员密码失败: %v", err)
	}

	fmt.Printf("✅ 管理员 %s 密码更新成功\n", username)
	return nil
}

// createAdminUser 创建管理员用户
func (s *adminService) createAdminUser(username, password string) error {
	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("密码加密失败: %v", err)
	}

	// 创建管理员用户
	phone := "13800138000"
	admin := &model.AdminUser{
		Username: username,
		Email:    fmt.Sprintf("%<EMAIL>", username),
		Password: string(hashedPassword),
		RealName: "超级管理员",
		Phone:    &phone,
		Status:   "active",
	}

	if err := s.adminRepo.Create(admin); err != nil {
		return fmt.Errorf("创建管理员用户失败: %v", err)
	}

	// 查找超级管理员角色
	superAdminRole, err := s.roleRepo.GetByCode("super_admin")
	if err != nil {
		return fmt.Errorf("查找超级管理员角色失败: %v", err)
	}

	// 分配超级管理员角色
	if err := s.AssignRoles(admin.ID, []uint{superAdminRole.ID}); err != nil {
		return fmt.Errorf("分配超级管理员角色失败: %v", err)
	}

	fmt.Printf("✅ 管理员 %s 创建成功并分配超级管理员角色\n", username)
	return nil
}
