package service

import (
	"weishi-server/internal/model"
	"weishi-server/internal/repository"
)

type AdminLogService interface {
	Create(req CreateAdminLogRequest) error
	GetByID(id uint) (*model.AdminLog, error)
	GetAll(req QueryAdminLogRequest) ([]model.AdminLog, int64, error)
	GetStatistics() (*repository.AdminLogStatistics, error)
	GetTodayLogs() ([]model.AdminLog, error)
	GetUserLogs(userID uint, page, pageSize int) ([]model.AdminLog, int64, error)
	GetModuleLogs(module string, page, pageSize int) ([]model.AdminLog, int64, error)
	DeleteOldLogs(days int) error
}

type CreateAdminLogRequest struct {
	AdminUserID uint   `json:"admin_user_id"`
	Username    string `json:"username"`
	Action      string `json:"action" binding:"required"`
	Module      string `json:"module" binding:"required"`
	Method      string `json:"method"`
	URL         string `json:"url"`
	IP          string `json:"ip"`
	UserAgent   string `json:"user_agent"`
	Params      string `json:"params"`
	Result      string `json:"result"`
	Status      string `json:"status"`
	ErrorMsg    string `json:"error_msg"`
	Duration    int    `json:"duration"`
}

type QueryAdminLogRequest struct {
	Page      int    `json:"page" form:"page"`
	PageSize  int    `json:"page_size" form:"page_size"`
	Keyword   string `json:"keyword" form:"keyword"`
	UserID    uint   `json:"user_id" form:"user_id"`
	Module    string `json:"module" form:"module"`
	Action    string `json:"action" form:"action"`
	Status    string `json:"status" form:"status"`
	StartDate string `json:"start_date" form:"start_date"`
	EndDate   string `json:"end_date" form:"end_date"`
}

type adminLogService struct {
	adminLogRepo repository.AdminLogRepository
}

func NewAdminLogService(adminLogRepo repository.AdminLogRepository) AdminLogService {
	return &adminLogService{
		adminLogRepo: adminLogRepo,
	}
}

func (s *adminLogService) Create(req CreateAdminLogRequest) error {
	status := req.Status
	if status == "" {
		status = "success"
	}

	log := &model.AdminLog{
		Action: req.Action,
		Module: req.Module,
		Status: status,
	}

	if req.AdminUserID > 0 {
		log.AdminUserID = &req.AdminUserID
	}
	if req.Username != "" {
		log.Username = &req.Username
	}
	if req.Method != "" {
		log.Method = &req.Method
	}
	if req.URL != "" {
		log.URL = &req.URL
	}
	if req.IP != "" {
		log.IP = &req.IP
	}
	if req.UserAgent != "" {
		log.UserAgent = &req.UserAgent
	}
	if req.Params != "" {
		log.Params = &req.Params
	}
	if req.Result != "" {
		log.Result = &req.Result
	}
	if req.ErrorMsg != "" {
		log.ErrorMsg = &req.ErrorMsg
	}
	if req.Duration > 0 {
		log.Duration = &req.Duration
	}

	return s.adminLogRepo.Create(log)
}

func (s *adminLogService) GetByID(id uint) (*model.AdminLog, error) {
	return s.adminLogRepo.GetByID(id)
}

func (s *adminLogService) GetAll(req QueryAdminLogRequest) ([]model.AdminLog, int64, error) {
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 10
	}

	filters := make(map[string]interface{})
	if req.Keyword != "" {
		filters["keyword"] = req.Keyword
	}
	if req.UserID > 0 {
		filters["user_id"] = req.UserID
	}
	if req.Module != "" {
		filters["module"] = req.Module
	}
	if req.Action != "" {
		filters["action"] = req.Action
	}
	if req.Status != "" {
		filters["status"] = req.Status
	}
	if req.StartDate != "" {
		filters["start_date"] = req.StartDate
	}
	if req.EndDate != "" {
		filters["end_date"] = req.EndDate
	}

	offset := (req.Page - 1) * req.PageSize
	return s.adminLogRepo.GetAll(offset, req.PageSize, filters)
}

func (s *adminLogService) GetStatistics() (*repository.AdminLogStatistics, error) {
	return s.adminLogRepo.GetStatistics()
}

func (s *adminLogService) GetTodayLogs() ([]model.AdminLog, error) {
	return s.adminLogRepo.GetTodayLogs()
}

func (s *adminLogService) GetUserLogs(userID uint, page, pageSize int) ([]model.AdminLog, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	return s.adminLogRepo.GetUserLogs(userID, offset, pageSize)
}

func (s *adminLogService) GetModuleLogs(module string, page, pageSize int) ([]model.AdminLog, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	return s.adminLogRepo.GetModuleLogs(module, offset, pageSize)
}

func (s *adminLogService) DeleteOldLogs(days int) error {
	if days <= 0 {
		days = 30 // 默认删除30天前的日志
	}
	return s.adminLogRepo.DeleteOldLogs(days)
}
