package service

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"gorm.io/gorm"
)

type DataImportService struct {
	db *gorm.DB
}

func NewDataImportService(db *gorm.DB) *DataImportService {
	return &DataImportService{
		db: db,
	}
}

// ImportData 根据配置导入数据（已废弃，使用 ImportFullData）
func (s *DataImportService) ImportData(importSeedData, importAdminData bool) error {
	log.Printf("⚠️ ImportData 方法已废弃，请使用 ImportFullData")
	return s.ImportFullData()
}

// hasExistingData 检查是否已有数据
func (s *DataImportService) hasExistingData() bool {
	var count int64

	// 检查关键业务表是否有数据（不包括admin_users，因为管理员可能已经初始化）
	tables := []string{"swipers", "friend_links", "partners"}

	for _, table := range tables {
		s.db.Table(table).Count(&count)
		if count > 0 {
			log.Printf("📊 表 %s 已有 %d 条数据，跳过完整数据导入", table, count)
			return true
		}
	}

	log.Printf("📋 未检测到现有业务数据，可以执行完整数据导入")
	return false
}

// ImportFullData 导入完整数据（包含所有数据）
func (s *DataImportService) ImportFullData() error {
	log.Printf("🗄️ 开始导入完整数据...")

	// 检查数据是否已存在
	if s.hasExistingData() {
		log.Printf("⚠️ 检测到现有业务数据，跳过导入以避免重复")
		return nil
	}

	// 查找完整数据文件
	sqlFiles := []string{
		"scripts/database_full.sql",
		"scripts/full_data.sql",
		"/app/scripts/database_full.sql",
		"/app/scripts/full_data.sql",
		"./scripts/database_full.sql",
		"./database_full.sql",
	}

	sqlFile := s.findSQLFile(sqlFiles)
	if sqlFile == "" {
		log.Printf("❌ 未找到完整数据文件，尝试的路径:")
		for _, file := range sqlFiles {
			log.Printf("   - %s", file)
		}
		return fmt.Errorf("未找到完整数据文件")
	}

	log.Printf("📄 使用数据文件: %s", sqlFile)
	if err := s.executeSQLFile(sqlFile); err != nil {
		log.Printf("❌ 完整数据导入失败: %v", err)
		return err
	}

	log.Printf("✅ 完整数据导入成功完成")
	return nil
}

// findSQLFile 查找SQL文件
func (s *DataImportService) findSQLFile(files []string) string {
	for _, file := range files {
		if _, err := os.Stat(file); err == nil {
			log.Printf("📄 找到数据文件: %s", file)
			return file
		}
	}
	return ""
}

// executeSQLFile 执行SQL文件
func (s *DataImportService) executeSQLFile(sqlFile string) error {
	log.Printf("⚡ 执行SQL文件: %s", sqlFile)

	// 读取SQL文件
	content, err := os.ReadFile(sqlFile)
	if err != nil {
		return fmt.Errorf("读取SQL文件失败: %v", err)
	}

	// 智能分割SQL语句（考虑字符串中的分号）
	sqlStatements := s.splitSQLStatements(string(content))

	// 执行每个SQL语句
	for i, stmt := range sqlStatements {
		stmt = strings.TrimSpace(stmt)
		if stmt == "" {
			continue
		}

		if err := s.db.Exec(stmt).Error; err != nil {
			log.Printf("❌ SQL语句执行失败 (第%d条): %v", i+1, err)
			log.Printf("SQL: %s", stmt[:min(len(stmt), 100)]+"...")
			return fmt.Errorf("SQL执行失败: %v", err)
		}
	}

	log.Printf("✅ SQL文件执行完成: %s", filepath.Base(sqlFile))
	return nil
}

// splitSQLStatements 智能分割SQL语句，正确处理字符串中的分号
func (s *DataImportService) splitSQLStatements(content string) []string {
	var statements []string
	var current strings.Builder
	var inString bool
	var stringChar rune

	runes := []rune(content)
	for i := 0; i < len(runes); i++ {
		char := runes[i]

		// 处理字符串状态
		if !inString && (char == '\'' || char == '"') {
			inString = true
			stringChar = char
			current.WriteRune(char)
		} else if inString && char == stringChar {
			// 检查是否是转义的引号
			if i > 0 && runes[i-1] == '\\' {
				current.WriteRune(char)
			} else {
				inString = false
				current.WriteRune(char)
			}
		} else if !inString && char == ';' {
			// 只有在字符串外的分号才作为语句分隔符
			stmt := strings.TrimSpace(current.String())
			if stmt != "" {
				statements = append(statements, stmt)
			}
			current.Reset()
		} else {
			current.WriteRune(char)
		}
	}

	// 添加最后一个语句
	stmt := strings.TrimSpace(current.String())
	if stmt != "" {
		statements = append(statements, stmt)
	}

	return statements
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// GetDataStats 获取数据统计
func (s *DataImportService) GetDataStats() map[string]int64 {
	stats := make(map[string]int64)

	tables := []string{
		"admin_users",
		"our_services",
		"swipers",
		"news",
		"products",
		"cases",
	}

	for _, table := range tables {
		var count int64
		s.db.Table(table).Count(&count)
		stats[table] = count
	}

	return stats
}

// LogDataStats 记录数据统计
func (s *DataImportService) LogDataStats() {
	stats := s.GetDataStats()

	log.Printf("📊 数据库统计:")
	for table, count := range stats {
		log.Printf("  %s: %d 条", table, count)
	}
}
