package model

import (
	"time"
)

// AdminUser 管理员用户模型
type AdminUser struct {
	BaseModel
	Username    string      `gorm:"type:varchar(50);not null;uniqueIndex" json:"username"`
	Email       string      `gorm:"type:varchar(255);not null;uniqueIndex" json:"email"`
	Password    string      `gorm:"type:varchar(255);not null" json:"-"`
	RealName    string      `gorm:"type:varchar(100);not null" json:"real_name"`
	Avatar      *string     `gorm:"type:varchar(500)" json:"avatar"`
	Phone       *string     `gorm:"type:varchar(20)" json:"phone"`
	Status      string      `gorm:"type:enum('active','inactive','banned');default:'active'" json:"status"`
	LastLoginAt *time.Time  `json:"last_login_at"`
	LastLoginIP *string     `gorm:"type:varchar(45)" json:"last_login_ip"`
	LoginCount  int         `gorm:"default:0" json:"login_count"`
	Remark      *string     `gorm:"type:text" json:"remark"`
	CreatedBy   *int        `json:"created_by"`
	UpdatedBy   *int        `json:"updated_by"`
	Roles       []AdminRole `gorm:"many2many:admin_user_roles;foreignKey:ID;joinForeignKey:admin_user_id;References:ID;joinReferences:role_id" json:"roles"`
}

// TableName 指定表名
func (AdminUser) TableName() string {
	return "admin_users"
}

// AdminRole 管理员角色模型
type AdminRole struct {
	BaseModel
	Name        string            `gorm:"type:varchar(50);not null;uniqueIndex" json:"name"`
	Code        string            `gorm:"type:varchar(50);not null;uniqueIndex" json:"code"`
	Description *string           `gorm:"type:text" json:"description"`
	Status      string            `gorm:"type:enum('active','inactive');default:'active'" json:"status"`
	Sort        int               `gorm:"default:0" json:"sort"`
	Remark      *string           `gorm:"type:text" json:"remark"`
	CreatedBy   *int              `json:"created_by"`
	UpdatedBy   *int              `json:"updated_by"`
	Permissions []AdminPermission `gorm:"many2many:admin_role_permissions;foreignKey:ID;joinForeignKey:role_id;References:ID;joinReferences:permission_id;" json:"permissions,omitempty"`
}

// TableName 指定表名
func (AdminRole) TableName() string {
	return "admin_roles"
}

// AdminPermissionGroup 管理员权限分组模型
type AdminPermissionGroup struct {
	BaseModel
	Name        string  `gorm:"type:varchar(100);not null" json:"name"`
	Code        string  `gorm:"type:varchar(100);not null;uniqueIndex" json:"code"`
	Description *string `gorm:"type:text" json:"description"`
	Icon        *string `gorm:"type:varchar(100)" json:"icon"`
	Sort        int     `gorm:"default:0" json:"sort"`
	Status      string  `gorm:"type:enum('active','inactive');default:'active'" json:"status"`
}

// TableName 指定表名
func (AdminPermissionGroup) TableName() string {
	return "admin_permission_groups"
}

// AdminPermission 管理员权限模型
type AdminPermission struct {
	BaseModel
	Name        string                `gorm:"type:varchar(100);not null" json:"name"`
	Code        string                `gorm:"type:varchar(100);not null;uniqueIndex" json:"code"`
	Type        string                `gorm:"type:enum('menu','button','api');not null" json:"type"`
	ParentID    *uint                 `json:"parent_id"`
	GroupID     *uint                 `json:"group_id"`
	Path        *string               `gorm:"type:varchar(255)" json:"path"`
	Component   *string               `gorm:"type:varchar(255)" json:"component"`
	Icon        *string               `gorm:"type:varchar(100)" json:"icon"`
	Method      *string               `gorm:"type:varchar(10)" json:"method"`
	Description *string               `gorm:"type:text" json:"description"`
	Status      string                `gorm:"type:enum('active','inactive');default:'active'" json:"status"`
	Sort        int                   `gorm:"default:0" json:"sort"`
	Remark      *string               `gorm:"type:text" json:"remark"`
	CreatedBy   *int                  `json:"created_by"`
	UpdatedBy   *int                  `json:"updated_by"`
	Group       *AdminPermissionGroup `gorm:"foreignKey:GroupID" json:"group,omitempty"`
	Children    []AdminPermission     `gorm:"foreignKey:ParentID" json:"children,omitempty"`
}

// TableName 指定表名
func (AdminPermission) TableName() string {
	return "admin_permissions"
}

// AdminUserRole 管理员用户角色关联模型
type AdminUserRole struct {
	ID          uint      `gorm:"primarykey" json:"id"`
	AdminUserID uint      `gorm:"not null" json:"admin_user_id"`
	RoleID      uint      `gorm:"not null" json:"role_id"`
	CreatedBy   *int      `json:"created_by"`
	CreatedAt   time.Time `json:"created_at"`
}

// TableName 指定表名
func (AdminUserRole) TableName() string {
	return "admin_user_roles"
}

// AdminRolePermission 管理员角色权限关联模型
type AdminRolePermission struct {
	ID           uint      `gorm:"primarykey" json:"id"`
	RoleID       uint      `gorm:"not null" json:"role_id"`
	PermissionID uint      `gorm:"not null" json:"permission_id"`
	CreatedBy    *int      `json:"created_by"`
	CreatedAt    time.Time `json:"created_at"`
}

// TableName 指定表名
func (AdminRolePermission) TableName() string {
	return "admin_role_permissions"
}

// AdminLog 管理员操作日志模型
type AdminLog struct {
	BaseModel
	AdminUserID *uint   `json:"admin_user_id"`
	Username    *string `gorm:"type:varchar(50)" json:"username"`
	Action      string  `gorm:"type:varchar(100);not null" json:"action"`
	Module      string  `gorm:"type:varchar(50);not null" json:"module"`
	Method      *string `gorm:"type:varchar(10)" json:"method"`
	URL         *string `gorm:"type:varchar(500)" json:"url"`
	IP          *string `gorm:"type:varchar(45)" json:"ip"`
	UserAgent   *string `gorm:"type:text" json:"user_agent"`
	Params      *string `gorm:"type:json" json:"params"`
	Result      *string `gorm:"type:text" json:"result"`
	Status      string  `gorm:"type:varchar(20);default:'success'" json:"status"`
	ErrorMsg    *string `gorm:"type:text" json:"error_msg"`
	Duration    *int    `json:"duration"`
}

// TableName 指定表名
func (AdminLog) TableName() string {
	return "admin_logs"
}
