package repository

import (
	"weishi-server/internal/model"

	"gorm.io/gorm"
)

type PermissionRepository interface {
	Create(permission *model.AdminPermission) error
	GetByID(id uint) (*model.AdminPermission, error)
	GetByCode(code string) (*model.AdminPermission, error)
	GetAll(offset, limit int, keyword, permType, status string, parentID *uint) ([]model.AdminPermission, int64, error)
	GetAllActive() ([]model.AdminPermission, error)
	GetTree() ([]model.AdminPermission, error)
	GetChildren(parentID uint) ([]model.AdminPermission, error)
	GetMenuPermissions() ([]model.AdminPermission, error)
	Update(id uint, permission *model.AdminPermission) error
	Delete(id uint) error
	BatchDelete(ids []uint) error
	ToggleStatus(id uint) error
	GetUserPermissions(userID uint) ([]model.AdminPermission, error)
	CountPermissions() (int64, error)
}

type permissionRepository struct {
	db *gorm.DB
}

func NewPermissionRepository(db *gorm.DB) PermissionRepository {
	return &permissionRepository{db: db}
}

func (r *permissionRepository) Create(permission *model.AdminPermission) error {
	return r.db.Create(permission).Error
}

func (r *permissionRepository) GetByID(id uint) (*model.AdminPermission, error) {
	var permission model.AdminPermission
	err := r.db.First(&permission, id).Error
	return &permission, err
}

func (r *permissionRepository) GetByCode(code string) (*model.AdminPermission, error) {
	var permission model.AdminPermission
	err := r.db.Where("code = ?", code).First(&permission).Error
	return &permission, err
}

func (r *permissionRepository) GetAll(offset, limit int, keyword, permType, status string, parentID *uint) ([]model.AdminPermission, int64, error) {
	var permissions []model.AdminPermission
	var total int64

	// 构建查询条件
	query := r.db.Model(&model.AdminPermission{})

	// 关键词搜索（搜索名称和编码）
	if keyword != "" {
		query = query.Where("name LIKE ? OR code LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 权限类型筛选
	if permType != "" {
		query = query.Where("type = ?", permType)
	}

	// 状态筛选
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 父权限ID筛选
	if parentID != nil {
		query = query.Where("parent_id = ?", *parentID)
	}

	// 获取总数
	query.Count(&total)

	// 获取分页数据
	err := query.Order("parent_id ASC, sort ASC, id ASC").
		Offset(offset).Limit(limit).
		Find(&permissions).Error

	return permissions, total, err
}

func (r *permissionRepository) GetAllActive() ([]model.AdminPermission, error) {
	var permissions []model.AdminPermission
	err := r.db.Where("status = ?", "active").
		Order("parent_id ASC, sort ASC, id ASC").
		Find(&permissions).Error
	return permissions, err
}

func (r *permissionRepository) GetTree() ([]model.AdminPermission, error) {
	var permissions []model.AdminPermission
	err := r.db.Where("status = ?", "active").
		Order("parent_id ASC, sort ASC, id ASC").
		Find(&permissions).Error
	if err != nil {
		return nil, err
	}

	// 构建树形结构
	return r.buildTree(permissions, 0), nil
}

func (r *permissionRepository) GetChildren(parentID uint) ([]model.AdminPermission, error) {
	var permissions []model.AdminPermission
	err := r.db.Where("parent_id = ? AND status = ?", parentID, "active").
		Order("sort ASC, id ASC").
		Find(&permissions).Error
	return permissions, err
}

func (r *permissionRepository) GetMenuPermissions() ([]model.AdminPermission, error) {
	var permissions []model.AdminPermission
	err := r.db.Where("type = ? AND status = ?", "menu", "active").
		Order("parent_id ASC, sort ASC, id ASC").
		Find(&permissions).Error
	if err != nil {
		return nil, err
	}

	return r.buildTree(permissions, 0), nil
}

func (r *permissionRepository) Update(id uint, permission *model.AdminPermission) error {
	return r.db.Model(&model.AdminPermission{}).Where("id = ?", id).Updates(permission).Error
}

func (r *permissionRepository) Delete(id uint) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 检查是否有子权限
		var count int64
		tx.Model(&model.AdminPermission{}).Where("parent_id = ?", id).Count(&count)
		if count > 0 {
			return gorm.ErrInvalidData
		}

		// 删除权限角色关联
		if err := tx.Where("permission_id = ?", id).Delete(&model.AdminRolePermission{}).Error; err != nil {
			return err
		}

		// 删除权限
		return tx.Delete(&model.AdminPermission{}, id).Error
	})
}

func (r *permissionRepository) BatchDelete(ids []uint) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 检查是否有子权限
		var count int64
		tx.Model(&model.AdminPermission{}).Where("parent_id IN ?", ids).Count(&count)
		if count > 0 {
			return gorm.ErrInvalidData
		}

		// 删除权限角色关联
		if err := tx.Where("permission_id IN ?", ids).Delete(&model.AdminRolePermission{}).Error; err != nil {
			return err
		}

		// 删除权限
		return tx.Where("id IN ?", ids).Delete(&model.AdminPermission{}).Error
	})
}

func (r *permissionRepository) ToggleStatus(id uint) error {
	return r.db.Model(&model.AdminPermission{}).
		Where("id = ?", id).
		Update("status", gorm.Expr("CASE WHEN status = 'active' THEN 'inactive' ELSE 'active' END")).Error
}

func (r *permissionRepository) GetUserPermissions(userID uint) ([]model.AdminPermission, error) {
	var permissions []model.AdminPermission
	err := r.db.Table("admin_permissions").
		Joins("JOIN admin_role_permissions ON admin_permissions.id = admin_role_permissions.permission_id").
		Joins("JOIN admin_user_roles ON admin_role_permissions.role_id = admin_user_roles.role_id").
		Where("admin_user_roles.admin_user_id = ? AND admin_permissions.status = ?", userID, "active").
		Group("admin_permissions.id").
		Find(&permissions).Error
	return permissions, err
}

// buildTree 构建权限树
func (r *permissionRepository) buildTree(permissions []model.AdminPermission, parentID uint) []model.AdminPermission {
	var result []model.AdminPermission

	for _, permission := range permissions {
		if permission.ParentID != nil && *permission.ParentID == parentID {
			children := r.buildTree(permissions, permission.ID)
			if len(children) > 0 {
				// 为了避免修改原始数据，创建新的permission对象
				newPermission := permission
				result = append(result, newPermission)
			} else {
				result = append(result, permission)
			}
		}
	}

	return result
}

// CountPermissions 统计权限总数
func (r *permissionRepository) CountPermissions() (int64, error) {
	var count int64
	err := r.db.Model(&model.AdminPermission{}).Count(&count).Error
	return count, err
}
