package repository

import (
	"weishi-server/internal/model"

	"gorm.io/gorm"
)

type RoleRepository interface {
	Create(role *model.AdminRole) error
	GetByID(id uint) (*model.AdminRole, error)
	GetByCode(code string) (*model.AdminRole, error)
	GetAll(offset, limit int, keyword, status string) ([]model.AdminRole, int64, error)
	GetAllActive() ([]model.AdminRole, error)
	Update(id uint, role *model.AdminRole) error
	Delete(id uint) error
	BatchDelete(ids []uint) error
	GetRolePermissions(roleID uint) ([]model.AdminPermission, error)
	AssignPermissions(roleID uint, permissionIDs []uint) error
	ToggleStatus(id uint) error
	GetUserRoles(userID uint) ([]model.AdminRole, error)
	AssignRolesToUser(userID uint, roleIDs []uint) error
	CountRoles() (int64, error)
}

type roleRepository struct {
	db *gorm.DB
}

func NewRoleRepository(db *gorm.DB) RoleRepository {
	return &roleRepository{db: db}
}

func (r *roleRepository) Create(role *model.AdminRole) error {
	return r.db.Create(role).Error
}

func (r *roleRepository) GetByID(id uint) (*model.AdminRole, error) {
	var role model.AdminRole
	err := r.db.Preload("Permissions").First(&role, id).Error
	return &role, err
}

func (r *roleRepository) GetByCode(code string) (*model.AdminRole, error) {
	var role model.AdminRole
	err := r.db.Where("code = ?", code).First(&role).Error
	return &role, err
}

func (r *roleRepository) GetAll(offset, limit int, keyword, status string) ([]model.AdminRole, int64, error) {
	var roles []model.AdminRole
	var total int64

	// 构建查询条件
	query := r.db.Model(&model.AdminRole{})

	// 关键词搜索：搜索角色名称、编码、描述
	if keyword != "" {
		query = query.Where("name LIKE ? OR code LIKE ? OR description LIKE ?",
			"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 状态过滤
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 获取总数
	query.Count(&total)

	// 获取数据
	err := query.Preload("Permissions").
		Order("sort ASC, id DESC").
		Offset(offset).Limit(limit).
		Find(&roles).Error

	return roles, total, err
}

func (r *roleRepository) GetAllActive() ([]model.AdminRole, error) {
	var roles []model.AdminRole
	err := r.db.Where("status = ?", "active").
		Order("sort ASC, id DESC").
		Find(&roles).Error
	return roles, err
}

func (r *roleRepository) Update(id uint, role *model.AdminRole) error {
	return r.db.Model(&model.AdminRole{}).Where("id = ?", id).Updates(role).Error
}

func (r *roleRepository) Delete(id uint) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 删除角色权限关联
		if err := tx.Where("role_id = ?", id).Delete(&model.AdminRolePermission{}).Error; err != nil {
			return err
		}
		// 删除用户角色关联
		if err := tx.Where("role_id = ?", id).Delete(&model.AdminUserRole{}).Error; err != nil {
			return err
		}
		// 删除角色
		return tx.Delete(&model.AdminRole{}, id).Error
	})
}

func (r *roleRepository) BatchDelete(ids []uint) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 删除角色权限关联
		if err := tx.Where("role_id IN ?", ids).Delete(&model.AdminRolePermission{}).Error; err != nil {
			return err
		}
		// 删除用户角色关联
		if err := tx.Where("role_id IN ?", ids).Delete(&model.AdminUserRole{}).Error; err != nil {
			return err
		}
		// 删除角色
		return tx.Where("id IN ?", ids).Delete(&model.AdminRole{}).Error
	})
}

func (r *roleRepository) GetRolePermissions(roleID uint) ([]model.AdminPermission, error) {
	var permissions []model.AdminPermission
	err := r.db.Table("admin_permissions").
		Joins("JOIN admin_role_permissions ON admin_permissions.id = admin_role_permissions.permission_id").
		Where("admin_role_permissions.role_id = ?", roleID).
		Find(&permissions).Error
	return permissions, err
}

func (r *roleRepository) AssignPermissions(roleID uint, permissionIDs []uint) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 删除现有权限关联
		if err := tx.Where("role_id = ?", roleID).Delete(&model.AdminRolePermission{}).Error; err != nil {
			return err
		}

		// 添加新的权限关联
		for _, permissionID := range permissionIDs {
			rolePermission := model.AdminRolePermission{
				RoleID:       roleID,
				PermissionID: permissionID,
			}
			if err := tx.Create(&rolePermission).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

func (r *roleRepository) ToggleStatus(id uint) error {
	return r.db.Model(&model.AdminRole{}).
		Where("id = ?", id).
		Update("status", gorm.Expr("CASE WHEN status = 'active' THEN 'inactive' ELSE 'active' END")).Error
}

func (r *roleRepository) GetUserRoles(userID uint) ([]model.AdminRole, error) {
	var roles []model.AdminRole
	err := r.db.Table("admin_roles").
		Joins("JOIN admin_user_roles ON admin_roles.id = admin_user_roles.role_id").
		Where("admin_user_roles.admin_user_id = ?", userID).
		Find(&roles).Error
	return roles, err
}

func (r *roleRepository) AssignRolesToUser(userID uint, roleIDs []uint) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 删除现有角色关联
		if err := tx.Where("admin_user_id = ?", userID).Delete(&model.AdminUserRole{}).Error; err != nil {
			return err
		}

		// 添加新的角色关联
		for _, roleID := range roleIDs {
			userRole := model.AdminUserRole{
				AdminUserID: userID,
				RoleID:      roleID,
			}
			if err := tx.Create(&userRole).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// CountRoles 统计角色总数
func (r *roleRepository) CountRoles() (int64, error) {
	var count int64
	err := r.db.Model(&model.AdminRole{}).Count(&count).Error
	return count, err
}
