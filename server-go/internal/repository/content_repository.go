package repository

import (
	"weishi-server/internal/model"

	"gorm.io/gorm"
)

type ContentRepository interface {
	// 新闻相关方法
	GetAllNews() ([]model.News, error)
	GetHomePageNews() ([]model.News, error)
	GetNewsByID(id uint) (*model.News, error)
	CreateNews(news *model.News) error
	UpdateNews(news *model.News) error
	DeleteNews(id uint) error
	SetNewsHomePage(id uint, isHomePage bool) error

	// 轮播图相关方法
	GetAllSwipers() ([]model.Swiper, error)
	GetSwiperByID(id uint) (*model.Swiper, error)
	CreateSwiper(swiper *model.Swiper) error
	UpdateSwiper(swiper *model.Swiper) error
	DeleteSwiper(id uint) error

	// 合作伙伴相关方法
	GetAllPartners() ([]model.Partner, error)
	GetPartnerByID(id uint) (*model.Partner, error)
	CreatePartner(partner *model.Partner) error
	UpdatePartner(partner *model.Partner) error
	DeletePartner(id uint) error
	// 友情链接相关方法
	GetAllFriendLinks() ([]model.FriendLink, error)
	GetFriendLinkByID(id uint) (*model.FriendLink, error)
	CreateFriendLink(friendLink *model.FriendLink) error
	UpdateFriendLink(friendLink *model.FriendLink) error
	DeleteFriendLink(id uint) error

	// 招聘信息相关方法
	GetAllRecruitments() ([]model.Recruitment, error)
	GetRecruitmentByID(id uint) (*model.Recruitment, error)
	CreateRecruitment(recruitment *model.Recruitment) error
	UpdateRecruitment(recruitment *model.Recruitment) error
	DeleteRecruitment(id uint) error

	// 平台相关方法
	GetAllPartPlatforms() ([]model.PartPlatform, error)
	GetPartPlatformByID(id uint) (*model.PartPlatform, error)
	GetPartPlatformWithExtensions(id uint) (*model.PartPlatform, []model.PartPlatformExtension, error)
	CreatePartPlatform(platform *model.PartPlatform) error
	UpdatePartPlatform(platform *model.PartPlatform) error
	DeletePartPlatform(id uint) error

	// 平台扩展数据相关方法
	GetPartPlatformExtensionsByPlatformID(platformID uint) ([]model.PartPlatformExtension, error)
	CreatePartPlatformExtension(extension *model.PartPlatformExtension) error
	UpdatePartPlatformExtension(extension *model.PartPlatformExtension) error
	DeletePartPlatformExtension(id uint) error
	CreatePartPlatformTable(table *model.PartPlatformTable) error
	UpdatePartPlatformTable(table *model.PartPlatformTable) error
	DeletePartPlatformTable(id uint) error
	GetPartPlatformTableColumnsByTableID(tableID uint) ([]model.PartPlatformTableColumn, error)
	CreatePartPlatformTableColumn(column *model.PartPlatformTableColumn) error
	UpdatePartPlatformTableColumn(column *model.PartPlatformTableColumn) error
	DeletePartPlatformTableColumn(id uint) error

	// 列模板相关方法
	GetColumnTemplates() ([]model.ColumnTemplate, error)
	GetColumnTemplatesByCategory(category string) ([]model.ColumnTemplate, error)
	CreateColumnTemplate(template *model.ColumnTemplate) error
	UpdateColumnTemplate(template *model.ColumnTemplate) error
	DeleteColumnTemplate(id uint) error

	// 组合模板相关方法
	GetCombinationTemplates() ([]model.CombinationTemplate, error)
	GetCombinationTemplateByID(id uint) (*model.CombinationTemplate, error)
	CreateCombinationTemplate(template *model.CombinationTemplate) error
	UpdateCombinationTemplate(template *model.CombinationTemplate) error
	DeleteCombinationTemplate(id uint) error

	// 表格列配置相关方法（新的基于模板的方法）
	GetTableColumnsWithTemplate(tableID uint) ([]model.ColumnTemplate, []model.PartPlatformTableColumnOverride, error)
	CreateTableColumnOverride(override *model.PartPlatformTableColumnOverride) error
	UpdateTableColumnOverride(override *model.PartPlatformTableColumnOverride) error
	DeleteTableColumnOverride(id uint) error
	GetPartPlatformTableRowsByTableID(tableID uint) ([]model.PartPlatformTableRow, error)
	CreatePartPlatformTableRow(row *model.PartPlatformTableRow) error
	UpdatePartPlatformTableRow(row *model.PartPlatformTableRow) error
	DeletePartPlatformTableRow(id uint) error

	// 项目案例相关方法
	GetAllProjectCases() ([]model.ProjectCase, error)
	GetProjectCaseByID(id uint) (*model.ProjectCase, error)
	CreateProjectCase(projectCase *model.ProjectCase) error
	UpdateProjectCase(projectCase *model.ProjectCase) error
	DeleteProjectCase(id uint) error

	// 文件上传相关方法
	GetFileUploadByID(id uint) (*model.FileUpload, error)
	CreateFileUpload(fileUpload *model.FileUpload) error
	UpdateFileUpload(fileUpload *model.FileUpload) error
	DeleteFileUpload(id uint) error
	GetFileUploadsByModule(module string) ([]model.FileUpload, error)
	GetFileUploadsByUser(userID uint) ([]model.FileUpload, error)
}

type contentRepository struct {
	db *gorm.DB
}

func NewContentRepository(db *gorm.DB) ContentRepository {
	return &contentRepository{
		db: db,
	}
}

func (r *contentRepository) GetAllNews() ([]model.News, error) {
	var news []model.News
	err := r.db.Find(&news).Error
	return news, err
}

func (r *contentRepository) GetHomePageNews() ([]model.News, error) {
	var news []model.News
	err := r.db.Where("is_home_page = ?", true).Find(&news).Error
	return news, err
}

func (r *contentRepository) GetNewsByID(id uint) (*model.News, error) {
	var news model.News
	err := r.db.First(&news, id).Error
	return &news, err
}

func (r *contentRepository) CreateNews(news *model.News) error {
	return r.db.Create(news).Error
}

func (r *contentRepository) UpdateNews(news *model.News) error {
	return r.db.Save(news).Error
}

func (r *contentRepository) DeleteNews(id uint) error {
	return r.db.Delete(&model.News{}, id).Error
}

func (r *contentRepository) SetNewsHomePage(id uint, isHomePage bool) error {
	return r.db.Model(&model.News{}).Where("id = ?", id).Update("is_home_page", isHomePage).Error
}

func (r *contentRepository) GetAllSwipers() ([]model.Swiper, error) {
	var swipers []model.Swiper
	err := r.db.Order("`order` ASC").Find(&swipers).Error
	return swipers, err
}

func (r *contentRepository) GetSwiperByID(id uint) (*model.Swiper, error) {
	var swiper model.Swiper
	err := r.db.First(&swiper, id).Error
	if err != nil {
		return nil, err
	}
	return &swiper, nil
}

func (r *contentRepository) CreateSwiper(swiper *model.Swiper) error {
	return r.db.Create(swiper).Error
}

func (r *contentRepository) UpdateSwiper(swiper *model.Swiper) error {
	return r.db.Save(swiper).Error
}

func (r *contentRepository) DeleteSwiper(id uint) error {
	return r.db.Delete(&model.Swiper{}, id).Error
}

func (r *contentRepository) GetAllPartners() ([]model.Partner, error) {
	var partners []model.Partner
	err := r.db.Find(&partners).Error
	return partners, err
}

func (r *contentRepository) GetPartnerByID(id uint) (*model.Partner, error) {
	var partner model.Partner
	err := r.db.First(&partner, id).Error
	if err != nil {
		return nil, err
	}
	return &partner, nil
}

func (r *contentRepository) CreatePartner(partner *model.Partner) error {
	return r.db.Create(partner).Error
}

func (r *contentRepository) UpdatePartner(partner *model.Partner) error {
	return r.db.Save(partner).Error
}

func (r *contentRepository) DeletePartner(id uint) error {
	return r.db.Delete(&model.Partner{}, id).Error
}

func (r *contentRepository) GetAllFriendLinks() ([]model.FriendLink, error) {
	var links []model.FriendLink
	err := r.db.Order("`order` ASC").Find(&links).Error
	return links, err
}

func (r *contentRepository) GetFriendLinkByID(id uint) (*model.FriendLink, error) {
	var friendLink model.FriendLink
	err := r.db.First(&friendLink, id).Error
	if err != nil {
		return nil, err
	}
	return &friendLink, nil
}

func (r *contentRepository) CreateFriendLink(friendLink *model.FriendLink) error {
	return r.db.Create(friendLink).Error
}

func (r *contentRepository) UpdateFriendLink(friendLink *model.FriendLink) error {
	return r.db.Save(friendLink).Error
}

func (r *contentRepository) DeleteFriendLink(id uint) error {
	return r.db.Delete(&model.FriendLink{}, id).Error
}

func (r *contentRepository) GetAllRecruitments() ([]model.Recruitment, error) {
	var recruitments []model.Recruitment
	err := r.db.Order("`order` ASC").Find(&recruitments).Error
	return recruitments, err
}

func (r *contentRepository) GetRecruitmentByID(id uint) (*model.Recruitment, error) {
	var recruitment model.Recruitment
	err := r.db.First(&recruitment, id).Error
	if err != nil {
		return nil, err
	}
	return &recruitment, nil
}

func (r *contentRepository) CreateRecruitment(recruitment *model.Recruitment) error {
	return r.db.Create(recruitment).Error
}

func (r *contentRepository) UpdateRecruitment(recruitment *model.Recruitment) error {
	return r.db.Save(recruitment).Error
}

func (r *contentRepository) DeleteRecruitment(id uint) error {
	return r.db.Delete(&model.Recruitment{}, id).Error
}

func (r *contentRepository) GetAllPartPlatforms() ([]model.PartPlatform, error) {
	var platforms []model.PartPlatform
	err := r.db.Order("`order` ASC").Find(&platforms).Error
	return platforms, err
}

func (r *contentRepository) GetPartPlatformByID(id uint) (*model.PartPlatform, error) {
	var platform model.PartPlatform
	err := r.db.First(&platform, id).Error
	if err != nil {
		return nil, err
	}
	return &platform, nil
}

// GetPartPlatformWithExtensions 获取包含扩展数据的平台详情
func (r *contentRepository) GetPartPlatformWithExtensions(id uint) (*model.PartPlatform, []model.PartPlatformExtension, error) {
	var platform model.PartPlatform
	err := r.db.First(&platform, id).Error
	if err != nil {
		return nil, nil, err
	}

	var extensions []model.PartPlatformExtension
	err = r.db.Where("part_platform_id = ?", id).
		Order("sort_order ASC").
		Preload("Tables", func(db *gorm.DB) *gorm.DB {
			return db.Order("sort_order ASC").
				Preload("Columns", func(db *gorm.DB) *gorm.DB {
					return db.Order("sort_order ASC")
				}).
				Preload("Rows", func(db *gorm.DB) *gorm.DB {
					return db.Order("sort_order ASC")
				})
		}).
		Find(&extensions).Error

	return &platform, extensions, err
}

// PartPlatformExtension 相关方法
func (r *contentRepository) GetPartPlatformExtensionsByPlatformID(platformID uint) ([]model.PartPlatformExtension, error) {
	var extensions []model.PartPlatformExtension
	err := r.db.Where("part_platform_id = ?", platformID).
		Order("sort_order ASC").
		Preload("Tables", func(db *gorm.DB) *gorm.DB {
			return db.Order("sort_order ASC").
				Preload("Columns", func(db *gorm.DB) *gorm.DB {
					return db.Order("sort_order ASC")
				}).
				Preload("Rows", func(db *gorm.DB) *gorm.DB {
					return db.Order("sort_order ASC")
				})
		}).
		Find(&extensions).Error
	return extensions, err
}

func (r *contentRepository) CreatePartPlatformExtension(extension *model.PartPlatformExtension) error {
	return r.db.Create(extension).Error
}

func (r *contentRepository) UpdatePartPlatformExtension(extension *model.PartPlatformExtension) error {
	return r.db.Save(extension).Error
}

func (r *contentRepository) DeletePartPlatformExtension(id uint) error {
	return r.db.Delete(&model.PartPlatformExtension{}, id).Error
}

// PartPlatformTable 相关方法
func (r *contentRepository) CreatePartPlatformTable(table *model.PartPlatformTable) error {
	return r.db.Create(table).Error
}

func (r *contentRepository) UpdatePartPlatformTable(table *model.PartPlatformTable) error {
	return r.db.Save(table).Error
}

func (r *contentRepository) DeletePartPlatformTable(id uint) error {
	return r.db.Delete(&model.PartPlatformTable{}, id).Error
}

// PartPlatformTableColumn 相关方法
func (r *contentRepository) GetPartPlatformTableColumnsByTableID(tableID uint) ([]model.PartPlatformTableColumn, error) {
	var columns []model.PartPlatformTableColumn
	err := r.db.Where("table_id = ?", tableID).
		Order("sort_order ASC").
		Find(&columns).Error
	return columns, err
}

func (r *contentRepository) CreatePartPlatformTableColumn(column *model.PartPlatformTableColumn) error {
	return r.db.Create(column).Error
}

func (r *contentRepository) UpdatePartPlatformTableColumn(column *model.PartPlatformTableColumn) error {
	return r.db.Save(column).Error
}

func (r *contentRepository) DeletePartPlatformTableColumn(id uint) error {
	return r.db.Delete(&model.PartPlatformTableColumn{}, id).Error
}

// PartPlatformTableRow 相关方法
func (r *contentRepository) GetPartPlatformTableRowsByTableID(tableID uint) ([]model.PartPlatformTableRow, error) {
	var rows []model.PartPlatformTableRow
	err := r.db.Where("table_id = ?", tableID).
		Order("sort_order ASC").
		Find(&rows).Error
	return rows, err
}

func (r *contentRepository) CreatePartPlatformTableRow(row *model.PartPlatformTableRow) error {
	return r.db.Create(row).Error
}

func (r *contentRepository) UpdatePartPlatformTableRow(row *model.PartPlatformTableRow) error {
	return r.db.Save(row).Error
}

func (r *contentRepository) DeletePartPlatformTableRow(id uint) error {
	return r.db.Delete(&model.PartPlatformTableRow{}, id).Error
}

func (r *contentRepository) CreatePartPlatform(platform *model.PartPlatform) error {
	return r.db.Create(platform).Error
}

func (r *contentRepository) UpdatePartPlatform(platform *model.PartPlatform) error {
	return r.db.Save(platform).Error
}

func (r *contentRepository) DeletePartPlatform(id uint) error {
	return r.db.Delete(&model.PartPlatform{}, id).Error
}

func (r *contentRepository) GetAllProjectCases() ([]model.ProjectCase, error) {
	var cases []model.ProjectCase
	err := r.db.Order("sort ASC").Find(&cases).Error
	return cases, err
}

func (r *contentRepository) GetProjectCaseByID(id uint) (*model.ProjectCase, error) {
	var projectCase model.ProjectCase
	err := r.db.First(&projectCase, id).Error
	if err != nil {
		return nil, err
	}
	return &projectCase, nil
}

func (r *contentRepository) CreateProjectCase(projectCase *model.ProjectCase) error {
	return r.db.Create(projectCase).Error
}

func (r *contentRepository) UpdateProjectCase(projectCase *model.ProjectCase) error {
	return r.db.Save(projectCase).Error
}

func (r *contentRepository) DeleteProjectCase(id uint) error {
	return r.db.Delete(&model.ProjectCase{}, id).Error
}

// 文件上传相关方法实现
func (r *contentRepository) GetFileUploadByID(id uint) (*model.FileUpload, error) {
	var fileUpload model.FileUpload
	err := r.db.First(&fileUpload, id).Error
	if err != nil {
		return nil, err
	}
	return &fileUpload, nil
}

func (r *contentRepository) CreateFileUpload(fileUpload *model.FileUpload) error {
	return r.db.Create(fileUpload).Error
}

func (r *contentRepository) UpdateFileUpload(fileUpload *model.FileUpload) error {
	return r.db.Save(fileUpload).Error
}

func (r *contentRepository) DeleteFileUpload(id uint) error {
	return r.db.Delete(&model.FileUpload{}, id).Error
}

// 列模板相关方法实现
func (r *contentRepository) GetColumnTemplates() ([]model.ColumnTemplate, error) {
	var templates []model.ColumnTemplate
	err := r.db.Order("category ASC, sort_order ASC, name ASC").Find(&templates).Error
	return templates, err
}

func (r *contentRepository) GetColumnTemplatesByCategory(category string) ([]model.ColumnTemplate, error) {
	var templates []model.ColumnTemplate
	query := r.db.Order("sort_order ASC, name ASC")
	if category != "" && category != "all" {
		query = query.Where("category = ?", category)
	}
	err := query.Find(&templates).Error
	return templates, err
}

func (r *contentRepository) CreateColumnTemplate(template *model.ColumnTemplate) error {
	return r.db.Create(template).Error
}

func (r *contentRepository) UpdateColumnTemplate(template *model.ColumnTemplate) error {
	return r.db.Save(template).Error
}

func (r *contentRepository) DeleteColumnTemplate(id uint) error {
	return r.db.Delete(&model.ColumnTemplate{}, id).Error
}

// 组合模板相关方法实现
func (r *contentRepository) GetCombinationTemplates() ([]model.CombinationTemplate, error) {
	var templates []model.CombinationTemplate
	err := r.db.Preload("Columns", func(db *gorm.DB) *gorm.DB {
		return db.Order("combination_template_columns.sort_order ASC")
	}).Order("sort_order ASC, name ASC").Find(&templates).Error
	return templates, err
}

func (r *contentRepository) GetCombinationTemplateByID(id uint) (*model.CombinationTemplate, error) {
	var template model.CombinationTemplate
	err := r.db.Preload("Columns", func(db *gorm.DB) *gorm.DB {
		return db.Order("combination_template_columns.sort_order ASC")
	}).First(&template, id).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

func (r *contentRepository) CreateCombinationTemplate(template *model.CombinationTemplate) error {
	return r.db.Create(template).Error
}

func (r *contentRepository) UpdateCombinationTemplate(template *model.CombinationTemplate) error {
	return r.db.Save(template).Error
}

func (r *contentRepository) DeleteCombinationTemplate(id uint) error {
	return r.db.Delete(&model.CombinationTemplate{}, id).Error
}

// 表格列配置相关方法实现（新的基于模板的方法）
func (r *contentRepository) GetTableColumnsWithTemplate(tableID uint) ([]model.ColumnTemplate, []model.PartPlatformTableColumnOverride, error) {
	// 1. 获取表格信息
	var table model.PartPlatformTable
	err := r.db.Preload("CombinationTemplate.Columns", func(db *gorm.DB) *gorm.DB {
		return db.Order("combination_template_columns.sort_order ASC")
	}).First(&table, tableID).Error
	if err != nil {
		return nil, nil, err
	}

	// 2. 获取模板列
	var templateColumns []model.ColumnTemplate
	if table.CombinationTemplate != nil {
		templateColumns = table.CombinationTemplate.Columns
	}

	// 3. 获取覆盖配置
	var overrides []model.PartPlatformTableColumnOverride
	err = r.db.Preload("ColumnTemplate").Where("table_id = ?", tableID).
		Order("sort_order ASC").Find(&overrides).Error
	if err != nil {
		return templateColumns, nil, err
	}

	return templateColumns, overrides, nil
}

func (r *contentRepository) CreateTableColumnOverride(override *model.PartPlatformTableColumnOverride) error {
	return r.db.Create(override).Error
}

func (r *contentRepository) UpdateTableColumnOverride(override *model.PartPlatformTableColumnOverride) error {
	return r.db.Save(override).Error
}

func (r *contentRepository) DeleteTableColumnOverride(id uint) error {
	return r.db.Delete(&model.PartPlatformTableColumnOverride{}, id).Error
}

func (r *contentRepository) GetFileUploadsByModule(module string) ([]model.FileUpload, error) {
	var fileUploads []model.FileUpload
	err := r.db.Where("module = ? AND status = ?", module, "active").
		Order("created_at DESC").Find(&fileUploads).Error
	return fileUploads, err
}

func (r *contentRepository) GetFileUploadsByUser(userID uint) ([]model.FileUpload, error) {
	var fileUploads []model.FileUpload
	err := r.db.Where("uploaded_by = ? AND status = ?", userID, "active").
		Order("created_at DESC").Find(&fileUploads).Error
	return fileUploads, err
}
