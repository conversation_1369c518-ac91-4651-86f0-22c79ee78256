package repository

import (
	"time"
	"weishi-server/internal/model"

	"gorm.io/gorm"
)

type AdminLogRepository interface {
	Create(log *model.AdminLog) error
	GetByID(id uint) (*model.AdminLog, error)
	GetAll(offset, limit int, filters map[string]interface{}) ([]model.AdminLog, int64, error)
	GetStatistics() (*AdminLogStatistics, error)
	GetTodayLogs() ([]model.AdminLog, error)
	GetUserLogs(userID uint, offset, limit int) ([]model.AdminLog, int64, error)
	GetModuleLogs(module string, offset, limit int) ([]model.AdminLog, int64, error)
	DeleteOldLogs(days int) error
	CountTodayLogins() (int64, error)
	CountTodayOperations() (int64, error)
	GetRecentLogs(limit int) ([]model.AdminLog, error)
	GetModuleStats() ([]ModuleStat, error)
	GetOperationStats(days int) ([]OperationStat, error)
}

type AdminLogStatistics struct {
	TotalLogs   int64                    `json:"total_logs"`
	TodayLogs   int64                    `json:"today_logs"`
	SuccessRate float64                  `json:"success_rate"`
	TopModules  []map[string]interface{} `json:"top_modules"`
	TopActions  []map[string]interface{} `json:"top_actions"`
	TopUsers    []map[string]interface{} `json:"top_users"`
}

type adminLogRepository struct {
	db *gorm.DB
}

func NewAdminLogRepository(db *gorm.DB) AdminLogRepository {
	return &adminLogRepository{db: db}
}

func (r *adminLogRepository) Create(log *model.AdminLog) error {
	return r.db.Create(log).Error
}

func (r *adminLogRepository) GetByID(id uint) (*model.AdminLog, error) {
	var log model.AdminLog
	err := r.db.First(&log, id).Error
	return &log, err
}

func (r *adminLogRepository) GetAll(offset, limit int, filters map[string]interface{}) ([]model.AdminLog, int64, error) {
	var logs []model.AdminLog
	var total int64

	query := r.db.Model(&model.AdminLog{})

	// 应用过滤条件
	if userID, exists := filters["user_id"]; exists && userID != nil {
		query = query.Where("admin_user_id = ?", userID)
	}

	if module, exists := filters["module"]; exists && module != "" {
		query = query.Where("module = ?", module)
	}

	if action, exists := filters["action"]; exists && action != "" {
		query = query.Where("action LIKE ?", "%"+action.(string)+"%")
	}

	if status, exists := filters["status"]; exists && status != "" {
		query = query.Where("status = ?", status)
	}

	if keyword, exists := filters["keyword"]; exists && keyword != "" {
		query = query.Where("username LIKE ? OR action LIKE ? OR module LIKE ?",
			"%"+keyword.(string)+"%",
			"%"+keyword.(string)+"%",
			"%"+keyword.(string)+"%")
	}

	if startDate, exists := filters["start_date"]; exists && startDate != "" {
		query = query.Where("created_at >= ?", startDate)
	}

	if endDate, exists := filters["end_date"]; exists && endDate != "" {
		query = query.Where("created_at <= ?", endDate)
	}

	// 获取总数
	query.Count(&total)

	// 获取数据
	err := query.Order("created_at DESC").
		Offset(offset).Limit(limit).
		Find(&logs).Error

	return logs, total, err
}

func (r *adminLogRepository) GetStatistics() (*AdminLogStatistics, error) {
	stats := &AdminLogStatistics{}

	// 总日志数
	r.db.Model(&model.AdminLog{}).Count(&stats.TotalLogs)

	// 今日日志数
	today := time.Now().Format("2006-01-02")
	r.db.Model(&model.AdminLog{}).
		Where("DATE(created_at) = ?", today).
		Count(&stats.TodayLogs)

	// 成功率
	var successCount int64
	r.db.Model(&model.AdminLog{}).
		Where("status = ?", "success").
		Count(&successCount)

	if stats.TotalLogs > 0 {
		stats.SuccessRate = float64(successCount) / float64(stats.TotalLogs) * 100
	}

	// Top模块
	type ModuleCount struct {
		Module string `json:"module"`
		Count  int64  `json:"count"`
	}
	var moduleCounts []ModuleCount
	r.db.Model(&model.AdminLog{}).
		Select("module, COUNT(*) as count").
		Group("module").
		Order("count DESC").
		Limit(10).
		Find(&moduleCounts)

	for _, mc := range moduleCounts {
		stats.TopModules = append(stats.TopModules, map[string]interface{}{
			"module": mc.Module,
			"count":  mc.Count,
		})
	}

	// Top操作
	type ActionCount struct {
		Action string `json:"action"`
		Count  int64  `json:"count"`
	}
	var actionCounts []ActionCount
	r.db.Model(&model.AdminLog{}).
		Select("action, COUNT(*) as count").
		Group("action").
		Order("count DESC").
		Limit(10).
		Find(&actionCounts)

	for _, ac := range actionCounts {
		stats.TopActions = append(stats.TopActions, map[string]interface{}{
			"action": ac.Action,
			"count":  ac.Count,
		})
	}

	// Top用户
	type UserCount struct {
		Username string `json:"username"`
		Count    int64  `json:"count"`
	}
	var userCounts []UserCount
	r.db.Model(&model.AdminLog{}).
		Select("username, COUNT(*) as count").
		Where("username IS NOT NULL").
		Group("username").
		Order("count DESC").
		Limit(10).
		Find(&userCounts)

	for _, uc := range userCounts {
		stats.TopUsers = append(stats.TopUsers, map[string]interface{}{
			"username": uc.Username,
			"count":    uc.Count,
		})
	}

	return stats, nil
}

func (r *adminLogRepository) GetTodayLogs() ([]model.AdminLog, error) {
	var logs []model.AdminLog
	today := time.Now().Format("2006-01-02")
	err := r.db.Where("DATE(created_at) = ?", today).
		Order("created_at DESC").
		Find(&logs).Error
	return logs, err
}

func (r *adminLogRepository) GetUserLogs(userID uint, offset, limit int) ([]model.AdminLog, int64, error) {
	var logs []model.AdminLog
	var total int64

	r.db.Model(&model.AdminLog{}).Where("admin_user_id = ?", userID).Count(&total)
	err := r.db.Where("admin_user_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).Limit(limit).
		Find(&logs).Error

	return logs, total, err
}

func (r *adminLogRepository) GetModuleLogs(module string, offset, limit int) ([]model.AdminLog, int64, error) {
	var logs []model.AdminLog
	var total int64

	r.db.Model(&model.AdminLog{}).Where("module = ?", module).Count(&total)
	err := r.db.Where("module = ?", module).
		Order("created_at DESC").
		Offset(offset).Limit(limit).
		Find(&logs).Error

	return logs, total, err
}

func (r *adminLogRepository) DeleteOldLogs(days int) error {
	cutoffDate := time.Now().AddDate(0, 0, -days)
	return r.db.Where("created_at < ?", cutoffDate).Delete(&model.AdminLog{}).Error
}

// CountTodayLogins 统计今日登录次数
func (r *adminLogRepository) CountTodayLogins() (int64, error) {
	var count int64
	today := time.Now().Format("2006-01-02")
	err := r.db.Model(&model.AdminLog{}).
		Where("action = ? AND DATE(created_at) = ?", "login", today).
		Count(&count).Error
	return count, err
}

// CountTodayOperations 统计今日操作次数
func (r *adminLogRepository) CountTodayOperations() (int64, error) {
	var count int64
	today := time.Now().Format("2006-01-02")
	err := r.db.Model(&model.AdminLog{}).
		Where("DATE(created_at) = ?", today).
		Count(&count).Error
	return count, err
}

// GetRecentLogs 获取最近的日志记录
func (r *adminLogRepository) GetRecentLogs(limit int) ([]model.AdminLog, error) {
	var logs []model.AdminLog
	err := r.db.Order("created_at DESC").Limit(limit).Find(&logs).Error
	return logs, err
}

// ModuleStat 模块统计结构
type ModuleStat struct {
	Module string
	Count  int64
}

// GetModuleStats 获取模块访问统计
func (r *adminLogRepository) GetModuleStats() ([]ModuleStat, error) {
	var stats []ModuleStat
	err := r.db.Model(&model.AdminLog{}).
		Select("module, COUNT(*) as count").
		Group("module").
		Order("count DESC").
		Scan(&stats).Error
	return stats, err
}

// OperationStat 操作统计结构
type OperationStat struct {
	Date    string
	Success int64
	Failed  int64
}

// GetOperationStats 获取操作统计
func (r *adminLogRepository) GetOperationStats(days int) ([]OperationStat, error) {
	var stats []OperationStat

	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -days)

	err := r.db.Model(&model.AdminLog{}).
		Select("DATE(created_at) as date, "+
			"SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success, "+
			"SUM(CASE WHEN status != 'success' THEN 1 ELSE 0 END) as failed").
		Where("created_at BETWEEN ? AND ?", startDate, endDate).
		Group("DATE(created_at)").
		Order("date").
		Scan(&stats).Error

	return stats, err
}
