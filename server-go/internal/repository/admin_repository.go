package repository

import (
	"weishi-server/internal/model"

	"gorm.io/gorm"
)

type AdminRepository interface {
	Create(admin *model.AdminUser) error
	GetByID(id uint) (*model.AdminUser, error)
	GetByIDWithRoles(id uint) (*model.AdminUser, error)
	GetByUsername(username string) (*model.AdminUser, error)
	GetByEmail(email string) (*model.AdminUser, error)
	GetAll(offset, limit int, filters map[string]interface{}) ([]model.AdminUser, int64, error)
	Update(id uint, admin *model.AdminUser) error
	UpdatePassword(id uint, hashedPassword string) error
	UpdateLastLogin(id uint, ip string) error
	Delete(id uint) error
	BatchDelete(ids []uint) error
	ToggleStatus(id uint) error
	ResetPassword(id uint, hashedPassword string) error
	GetUserRoles(userID uint) ([]model.AdminRole, error)
	AssignRoles(userID uint, roleIDs []uint) error
	CountUsers() (int64, error)
	CountActiveUsers() (int64, error)
}

type adminRepository struct {
	db *gorm.DB
}

func NewAdminRepository(db *gorm.DB) AdminRepository {
	return &adminRepository{db: db}
}

func (r *adminRepository) Create(admin *model.AdminUser) error {
	return r.db.Create(admin).Error
}

func (r *adminRepository) GetByID(id uint) (*model.AdminUser, error) {
	var admin model.AdminUser
	err := r.db.First(&admin, id).Error
	return &admin, err
}

func (r *adminRepository) GetByIDWithRoles(id uint) (*model.AdminUser, error) {
	var admin model.AdminUser
	err := r.db.Preload("Roles").First(&admin, id).Error
	return &admin, err
}

func (r *adminRepository) GetByUsername(username string) (*model.AdminUser, error) {
	var admin model.AdminUser
	err := r.db.Where("username = ?", username).First(&admin).Error
	return &admin, err
}

func (r *adminRepository) GetByEmail(email string) (*model.AdminUser, error) {
	var admin model.AdminUser
	err := r.db.Where("email = ?", email).First(&admin).Error
	return &admin, err
}

func (r *adminRepository) GetAll(offset, limit int, filters map[string]interface{}) ([]model.AdminUser, int64, error) {
	var admins []model.AdminUser
	var total int64

	// 创建基础查询（用于计数）
	baseQuery := r.db.Model(&model.AdminUser{})

	// 应用过滤条件
	if keyword, exists := filters["keyword"]; exists && keyword != "" {
		baseQuery = baseQuery.Where("username LIKE ? OR real_name LIKE ? OR email LIKE ?",
			"%"+keyword.(string)+"%",
			"%"+keyword.(string)+"%",
			"%"+keyword.(string)+"%")
	}

	if status, exists := filters["status"]; exists && status != "" {
		baseQuery = baseQuery.Where("status = ?", status)
	}

	if roleID, exists := filters["role_id"]; exists && roleID != nil {
		baseQuery = baseQuery.Joins("JOIN admin_user_roles ON admin_users.id = admin_user_roles.admin_user_id").
			Where("admin_user_roles.role_id = ?", roleID)
	}

	if startDate, exists := filters["start_date"]; exists && startDate != "" {
		baseQuery = baseQuery.Where("created_at >= ?", startDate)
	}

	if endDate, exists := filters["end_date"]; exists && endDate != "" {
		baseQuery = baseQuery.Where("created_at <= ?", endDate)
	}

	// 获取总数（使用baseQuery的副本）
	countQuery := baseQuery
	countQuery.Count(&total)

	// 创建新的查询用于获取数据（包含preload）
	dataQuery := r.db.Model(&model.AdminUser{}).Preload("Roles")

	// 重新应用相同的过滤条件到数据查询
	if keyword, exists := filters["keyword"]; exists && keyword != "" {
		dataQuery = dataQuery.Where("username LIKE ? OR real_name LIKE ? OR email LIKE ?",
			"%"+keyword.(string)+"%",
			"%"+keyword.(string)+"%",
			"%"+keyword.(string)+"%")
	}

	if status, exists := filters["status"]; exists && status != "" {
		dataQuery = dataQuery.Where("status = ?", status)
	}

	if roleID, exists := filters["role_id"]; exists && roleID != nil {
		dataQuery = dataQuery.Joins("JOIN admin_user_roles ON admin_users.id = admin_user_roles.admin_user_id").
			Where("admin_user_roles.role_id = ?", roleID)
	}

	if startDate, exists := filters["start_date"]; exists && startDate != "" {
		dataQuery = dataQuery.Where("created_at >= ?", startDate)
	}

	if endDate, exists := filters["end_date"]; exists && endDate != "" {
		dataQuery = dataQuery.Where("created_at <= ?", endDate)
	}

	// 获取数据
	err := dataQuery.Order("created_at DESC").
		Offset(offset).Limit(limit).
		Find(&admins).Error

	return admins, total, err
}

func (r *adminRepository) Update(id uint, admin *model.AdminUser) error {
	// 使用Select明确指定要更新的字段，避免GORM忽略零值
	return r.db.Model(&model.AdminUser{}).Where("id = ?", id).
		Select("username", "email", "real_name", "phone", "status", "avatar", "remark", "updated_at").
		Updates(admin).Error
}

func (r *adminRepository) UpdatePassword(id uint, hashedPassword string) error {
	return r.db.Model(&model.AdminUser{}).
		Where("id = ?", id).
		Update("password", hashedPassword).Error
}

func (r *adminRepository) UpdateLastLogin(id uint, ip string) error {
	return r.db.Model(&model.AdminUser{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"last_login_at": "NOW()",
			"last_login_ip": ip,
			"login_count":   gorm.Expr("login_count + 1"),
		}).Error
}

func (r *adminRepository) Delete(id uint) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 删除用户角色关联
		if err := tx.Where("admin_user_id = ?", id).Delete(&model.AdminUserRole{}).Error; err != nil {
			return err
		}
		// 删除管理员用户
		return tx.Delete(&model.AdminUser{}, id).Error
	})
}

func (r *adminRepository) BatchDelete(ids []uint) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 删除用户角色关联
		if err := tx.Where("admin_user_id IN ?", ids).Delete(&model.AdminUserRole{}).Error; err != nil {
			return err
		}
		// 删除管理员用户
		return tx.Where("id IN ?", ids).Delete(&model.AdminUser{}).Error
	})
}

func (r *adminRepository) ToggleStatus(id uint) error {
	return r.db.Model(&model.AdminUser{}).
		Where("id = ?", id).
		Update("status", gorm.Expr("CASE WHEN status = 'active' THEN 'inactive' ELSE 'active' END")).Error
}

func (r *adminRepository) ResetPassword(id uint, hashedPassword string) error {
	return r.db.Model(&model.AdminUser{}).
		Where("id = ?", id).
		Update("password", hashedPassword).Error
}

func (r *adminRepository) GetUserRoles(userID uint) ([]model.AdminRole, error) {
	var roles []model.AdminRole
	err := r.db.Table("admin_roles").
		Joins("JOIN admin_user_roles ON admin_roles.id = admin_user_roles.role_id").
		Where("admin_user_roles.admin_user_id = ?", userID).
		Find(&roles).Error
	return roles, err
}

func (r *adminRepository) AssignRoles(userID uint, roleIDs []uint) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 删除现有角色关联
		if err := tx.Where("admin_user_id = ?", userID).Delete(&model.AdminUserRole{}).Error; err != nil {
			return err
		}

		// 添加新的角色关联
		for _, roleID := range roleIDs {
			userRole := model.AdminUserRole{
				AdminUserID: userID,
				RoleID:      roleID,
			}
			if err := tx.Create(&userRole).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// CountUsers 统计用户总数
func (r *adminRepository) CountUsers() (int64, error) {
	var count int64
	err := r.db.Model(&model.AdminUser{}).Count(&count).Error
	return count, err
}

// CountActiveUsers 统计活跃用户数
func (r *adminRepository) CountActiveUsers() (int64, error) {
	var count int64
	err := r.db.Model(&model.AdminUser{}).Where("status = ?", "active").Count(&count).Error
	return count, err
}
