package handler

import (
	"strconv"
	"weishi-server/internal/service"
	"weishi-server/pkg/response"

	"github.com/gin-gonic/gin"
)

type AdminLogHandler struct {
	adminLogService service.AdminLogService
}

func NewAdminLogHandler(adminLogService service.AdminLogService) *AdminLogHandler {
	return &AdminLogHandler{
		adminLogService: adminLogService,
	}
}

// GetAdminLogs 获取操作日志列表
func (h *AdminLogHandler) GetAdminLogs(c *gin.Context) {
	var req service.QueryAdminLogRequest

	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 10
	}

	logs, total, err := h.adminLogService.GetAll(req)
	if err != nil {
		response.InternalServerError(c, "获取操作日志失败")
		return
	}

	response.SuccessPage(c, logs, total, req.Page, req.PageSize)
}

// GetAdminLog 获取操作日志详情
func (h *AdminLogHandler) GetAdminLog(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的日志ID")
		return
	}

	log, err := h.adminLogService.GetByID(uint(id))
	if err != nil {
		response.NotFound(c, "日志不存在")
		return
	}

	response.Success(c, log)
}

// GetAdminLogStatistics 获取操作日志统计
func (h *AdminLogHandler) GetAdminLogStatistics(c *gin.Context) {
	stats, err := h.adminLogService.GetStatistics()
	if err != nil {
		response.InternalServerError(c, "获取日志统计失败")
		return
	}

	response.Success(c, stats)
}

// GetTodayLogs 获取今日日志
func (h *AdminLogHandler) GetTodayLogs(c *gin.Context) {
	logs, err := h.adminLogService.GetTodayLogs()
	if err != nil {
		response.InternalServerError(c, "获取今日日志失败")
		return
	}

	response.Success(c, logs)
}

// GetUserLogs 获取用户操作日志
func (h *AdminLogHandler) GetUserLogs(c *gin.Context) {
	idStr := c.Param("userId")
	userId, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	logs, total, err := h.adminLogService.GetUserLogs(uint(userId), page, pageSize)
	if err != nil {
		response.InternalServerError(c, "获取用户日志失败")
		return
	}

	response.SuccessPage(c, logs, total, page, pageSize)
}

// GetModuleLogs 获取模块操作日志
func (h *AdminLogHandler) GetModuleLogs(c *gin.Context) {
	module := c.Param("module")
	if module == "" {
		response.BadRequest(c, "模块名称不能为空")
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	logs, total, err := h.adminLogService.GetModuleLogs(module, page, pageSize)
	if err != nil {
		response.InternalServerError(c, "获取模块日志失败")
		return
	}

	response.SuccessPage(c, logs, total, page, pageSize)
}

// DeleteOldLogs 清理过期日志
func (h *AdminLogHandler) DeleteOldLogs(c *gin.Context) {
	var req struct {
		Days int `json:"days" binding:"required,min=1"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	if err := h.adminLogService.DeleteOldLogs(req.Days); err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMsg(c, nil, "过期日志清理成功")
}

// CreateAdminLog 创建操作日志（内部使用）
func (h *AdminLogHandler) CreateAdminLog(c *gin.Context) {
	var req service.CreateAdminLogRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	if err := h.adminLogService.Create(req); err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMsg(c, nil, "日志记录成功")
}
