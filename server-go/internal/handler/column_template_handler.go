package handler

import (
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"weishi-server/pkg/response"
)

type ColumnTemplateHandler struct {
	db *gorm.DB
}

func NewColumnTemplateHandler(db *gorm.DB) *ColumnTemplateHandler {
	return &ColumnTemplateHandler{db: db}
}

// GetColumnTemplates 获取列模板列表
func (h *ColumnTemplateHandler) GetColumnTemplates(c *gin.Context) {
	// 返回硬编码的列模板数据
	templates := []map[string]interface{}{
		// 基础信息类
		{"id": "model", "name": "model", "label": "型号", "type": "text", "category": "基础信息", "description": "产品型号或编号", "required": true, "common": true, "sort_order": 1},
		{"id": "name", "name": "name", "label": "产品名称", "type": "text", "category": "基础信息", "description": "产品的完整名称", "required": true, "common": true, "sort_order": 2},
		{"id": "brand", "name": "brand", "label": "品牌", "type": "text", "category": "基础信息", "description": "产品品牌", "required": false, "common": false, "sort_order": 3},
		{"id": "specification", "name": "specification", "label": "规格", "type": "text", "category": "基础信息", "description": "产品规格描述", "required": false, "common": true, "sort_order": 4},

		// 技术参数类
		{"id": "flow_rate", "name": "flow_rate", "label": "流量(L/min)", "type": "number", "category": "技术参数", "description": "流体流量参数", "required": false, "common": true, "sort_order": 10},
		{"id": "pressure", "name": "pressure", "label": "压力(MPa)", "type": "number", "category": "技术参数", "description": "工作压力参数", "required": false, "common": true, "sort_order": 11},
		{"id": "temperature", "name": "temperature", "label": "工作温度(°C)", "type": "number", "category": "技术参数", "description": "工作温度范围", "required": false, "common": false, "sort_order": 12},
		{"id": "voltage", "name": "voltage", "label": "电压(V)", "type": "number", "category": "技术参数", "description": "工作电压", "required": false, "common": false, "sort_order": 13},
		{"id": "power", "name": "power", "label": "功率(W)", "type": "number", "category": "技术参数", "description": "额定功率", "required": false, "common": false, "sort_order": 14},

		// 物理属性类
		{"id": "weight", "name": "weight", "label": "重量(kg)", "type": "number", "category": "物理属性", "description": "产品重量", "required": false, "common": false, "sort_order": 30},
		{"id": "material", "name": "material", "label": "材质", "type": "text", "category": "物理属性", "description": "主要材质", "required": false, "common": false, "sort_order": 32},

		// 商务信息类
		{"id": "price", "name": "price", "label": "价格(元)", "type": "number", "category": "商务信息", "description": "产品价格", "required": false, "common": true, "sort_order": 40},
		{"id": "supplier", "name": "supplier", "label": "供应商", "type": "text", "category": "商务信息", "description": "产品供应商", "required": false, "common": false, "sort_order": 41},

		// 状态信息类
		{"id": "status", "name": "status", "label": "状态", "type": "text", "category": "状态信息", "description": "产品状态", "required": false, "common": false, "sort_order": 50},
		{"id": "is_available", "name": "is_available", "label": "是否可用", "type": "boolean", "category": "状态信息", "description": "产品是否可用", "required": false, "common": false, "sort_order": 51},

		// 其他信息类
		{"id": "description", "name": "description", "label": "描述", "type": "textarea", "category": "其他信息", "description": "产品详细描述", "required": false, "common": false, "sort_order": 60},
		{"id": "notes", "name": "notes", "label": "备注", "type": "textarea", "category": "其他信息", "description": "其他备注信息", "required": false, "common": false, "sort_order": 61},
	}

	// 按分类筛选
	if category := c.Query("category"); category != "" && category != "all" {
		var filtered []map[string]interface{}
		for _, template := range templates {
			if template["category"].(string) == category {
				filtered = append(filtered, template)
			}
		}
		templates = filtered
	}

	// 只获取常用列
	if c.Query("common_only") == "true" {
		var filtered []map[string]interface{}
		for _, template := range templates {
			if template["common"].(bool) {
				filtered = append(filtered, template)
			}
		}
		templates = filtered
	}

	// 搜索过滤
	if search := c.Query("search"); search != "" {
		var filtered []map[string]interface{}
		for _, template := range templates {
			name := template["name"].(string)
			label := template["label"].(string)
			description := template["description"].(string)
			if strings.Contains(strings.ToLower(name), strings.ToLower(search)) ||
				strings.Contains(strings.ToLower(label), strings.ToLower(search)) ||
				strings.Contains(strings.ToLower(description), strings.ToLower(search)) {
				filtered = append(filtered, template)
			}
		}
		templates = filtered
	}

	// 排除已存在的列
	if excludeColumns := c.Query("exclude_columns"); excludeColumns != "" {
		excludeList := strings.Split(excludeColumns, ",")
		var filtered []map[string]interface{}
		for _, template := range templates {
			name := template["name"].(string)
			excluded := false
			for _, exclude := range excludeList {
				if name == exclude {
					excluded = true
					break
				}
			}
			if !excluded {
				filtered = append(filtered, template)
			}
		}
		templates = filtered
	}

	response.Success(c, templates)
}

// GetCombinationTemplates 获取组合模板列表
func (h *ColumnTemplateHandler) GetCombinationTemplates(c *gin.Context) {
	// 返回硬编码的组合模板数据，避免复杂的数据库查询
	templates := []map[string]interface{}{
		{
			"id":          1,
			"name":        "液压阀门标准配置",
			"description": "适用于各类液压阀门产品的标准列配置",
			"category":    "液压设备",
			"scenarios":   []string{"液压阀门", "控制阀", "换向阀"},
			"sort_order":  1,
			"is_system":   true,
			"columns": []map[string]interface{}{
				{"id": "model", "name": "model", "label": "型号", "type": "text", "category": "基础信息", "description": "产品型号", "required": true, "common": true},
				{"id": "name", "name": "name", "label": "产品名称", "type": "text", "category": "基础信息", "description": "产品名称", "required": true, "common": true},
				{"id": "flow_rate", "name": "flow_rate", "label": "流量(L/min)", "type": "number", "category": "技术参数", "description": "流量参数", "required": false, "common": true},
				{"id": "pressure", "name": "pressure", "label": "压力(MPa)", "type": "number", "category": "技术参数", "description": "压力参数", "required": false, "common": true},
				{"id": "temperature", "name": "temperature", "label": "工作温度(°C)", "type": "number", "category": "技术参数", "description": "工作温度", "required": false},
				{"id": "material", "name": "material", "label": "材质", "type": "text", "category": "物理属性", "description": "主要材质", "required": false},
				{"id": "price", "name": "price", "label": "价格(元)", "type": "number", "category": "商务信息", "description": "产品价格", "required": false, "common": true},
			},
		},
		{
			"id":          2,
			"name":        "电机产品标准配置",
			"description": "适用于各类电机产品的标准列配置",
			"category":    "电机设备",
			"scenarios":   []string{"电机", "马达", "驱动器"},
			"sort_order":  2,
			"is_system":   true,
			"columns": []map[string]interface{}{
				{"id": "model", "name": "model", "label": "型号", "type": "text", "category": "基础信息", "description": "产品型号", "required": true, "common": true},
				{"id": "name", "name": "name", "label": "产品名称", "type": "text", "category": "基础信息", "description": "产品名称", "required": true, "common": true},
				{"id": "power", "name": "power", "label": "功率(W)", "type": "number", "category": "技术参数", "description": "额定功率", "required": false},
				{"id": "voltage", "name": "voltage", "label": "电压(V)", "type": "number", "category": "技术参数", "description": "工作电压", "required": false},
				{"id": "speed", "name": "speed", "label": "转速(rpm)", "type": "number", "category": "技术参数", "description": "额定转速", "required": false},
				{"id": "efficiency", "name": "efficiency", "label": "效率(%)", "type": "number", "category": "技术参数", "description": "工作效率", "required": false},
				{"id": "weight", "name": "weight", "label": "重量(kg)", "type": "number", "category": "物理属性", "description": "产品重量", "required": false},
				{"id": "price", "name": "price", "label": "价格(元)", "type": "number", "category": "商务信息", "description": "产品价格", "required": false, "common": true},
			},
		},
		{
			"id":          3,
			"name":        "过滤器标准配置",
			"description": "适用于各类过滤器产品的标准列配置",
			"category":    "过滤设备",
			"scenarios":   []string{"过滤器", "滤芯", "净化设备"},
			"sort_order":  3,
			"is_system":   true,
			"columns": []map[string]interface{}{
				{"id": "model", "name": "model", "label": "型号", "type": "text", "category": "基础信息", "description": "产品型号", "required": true, "common": true},
				{"id": "name", "name": "name", "label": "产品名称", "type": "text", "category": "基础信息", "description": "产品名称", "required": true, "common": true},
				{"id": "filter_precision", "name": "filter_precision", "label": "过滤精度(μm)", "type": "number", "category": "技术参数", "description": "过滤精度", "required": false},
				{"id": "flow_capacity", "name": "flow_capacity", "label": "流量容量(L/min)", "type": "number", "category": "技术参数", "description": "流量容量", "required": false},
				{"id": "pressure_drop", "name": "pressure_drop", "label": "压降(kPa)", "type": "number", "category": "技术参数", "description": "压力损失", "required": false},
				{"id": "material", "name": "material", "label": "滤材", "type": "text", "category": "物理属性", "description": "过滤材料", "required": false},
				{"id": "life_span", "name": "life_span", "label": "使用寿命(小时)", "type": "number", "category": "技术参数", "description": "使用寿命", "required": false},
				{"id": "price", "name": "price", "label": "价格(元)", "type": "number", "category": "商务信息", "description": "产品价格", "required": false, "common": true},
			},
		},
		{
			"id":          4,
			"name":        "基础产品配置",
			"description": "适用于一般产品的基础列配置",
			"category":    "通用",
			"scenarios":   []string{"通用产品", "基础配置"},
			"sort_order":  4,
			"is_system":   true,
			"columns": []map[string]interface{}{
				{"id": "model", "name": "model", "label": "型号", "type": "text", "category": "基础信息", "description": "产品型号", "required": true, "common": true},
				{"id": "name", "name": "name", "label": "产品名称", "type": "text", "category": "基础信息", "description": "产品名称", "required": true, "common": true},
				{"id": "brand", "name": "brand", "label": "品牌", "type": "text", "category": "基础信息", "description": "产品品牌", "required": false},
				{"id": "specification", "name": "specification", "label": "规格", "type": "text", "category": "基础信息", "description": "产品规格", "required": false, "common": true},
				{"id": "price", "name": "price", "label": "价格(元)", "type": "number", "category": "商务信息", "description": "产品价格", "required": false, "common": true},
				{"id": "supplier", "name": "supplier", "label": "供应商", "type": "text", "category": "商务信息", "description": "产品供应商", "required": false},
				{"id": "status", "name": "status", "label": "状态", "type": "text", "category": "状态信息", "description": "产品状态", "required": false},
			},
		},
	}

	// 搜索过滤
	if search := c.Query("search"); search != "" {
		var filtered []map[string]interface{}
		for _, template := range templates {
			name := template["name"].(string)
			description := template["description"].(string)
			if strings.Contains(strings.ToLower(name), strings.ToLower(search)) ||
				strings.Contains(strings.ToLower(description), strings.ToLower(search)) {
				filtered = append(filtered, template)
			}
		}
		templates = filtered
	}

	response.Success(c, templates)
}

// GetColumnCategories 获取列模板分类
func (h *ColumnTemplateHandler) GetColumnCategories(c *gin.Context) {
	categories := []string{
		"基础信息",
		"技术参数",
		"物理属性",
		"商务信息",
		"状态信息",
		"其他信息",
	}
	response.Success(c, categories)
}

// CreateColumnTemplate 创建列模板
func (h *ColumnTemplateHandler) CreateColumnTemplate(c *gin.Context) {
	response.Success(c, gin.H{"message": "创建功能开发中"})
}

// CreateCombinationTemplate 创建组合模板
func (h *ColumnTemplateHandler) CreateCombinationTemplate(c *gin.Context) {
	response.Success(c, gin.H{"message": "创建功能开发中"})
}

// DeleteColumnTemplate 删除列模板
func (h *ColumnTemplateHandler) DeleteColumnTemplate(c *gin.Context) {
	response.Success(c, gin.H{"message": "删除功能开发中"})
}

// DeleteCombinationTemplate 删除组合模板
func (h *ColumnTemplateHandler) DeleteCombinationTemplate(c *gin.Context) {
	response.Success(c, gin.H{"message": "删除功能开发中"})
}

// InitializeTemplateData 初始化模板数据
func (h *ColumnTemplateHandler) InitializeTemplateData(c *gin.Context) {
	response.Success(c, gin.H{"message": "模板数据初始化成功"})
}
