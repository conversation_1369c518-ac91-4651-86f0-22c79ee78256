package handler

import (
	"strconv"
	"weishi-server/internal/service"
	"weishi-server/pkg/response"

	"github.com/gin-gonic/gin"
)

type DashboardHandler struct {
	dashboardService service.DashboardService
}

func NewDashboardHandler(dashboardService service.DashboardService) *DashboardHandler {
	return &DashboardHandler{
		dashboardService: dashboardService,
	}
}

// GetOverviewStats 获取概览统计数据
func (h *DashboardHandler) GetOverviewStats(c *gin.Context) {
	stats, err := h.dashboardService.GetOverviewStats()
	if err != nil {
		response.InternalServerError(c, "获取概览统计失败")
		return
	}

	response.Success(c, stats)
}

// GetSystemInfo 获取系统信息
func (h *DashboardHandler) GetSystemInfo(c *gin.Context) {
	systemInfo, err := h.dashboardService.GetSystemInfo()
	if err != nil {
		response.InternalServerError(c, "获取系统信息失败")
		return
	}

	response.Success(c, systemInfo)
}

// GetRecentActivity 获取最近活动记录
func (h *DashboardHandler) GetRecentActivity(c *gin.Context) {
	activities, err := h.dashboardService.GetRecentActivity()
	if err != nil {
		response.InternalServerError(c, "获取活动记录失败")
		return
	}

	response.Success(c, activities)
}

// GetUserGrowthStats 获取用户增长统计
func (h *DashboardHandler) GetUserGrowthStats(c *gin.Context) {
	days, _ := strconv.Atoi(c.DefaultQuery("days", "7"))
	if days <= 0 {
		days = 7
	}

	growthStats, err := h.dashboardService.GetUserGrowthStats(days)
	if err != nil {
		response.InternalServerError(c, "获取用户增长统计失败")
		return
	}

	response.Success(c, growthStats)
}

// GetModuleAccessStats 获取模块访问统计
func (h *DashboardHandler) GetModuleAccessStats(c *gin.Context) {
	moduleStats, err := h.dashboardService.GetModuleAccessStats()
	if err != nil {
		response.InternalServerError(c, "获取模块访问统计失败")
		return
	}

	response.Success(c, moduleStats)
}

// GetOperationStats 获取操作统计
func (h *DashboardHandler) GetOperationStats(c *gin.Context) {
	days, _ := strconv.Atoi(c.DefaultQuery("days", "7"))
	if days <= 0 {
		days = 7
	}

	operationStats, err := h.dashboardService.GetOperationStats(days)
	if err != nil {
		response.InternalServerError(c, "获取操作统计失败")
		return
	}

	response.Success(c, operationStats)
}
