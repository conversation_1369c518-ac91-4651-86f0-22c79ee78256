package handler

import (
	"strconv"
	"weishi-server/internal/config"
	"weishi-server/internal/service"
	"weishi-server/pkg/jwt"
	"weishi-server/pkg/response"

	"github.com/gin-gonic/gin"
)

type AdminHandler struct {
	adminService service.AdminService
	cfg          *config.Config
}

func NewAdminHandler(adminService service.AdminService, cfg *config.Config) *AdminHandler {
	return &AdminHandler{
		adminService: adminService,
		cfg:          cfg,
	}
}

// Login 管理员登录
func (h *AdminHandler) Login(c *gin.Context) {
	var req service.LoginRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	admin, err := h.adminService.Login(req)
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	// 生成 JWT token
	token, err := jwt.GenerateToken(admin.ID, admin.Username, h.cfg.JWT)
	if err != nil {
		response.InternalServerError(c, "生成token失败")
		return
	}

	response.SuccessWithMsg(c, gin.H{
		"token": token,
		"admin": admin,
	}, "登录成功")
}

// Logout 管理员登出
func (h *AdminHandler) Logout(c *gin.Context) {
	// 从请求头中获取token
	token := c.GetHeader("Authorization")
	if token == "" {
		response.SuccessWithMsg(c, nil, "登出成功")
		return
	}

	// 在实际应用中，这里可以将token加入黑名单
	// 或者在Redis中标记为无效，但由于是无状态JWT，
	// 我们只需要客户端删除token即可

	// 记录登出操作（通过中间件自动记录）

	response.SuccessWithMsg(c, nil, "登出成功")
}

// GetProfile 获取个人资料
func (h *AdminHandler) GetProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "未找到用户信息")
		return
	}

	admin, err := h.adminService.GetByIDWithRoles(userID.(uint))
	if err != nil {
		response.NotFound(c, "用户不存在")
		return
	}

	// 获取用户权限
	permissions, err := h.adminService.GetUserPermissions(userID.(uint))
	if err != nil {
		// 如果获取权限失败，返回空权限列表
		permissions = []string{}
	}

	response.Success(c, gin.H{
		"userInfo":    admin,
		"permissions": permissions,
	})
}

// UpdateProfile 更新个人资料
func (h *AdminHandler) UpdateProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "未找到用户信息")
		return
	}

	var req service.UpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	admin, err := h.adminService.UpdateProfile(userID.(uint), req)
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMsg(c, admin, "个人资料更新成功")
}

// ChangePassword 修改密码
func (h *AdminHandler) ChangePassword(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "未找到用户信息")
		return
	}

	var req service.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	if err := h.adminService.ChangePassword(userID.(uint), req); err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMsg(c, nil, "密码修改成功")
}

// CreateAdmin 创建管理员
func (h *AdminHandler) CreateAdmin(c *gin.Context) {
	var req service.CreateAdminRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}
	admin, err := h.adminService.Create(req)
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}
	response.SuccessWithMsg(c, admin, "管理员创建成功")
}

// GetAdmin 获取单个管理员
func (h *AdminHandler) GetAdmin(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的管理员ID")
		return
	}
	admin, err := h.adminService.GetByIDWithRoles(uint(id))
	if err != nil {
		response.NotFound(c, "管理员不存在")
		return
	}
	response.Success(c, admin)
}

// GetAdmins 获取管理员列表
func (h *AdminHandler) GetAdmins(c *gin.Context) {
	var req service.QueryAdminRequest

	// 绑定查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 10
	}

	admins, total, err := h.adminService.GetAll(req)
	if err != nil {
		response.InternalServerError(c, "获取管理员列表失败")
		return
	}

	response.SuccessPage(c, admins, total, req.Page, req.PageSize)
}

// UpdateAdmin 更新管理员
func (h *AdminHandler) UpdateAdmin(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的管理员ID")
		return
	}
	var req service.UpdateAdminRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}
	admin, err := h.adminService.Update(uint(id), req)
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}
	response.SuccessWithMsg(c, admin, "管理员更新成功")
}

// DeleteAdmin 删除管理员
func (h *AdminHandler) DeleteAdmin(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的管理员ID")
		return
	}
	if err := h.adminService.Delete(uint(id)); err != nil {
		response.BadRequest(c, err.Error())
		return
	}
	response.SuccessWithMsg(c, nil, "管理员删除成功")
}

// BatchDeleteAdmins 批量删除管理员
func (h *AdminHandler) BatchDeleteAdmins(c *gin.Context) {
	var req struct {
		IDs []uint `json:"ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	if err := h.adminService.BatchDelete(req.IDs); err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMsg(c, nil, "批量删除成功")
}

// ToggleStatus 切换管理员状态
func (h *AdminHandler) ToggleStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的管理员ID")
		return
	}

	admin, err := h.adminService.ToggleStatus(uint(id))
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMsg(c, admin, "状态切换成功")
}

// ResetPassword 重置密码
func (h *AdminHandler) ResetPassword(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的管理员ID")
		return
	}

	var req struct {
		NewPassword string `json:"new_password"`
	}
	c.ShouldBindJSON(&req)

	newPassword, err := h.adminService.ResetPassword(uint(id), req.NewPassword)
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMsg(c, gin.H{
		"password": newPassword,
	}, "密码重置成功")
}

// GetUserRoles 获取用户角色
func (h *AdminHandler) GetUserRoles(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	roles, err := h.adminService.GetUserRoles(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取用户角色失败")
		return
	}

	response.Success(c, roles)
}

// AssignRoles 分配角色
func (h *AdminHandler) AssignRoles(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	var req struct {
		RoleIDs []uint `json:"role_ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	if err := h.adminService.AssignRoles(uint(id), req.RoleIDs); err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMsg(c, nil, "角色分配成功")
}
