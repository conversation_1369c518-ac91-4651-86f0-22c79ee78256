package handler

import (
	"strconv"
	"weishi-server/internal/service"
	"weishi-server/pkg/response"

	"github.com/gin-gonic/gin"
)

type RoleHandler struct {
	roleService service.RoleService
}

func NewRoleHandler(roleService service.RoleService) *RoleHandler {
	return &RoleHandler{
		roleService: roleService,
	}
}

// GetRoles 获取角色列表
func (h *RoleHandler) GetRoles(c *gin.Context) {
	var req service.QueryRoleRequest

	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 10
	}

	offset := (req.Page - 1) * req.PageSize
	roles, total, err := h.roleService.GetAll(offset, req.PageSize, req.Keyword, req.Status)
	if err != nil {
		response.InternalServerError(c, "获取角色列表失败")
		return
	}

	response.SuccessPage(c, roles, total, req.Page, req.PageSize)
}

// GetAllRoles 获取所有角色（不分页）
func (h *RoleHandler) GetAllRoles(c *gin.Context) {
	roles, err := h.roleService.GetAllActive()
	if err != nil {
		response.InternalServerError(c, "获取角色列表失败")
		return
	}

	response.Success(c, roles)
}

// GetRole 获取角色详情
func (h *RoleHandler) GetRole(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的角色ID")
		return
	}

	role, err := h.roleService.GetByID(uint(id))
	if err != nil {
		response.NotFound(c, "角色不存在")
		return
	}

	response.Success(c, role)
}

// CreateRole 创建角色
func (h *RoleHandler) CreateRole(c *gin.Context) {
	var req service.CreateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	role, err := h.roleService.Create(req)
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMsg(c, role, "角色创建成功")
}

// UpdateRole 更新角色
func (h *RoleHandler) UpdateRole(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的角色ID")
		return
	}

	var req service.UpdateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	role, err := h.roleService.Update(uint(id), req)
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMsg(c, role, "角色更新成功")
}

// DeleteRole 删除角色
func (h *RoleHandler) DeleteRole(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的角色ID")
		return
	}

	if err := h.roleService.Delete(uint(id)); err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMsg(c, nil, "角色删除成功")
}

// BatchDeleteRoles 批量删除角色
func (h *RoleHandler) BatchDeleteRoles(c *gin.Context) {
	var req struct {
		IDs []uint `json:"ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	if err := h.roleService.BatchDelete(req.IDs); err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMsg(c, nil, "批量删除成功")
}

// ToggleStatus 切换角色状态
func (h *RoleHandler) ToggleStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的角色ID")
		return
	}

	err = h.roleService.ToggleStatus(uint(id))
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	// 获取更新后的角色信息
	role, err := h.roleService.GetByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取角色信息失败")
		return
	}

	response.SuccessWithMsg(c, role, "状态切换成功")
}

// GetRolePermissions 获取角色权限
func (h *RoleHandler) GetRolePermissions(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的角色ID")
		return
	}

	permissions, err := h.roleService.GetRolePermissions(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取角色权限失败")
		return
	}

	response.Success(c, permissions)
}

// AssignPermissions 分配权限给角色
func (h *RoleHandler) AssignPermissions(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的角色ID")
		return
	}

	var req struct {
		PermissionIDs []uint `json:"permission_ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	if err := h.roleService.AssignPermissions(uint(id), req.PermissionIDs); err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMsg(c, nil, "权限分配成功")
}

// CopyRole 复制角色 (暂时禁用，需要实现service层方法)
/*
func (h *RoleHandler) CopyRole(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的角色ID")
		return
	}

	var req struct {
		Name string `json:"name" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	role, err := h.roleService.CopyRole(uint(id), req.Name)
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMsg(c, role, "角色复制成功")
}
*/
