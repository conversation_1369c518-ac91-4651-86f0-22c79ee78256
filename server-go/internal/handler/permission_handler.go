package handler

import (
	"strconv"
	"weishi-server/internal/service"
	"weishi-server/pkg/response"

	"github.com/gin-gonic/gin"
)

type PermissionHandler struct {
	permissionService service.PermissionService
}

func NewPermissionHandler(permissionService service.PermissionService) *PermissionHandler {
	return &PermissionHandler{
		permissionService: permissionService,
	}
}

// GetPermissions 获取权限列表
func (h *PermissionHandler) GetPermissions(c *gin.Context) {
	var req service.QueryPermissionRequest

	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 10
	}

	permissions, total, err := h.permissionService.GetAll(req)
	if err != nil {
		response.InternalServerError(c, "获取权限列表失败")
		return
	}

	response.SuccessPage(c, permissions, total, req.Page, req.PageSize)
}

// GetPermissionTree 获取权限树
func (h *PermissionHandler) GetPermissionTree(c *gin.Context) {
	permissions, err := h.permissionService.GetTree()
	if err != nil {
		response.InternalServerError(c, "获取权限树失败")
		return
	}

	response.Success(c, permissions)
}

// GetAllPermissions 获取所有权限（不分页）
func (h *PermissionHandler) GetAllPermissions(c *gin.Context) {
	permissions, err := h.permissionService.GetAllActive()
	if err != nil {
		response.InternalServerError(c, "获取权限列表失败")
		return
	}

	response.Success(c, permissions)
}

// GetMenuPermissions 获取菜单权限
func (h *PermissionHandler) GetMenuPermissions(c *gin.Context) {
	permissions, err := h.permissionService.GetMenuPermissions()
	if err != nil {
		response.InternalServerError(c, "获取菜单权限失败")
		return
	}

	response.Success(c, permissions)
}

// GetPermission 获取权限详情
func (h *PermissionHandler) GetPermission(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的权限ID")
		return
	}

	permission, err := h.permissionService.GetByID(uint(id))
	if err != nil {
		response.NotFound(c, "权限不存在")
		return
	}

	response.Success(c, permission)
}

// GetChildPermissions 获取子权限
func (h *PermissionHandler) GetChildPermissions(c *gin.Context) {
	idStr := c.Param("parentId")
	parentId, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的父权限ID")
		return
	}

	permissions, err := h.permissionService.GetChildren(uint(parentId))
	if err != nil {
		response.InternalServerError(c, "获取子权限失败")
		return
	}

	response.Success(c, permissions)
}

// CreatePermission 创建权限
func (h *PermissionHandler) CreatePermission(c *gin.Context) {
	var req service.CreatePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	permission, err := h.permissionService.Create(req)
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMsg(c, permission, "权限创建成功")
}

// UpdatePermission 更新权限
func (h *PermissionHandler) UpdatePermission(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的权限ID")
		return
	}

	var req service.UpdatePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	permission, err := h.permissionService.Update(uint(id), req)
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMsg(c, permission, "权限更新成功")
}

// DeletePermission 删除权限
func (h *PermissionHandler) DeletePermission(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的权限ID")
		return
	}

	if err := h.permissionService.Delete(uint(id)); err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMsg(c, nil, "权限删除成功")
}

// BatchDeletePermissions 批量删除权限
func (h *PermissionHandler) BatchDeletePermissions(c *gin.Context) {
	var req struct {
		IDs []uint `json:"ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	if err := h.permissionService.BatchDelete(req.IDs); err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMsg(c, nil, "批量删除成功")
}

// ToggleStatus 切换权限状态
func (h *PermissionHandler) ToggleStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的权限ID")
		return
	}

	err = h.permissionService.ToggleStatus(uint(id))
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	// 获取更新后的权限信息
	permission, err := h.permissionService.GetByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取权限信息失败")
		return
	}

	response.SuccessWithMsg(c, permission, "状态切换成功")
}

// MovePermission 移动权限位置 (暂时禁用，需要实现service层方法)
/*
func (h *PermissionHandler) MovePermission(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的权限ID")
		return
	}

	var req struct {
		TargetParentID uint `json:"target_parent_id" binding:"required"`
		Position       int  `json:"position" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	if err := h.permissionService.MovePermission(uint(id), req.TargetParentID, req.Position); err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMsg(c, nil, "权限移动成功")
}
*/
