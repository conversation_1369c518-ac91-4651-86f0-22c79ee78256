package handler

import (
	"crypto/rand"
	"fmt"
	"strconv"
	"strings"
	"time"
	"weishi-server/pkg/response"

	"github.com/gin-gonic/gin"
)

type CaptchaHandler struct {
	captchaStore map[string]CaptchaData // 简单的内存存储，生产环境应使用Redis
}

type CaptchaData struct {
	Answer    string
	ExpiresAt time.Time
}

type CaptchaResponse struct {
	ID    string `json:"id"`
	Image string `json:"image"`
}

func NewCaptchaHandler() *CaptchaHandler {
	return &CaptchaHandler{
		captchaStore: make(map[string]CaptchaData),
	}
}

// GenerateCaptcha 生成验证码
func (h *CaptchaHandler) GenerateCaptcha(c *gin.Context) {
	// 生成随机ID
	id := generateRandomString(32)

	// 生成简单的数学验证码
	a := generateRandomNumber(1, 10)
	b := generateRandomNumber(1, 10)
	operators := []string{"+", "-", "*"}
	operator := operators[generateRandomNumber(0, len(operators)-1)]

	var answer int
	var question string

	switch operator {
	case "+":
		answer = a + b
		question = fmt.Sprintf("%d + %d = ?", a, b)
	case "-":
		if a < b {
			a, b = b, a // 确保结果为正数
		}
		answer = a - b
		question = fmt.Sprintf("%d - %d = ?", a, b)
	case "*":
		answer = a * b
		question = fmt.Sprintf("%d × %d = ?", a, b)
	}

	// 存储验证码（5分钟过期）
	h.captchaStore[id] = CaptchaData{
		Answer:    strconv.Itoa(answer),
		ExpiresAt: time.Now().Add(5 * time.Minute),
	}

	// 清理过期的验证码
	go h.cleanExpiredCaptcha()

	response.Success(c, CaptchaResponse{
		ID:    id,
		Image: question, // 简单文本形式，生产环境可以生成图片
	})
}

// VerifyCaptcha 验证验证码
func (h *CaptchaHandler) VerifyCaptcha(c *gin.Context) {
	var req struct {
		ID     string `json:"id" binding:"required"`
		Answer string `json:"answer" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 检查验证码是否存在
	captcha, exists := h.captchaStore[req.ID]
	if !exists {
		response.BadRequest(c, "验证码不存在或已过期")
		return
	}

	// 检查是否过期
	if time.Now().After(captcha.ExpiresAt) {
		delete(h.captchaStore, req.ID)
		response.BadRequest(c, "验证码已过期")
		return
	}

	// 验证答案
	if strings.TrimSpace(req.Answer) != captcha.Answer {
		response.BadRequest(c, "验证码错误")
		return
	}

	// 验证成功后删除验证码
	delete(h.captchaStore, req.ID)

	response.SuccessWithMsg(c, nil, "验证码验证成功")
}

// 验证验证码的内部方法
func (h *CaptchaHandler) VerifyCaptchaInternal(id, answer string) bool {
	captcha, exists := h.captchaStore[id]
	if !exists {
		return false
	}

	if time.Now().After(captcha.ExpiresAt) {
		delete(h.captchaStore, id)
		return false
	}

	if strings.TrimSpace(answer) != captcha.Answer {
		return false
	}

	// 验证成功后删除验证码
	delete(h.captchaStore, id)
	return true
}

// 清理过期验证码
func (h *CaptchaHandler) cleanExpiredCaptcha() {
	now := time.Now()
	for id, captcha := range h.captchaStore {
		if now.After(captcha.ExpiresAt) {
			delete(h.captchaStore, id)
		}
	}
}

// 生成随机字符串
func generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	rand.Read(b)
	for i := range b {
		b[i] = charset[b[i]%byte(len(charset))]
	}
	return string(b)
}

// 生成随机数
func generateRandomNumber(min, max int) int {
	b := make([]byte, 1)
	rand.Read(b)
	return min + int(b[0])%(max-min+1)
}
