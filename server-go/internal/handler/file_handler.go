package handler

import (
	"strconv"
	"weishi-server/internal/service"
	"weishi-server/pkg/response"

	"github.com/gin-gonic/gin"
)

type FileHandler struct {
	fileService service.FileService
}

func NewFileHandler(fileService service.FileService) *FileHandler {
	return &FileHandler{
		fileService: fileService,
	}
}

// UploadFile 上传文件
func (h *FileHandler) UploadFile(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未登录")
		return
	}

	// 获取模块参数
	module := c.PostForm("module")
	if module == "" {
		module = "general" // 默认模块
	}

	// 获取上传的文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		response.BadRequest(c, "请选择要上传的文件")
		return
	}
	defer file.Close()

	// 调用服务层上传文件
	result, err := h.fileService.UploadFile(file, header, module, userID.(uint))
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.Success(c, gin.H{
		"id":           result.ID,
		"originalName": result.OriginalName,
		"fileName":     result.FileName,
		"fileSize":     result.FileSize,
		"mimeType":     result.MimeType,
		"cosUrl":       result.CosURL,
		"module":       result.Module,
		"uploadTime":   result.CreatedAt,
	})
}

// DeleteFile 删除文件
func (h *FileHandler) DeleteFile(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未登录")
		return
	}

	// 获取文件ID
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的文件ID")
		return
	}

	// 调用服务层删除文件
	err = h.fileService.DeleteFile(uint(id), userID.(uint))
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.Success(c, "文件删除成功")
}

// GetFile 获取文件信息
func (h *FileHandler) GetFile(c *gin.Context) {
	// 获取文件ID
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的文件ID")
		return
	}

	// 调用服务层获取文件信息
	file, err := h.fileService.GetFileByID(uint(id))
	if err != nil {
		response.NotFound(c, "文件不存在")
		return
	}

	response.Success(c, gin.H{
		"id":           file.ID,
		"originalName": file.OriginalName,
		"fileName":     file.FileName,
		"fileSize":     file.FileSize,
		"mimeType":     file.MimeType,
		"cosUrl":       file.CosURL,
		"module":       file.Module,
		"uploadTime":   file.CreatedAt,
		"status":       file.Status,
	})
}

// GetFilesByModule 根据模块获取文件列表
func (h *FileHandler) GetFilesByModule(c *gin.Context) {
	// 获取模块参数
	module := c.Query("module")
	if module == "" {
		response.BadRequest(c, "请指定模块名称")
		return
	}

	// 调用服务层获取文件列表
	files, err := h.fileService.GetFilesByModule(module)
	if err != nil {
		response.InternalServerError(c, "获取文件列表失败")
		return
	}

	// 转换数据格式
	var result []gin.H
	for _, file := range files {
		result = append(result, gin.H{
			"id":           file.ID,
			"originalName": file.OriginalName,
			"fileName":     file.FileName,
			"fileSize":     file.FileSize,
			"mimeType":     file.MimeType,
			"cosUrl":       file.CosURL,
			"module":       file.Module,
			"uploadTime":   file.CreatedAt,
		})
	}

	response.Success(c, gin.H{
		"files": result,
		"total": len(result),
	})
}

// GetMyFiles 获取当前用户的文件列表
func (h *FileHandler) GetMyFiles(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未登录")
		return
	}

	// 调用服务层获取文件列表
	files, err := h.fileService.GetFilesByUser(userID.(uint))
	if err != nil {
		response.InternalServerError(c, "获取文件列表失败")
		return
	}

	// 转换数据格式
	var result []gin.H
	for _, file := range files {
		result = append(result, gin.H{
			"id":           file.ID,
			"originalName": file.OriginalName,
			"fileName":     file.FileName,
			"fileSize":     file.FileSize,
			"mimeType":     file.MimeType,
			"cosUrl":       file.CosURL,
			"module":       file.Module,
			"uploadTime":   file.CreatedAt,
		})
	}

	response.Success(c, gin.H{
		"files": result,
		"total": len(result),
	})
}

// GetUploadConfig 获取上传配置信息
func (h *FileHandler) GetUploadConfig(c *gin.Context) {
	response.Success(c, gin.H{
		"maxSize": 50 * 1024 * 1024, // 50MB
		"allowedTypes": []string{
			"image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp",
			"application/pdf", "application/msword",
			"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			"application/vnd.ms-excel",
			"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
			"application/vnd.ms-powerpoint",
			"application/vnd.openxmlformats-officedocument.presentationml.presentation",
			"text/plain", "application/zip", "application/x-rar-compressed",
		},
		"modules": []string{
			"news", "service", "swiper", "partner", "recruitment",
			"platform", "project", "avatar", "general",
		},
	})
}
