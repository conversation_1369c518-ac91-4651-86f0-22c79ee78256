package handler

import (
	"encoding/json"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"weishi-server/pkg/response"
)

type PlatformConfigHandler struct {
	db *gorm.DB
}

func NewPlatformConfigHandler(db *gorm.DB) *PlatformConfigHandler {
	return &PlatformConfigHandler{db: db}
}

// GetPlatformConfig 获取平台配置数据
func (h *PlatformConfigHandler) GetPlatformConfig(c *gin.Context) {
	platformID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的平台ID")
		return
	}

	// 查询平台基本信息
	var platform struct {
		ID          uint   `json:"id"`
		Name        string `json:"name"`
		Description string `json:"description"`
	}

	if err := h.db.Table("part_platform").Where("id = ?", platformID).First(&platform).Error; err != nil {
		response.Error(c, http.StatusNotFound, "平台不存在")
		return
	}

	// 查询扩展分类
	var extensions []struct {
		ID          uint   `json:"id"`
		Title       string `json:"title"`
		Description string `json:"description"`
		ImageURL    string `json:"image_url"`
		SortOrder   int    `json:"sort_order"`
	}

	if err := h.db.Table("part_platform_extension").
		Where("part_platform_id = ?", platformID).
		Order("sort_order ASC, id ASC").
		Find(&extensions).Error; err != nil {
		response.Error(c, http.StatusInternalServerError, "查询扩展分类失败")
		return
	}

	// 为每个扩展查询表格
	extensionList := make([]map[string]interface{}, 0, len(extensions))
	for _, ext := range extensions {
		// 查询表格
		var tables []struct {
			ID        uint   `json:"id"`
			TableName string `json:"table_name"`
			SortOrder int    `json:"sort_order"`
		}

		if err := h.db.Table("part_platform_table").
			Where("extension_id = ?", ext.ID).
			Order("sort_order ASC, id ASC").
			Find(&tables).Error; err != nil {
			continue // 跳过查询失败的扩展
		}

		// 为每个表格查询列配置
		tableList := make([]map[string]interface{}, 0, len(tables))
		for _, table := range tables {
			var columns []struct {
				ID          uint   `json:"id"`
				ColumnName  string `json:"column_name"`
				ColumnLabel string `json:"column_label"`
				ColumnType  string `json:"column_type"`
				IsRequired  bool   `json:"is_required"`
				SortOrder   int    `json:"sort_order"`
			}

			h.db.Table("part_platform_table_column").
				Where("table_id = ?", table.ID).
				Order("sort_order ASC, id ASC").
				Find(&columns)

			tableList = append(tableList, map[string]interface{}{
				"id":         table.ID,
				"table_name": table.TableName,
				"sort_order": table.SortOrder,
				"columns":    columns,
			})
		}

		extensionList = append(extensionList, map[string]interface{}{
			"id":          ext.ID,
			"title":       ext.Title,
			"description": ext.Description,
			"image_url":   ext.ImageURL,
			"sort_order":  ext.SortOrder,
			"tables":      tableList,
		})
	}

	config := map[string]interface{}{
		"platform": map[string]interface{}{
			"id":          platform.ID,
			"name":        platform.Name,
			"description": platform.Description,
		},
		"extensions": extensionList,
	}

	response.Success(c, config)
}

// CreateExtension 创建扩展分类
func (h *PlatformConfigHandler) CreateExtension(c *gin.Context) {
	var req struct {
		PartPlatformID uint   `json:"part_platform_id" binding:"required"`
		Title          string `json:"title" binding:"required"`
		Description    string `json:"description"`
		ImageURL       string `json:"image_url"`
		SortOrder      int    `json:"sort_order"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误")
		return
	}

	// 模拟创建
	extension := map[string]interface{}{
		"id":               100, // 模拟新ID
		"part_platform_id": req.PartPlatformID,
		"title":            req.Title,
		"description":      req.Description,
		"image_url":        req.ImageURL,
		"sort_order":       req.SortOrder,
		"tables":           []map[string]interface{}{},
	}

	response.Success(c, extension)
}

// UpdateExtension 更新扩展分类
func (h *PlatformConfigHandler) UpdateExtension(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的ID")
		return
	}

	var req struct {
		Title       string `json:"title" binding:"required"`
		Description string `json:"description"`
		ImageURL    string `json:"image_url"`
		SortOrder   int    `json:"sort_order"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误")
		return
	}

	// 模拟更新
	extension := map[string]interface{}{
		"id":          id,
		"title":       req.Title,
		"description": req.Description,
		"image_url":   req.ImageURL,
		"sort_order":  req.SortOrder,
	}

	response.Success(c, extension)
}

// DeleteExtension 删除扩展分类
func (h *PlatformConfigHandler) DeleteExtension(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的ID")
		return
	}

	// 模拟删除
	response.Success(c, gin.H{"message": "删除成功", "id": id})
}

// CreateTable 创建表格
func (h *PlatformConfigHandler) CreateTable(c *gin.Context) {
	var req struct {
		ExtensionID uint   `json:"extension_id" binding:"required"`
		TableName   string `json:"table_name" binding:"required"`
		SortOrder   int    `json:"sort_order"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误")
		return
	}

	// 模拟创建
	table := map[string]interface{}{
		"id":           200, // 模拟新ID
		"extension_id": req.ExtensionID,
		"table_name":   req.TableName,
		"sort_order":   req.SortOrder,
		"columns":      []map[string]interface{}{},
		"rows":         []map[string]interface{}{},
	}

	response.Success(c, table)
}

// UpdateTable 更新表格
func (h *PlatformConfigHandler) UpdateTable(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的ID")
		return
	}

	var req struct {
		TableName string `json:"table_name" binding:"required"`
		SortOrder int    `json:"sort_order"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误")
		return
	}

	// 模拟更新
	table := map[string]interface{}{
		"id":         id,
		"table_name": req.TableName,
		"sort_order": req.SortOrder,
	}

	response.Success(c, table)
}

// DeleteTable 删除表格
func (h *PlatformConfigHandler) DeleteTable(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的ID")
		return
	}

	// 模拟删除
	response.Success(c, gin.H{"message": "删除成功", "id": id})
}

// GetTableColumns 获取表格列配置
func (h *PlatformConfigHandler) GetTableColumns(c *gin.Context) {
	tableID, err := strconv.ParseUint(c.Param("tableId"), 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的表格ID")
		return
	}

	// 从数据库查询列配置
	var columns []struct {
		ID          uint   `json:"id"`
		TableID     uint   `json:"table_id"`
		ColumnName  string `json:"column_name"`
		ColumnLabel string `json:"column_label"`
		ColumnType  string `json:"column_type"`
		IsRequired  bool   `json:"is_required"`
		SortOrder   int    `json:"sort_order"`
		Remark      string `json:"remark"`
	}

	if err := h.db.Table("part_platform_table_column").
		Where("table_id = ?", tableID).
		Order("sort_order ASC, id ASC").
		Find(&columns).Error; err != nil {
		response.Error(c, http.StatusInternalServerError, "查询列配置失败")
		return
	}

	response.Success(c, columns)
}

// CreateTableColumn 创建表格列
func (h *PlatformConfigHandler) CreateTableColumn(c *gin.Context) {
	var req struct {
		TableID     uint   `json:"table_id" binding:"required"`
		ColumnName  string `json:"column_name" binding:"required"`
		ColumnLabel string `json:"column_label" binding:"required"`
		ColumnType  string `json:"column_type" binding:"required"`
		IsRequired  bool   `json:"is_required"`
		SortOrder   int    `json:"sort_order"`
		Remark      string `json:"remark"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误")
		return
	}

	// 创建数据库记录
	var newColumn struct {
		ID          uint   `gorm:"primaryKey;autoIncrement"`
		TableID     uint   `gorm:"column:table_id"`
		ColumnName  string `gorm:"column:column_name"`
		ColumnLabel string `gorm:"column:column_label"`
		ColumnType  string `gorm:"column:column_type"`
		IsRequired  bool   `gorm:"column:is_required"`
		SortOrder   int    `gorm:"column:sort_order"`
		Remark      string `gorm:"column:remark"`
	}

	newColumn.TableID = req.TableID
	newColumn.ColumnName = req.ColumnName
	newColumn.ColumnLabel = req.ColumnLabel
	newColumn.ColumnType = req.ColumnType
	newColumn.IsRequired = req.IsRequired
	newColumn.SortOrder = req.SortOrder
	newColumn.Remark = req.Remark

	if err := h.db.Table("part_platform_table_column").Create(&newColumn).Error; err != nil {
		response.Error(c, http.StatusInternalServerError, "创建列失败")
		return
	}

	result := map[string]interface{}{
		"id":           newColumn.ID,
		"table_id":     newColumn.TableID,
		"column_name":  newColumn.ColumnName,
		"column_label": newColumn.ColumnLabel,
		"column_type":  newColumn.ColumnType,
		"is_required":  newColumn.IsRequired,
		"sort_order":   newColumn.SortOrder,
		"remark":       newColumn.Remark,
	}

	response.Success(c, result)
}

// UpdateTableColumn 更新表格列
func (h *PlatformConfigHandler) UpdateTableColumn(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的ID")
		return
	}

	var req struct {
		ColumnName  string `json:"column_name" binding:"required"`
		ColumnLabel string `json:"column_label" binding:"required"`
		ColumnType  string `json:"column_type" binding:"required"`
		IsRequired  bool   `json:"is_required"`
		SortOrder   int    `json:"sort_order"`
		Remark      string `json:"remark"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误")
		return
	}

	// 模拟更新
	column := map[string]interface{}{
		"id":           id,
		"column_name":  req.ColumnName,
		"column_label": req.ColumnLabel,
		"column_type":  req.ColumnType,
		"is_required":  req.IsRequired,
		"sort_order":   req.SortOrder,
		"remark":       req.Remark,
	}

	response.Success(c, column)
}

// DeleteTableColumn 删除表格列
func (h *PlatformConfigHandler) DeleteTableColumn(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的ID")
		return
	}

	// 模拟删除
	response.Success(c, gin.H{"message": "删除成功", "id": id})
}

// GetTableRows 获取表格数据
func (h *PlatformConfigHandler) GetTableRows(c *gin.Context) {
	tableID, err := strconv.ParseUint(c.Param("tableId"), 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的表格ID")
		return
	}

	// 从数据库查询表格行数据
	var rows []struct {
		ID        uint   `json:"id"`
		TableID   uint   `json:"table_id"`
		Data      string `json:"row_data" gorm:"column:row_data"` // JSON字符串
		SortOrder int    `json:"sort_order"`
	}

	if err := h.db.Table("part_platform_table_row").
		Where("table_id = ?", tableID).
		Order("sort_order ASC, id ASC").
		Find(&rows).Error; err != nil {
		response.Error(c, http.StatusInternalServerError, "查询表格数据失败")
		return
	}

	// 解析JSON数据并格式化返回
	result := make([]map[string]interface{}, 0, len(rows))
	for _, row := range rows {
		rowData := map[string]interface{}{
			"id":         row.ID,
			"table_id":   row.TableID,
			"sort_order": row.SortOrder,
		}

		// 解析JSON数据
		if row.Data != "" {
			var data map[string]interface{}
			if err := json.Unmarshal([]byte(row.Data), &data); err == nil {
				// 将解析的数据合并到rowData中
				for key, value := range data {
					rowData[key] = value
				}
			}
		}

		result = append(result, rowData)
	}

	response.Success(c, result)
}

// CreateTableRow 创建表格数据行
func (h *PlatformConfigHandler) CreateTableRow(c *gin.Context) {
	var req struct {
		TableID   uint                   `json:"table_id" binding:"required"`
		Data      map[string]interface{} `json:"data" binding:"required"`
		SortOrder int                    `json:"sort_order"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误")
		return
	}

	// 将数据序列化为JSON
	dataJSON, err := json.Marshal(req.Data)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "数据格式错误")
		return
	}

	// 创建数据库记录
	var newRow struct {
		ID        uint   `gorm:"primaryKey;autoIncrement"`
		TableID   uint   `gorm:"column:table_id"`
		Data      string `gorm:"column:row_data"`
		SortOrder int    `gorm:"column:sort_order"`
	}

	newRow.TableID = req.TableID
	newRow.Data = string(dataJSON)
	newRow.SortOrder = req.SortOrder

	if err := h.db.Table("part_platform_table_row").Create(&newRow).Error; err != nil {
		response.Error(c, http.StatusInternalServerError, "创建数据失败")
		return
	}

	// 构造返回数据
	result := map[string]interface{}{
		"id":         newRow.ID,
		"table_id":   newRow.TableID,
		"sort_order": newRow.SortOrder,
	}

	// 将data中的字段展开到顶层
	for key, value := range req.Data {
		result[key] = value
	}

	response.Success(c, result)
}

// UpdateTableRow 更新表格数据行
func (h *PlatformConfigHandler) UpdateTableRow(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的ID")
		return
	}

	var req struct {
		Data      map[string]interface{} `json:"data" binding:"required"`
		SortOrder int                    `json:"sort_order"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误")
		return
	}

	// 将数据序列化为JSON
	dataJSON, err := json.Marshal(req.Data)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "数据格式错误")
		return
	}

	// 更新数据库记录
	updateData := map[string]interface{}{
		"row_data":   string(dataJSON),
		"sort_order": req.SortOrder,
	}

	if err := h.db.Table("part_platform_table_row").
		Where("id = ?", id).
		Updates(updateData).Error; err != nil {
		response.Error(c, http.StatusInternalServerError, "更新数据失败")
		return
	}

	// 查询更新后的数据
	var updatedRow struct {
		ID        uint   `json:"id"`
		TableID   uint   `json:"table_id"`
		Data      string `json:"row_data" gorm:"column:row_data"`
		SortOrder int    `json:"sort_order"`
	}

	if err := h.db.Table("part_platform_table_row").
		Where("id = ?", id).
		First(&updatedRow).Error; err != nil {
		response.Error(c, http.StatusInternalServerError, "查询更新后数据失败")
		return
	}

	// 构造返回数据
	result := map[string]interface{}{
		"id":         updatedRow.ID,
		"table_id":   updatedRow.TableID,
		"sort_order": updatedRow.SortOrder,
	}

	// 解析JSON数据
	if updatedRow.Data != "" {
		var data map[string]interface{}
		if err := json.Unmarshal([]byte(updatedRow.Data), &data); err == nil {
			for key, value := range data {
				result[key] = value
			}
		}
	}

	response.Success(c, result)
}

// DeleteTableRow 删除表格数据行
func (h *PlatformConfigHandler) DeleteTableRow(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的ID")
		return
	}

	// 从数据库删除
	if err := h.db.Table("part_platform_table_row").Where("id = ?", id).Delete(nil).Error; err != nil {
		response.Error(c, http.StatusInternalServerError, "删除失败")
		return
	}

	response.Success(c, gin.H{"message": "删除成功", "id": id})
}
