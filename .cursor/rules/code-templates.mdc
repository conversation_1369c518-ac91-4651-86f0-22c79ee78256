---
description: 
globs: 
alwaysApply: false
---
# 蔚之智科官网代码模板

## Nuxt3 页面模板

### 标准页面模板
```vue
<!-- pages/example.vue -->
<template>
  <div class="example-page">
    <PageHeader 
      :title="pageTitle" 
      :description="pageDescription" 
    />
    
    <div class="container mx-auto px-4 py-8">
      <LoadingSpinner v-if="pending" />
      
      <div v-else-if="error" class="error-state">
        <h2>加载失败</h2>
        <p>{{ error.message }}</p>
        <Button @click="refresh">重试</Button>
      </div>
      
      <div v-else class="content">
        <!-- 页面内容 -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ExampleData } from '@weizhi/types'

// 页面元数据
definePageMeta({
  title: '示例页面',
  layout: 'default'
})

// 响应式数据
const pageTitle = '示例页面'
const pageDescription = '这是一个示例页面的描述'

// 数据获取
const { data, pending, error, refresh } = await useFetch<ExampleData>('/api/example')

// SEO 优化
useSeoMeta({
  title: pageTitle,
  description: pageDescription
})
</script>

<style scoped>
.example-page {
  min-height: 100vh;
}

.error-state {
  @apply text-center py-12;
}
</style>
```

### 动态路由页面模板
```vue
<!-- pages/news/[id].vue -->
<template>
  <div class="news-detail">
    <div v-if="pending" class="loading">
      <LoadingSpinner />
    </div>
    
    <div v-else-if="error" class="error">
      <ErrorMessage :error="error" @retry="refresh" />
    </div>
    
    <article v-else-if="data" class="news-article">
      <header class="article-header">
        <h1 class="article-title">{{ data.title }}</h1>
        <div class="article-meta">
          <time :datetime="data.createdAt">
            {{ formatDate(data.createdAt) }}
          </time>
          <span class="author">{{ data.author?.name }}</span>
        </div>
      </header>
      
      <div class="article-content" v-html="data.content"></div>
    </article>
  </div>
</template>

<script setup lang="ts">
import type { NewsResponse } from '@weizhi/types'

const route = useRoute()
const newsId = route.params.id as string

// 页面元数据
definePageMeta({
  title: '新闻详情',
  layout: 'default'
})

// 数据获取
const { data, pending, error, refresh } = await useFetch<NewsResponse>(
  `/api/news/${newsId}`,
  {
    key: `news-${newsId}`
  }
)

// 动态 SEO
useSeoMeta({
  title: computed(() => data.value?.title || '新闻详情'),
  description: computed(() => 
    data.value?.content?.substring(0, 160) || '查看新闻详情'
  )
})

// 工具函数
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}
</script>
```

## Vue 组件模板

### 基础组件模板
```vue
<!-- components/ExampleCard.vue -->
<template>
  <div class="example-card" :class="cardClasses">
    <div v-if="loading" class="loading-state">
      <LoadingSpinner size="sm" />
    </div>
    
    <div v-else class="card-content">
      <header v-if="title" class="card-header">
        <h3 class="card-title">{{ title }}</h3>
        <div v-if="$slots.actions" class="card-actions">
          <slot name="actions" />
        </div>
      </header>
      
      <div class="card-body">
        <slot />
      </div>
      
      <footer v-if="$slots.footer" class="card-footer">
        <slot name="footer" />
      </footer>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title?: string
  variant?: 'default' | 'bordered' | 'shadow'
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  loading: false
})

const cardClasses = computed(() => [
  'example-card',
  `card-${props.variant}`,
  {
    'card-loading': props.loading
  }
])
</script>

<style scoped>
.example-card {
  @apply rounded-lg transition-all duration-200;
}

.card-default {
  @apply bg-white dark:bg-gray-800;
}

.card-bordered {
  @apply border border-gray-200 dark:border-gray-700;
}

.card-shadow {
  @apply shadow-md hover:shadow-lg;
}

.card-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700;
}

.card-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.card-body {
  @apply p-4;
}

.card-footer {
  @apply p-4 border-t border-gray-200 dark:border-gray-700;
}
</style>
```

### 表单组件模板
```vue
<!-- components/forms/ExampleForm.vue -->
<template>
  <form @submit.prevent="handleSubmit" class="example-form">
    <div class="form-fields space-y-4">
      <FormField
        v-model="form.title"
        label="标题"
        name="title"
        :error="errors.title"
        :required="true"
      />
      
      <FormField
        v-model="form.content"
        label="内容"
        name="content"
        type="textarea"
        :error="errors.content"
        :required="true"
      />
      
      <FormField
        v-model="form.category"
        label="分类"
        name="category"
        type="select"
        :options="categoryOptions"
        :error="errors.category"
        :required="true"
      />
    </div>
    
    <div class="form-actions flex justify-end space-x-3 mt-6">
      <Button 
        type="button" 
        variant="outline" 
        @click="handleCancel"
        :disabled="isSubmitting"
      >
        取消
      </Button>
      
      <Button 
        type="submit" 
        :loading="isSubmitting"
        :disabled="!isFormValid"
      >
        {{ submitLabel }}
      </Button>
    </div>
  </form>
</template>

<script setup lang="ts">
import type { CreateNewsRequest, NewsCategory } from '@weishi/types'

interface Props {
  initialData?: Partial<CreateNewsRequest>
  submitLabel?: string
}

interface Emits {
  submit: [data: CreateNewsRequest]
  cancel: []
}

const props = withDefaults(defineProps<Props>(), {
  submitLabel: '提交'
})

const emit = defineEmits<Emits>()

// 表单状态
const form = reactive<CreateNewsRequest>({
  title: props.initialData?.title || '',
  content: props.initialData?.content || '',
  category: props.initialData?.category || '',
  isPublished: props.initialData?.isPublished || false
})

const errors = reactive<Record<string, string>>({})
const isSubmitting = ref(false)

// 分类选项
const categoryOptions = [
  { value: 'company', label: '公司新闻' },
  { value: 'product', label: '产品动态' },
  { value: 'industry', label: '行业资讯' },
  { value: 'announcement', label: '公告通知' }
]

// 表单验证
const isFormValid = computed(() => {
  return form.title.trim() && 
         form.content.trim() && 
         form.category &&
         Object.keys(errors).length === 0
})

// 验证方法
const validateForm = () => {
  // 清空错误
  Object.keys(errors).forEach(key => delete errors[key])
  
  if (!form.title.trim()) {
    errors.title = '标题不能为空'
  } else if (form.title.length > 200) {
    errors.title = '标题不能超过200字符'
  }
  
  if (!form.content.trim()) {
    errors.content = '内容不能为空'
  }
  
  if (!form.category) {
    errors.category = '请选择分类'
  }
}

// 提交处理
const handleSubmit = async () => {
  validateForm()
  
  if (!isFormValid.value) return
  
  isSubmitting.value = true
  
  try {
    emit('submit', { ...form })
  } finally {
    isSubmitting.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

// 监听表单变化，实时验证
watch(() => form.title, validateForm)
watch(() => form.content, validateForm)
watch(() => form.category, validateForm)
</script>
```

## Composables 模板

### API 服务 Composable
```typescript
// composables/use-news-service.ts
import type { 
  NewsResponse, 
  NewsListResponse, 
  CreateNewsRequest, 
  UpdateNewsRequest,
  NewsQuery 
} from '@weishi/types'

export const useNewsService = () => {
  const { $fetch } = useNuxtApp()

  // 获取新闻列表
  const getNewsList = async (query?: NewsQuery): Promise<NewsListResponse> => {
    const params = new URLSearchParams()
    
    if (query?.page) params.append('page', query.page.toString())
    if (query?.limit) params.append('limit', query.limit.toString())
    if (query?.search) params.append('search', query.search)
    if (query?.category) params.append('category', query.category)
    
    return await $fetch(`/api/news?${params.toString()}`)
  }

  // 获取新闻详情
  const getNews = async (id: string): Promise<NewsResponse> => {
    return await $fetch(`/api/news/${id}`)
  }

  // 创建新闻
  const createNews = async (data: CreateNewsRequest): Promise<NewsResponse> => {
    return await $fetch('/api/news', {
      method: 'POST',
      body: data
    })
  }

  // 更新新闻
  const updateNews = async (id: string, data: UpdateNewsRequest): Promise<NewsResponse> => {
    return await $fetch(`/api/news/${id}`, {
      method: 'PUT',
      body: data
    })
  }

  // 删除新闻
  const deleteNews = async (id: string): Promise<void> => {
    await $fetch(`/api/news/${id}`, {
      method: 'DELETE'
    })
  }

  return {
    getNewsList,
    getNews,
    createNews,
    updateNews,
    deleteNews
  }
}
```

### 表单状态管理 Composable
```typescript
// composables/use-form.ts
import type { ValidationRules } from '@weishi/types'

export const useForm = <T extends Record<string, any>>(
  initialData: T,
  validationRules?: ValidationRules<T>
) => {
  const form = reactive<T>({ ...initialData })
  const errors = reactive<Record<keyof T, string>>({} as Record<keyof T, string>)
  const touched = reactive<Record<keyof T, boolean>>({} as Record<keyof T, boolean>)
  const isSubmitting = ref(false)

  // 验证单个字段
  const validateField = (field: keyof T): boolean => {
    const value = form[field]
    const rules = validationRules?.[field]
    
    if (!rules) return true

    // 清除之前的错误
    delete errors[field]

    // 必填验证
    if (rules.required && (!value || (typeof value === 'string' && !value.trim()))) {
      errors[field] = `${String(field)} 是必填项`
      return false
    }

    // 字符串长度验证
    if (typeof value === 'string') {
      if (rules.minLength && value.length < rules.minLength) {
        errors[field] = `${String(field)} 至少需要 ${rules.minLength} 个字符`
        return false
      }
      
      if (rules.maxLength && value.length > rules.maxLength) {
        errors[field] = `${String(field)} 不能超过 ${rules.maxLength} 个字符`
        return false
      }
    }

    // 正则验证
    if (rules.pattern && typeof value === 'string' && !rules.pattern.test(value)) {
      errors[field] = `${String(field)} 格式不正确`
      return false
    }

    // 自定义验证
    if (rules.custom) {
      const error = rules.custom(value)
      if (error) {
        errors[field] = error
        return false
      }
    }

    return true
  }

  // 验证所有字段
  const validateForm = (): boolean => {
    let isValid = true
    
    if (validationRules) {
      for (const field in validationRules) {
        if (!validateField(field as keyof T)) {
          isValid = false
        }
      }
    }
    
    return isValid
  }

  // 重置表单
  const resetForm = () => {
    Object.assign(form, initialData)
    Object.keys(errors).forEach(key => delete errors[key as keyof T])
    Object.keys(touched).forEach(key => delete touched[key as keyof T])
    isSubmitting.value = false
  }

  // 设置字段值
  const setFieldValue = (field: keyof T, value: T[keyof T]) => {
    form[field] = value
    touched[field] = true
    validateField(field)
  }

  // 计算属性
  const isFormValid = computed(() => {
    return Object.keys(errors).length === 0 && 
           (!validationRules || Object.keys(validationRules).every(field => 
             validateField(field as keyof T)
           ))
  })

  const isDirty = computed(() => {
    return Object.keys(touched).some(key => touched[key as keyof T])
  })

  return {
    form: readonly(form),
    errors: readonly(errors),
    touched: readonly(touched),
    isSubmitting: readonly(isSubmitting),
    isFormValid,
    isDirty,
    validateField,
    validateForm,
    resetForm,
    setFieldValue,
    setSubmitting: (value: boolean) => { isSubmitting.value = value }
  }
}
```

## NestJS 模块模板

### 基础模块模板
```typescript
// src/modules/example/example.module.ts
import { Module } from '@nestjs/common'
import { ExampleController } from './example.controller'
import { ExampleService } from './example.service'

@Module({
  controllers: [ExampleController],
  providers: [ExampleService],
  exports: [ExampleService]
})
export class ExampleModule {}
```

### Controller 模板
```typescript
// src/modules/example/example.controller.ts
import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus
} from '@nestjs/common'
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiBearerAuth
} from '@nestjs/swagger'
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard'
import { GetUser } from '../../common/decorators/get-user.decorator'
import { ExampleService } from './example.service'
import { CreateExampleDto, UpdateExampleDto, ExampleQueryDto } from './dto'
import type { UserPayload, PaginatedResponse, ExampleResponse } from '@weishi/types'

@Controller('example')
@ApiTags('示例模块')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ExampleController {
  constructor(private readonly exampleService: ExampleService) {}

  @Get()
  @ApiOperation({ summary: '获取示例列表' })
  @ApiResponse({ 
    status: 200, 
    description: '成功获取示例列表'
  })
  async findAll(
    @Query() query: ExampleQueryDto
  ): Promise<PaginatedResponse<ExampleResponse>> {
    return this.exampleService.findAll(query)
  }

  @Get(':id')
  @ApiOperation({ summary: '获取示例详情' })
  @ApiParam({ name: 'id', description: '示例ID' })
  async findOne(
    @Param('id') id: string
  ): Promise<ExampleResponse> {
    return this.exampleService.findOne(id)
  }

  @Post()
  @ApiOperation({ summary: '创建示例' })
  @ApiBody({ type: CreateExampleDto })
  @HttpCode(HttpStatus.CREATED)
  async create(
    @Body() createDto: CreateExampleDto,
    @GetUser() user: UserPayload
  ): Promise<ExampleResponse> {
    return this.exampleService.create(createDto, user.id)
  }

  @Put(':id')
  @ApiOperation({ summary: '更新示例' })
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateExampleDto,
    @GetUser() user: UserPayload
  ): Promise<ExampleResponse> {
    return this.exampleService.update(id, updateDto, user.id)
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除示例' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(
    @Param('id') id: string,
    @GetUser() user: UserPayload
  ): Promise<void> {
    return this.exampleService.remove(id, user.id)
  }
}
```

### Service 模板
```typescript
// src/modules/example/example.service.ts
import { Injectable, NotFoundException, Logger, Inject } from '@nestjs/common'
import { eq, like, desc, count } from 'drizzle-orm'
import type { DatabaseConnection } from '../../types/database.types'
import { exampleTable } from '../../database/schema'
import { CreateExampleDto, UpdateExampleDto, ExampleQueryDto } from './dto'
import type { PaginatedResponse, ExampleResponse } from '@weishi/types'

@Injectable()
export class ExampleService {
  private readonly logger = new Logger(ExampleService.name)

  constructor(
    @Inject('DATABASE_CONNECTION') private db: DatabaseConnection
  ) {}

  async findAll(query: ExampleQueryDto): Promise<PaginatedResponse<ExampleResponse>> {
    try {
      const { page = 1, limit = 10, search } = query
      const offset = (page - 1) * limit

      let baseQuery = this.db
        .select()
        .from(exampleTable)

      if (search) {
        baseQuery = baseQuery.where(
          like(exampleTable.name, `%${search}%`)
        )
      }

      const [items, totalResult] = await Promise.all([
        baseQuery
          .orderBy(desc(exampleTable.createdAt))
          .limit(limit)
          .offset(offset),
        this.db
          .select({ count: count() })
          .from(exampleTable)
      ])

      const total = totalResult[0].count

      return {
        items: items.map(item => this.mapToResponse(item)),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    } catch (error) {
      this.logger.error('Failed to fetch examples', error)
      throw error
    }
  }

  async findOne(id: string): Promise<ExampleResponse> {
    const example = await this.db
      .select()
      .from(exampleTable)
      .where(eq(exampleTable.id, id))
      .limit(1)

    if (!example.length) {
      throw new NotFoundException('示例不存在')
    }

    return this.mapToResponse(example[0])
  }

  async create(dto: CreateExampleDto, userId: string): Promise<ExampleResponse> {
    try {
      const [example] = await this.db
        .insert(exampleTable)
        .values({
          ...dto,
          createdBy: userId,
          createdAt: new Date(),
          updatedAt: new Date()
        })
        .returning()

      return this.mapToResponse(example)
    } catch (error) {
      this.logger.error('Failed to create example', error)
      throw error
    }
  }

  async update(id: string, dto: UpdateExampleDto, userId: string): Promise<ExampleResponse> {
    const existing = await this.findOne(id)
    
    const [updated] = await this.db
      .update(exampleTable)
      .set({
        ...dto,
        updatedBy: userId,
        updatedAt: new Date()
      })
      .where(eq(exampleTable.id, id))
      .returning()

    return this.mapToResponse(updated)
  }

  async remove(id: string, userId: string): Promise<void> {
    await this.findOne(id) // 确保存在
    
    await this.db
      .delete(exampleTable)
      .where(eq(exampleTable.id, id))
  }

  private mapToResponse(example: any): ExampleResponse {
    return {
      id: example.id,
      name: example.name,
      description: example.description,
      createdAt: example.createdAt.toISOString(),
      updatedAt: example.updatedAt.toISOString()
    }
  }
}
```

### DTO 模板
```typescript
// src/modules/example/dto/create-example.dto.ts
import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsOptional, MinLength, MaxLength } from 'class-validator'

export class CreateExampleDto {
  @ApiProperty({ description: '名称', example: '示例名称' })
  @IsString()
  @MinLength(1, { message: '名称不能为空' })
  @MaxLength(100, { message: '名称不能超过100字符' })
  name: string

  @ApiProperty({ description: '描述', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: '描述不能超过500字符' })
  description?: string
}
```
