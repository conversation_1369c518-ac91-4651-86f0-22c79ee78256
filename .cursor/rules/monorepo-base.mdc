---
description: 
globs: 
alwaysApply: false
---
# 蔚之智科官网 Monorepo 项目开发规范

## 项目架构
- Monorepo 结构：packages/types (共享类型)、web (Nuxt3前端)、server (NestJS后端)
- 包管理器：pnpm workspace
- 版本控制：统一使用语义化版本
- 类型共享：通过 @weishi/types 包实现前后端类型统一

## 通用 TypeScript 规范

### 基础原则
- 全面使用 TypeScript，严格模式开发
- 优先使用 `interface` 而非 `type`
- 避免使用 `enum`，改用 `const` 对象或 `as const` 断言
- 使用具名导出，提高代码可读性
- 函数式编程优先，避免使用类

### 类型定义
```typescript
// ✅ 推荐：使用 interface
interface UserInfo {
  id: string
  name: string
  email: string
}

// ✅ 推荐：使用 const 对象替代 enum
const UserRole = {
  ADMIN: 'admin',
  USER: 'user',
  GUEST: 'guest'
} as const
type UserRole = typeof UserRole[keyof typeof UserRole]

// ❌ 避免：使用 enum
enum UserRole {
  ADMIN = 'admin',
  USER = 'user'
}
```

### 导入导出规范
```typescript
// ✅ 具名导出
export const useUserService = () => { ... }
export interface ApiResponse<T> { ... }

// ✅ 统一从共享类型包导入
import type { NewsItem, ApiResponse } from '@weishi/types'

// ❌ 避免默认导出（除非是组件）
export default { ... }
```

## 文件命名约定

### 通用规则
- 文件和目录：kebab-case (`user-service.ts`)
- 组件文件：PascalCase (`UserProfile.vue`, `NewsCard.tsx`)
- 类型文件：kebab-case (`user-types.ts`)
- 常量文件：kebab-case (`api-constants.ts`)

## 开发最佳实践
- 所有代码必须通过 TypeScript 类型检查
- 使用 ESLint 和 Prettier 保持代码风格一致
- 提交前运行完整的类型检查和代码格式化
- 优先使用组合式函数而非类
- 保持模块小而专注，便于测试和维护
