-- 列模板表
CREATE TABLE `column_templates` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '字段名',
  `label` varchar(100) NOT NULL COMMENT '显示标签',
  `type` enum('text','number','date','boolean','textarea') NOT NULL COMMENT '数据类型',
  `category` varchar(50) NOT NULL COMMENT '分类',
  `description` text COMMENT '描述',
  `is_required` tinyint(1) DEFAULT '0' COMMENT '是否必填',
  `is_common` tinyint(1) DEFAULT '0' COMMENT '是否常用',
  `default_value` text COMMENT '默认值',
  `options` json COMMENT '选项（用于下拉等）',
  `validation_rules` json COMMENT '验证规则',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `is_system` tinyint(1) DEFAULT '1' COMMENT '是否系统预设',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_category` (`category`),
  KEY `idx_common` (`is_common`),
  KEY `idx_system` (`is_system`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='列模板表';

-- 组合模板表
CREATE TABLE `combination_templates` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '组合名称',
  `description` text COMMENT '描述',
  `category` varchar(50) COMMENT '分类',
  `scenarios` json COMMENT '适用场景',
  `is_system` tinyint(1) DEFAULT '1' COMMENT '是否系统预设',
  `created_by` bigint unsigned COMMENT '创建者ID',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_system` (`is_system`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='组合模板表';

-- 组合模板与列模板关联表
CREATE TABLE `combination_template_columns` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `combination_template_id` bigint unsigned NOT NULL COMMENT '组合模板ID',
  `column_template_id` bigint unsigned NOT NULL COMMENT '列模板ID',
  `sort_order` int DEFAULT '0' COMMENT '在组合中的排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_combination_column` (`combination_template_id`,`column_template_id`),
  KEY `idx_combination` (`combination_template_id`),
  KEY `idx_column` (`column_template_id`),
  CONSTRAINT `fk_combination_columns_combination` FOREIGN KEY (`combination_template_id`) REFERENCES `combination_templates` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_combination_columns_column` FOREIGN KEY (`column_template_id`) REFERENCES `column_templates` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='组合模板列关联表';

-- 插入系统预设的列模板
INSERT INTO `column_templates` (`name`, `label`, `type`, `category`, `description`, `is_required`, `is_common`, `sort_order`, `is_system`) VALUES
-- 基础信息类
('model', '型号', 'text', '基础信息', '产品型号或编号', 1, 1, 1, 1),
('name', '产品名称', 'text', '基础信息', '产品的完整名称', 1, 1, 2, 1),
('brand', '品牌', 'text', '基础信息', '产品品牌', 0, 0, 3, 1),
('specification', '规格', 'text', '基础信息', '产品规格描述', 0, 1, 4, 1),

-- 技术参数类
('flow_rate', '流量(L/min)', 'number', '技术参数', '流体流量参数', 0, 1, 10, 1),
('pressure', '压力(MPa)', 'number', '技术参数', '工作压力参数', 0, 1, 11, 1),
('temperature', '工作温度(°C)', 'number', '技术参数', '工作温度范围', 0, 0, 12, 1),
('voltage', '电压(V)', 'number', '技术参数', '工作电压', 0, 0, 13, 1),
('power', '功率(W)', 'number', '技术参数', '额定功率', 0, 0, 14, 1),
('speed', '转速(rpm)', 'number', '技术参数', '额定转速', 0, 0, 15, 1),
('efficiency', '效率(%)', 'number', '技术参数', '工作效率', 0, 0, 16, 1),
('filter_precision', '过滤精度(μm)', 'number', '技术参数', '过滤精度', 0, 0, 17, 1),
('flow_capacity', '流量容量(L/min)', 'number', '技术参数', '流量容量', 0, 0, 18, 1),
('pressure_drop', '压降(kPa)', 'number', '技术参数', '压力损失', 0, 0, 19, 1),
('life_span', '使用寿命(小时)', 'number', '技术参数', '使用寿命', 0, 0, 20, 1),

-- 物理属性类
('weight', '重量(kg)', 'number', '物理属性', '产品重量', 0, 0, 30, 1),
('dimensions', '尺寸(mm)', 'text', '物理属性', '产品外形尺寸', 0, 0, 31, 1),
('material', '材质', 'text', '物理属性', '主要材质', 0, 0, 32, 1),
('color', '颜色', 'text', '物理属性', '产品颜色', 0, 0, 33, 1),

-- 商务信息类
('price', '价格(元)', 'number', '商务信息', '产品价格', 0, 1, 40, 1),
('supplier', '供应商', 'text', '商务信息', '产品供应商', 0, 0, 41, 1),
('lead_time', '交期(天)', 'number', '商务信息', '交货周期', 0, 0, 42, 1),
('warranty', '保修期(月)', 'number', '商务信息', '保修期限', 0, 0, 43, 1),

-- 状态信息类
('status', '状态', 'text', '状态信息', '产品状态', 0, 0, 50, 1),
('is_available', '是否可用', 'boolean', '状态信息', '产品是否可用', 0, 0, 51, 1),
('created_date', '创建日期', 'date', '状态信息', '记录创建日期', 0, 0, 52, 1),
('updated_date', '更新日期', 'date', '状态信息', '记录更新日期', 0, 0, 53, 1),

-- 其他信息类
('description', '描述', 'textarea', '其他信息', '产品详细描述', 0, 0, 60, 1),
('notes', '备注', 'textarea', '其他信息', '其他备注信息', 0, 0, 61, 1),
('image_url', '产品图片', 'text', '其他信息', '产品图片链接', 0, 0, 62, 1);

-- 插入系统预设的组合模板
INSERT INTO `combination_templates` (`name`, `description`, `category`, `scenarios`, `sort_order`, `is_system`) VALUES
('液压阀门标准配置', '适用于各类液压阀门产品的标准列配置', '液压设备', JSON_ARRAY('液压阀门', '控制阀', '换向阀'), 1, 1),
('电机产品标准配置', '适用于各类电机产品的标准列配置', '电机设备', JSON_ARRAY('电机', '马达', '驱动器'), 2, 1),
('过滤器标准配置', '适用于各类过滤器产品的标准列配置', '过滤设备', JSON_ARRAY('过滤器', '滤芯', '净化设备'), 3, 1),
('基础产品配置', '适用于一般产品的基础列配置', '通用', JSON_ARRAY('通用产品', '基础配置'), 4, 1);

-- 插入组合模板与列模板的关联关系
-- 液压阀门标准配置
INSERT INTO `combination_template_columns` (`combination_template_id`, `column_template_id`, `sort_order`) 
SELECT 1, id, 
  CASE name
    WHEN 'model' THEN 1
    WHEN 'name' THEN 2
    WHEN 'flow_rate' THEN 3
    WHEN 'pressure' THEN 4
    WHEN 'temperature' THEN 5
    WHEN 'material' THEN 6
    WHEN 'price' THEN 7
  END
FROM `column_templates` 
WHERE `name` IN ('model', 'name', 'flow_rate', 'pressure', 'temperature', 'material', 'price');

-- 电机产品标准配置
INSERT INTO `combination_template_columns` (`combination_template_id`, `column_template_id`, `sort_order`) 
SELECT 2, id, 
  CASE name
    WHEN 'model' THEN 1
    WHEN 'name' THEN 2
    WHEN 'power' THEN 3
    WHEN 'voltage' THEN 4
    WHEN 'speed' THEN 5
    WHEN 'efficiency' THEN 6
    WHEN 'weight' THEN 7
    WHEN 'price' THEN 8
  END
FROM `column_templates` 
WHERE `name` IN ('model', 'name', 'power', 'voltage', 'speed', 'efficiency', 'weight', 'price');

-- 过滤器标准配置
INSERT INTO `combination_template_columns` (`combination_template_id`, `column_template_id`, `sort_order`) 
SELECT 3, id, 
  CASE name
    WHEN 'model' THEN 1
    WHEN 'name' THEN 2
    WHEN 'filter_precision' THEN 3
    WHEN 'flow_capacity' THEN 4
    WHEN 'pressure_drop' THEN 5
    WHEN 'material' THEN 6
    WHEN 'life_span' THEN 7
    WHEN 'price' THEN 8
  END
FROM `column_templates` 
WHERE `name` IN ('model', 'name', 'filter_precision', 'flow_capacity', 'pressure_drop', 'material', 'life_span', 'price');

-- 基础产品配置
INSERT INTO `combination_template_columns` (`combination_template_id`, `column_template_id`, `sort_order`) 
SELECT 4, id, 
  CASE name
    WHEN 'model' THEN 1
    WHEN 'name' THEN 2
    WHEN 'brand' THEN 3
    WHEN 'specification' THEN 4
    WHEN 'price' THEN 5
    WHEN 'supplier' THEN 6
    WHEN 'status' THEN 7
  END
FROM `column_templates` 
WHERE `name` IN ('model', 'name', 'brand', 'specification', 'price', 'supplier', 'status');
