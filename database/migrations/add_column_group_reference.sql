-- 添加列配置组引用功能
-- 版本: 002
-- 描述: 支持表格引用列配置组，减少数据冗余
-- 创建时间: 2025-01-19

-- 开始事务
START TRANSACTION;

-- 1. 为 part_platform_table 添加组合模板引用字段
ALTER TABLE `part_platform_table` 
ADD COLUMN `combination_template_id` bigint unsigned NULL COMMENT '组合模板ID',
ADD COLUMN `custom_columns` json COMMENT '自定义列配置',
ADD INDEX `idx_combination_template` (`combination_template_id`),
ADD CONSTRAINT `fk_table_combination_template` 
    FOREIGN KEY (`combination_template_id`) 
    REFERENCES `combination_templates` (`id`) 
    ON DELETE SET NULL;

-- 2. 创建列覆盖配置表
CREATE TABLE `part_platform_table_column_override` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `table_id` bigint unsigned NOT NULL COMMENT '表格ID',
  `column_template_id` bigint unsigned NOT NULL COMMENT '列模板ID',
  `action` varchar(20) NOT NULL COMMENT '操作类型: hide, modify, add',
  `custom_label` varchar(255) COMMENT '自定义标签',
  `custom_config` json COMMENT '自定义配置',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_table_id` (`table_id`),
  KEY `idx_column_template_id` (`column_template_id`),
  KEY `idx_action` (`action`),
  CONSTRAINT `fk_override_table` 
    FOREIGN KEY (`table_id`) 
    REFERENCES `part_platform_table` (`id`) 
    ON DELETE CASCADE,
  CONSTRAINT `fk_override_column_template` 
    FOREIGN KEY (`column_template_id`) 
    REFERENCES `column_templates` (`id`) 
    ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表格列覆盖配置表';

-- 3. 数据迁移：将现有表格关联到合适的组合模板
-- 根据表格名称特征自动关联到对应的组合模板

-- 液压阀门相关表格
UPDATE `part_platform_table` 
SET `combination_template_id` = (
  SELECT `id` FROM `combination_templates` 
  WHERE `name` = '液压阀门标准配置' 
  LIMIT 1
)
WHERE `table_name` LIKE '%阀%' 
   OR `table_name` LIKE '%换向%'
   OR `table_name` LIKE '%控制%';

-- 电机相关表格  
UPDATE `part_platform_table` 
SET `combination_template_id` = (
  SELECT `id` FROM `combination_templates` 
  WHERE `name` = '电机产品标准配置' 
  LIMIT 1
)
WHERE `table_name` LIKE '%电机%' 
   OR `table_name` LIKE '%马达%'
   OR `table_name` LIKE '%驱动%';

-- 过滤器相关表格
UPDATE `part_platform_table` 
SET `combination_template_id` = (
  SELECT `id` FROM `combination_templates` 
  WHERE `name` = '过滤器标准配置' 
  LIMIT 1
)
WHERE `table_name` LIKE '%过滤%' 
   OR `table_name` LIKE '%滤芯%'
   OR `table_name` LIKE '%净化%';

-- 其他表格使用基础配置
UPDATE `part_platform_table` 
SET `combination_template_id` = (
  SELECT `id` FROM `combination_templates` 
  WHERE `name` = '基础产品配置' 
  LIMIT 1
)
WHERE `combination_template_id` IS NULL;

-- 4. 迁移现有的列配置到覆盖表
-- 将与模板不同的列配置迁移为覆盖配置
INSERT INTO `part_platform_table_column_override` 
  (`table_id`, `column_template_id`, `action`, `custom_label`, `sort_order`)
SELECT 
  ptc.`table_id`,
  ct.`id` as `column_template_id`,
  'modify' as `action`,
  ptc.`column_label` as `custom_label`,
  ptc.`sort_order`
FROM `part_platform_table_column` ptc
JOIN `column_templates` ct ON ct.`name` = ptc.`column_name`
JOIN `part_platform_table` pt ON pt.`id` = ptc.`table_id`
JOIN `combination_templates` cmt ON cmt.`id` = pt.`combination_template_id`
JOIN `combination_template_columns` ctc ON ctc.`combination_template_id` = cmt.`id` 
  AND ctc.`column_template_id` = ct.`id`
WHERE ptc.`column_label` != ct.`label`  -- 标签不同，需要覆盖
   OR ptc.`sort_order` != ctc.`sort_order`;  -- 排序不同，需要覆盖

-- 5. 添加自定义列（不在模板中的列）
INSERT INTO `part_platform_table_column_override` 
  (`table_id`, `column_template_id`, `action`, `custom_label`, `custom_config`, `sort_order`)
SELECT 
  ptc.`table_id`,
  COALESCE(ct.`id`, 0) as `column_template_id`,  -- 如果没有对应模板，使用0
  'add' as `action`,
  ptc.`column_label` as `custom_label`,
  JSON_OBJECT(
    'column_name', ptc.`column_name`,
    'column_type', ptc.`column_type`,
    'is_required', false
  ) as `custom_config`,
  ptc.`sort_order`
FROM `part_platform_table_column` ptc
LEFT JOIN `column_templates` ct ON ct.`name` = ptc.`column_name`
WHERE ct.`id` IS NULL;  -- 不在模板中的列

-- 提交事务
COMMIT;

-- 添加注释说明
-- 注意：此迁移脚本执行后，建议：
-- 1. 验证数据迁移的正确性
-- 2. 更新应用代码以使用新的数据结构
-- 3. 在确认无误后，可以考虑删除旧的 part_platform_table_column 表
