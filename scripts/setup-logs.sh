#!/bin/bash

# 设置日志目录脚本
# 用于创建日志目录结构并设置权限

set -e

echo "🔧 设置日志目录结构..."

# 获取脚本所在目录的父目录（项目根目录）
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 日志目录路径
LOGS_DIR="$PROJECT_ROOT/logs"
UPLOADS_DIR="$PROJECT_ROOT/uploads"

# 创建日志目录结构
echo "📁 创建日志目录..."
mkdir -p "$LOGS_DIR/server"
mkdir -p "$LOGS_DIR/caddy"
mkdir -p "$LOGS_DIR/mysql"
mkdir -p "$UPLOADS_DIR"

# 设置目录权限
echo "🔐 设置目录权限..."
chmod 755 "$LOGS_DIR"
chmod 755 "$LOGS_DIR/server"
chmod 755 "$LOGS_DIR/caddy"
chmod 755 "$LOGS_DIR/mysql"
chmod 755 "$UPLOADS_DIR"

# 创建 .gitkeep 文件保持目录结构
echo "📝 创建 .gitkeep 文件..."
touch "$LOGS_DIR/server/.gitkeep"
touch "$LOGS_DIR/caddy/.gitkeep"
touch "$LOGS_DIR/mysql/.gitkeep"
touch "$UPLOADS_DIR/.gitkeep"

# 创建 .gitignore 忽略日志文件但保留目录
echo "🚫 创建 .gitignore..."
cat > "$LOGS_DIR/.gitignore" << 'EOF'
# 忽略所有日志文件
*.log
*.out
*.err

# 但保留目录结构
!.gitkeep
!*/
EOF

cat > "$UPLOADS_DIR/.gitignore" << 'EOF'
# 忽略所有上传文件
*

# 但保留目录结构
!.gitkeep
EOF

echo "✅ 日志目录设置完成！"
echo ""
echo "📊 目录结构："
echo "├── logs/"
echo "│   ├── server/     # 后端服务日志"
echo "│   ├── caddy/      # Caddy 访问日志和错误日志"
echo "│   └── mysql/      # MySQL 数据库日志"
echo "└── uploads/        # 文件上传目录"
echo ""
echo "🔍 查看日志命令："
echo "# 后端日志"
echo "tail -f logs/server/app.log"
echo ""
echo "# Caddy 访问日志"
echo "tail -f logs/caddy/access.log"
echo ""
echo "# Caddy 错误日志"
echo "tail -f logs/caddy/error.log"
echo ""
echo "# MySQL 错误日志"
echo "tail -f logs/mysql/error.log"
