#!/bin/bash

# 管理后台 404 问题诊断脚本
# 功能：快速诊断和修复管理后台静态资源 404 问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_message $BLUE "🔍 管理后台 404 问题诊断"
echo ""

# 1. 检查静态文件目录结构
print_message $BLUE "📁 检查静态文件目录结构..."
if [[ -d "static/admin" ]]; then
    print_message $GREEN "✅ static/admin 目录存在"
    
    if [[ -f "static/admin/index.html" ]]; then
        print_message $GREEN "✅ index.html 存在"
        
        # 显示 index.html 中引用的资源
        echo "   index.html 中引用的资源:"
        grep -o 'assets/[^"]*\.\(js\|css\)' static/admin/index.html | head -5
    else
        print_message $RED "❌ index.html 不存在"
    fi
    
    if [[ -d "static/admin/assets" ]]; then
        local js_count=$(find static/admin/assets -name "*.js" | wc -l)
        local css_count=$(find static/admin/assets -name "*.css" | wc -l)
        print_message $GREEN "✅ assets 目录存在 ($js_count JS, $css_count CSS)"
        
        # 显示实际存在的文件
        echo "   实际存在的主要文件:"
        find static/admin/assets -name "index-*.js" | head -3
        find static/admin/assets -name "vendor-*.js" | head -1
    else
        print_message $RED "❌ assets 目录不存在"
    fi
else
    print_message $RED "❌ static/admin 目录不存在"
fi

echo ""

# 2. 检查 Docker 容器状态
print_message $BLUE "🐳 检查 Docker 容器状态..."
if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -E "(caddy|server|web)"; then
    print_message $GREEN "✅ 相关容器运行状态正常"
else
    print_message $RED "❌ 部分容器未运行"
fi

echo ""

# 3. 检查 Caddy 配置
print_message $BLUE "⚙️  检查 Caddy 配置..."
if [[ -f "caddy/Caddyfile" ]]; then
    print_message $GREEN "✅ Caddyfile 存在"
    
    # 检查静态文件路径配置
    if grep -q "/opt/weishi/static" caddy/Caddyfile; then
        print_message $GREEN "✅ 静态文件路径配置正确"
    else
        print_message $RED "❌ 静态文件路径配置有问题"
    fi
    
    # 检查 admin 路径处理
    if grep -q "handle /admin" caddy/Caddyfile; then
        print_message $GREEN "✅ admin 路径处理配置存在"
    else
        print_message $RED "❌ admin 路径处理配置缺失"
    fi
else
    print_message $RED "❌ Caddyfile 不存在"
fi

echo ""

# 4. 检查文件权限
print_message $BLUE "🔐 检查文件权限..."
if [[ -d "static/admin" ]]; then
    local perms=$(stat -c "%a" static/admin 2>/dev/null || stat -f "%A" static/admin 2>/dev/null || echo "unknown")
    print_message $GREEN "✅ static/admin 权限: $perms"
    
    if [[ -f "static/admin/index.html" ]]; then
        local file_perms=$(stat -c "%a" static/admin/index.html 2>/dev/null || stat -f "%A" static/admin/index.html 2>/dev/null || echo "unknown")
        print_message $GREEN "✅ index.html 权限: $file_perms"
    fi
fi

echo ""

# 5. 测试文件访问
print_message $BLUE "🌐 测试文件访问..."
local domain=$(grep "^DOMAIN=" docker.env 2>/dev/null | cut -d'=' -f2 || echo "localhost")

if command -v curl >/dev/null 2>&1; then
    # 测试 index.html
    local index_status=$(curl -s -k -o /dev/null -w "%{http_code}" "https://$domain/admin/" || echo "000")
    if [[ "$index_status" == "200" ]]; then
        print_message $GREEN "✅ /admin/ 返回 200"
    else
        print_message $RED "❌ /admin/ 返回 $index_status"
    fi
    
    # 测试一个具体的 JS 文件
    if [[ -f "static/admin/index.html" ]]; then
        local js_file=$(grep -o 'assets/[^"]*\.js' static/admin/index.html | head -1)
        if [[ -n "$js_file" ]]; then
            local js_status=$(curl -s -k -o /dev/null -w "%{http_code}" "https://$domain/admin/$js_file" || echo "000")
            if [[ "$js_status" == "200" ]]; then
                print_message $GREEN "✅ JS 文件 $js_file 返回 200"
            else
                print_message $RED "❌ JS 文件 $js_file 返回 $js_status"
            fi
        fi
    fi
else
    print_message $YELLOW "⚠️  无法测试访问（缺少 curl）"
fi

echo ""

# 6. 提供解决方案
print_message $BLUE "💡 问题分析和解决方案"
echo ""

# 检查是否是文件不匹配问题
if [[ -f "static/admin/index.html" ]]; then
    local referenced_files=$(grep -o 'assets/[^"]*\.\(js\|css\)' static/admin/index.html)
    local missing_files=0
    
    for file in $referenced_files; do
        if [[ ! -f "static/admin/$file" ]]; then
            ((missing_files++))
        fi
    done
    
    if [[ $missing_files -gt 0 ]]; then
        print_message $RED "❌ 发现问题：index.html 引用的文件与实际文件不匹配"
        print_message $YELLOW "🔧 解决方案：重新构建管理后台"
        echo ""
        echo "执行以下命令："
        echo "1. ./scripts/fix-admin-assets.sh --clean --rebuild"
        echo "2. 或手动执行："
        echo "   docker-compose --env-file=docker.env build admin-builder"
        echo "   docker-compose --env-file=docker.env --profile build up admin-builder"
        echo "   docker-compose --env-file=docker.env restart caddy"
    else
        print_message $GREEN "✅ 文件匹配正常"
        print_message $YELLOW "🔧 可能的解决方案："
        echo "1. 清理浏览器缓存（强制刷新）"
        echo "2. 重启 Caddy 服务：docker-compose --env-file=docker.env restart caddy"
        echo "3. 检查网络连接和防火墙设置"
    fi
else
    print_message $RED "❌ 发现问题：管理后台文件缺失"
    print_message $YELLOW "🔧 解决方案：重新构建管理后台"
    echo ""
    echo "执行以下命令："
    echo "./scripts/fix-admin-assets.sh --clean --rebuild"
fi

echo ""

# 7. 浏览器缓存清理说明
print_message $BLUE "🧹 浏览器缓存清理说明"
echo ""
print_message $YELLOW "如果文件已经正确部署，但仍然出现 404 错误，请清理浏览器缓存："
echo ""
echo "方法一：强制刷新"
echo "- Chrome/Edge/Firefox: Ctrl+Shift+R (Windows) 或 Cmd+Shift+R (Mac)"
echo "- Safari: Cmd+Option+R"
echo ""
echo "方法二：开发者工具"
echo "1. 按 F12 打开开发者工具"
echo "2. 右键点击刷新按钮"
echo "3. 选择 '清空缓存并硬性重新加载'"
echo ""
echo "方法三：无痕模式"
echo "- 使用浏览器的无痕/隐私模式访问"

echo ""
print_message $GREEN "🎯 诊断完成！请根据上述分析执行相应的解决方案。"
