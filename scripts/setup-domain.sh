#!/bin/bash

# 蔚之领域项目域名配置脚本

set -e

echo "🌐 蔚之领域项目域名配置向导"
echo "================================"
echo ""

# 检查环境变量文件是否存在
if [ ! -f "docker.env" ]; then
    echo "❌ docker.env 文件不存在"
    exit 1
fi

# 显示当前域名配置
current_domain=$(grep "DOMAIN=" docker.env | cut -d'=' -f2)
echo "📋 当前域名配置: $current_domain"
echo ""

# 提示用户选择配置方式
echo "请选择配置方式："
echo "1) 使用自定义域名（生产环境，自动HTTPS）"
echo "2) 使用localhost（开发环境，自签名证书）"
echo "3) 查看当前配置"
echo ""

read -p "请输入选择 (1-3): " choice

case $choice in
    1)
        echo ""
        echo "🔧 配置自定义域名"
        echo "=================="
        echo ""
        echo "⚠️  注意事项："
        echo "- 确保域名已解析到此服务器IP"
        echo "- 确保防火墙开放80和443端口"
        echo "- Caddy会自动申请Let's Encrypt SSL证书"
        echo ""
        
        read -p "请输入你的域名（例如：weishi.com 或 www.weishi.com）: " domain
        
        if [ -z "$domain" ]; then
            echo "❌ 域名不能为空"
            exit 1
        fi
        
        # 验证域名格式
        if [[ ! $domain =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$ ]]; then
            echo "❌ 域名格式不正确"
            exit 1
        fi
        
        # 更新环境变量文件
        sed -i.bak "s/DOMAIN=.*/DOMAIN=$domain/" docker.env
        
        echo ""
        echo "✅ 域名已配置为: $domain"
        echo ""
        
        # 检查域名解析
        echo "🔍 检查域名解析..."
        if nslookup $domain > /dev/null 2>&1; then
            echo "✅ 域名解析正常"
            resolved_ip=$(nslookup $domain | grep -A1 "Name:" | tail -1 | awk '{print $2}')
            echo "📍 解析IP: $resolved_ip"
        else
            echo "⚠️  域名解析检查失败，请确保域名已正确解析"
        fi
        
        echo ""
        echo "📋 接下来的步骤："
        echo "1. 确保防火墙开放端口: sudo ufw allow 80 && sudo ufw allow 443"
        echo "2. 启动服务: docker-compose --env-file=docker.env --profile caddy up -d"
        echo "3. 查看日志: docker-compose --env-file=docker.env logs -f caddy"
        echo "4. 访问网站: https://$domain"
        ;;
        
    2)
        echo ""
        echo "🔧 配置localhost（开发环境）"
        echo "=========================="
        
        # 更新为localhost
        sed -i.bak "s/DOMAIN=.*/DOMAIN=localhost/" docker.env
        
        echo "✅ 域名已配置为: localhost"
        echo ""
        echo "📋 接下来的步骤："
        echo "1. 启动服务: docker-compose --env-file=docker.env --profile caddy up -d"
        echo "2. 访问网站: http://localhost （会自动重定向到 https://localhost）"
        echo "3. ⚠️  浏览器会显示证书警告（自签名证书），点击'高级'继续访问即可"
        ;;
        
    3)
        echo ""
        echo "📋 当前配置信息"
        echo "==============="
        echo ""
        echo "域名配置:"
        grep "DOMAIN=" docker.env
        echo ""
        echo "端口配置:"
        grep "CADDY.*PORT=" docker.env
        echo ""
        echo "其他相关配置:"
        grep -E "(CORS_ORIGIN|NUXT_PUBLIC_API_BASE)" docker.env
        ;;
        
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "🎯 配置完成！"

# 询问是否立即启动服务
if [ "$choice" = "1" ] || [ "$choice" = "2" ]; then
    echo ""
    read -p "是否立即启动服务？(y/N): " start_now
    
    if [[ $start_now =~ ^[Yy]$ ]]; then
        echo ""
        echo "🚀 启动服务..."
        
        # 停止现有服务
        echo "🛑 停止现有服务..."
        docker-compose --env-file=docker.env down 2>/dev/null || true
        
        # 启动新服务
        echo "🔨 启动服务（包含Caddy）..."
        docker-compose --env-file=docker.env --profile caddy up -d
        
        echo ""
        echo "⏳ 等待服务启动..."
        sleep 10
        
        # 检查服务状态
        echo "📊 服务状态:"
        docker-compose --env-file=docker.env ps
        
        echo ""
        echo "📝 查看Caddy日志（观察证书申请过程）:"
        echo "docker-compose --env-file=docker.env logs -f caddy"
        
        if [ "$choice" = "1" ]; then
            domain=$(grep "DOMAIN=" docker.env | cut -d'=' -f2)
            echo ""
            echo "🌐 访问地址: https://$domain"
        else
            echo ""
            echo "🌐 访问地址: https://localhost"
        fi
    fi
fi

echo ""
echo "🎉 配置向导完成！" 