#!/bin/bash

# 生成数据库初始化脚本
# 支持动态设置管理员密码

set -e

echo "🔧 生成数据库初始化脚本..."

# 配置
ADMIN_USERNAME="${ADMIN_USERNAME:-admin}"
ADMIN_PASSWORD="${ADMIN_PASSWORD:-admin123}"
SOURCE_SQL="server-go/scripts/database_full.sql"
TARGET_SQL="server-go/scripts/init_database.sql"

# 检查源文件
if [ ! -f "$SOURCE_SQL" ]; then
    echo "❌ 源SQL文件不存在: $SOURCE_SQL"
    echo "请先运行: ./scripts/export-database.sh"
    exit 1
fi

echo "📝 配置信息:"
echo "   管理员用户名: $ADMIN_USERNAME"
echo "   管理员密码: [已设置]"
echo "   源文件: $SOURCE_SQL"
echo "   目标文件: $TARGET_SQL"

# 生成密码哈希 (简单的SHA256，实际应用中应使用bcrypt)
password_hash=$(echo -n "$ADMIN_PASSWORD" | openssl dgst -sha256 -binary | openssl base64)

echo "🔄 处理SQL文件..."

# 复制源文件（保持数据库名为 weizhi）
cp "$SOURCE_SQL" "$TARGET_SQL.tmp"

# 替换管理员密码
# 查找并替换admin用户的密码
sed -i.bak "s/INSERT INTO \`admin_users\` VALUES (1,'admin','[^']*'/INSERT INTO \`admin_users\` VALUES (1,'$ADMIN_USERNAME','$password_hash'/g" "$TARGET_SQL.tmp"

# 添加自定义头部
cat > "$TARGET_SQL" << EOF
-- =====================================================
-- 蔚之领域智能科技项目数据库初始化文件
-- 包含表结构和初始数据
-- 生成时间: $(date '+%Y-%m-%d %H:%M:%S')
-- 管理员账号: $ADMIN_USERNAME
-- =====================================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

EOF

# 添加处理后的内容（跳过原有的头部）
tail -n +10 "$TARGET_SQL.tmp" >> "$TARGET_SQL"

# 添加管理员账号更新语句
cat >> "$TARGET_SQL" << EOF

-- =====================================================
-- 管理员账号配置更新
-- =====================================================

-- 更新管理员密码（如果存在）
UPDATE admin_users 
SET password = '$password_hash', updated_at = NOW() 
WHERE username = '$ADMIN_USERNAME';

-- 如果管理员不存在则插入
INSERT IGNORE INTO admin_users (username, password, email, status, created_at, updated_at)
VALUES ('$ADMIN_USERNAME', '$password_hash', '<EMAIL>', 1, NOW(), NOW());

-- 确保管理员拥有超级管理员角色
INSERT IGNORE INTO admin_user_roles (user_id, role_id)
SELECT u.id, r.id
FROM admin_users u, admin_roles r
WHERE u.username = '$ADMIN_USERNAME' AND r.name = 'super_admin';

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 显示完成信息
SELECT '数据库初始化完成！' as message;
SELECT CONCAT('管理员账号: ', '$ADMIN_USERNAME') as admin_account;
SELECT '请首次登录后立即修改密码' as security_notice;
EOF

# 清理临时文件
rm -f "$TARGET_SQL.tmp" "$TARGET_SQL.tmp.bak"

echo "✅ 初始化脚本生成完成！"
echo ""
echo "📋 文件信息:"
echo "   文件路径: $TARGET_SQL"
echo "   文件大小: $(ls -lh "$TARGET_SQL" | awk '{print $5}')"
echo "   行数: $(wc -l < "$TARGET_SQL")"

echo ""
echo "🔍 验证内容:"
echo "   数据库名称: $(grep -c 'weizhi_db' "$TARGET_SQL") 处"
echo "   管理员配置: $(grep -c "$ADMIN_USERNAME" "$TARGET_SQL") 处"

echo ""
echo "✅ 生成完成！可以用于数据库初始化。"
