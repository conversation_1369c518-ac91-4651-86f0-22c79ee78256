#!/bin/bash

# 管理员账号设置脚本
# 支持通过环境变量或交互式方式设置管理员密码

set -e

echo "🔐 管理员账号设置"

# 默认配置
DEFAULT_USERNAME="admin"
DEFAULT_PASSWORD="admin123"

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="weishi"
DB_PASSWORD="Ydb3344%"
DB_NAME="weizhi"

# 读取环境变量
ADMIN_USERNAME="${ADMIN_USERNAME:-$DEFAULT_USERNAME}"
ADMIN_PASSWORD="${ADMIN_PASSWORD:-}"

# 配置文件路径
CONFIG_FILE="server-go/config/admin.yaml"
ENV_FILE="docker.env"

# 创建配置目录
mkdir -p "$(dirname "$CONFIG_FILE")"

# 显示当前配置
show_current_config() {
    echo ""
    echo "📊 当前管理员配置:"
    
    # 检查数据库中的管理员
    if docker ps --format "table {{.Names}}" | grep -q "weishi-mysql"; then
        if docker exec weishi-mysql mysql -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT username, created_at FROM admin_users WHERE username='$ADMIN_USERNAME';" 2>/dev/null; then
            echo "✅ 数据库中存在管理员账号"
        else
            echo "❌ 数据库中不存在管理员账号"
        fi
    elif command -v mysql &> /dev/null; then
        if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT username, created_at FROM admin_users WHERE username='$ADMIN_USERNAME';" 2>/dev/null; then
            echo "✅ 数据库中存在管理员账号"
        else
            echo "❌ 数据库中不存在管理员账号"
        fi
    else
        echo "⚠️  无法检查数据库状态（需要 MySQL 客户端或 Docker 容器）"
    fi
    
    # 检查配置文件
    if [ -f "$CONFIG_FILE" ]; then
        echo "✅ 管理员配置文件存在: $CONFIG_FILE"
    else
        echo "❌ 管理员配置文件不存在"
    fi
    
    # 检查环境变量
    if [ -n "$ADMIN_PASSWORD" ]; then
        echo "✅ 环境变量中设置了管理员密码"
    else
        echo "⚠️  环境变量中未设置管理员密码"
    fi
}

# 生成密码哈希
generate_password_hash() {
    local password="$1"
    # 使用 Go 程序生成 bcrypt 哈希
    cd server-go
    go run -c "
package main

import (
    \"fmt\"
    \"golang.org/x/crypto/bcrypt\"
)

func main() {
    hash, _ := bcrypt.GenerateFromPassword([]byte(\"$password\"), bcrypt.DefaultCost)
    fmt.Print(string(hash))
}
" 2>/dev/null || echo "failed"
}

# 交互式设置密码
interactive_setup() {
    echo ""
    echo "🔧 交互式设置管理员密码"
    echo ""
    
    # 输入用户名
    read -p "管理员用户名 [$ADMIN_USERNAME]: " input_username
    ADMIN_USERNAME="${input_username:-$ADMIN_USERNAME}"
    
    # 输入密码
    while true; do
        echo ""
        read -s -p "请输入管理员密码 (至少8位): " password1
        echo ""
        read -s -p "请确认管理员密码: " password2
        echo ""
        
        if [ "$password1" != "$password2" ]; then
            echo "❌ 密码不匹配，请重新输入"
            continue
        fi
        
        if [ ${#password1} -lt 8 ]; then
            echo "❌ 密码长度至少8位，请重新输入"
            continue
        fi
        
        ADMIN_PASSWORD="$password1"
        break
    done
}

# 创建配置文件
create_config_file() {
    echo ""
    echo "📝 创建管理员配置文件..."
    
    cat > "$CONFIG_FILE" << EOF
# 管理员账号配置
# 此文件包含敏感信息，请勿提交到版本控制系统

admin:
  # 默认管理员账号
  username: "$ADMIN_USERNAME"
  password: "$ADMIN_PASSWORD"
  
  # 密码策略
  password_policy:
    min_length: 8
    require_uppercase: true
    require_lowercase: true
    require_numbers: true
    require_symbols: false
  
  # 安全设置
  security:
    force_password_change: true  # 首次登录强制修改密码
    password_expire_days: 90     # 密码过期天数
    max_login_attempts: 5        # 最大登录尝试次数
    lockout_duration: 30         # 锁定时长（分钟）

# 生成时间
generated_at: "$(date '+%Y-%m-%d %H:%M:%S')"
EOF

    echo "✅ 配置文件已创建: $CONFIG_FILE"
}

# 更新环境变量文件
update_env_file() {
    echo ""
    echo "📝 更新环境变量文件..."
    
    if [ -f "$ENV_FILE" ]; then
        # 检查是否已存在管理员配置
        if grep -q "ADMIN_USERNAME" "$ENV_FILE"; then
            # 更新现有配置
            sed -i.bak "s/ADMIN_USERNAME=.*/ADMIN_USERNAME=$ADMIN_USERNAME/" "$ENV_FILE"
            sed -i.bak "s/ADMIN_PASSWORD=.*/ADMIN_PASSWORD=$ADMIN_PASSWORD/" "$ENV_FILE"
            rm -f "${ENV_FILE}.bak"
        else
            # 添加新配置
            echo "" >> "$ENV_FILE"
            echo "# 管理员账号配置" >> "$ENV_FILE"
            echo "ADMIN_USERNAME=$ADMIN_USERNAME" >> "$ENV_FILE"
            echo "ADMIN_PASSWORD=$ADMIN_PASSWORD" >> "$ENV_FILE"
        fi
        echo "✅ 环境变量文件已更新: $ENV_FILE"
    else
        echo "⚠️  环境变量文件不存在: $ENV_FILE"
    fi
}

# 更新数据库
update_database() {
    echo ""
    echo "🗄️  更新数据库中的管理员账号..."
    
    # 生成密码哈希
    echo "   生成密码哈希..."
    password_hash=$(echo -n "$ADMIN_PASSWORD" | openssl dgst -sha256 -binary | openssl base64)
    
    # 检查是否有 Docker 容器运行
    if docker ps --format "table {{.Names}}" | grep -q "weishi-mysql"; then
        echo "   使用 Docker 容器连接数据库..."
        # 使用 Docker 容器执行 MySQL 命令
        docker exec weishi-mysql mysql -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" << EOF
-- 更新或插入管理员账号
INSERT INTO admin_users (username, password, email, status, created_at, updated_at)
VALUES ('$ADMIN_USERNAME', '$password_hash', '<EMAIL>', 'active', NOW(), NOW())
ON DUPLICATE KEY UPDATE
    password = '$password_hash',
    updated_at = NOW();

-- 确保管理员拥有超级管理员角色
INSERT IGNORE INTO admin_user_roles (user_id, role_id)
SELECT u.id, r.id
FROM admin_users u, admin_roles r
WHERE u.username = '$ADMIN_USERNAME' AND r.code = 'super_admin';
EOF
    else
        echo "   MySQL 容器未运行，尝试直接连接..."
        # 如果容器未运行，尝试直接连接
        if command -v mysql &> /dev/null; then
            mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" << EOF
-- 更新或插入管理员账号
INSERT INTO admin_users (username, password, email, status, created_at, updated_at)
VALUES ('$ADMIN_USERNAME', '$password_hash', '<EMAIL>', 'active', NOW(), NOW())
ON DUPLICATE KEY UPDATE
    password = '$password_hash',
    updated_at = NOW();

-- 确保管理员拥有超级管理员角色
INSERT IGNORE INTO admin_user_roles (user_id, role_id)
SELECT u.id, r.id
FROM admin_users u, admin_roles r
WHERE u.username = '$ADMIN_USERNAME' AND r.code = 'super_admin';
EOF
        else
            echo "❌ 无法连接数据库：MySQL 客户端未安装且 Docker 容器未运行"
            echo "请先启动 Docker 服务：docker-compose --env-file=docker.env up -d"
            return 1
        fi
    fi

    if [ $? -eq 0 ]; then
        echo "✅ 数据库更新成功"
    else
        echo "❌ 数据库更新失败"
        return 1
    fi
}

# 验证设置
verify_setup() {
    echo ""
    echo "🔍 验证管理员设置..."
    
    # 检查数据库
    if docker ps --format "table {{.Names}}" | grep -q "weishi-mysql"; then
        result=$(docker exec weishi-mysql mysql -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT COUNT(*) as count FROM admin_users WHERE username='$ADMIN_USERNAME';" 2>/dev/null | tail -1)
    elif command -v mysql &> /dev/null; then
        result=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" \
            -e "SELECT COUNT(*) as count FROM admin_users WHERE username='$ADMIN_USERNAME';" 2>/dev/null | tail -1)
    else
        echo "⚠️  无法验证数据库，请手动检查"
        return 0
    fi
    
    if [ "$result" = "1" ]; then
        echo "✅ 数据库验证通过"
    else
        echo "❌ 数据库验证失败"
        return 1
    fi
    
    # 检查配置文件
    if [ -f "$CONFIG_FILE" ] && grep -q "$ADMIN_USERNAME" "$CONFIG_FILE"; then
        echo "✅ 配置文件验证通过"
    else
        echo "❌ 配置文件验证失败"
    fi
}

# 显示完成信息
show_completion() {
    echo ""
    echo "🎉 管理员账号设置完成！"
    echo ""
    echo "📋 账号信息:"
    echo "   用户名: $ADMIN_USERNAME"
    echo "   密码: [已设置]"
    echo ""
    echo "📁 相关文件:"
    echo "   配置文件: $CONFIG_FILE"
    echo "   环境变量: $ENV_FILE"
    echo ""
    echo "🌐 登录地址:"
    echo "   管理后台: http://localhost:5173"
    echo "   开发环境: http://localhost:5173"
    echo ""
    echo "⚠️  安全提醒:"
    echo "   1. 请妥善保管管理员密码"
    echo "   2. 首次登录后请立即修改密码"
    echo "   3. 定期更换密码"
    echo "   4. 不要将配置文件提交到版本控制"
}

# 主函数
main() {
    echo "管理员账号设置脚本"
    echo "===================="
    
    # 显示当前配置
    show_current_config
    
    # 检查是否通过环境变量设置
    if [ -n "$ADMIN_PASSWORD" ]; then
        echo ""
        echo "✅ 检测到环境变量中的管理员密码"
    else
        # 交互式设置
        interactive_setup
    fi
    
    # 创建配置文件
    create_config_file
    
    # 更新环境变量文件
    update_env_file
    
    # 更新数据库
    update_database
    
    # 验证设置
    verify_setup
    
    # 显示完成信息
    show_completion
}

# 显示帮助
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "管理员账号设置脚本"
    echo ""
    echo "用法:"
    echo "  $0                    # 交互式设置"
    echo "  ADMIN_PASSWORD=xxx $0 # 通过环境变量设置"
    echo ""
    echo "环境变量:"
    echo "  ADMIN_USERNAME        # 管理员用户名 (默认: admin)"
    echo "  ADMIN_PASSWORD        # 管理员密码"
    echo ""
    echo "示例:"
    echo "  $0"
    echo "  ADMIN_PASSWORD=mypassword123 $0"
    exit 0
fi

# 执行主函数
main
