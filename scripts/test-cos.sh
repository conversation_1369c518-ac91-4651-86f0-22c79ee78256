#!/bin/bash

# 腾讯云COS配置测试脚本
# 用于验证COS配置是否正确

set -e

echo "🔍 开始COS配置测试..."

# 读取环境变量
ENV_FILE="docker.env"
if [ -f "$ENV_FILE" ]; then
    source "$ENV_FILE"
else
    echo "❌ 环境变量文件 $ENV_FILE 不存在"
    exit 1
fi

# 检查必要的COS配置
echo "📋 检查COS配置..."

required_vars=("COS_SECRET_ID" "COS_SECRET_KEY" "COS_REGION" "COS_BUCKET")
missing_vars=()

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -gt 0 ]; then
    echo "❌ 缺少必要的COS配置变量:"
    for var in "${missing_vars[@]}"; do
        echo "   - $var"
    done
    echo ""
    echo "📖 配置指南:"
    echo "   1. 登录腾讯云控制台: https://console.cloud.tencent.com/"
    echo "   2. 进入访问管理 -> API密钥管理"
    echo "   3. 创建或查看SecretId和SecretKey"
    echo "   4. 进入对象存储COS控制台创建存储桶"
    echo "   5. 在 $ENV_FILE 中填写相关配置"
    exit 1
fi

echo "✅ COS基础配置检查通过"

# 显示配置信息
echo ""
echo "📊 当前COS配置:"
echo "   SecretID: ${COS_SECRET_ID:0:8}***"
echo "   SecretKey: ${COS_SECRET_KEY:0:8}***"
echo "   Region: $COS_REGION"
echo "   Bucket: $COS_BUCKET"
echo "   Domain: ${COS_DOMAIN:-未配置}"

# 检查文件上传配置
echo ""
echo "📁 文件上传配置:"
echo "   最大文件大小: ${MAX_FILE_SIZE:-52428800} 字节 ($(( ${MAX_FILE_SIZE:-52428800} / 1024 / 1024 ))MB)"
echo "   允许的文件类型: ${ALLOWED_FILE_TYPES:-默认类型}"

# 检查后端服务是否运行
echo ""
echo "🔍 检查后端服务状态..."
if docker-compose --env-file="$ENV_FILE" ps server | grep -q "Up"; then
    echo "✅ 后端服务正在运行"
    
    # 尝试访问文件上传API
    echo ""
    echo "🌐 测试文件上传API端点..."
    SERVER_PORT=${SERVER_PORT:-3001}
    
    # 测试健康检查
    if curl -f -s "http://localhost:$SERVER_PORT/api/health" > /dev/null; then
        echo "✅ 后端API健康检查通过"
        
        # 检查文件上传端点（需要认证，预期返回401）
        response=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:$SERVER_PORT/api/files/upload")
        if [ "$response" = "401" ]; then
            echo "✅ 文件上传API端点可访问（需要认证）"
        else
            echo "⚠️  文件上传API端点响应异常: $response"
        fi
    else
        echo "❌ 后端API健康检查失败"
    fi
else
    echo "❌ 后端服务未运行"
    echo "请先启动后端服务: docker-compose --env-file=$ENV_FILE up -d server"
fi

# 生成测试文件上传的curl命令示例
echo ""
echo "🧪 文件上传测试命令示例:"
echo "# 1. 首先登录获取token:"
echo "curl -X POST http://localhost:$SERVER_PORT/api/admin/auth/login \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{\"username\":\"admin\",\"password\":\"admin123\"}'"
echo ""
echo "# 2. 使用token上传文件:"
echo "curl -X POST http://localhost:$SERVER_PORT/api/files/upload \\"
echo "  -H \"Authorization: Bearer YOUR_TOKEN\" \\"
echo "  -F \"file=@/path/to/your/file.jpg\" \\"
echo "  -F \"module=test\""

echo ""
echo "📋 COS配置验证完成！"

if [ ${#missing_vars[@]} -eq 0 ]; then
    echo ""
    echo "🎉 COS配置完整，可以进行文件上传测试"
    echo ""
    echo "🔧 下一步操作："
    echo "   1. 确保后端服务正在运行"
    echo "   2. 使用管理后台测试文件上传功能"
    echo "   3. 检查COS存储桶中是否有上传的文件"
    exit 0
else
    echo ""
    echo "⚠️  请完善COS配置后重新测试"
    exit 1
fi
