#!/bin/bash

# 管理后台静态资源问题修复脚本
# 功能：重新构建管理后台并确保静态资源正确部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：显示帮助信息
show_help() {
    echo "管理后台静态资源问题修复脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  --clean                 清理旧的构建文件"
    echo "  --rebuild               强制重新构建"
    echo "  --check                 检查当前状态"
    echo ""
    echo "示例:"
    echo "  $0                      # 标准修复流程"
    echo "  $0 --clean --rebuild    # 完全重新构建"
    echo "  $0 --check              # 仅检查状态"
}

# 函数：检查当前状态
check_status() {
    print_message $BLUE "🔍 检查当前状态..."
    echo ""
    
    # 检查静态文件目录
    if [[ -d "static/admin" ]]; then
        print_message $GREEN "✅ 静态文件目录存在"
        
        # 检查关键文件
        if [[ -f "static/admin/index.html" ]]; then
            print_message $GREEN "✅ index.html 存在"
            
            # 显示引用的资源文件
            echo "   引用的 JS 文件:"
            grep -o 'assets/[^"]*\.js' static/admin/index.html | head -3
            echo "   引用的 CSS 文件:"
            grep -o 'assets/[^"]*\.css' static/admin/index.html | head -3
        else
            print_message $RED "❌ index.html 不存在"
        fi
        
        # 检查 assets 目录
        if [[ -d "static/admin/assets" ]]; then
            local js_count=$(find static/admin/assets -name "*.js" | wc -l)
            local css_count=$(find static/admin/assets -name "*.css" | wc -l)
            print_message $GREEN "✅ assets 目录存在 ($js_count JS, $css_count CSS)"
        else
            print_message $RED "❌ assets 目录不存在"
        fi
    else
        print_message $RED "❌ 静态文件目录不存在"
    fi
    
    echo ""
    
    # 检查 Docker 容器状态
    print_message $BLUE "🐳 检查 Docker 容器状态..."
    
    if docker ps | grep -q "weishi-caddy"; then
        print_message $GREEN "✅ Caddy 容器运行中"
    else
        print_message $YELLOW "⚠️  Caddy 容器未运行"
    fi
    
    if docker ps | grep -q "weishi-server"; then
        print_message $GREEN "✅ 后端服务运行中"
    else
        print_message $YELLOW "⚠️  后端服务未运行"
    fi
    
    echo ""
}

# 函数：清理旧文件
clean_old_files() {
    print_message $BLUE "🧹 清理旧的构建文件..."
    
    # 清理静态文件目录
    if [[ -d "static/admin" ]]; then
        print_message $YELLOW "   删除旧的静态文件..."
        rm -rf static/admin/*
        print_message $GREEN "✅ 旧文件已清理"
    fi
    
    # 清理 admin 构建目录
    if [[ -d "admin/dist" ]]; then
        print_message $YELLOW "   删除 admin/dist 目录..."
        rm -rf admin/dist
        print_message $GREEN "✅ 构建目录已清理"
    fi
    
    # 清理 Docker 构建缓存
    print_message $YELLOW "   清理 Docker 构建缓存..."
    docker builder prune -f >/dev/null 2>&1 || true
    print_message $GREEN "✅ Docker 缓存已清理"
}

# 函数：重新构建管理后台
rebuild_admin() {
    print_message $BLUE "🔨 重新构建管理后台..."
    
    # 确保静态文件目录存在
    mkdir -p static/admin
    
    # 停止并删除旧的构建容器
    print_message $YELLOW "   停止旧的构建容器..."
    docker stop weishi-admin-builder 2>/dev/null || true
    docker rm weishi-admin-builder 2>/dev/null || true
    
    # 重新构建管理后台
    print_message $YELLOW "   构建管理后台镜像..."
    docker-compose --env-file=docker.env build admin-builder
    
    # 运行构建容器
    print_message $YELLOW "   运行构建容器..."
    docker-compose --env-file=docker.env --profile build up admin-builder
    
    # 等待构建完成
    sleep 5
    
    # 检查构建结果
    if [[ -f "static/admin/index.html" ]]; then
        print_message $GREEN "✅ 管理后台构建成功"
        
        # 显示新的资源文件信息
        echo "   新的资源文件:"
        grep -o 'assets/[^"]*\.js' static/admin/index.html | head -3
    else
        print_message $RED "❌ 管理后台构建失败"
        return 1
    fi
}

# 函数：重启 Caddy 服务
restart_caddy() {
    print_message $BLUE "🔄 重启 Caddy 服务..."
    
    # 重启 Caddy 容器以重新加载静态文件
    if docker ps | grep -q "weishi-caddy"; then
        print_message $YELLOW "   重启 Caddy 容器..."
        docker-compose --env-file=docker.env restart caddy
        
        # 等待服务启动
        sleep 10
        
        print_message $GREEN "✅ Caddy 服务已重启"
    else
        print_message $YELLOW "   启动 Caddy 服务..."
        docker-compose --env-file=docker.env up -d caddy
        
        # 等待服务启动
        sleep 15
        
        print_message $GREEN "✅ Caddy 服务已启动"
    fi
}

# 函数：验证修复结果
verify_fix() {
    print_message $BLUE "✅ 验证修复结果..."
    
    # 检查静态文件
    if [[ -f "static/admin/index.html" ]]; then
        local js_files=$(grep -o 'assets/[^"]*\.js' static/admin/index.html)
        local css_files=$(grep -o 'assets/[^"]*\.css' static/admin/index.html)
        
        print_message $GREEN "✅ index.html 文件正常"
        
        # 检查引用的文件是否存在
        local missing_files=0
        
        for file in $js_files $css_files; do
            if [[ ! -f "static/admin/$file" ]]; then
                print_message $RED "❌ 缺少文件: $file"
                ((missing_files++))
            fi
        done
        
        if [[ $missing_files -eq 0 ]]; then
            print_message $GREEN "✅ 所有引用的资源文件都存在"
        else
            print_message $RED "❌ 有 $missing_files 个文件缺失"
            return 1
        fi
    else
        print_message $RED "❌ index.html 文件不存在"
        return 1
    fi
    
    # 测试访问
    print_message $BLUE "🌐 测试管理后台访问..."
    
    local domain=$(grep "^DOMAIN=" docker.env 2>/dev/null | cut -d'=' -f2 || echo "localhost")
    local test_url="https://$domain/admin/"
    
    echo "   测试 URL: $test_url"
    
    # 简单的连通性测试
    if command -v curl >/dev/null 2>&1; then
        if curl -s -k -o /dev/null -w "%{http_code}" "$test_url" | grep -q "200\|301\|302"; then
            print_message $GREEN "✅ 管理后台可以访问"
        else
            print_message $YELLOW "⚠️  管理后台访问可能有问题，请手动检查"
        fi
    else
        print_message $YELLOW "⚠️  无法测试访问（缺少 curl），请手动检查"
    fi
}

# 函数：清理浏览器缓存提示
show_cache_clear_instructions() {
    print_message $BLUE "🧹 清理浏览器缓存说明"
    echo ""
    print_message $YELLOW "如果问题仍然存在，请清理浏览器缓存："
    echo ""
    echo "Chrome/Edge:"
    echo "1. 按 F12 打开开发者工具"
    echo "2. 右键点击刷新按钮"
    echo "3. 选择 '清空缓存并硬性重新加载'"
    echo ""
    echo "Firefox:"
    echo "1. 按 Ctrl+Shift+R 强制刷新"
    echo "2. 或按 F12 -> 网络标签 -> 点击垃圾桶图标清理缓存"
    echo ""
    echo "Safari:"
    echo "1. 按 Cmd+Option+R 强制刷新"
    echo "2. 或开发菜单 -> 清空缓存"
    echo ""
}

# 主函数
main() {
    local clean_only=false
    local rebuild_only=false
    local check_only=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --clean)
                clean_only=true
                shift
                ;;
            --rebuild)
                rebuild_only=true
                shift
                ;;
            --check)
                check_only=true
                shift
                ;;
            *)
                print_message $RED "❌ 未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_message $BLUE "🔧 管理后台静态资源问题修复脚本"
    echo ""
    
    # 检查当前状态
    check_status
    
    # 如果只是检查，直接退出
    if [[ "$check_only" == "true" ]]; then
        exit 0
    fi
    
    # 执行修复流程
    if [[ "$clean_only" == "true" ]] || [[ "$rebuild_only" == "true" ]]; then
        clean_old_files
    fi
    
    if [[ "$rebuild_only" == "true" ]] || [[ "$clean_only" != "true" ]]; then
        rebuild_admin
        restart_caddy
        
        echo ""
        verify_fix
        
        echo ""
        show_cache_clear_instructions
    fi
    
    echo ""
    print_message $GREEN "🎉 修复完成！"
    print_message $BLUE "💡 如果问题仍然存在，请："
    echo "1. 清理浏览器缓存"
    echo "2. 检查网络连接"
    echo "3. 查看浏览器开发者工具的网络标签"
}

# 执行主函数
main "$@"
