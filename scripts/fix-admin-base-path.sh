#!/bin/bash

# 修复管理后台资源路径问题
# 问题：管理后台资源文件路径缺少 /admin/ 前缀
# 解决：重新构建时设置正确的 base 路径

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_message $BLUE "🔧 修复管理后台资源路径问题"
echo ""

# 1. 检查当前问题
print_message $BLUE "1️⃣ 检查当前问题..."
if [[ -f "static/admin/index.html" ]]; then
    print_message $GREEN "✅ 找到当前的 index.html"
    
    # 检查资源路径
    echo "   当前资源路径:"
    grep -o 'src="[^"]*"' static/admin/index.html | head -3
    grep -o 'href="[^"]*\.css"' static/admin/index.html | head -2
    
    # 检查是否缺少 /admin/ 前缀
    if grep -q 'src="/assets/' static/admin/index.html; then
        print_message $RED "❌ 发现问题：资源路径缺少 /admin/ 前缀"
        echo "   错误示例: src=\"/assets/index-xxx.js\""
        echo "   应该是: src=\"/admin/assets/index-xxx.js\""
    elif grep -q 'src="/admin/assets/' static/admin/index.html; then
        print_message $GREEN "✅ 资源路径正确，包含 /admin/ 前缀"
        echo "   如果仍有问题，可能是浏览器缓存导致"
    else
        print_message $YELLOW "⚠️  无法确定资源路径状态"
    fi
else
    print_message $RED "❌ 未找到 static/admin/index.html"
fi

echo ""

# 2. 清理旧文件
print_message $BLUE "2️⃣ 清理旧的构建文件..."
rm -rf static/admin/*
print_message $GREEN "✅ 旧文件已清理"

# 3. 停止旧容器
print_message $BLUE "3️⃣ 停止旧的构建容器..."
docker stop weishi-admin-builder 2>/dev/null || true
docker rm weishi-admin-builder 2>/dev/null || true
print_message $GREEN "✅ 旧容器已清理"

# 4. 重新构建镜像（确保使用最新的 Dockerfile）
print_message $BLUE "4️⃣ 重新构建管理后台镜像..."
print_message $YELLOW "   使用 --no-cache 确保获取最新配置..."
docker-compose --env-file=docker.env build --no-cache admin-builder

if [[ $? -eq 0 ]]; then
    print_message $GREEN "✅ 镜像构建成功"
else
    print_message $RED "❌ 镜像构建失败"
    exit 1
fi

# 5. 运行构建容器
print_message $BLUE "5️⃣ 运行构建容器..."
docker-compose --env-file=docker.env --profile build up admin-builder

if [[ $? -eq 0 ]]; then
    print_message $GREEN "✅ 构建容器运行完成"
else
    print_message $RED "❌ 构建容器运行失败"
    exit 1
fi

# 6. 验证构建结果
print_message $BLUE "6️⃣ 验证构建结果..."
if [[ -f "static/admin/index.html" ]]; then
    print_message $GREEN "✅ index.html 已生成"
    
    echo "   新的资源路径:"
    grep -o 'src="[^"]*"' static/admin/index.html | head -3
    grep -o 'href="[^"]*\.css"' static/admin/index.html | head -2
    
    # 验证路径是否正确
    if grep -q 'src="/admin/assets/' static/admin/index.html; then
        print_message $GREEN "✅ 资源路径正确，包含 /admin/ 前缀"
    else
        print_message $RED "❌ 资源路径仍然不正确"
        echo "   请检查 vite.config.ts 中的 base 配置"
        exit 1
    fi
    
    # 检查引用的文件是否存在
    echo ""
    print_message $BLUE "   检查文件完整性..."
    local missing=0
    for file in $(grep -o '/admin/assets/[^"]*\.\(js\|css\)' static/admin/index.html | sed 's|/admin/||'); do
        if [[ ! -f "static/admin/$file" ]]; then
            print_message $RED "❌ 缺少文件: $file"
            ((missing++))
        fi
    done
    
    if [[ $missing -eq 0 ]]; then
        print_message $GREEN "✅ 所有引用的文件都存在"
    else
        print_message $RED "❌ 有 $missing 个文件缺失"
        exit 1
    fi
else
    print_message $RED "❌ index.html 未生成，构建失败"
    exit 1
fi

# 7. 重启 Caddy 服务
print_message $BLUE "7️⃣ 重启 Caddy 服务..."
docker-compose --env-file=docker.env restart caddy

# 等待服务启动
print_message $YELLOW "   等待服务启动..."
sleep 15

if docker ps | grep -q "weishi-caddy"; then
    print_message $GREEN "✅ Caddy 服务重启成功"
else
    print_message $RED "❌ Caddy 服务重启失败"
    exit 1
fi

# 8. 测试访问
print_message $BLUE "8️⃣ 测试访问..."
local domain=$(grep "^DOMAIN=" docker.env 2>/dev/null | cut -d'=' -f2 || echo "localhost")

if command -v curl >/dev/null 2>&1; then
    # 测试主页
    local status=$(curl -s -k -o /dev/null -w "%{http_code}" "https://$domain/admin/" || echo "000")
    if [[ "$status" == "200" ]]; then
        print_message $GREEN "✅ 管理后台主页访问正常 (200)"
    else
        print_message $YELLOW "⚠️  管理后台主页返回 $status"
    fi
    
    # 测试一个 JS 文件
    local js_file=$(grep -o '/admin/assets/[^"]*\.js' static/admin/index.html | head -1 | sed 's|/admin/||')
    if [[ -n "$js_file" ]]; then
        local js_status=$(curl -s -k -o /dev/null -w "%{http_code}" "https://$domain/admin/$js_file" || echo "000")
        if [[ "$js_status" == "200" ]]; then
            print_message $GREEN "✅ JS 文件访问正常 (200)"
        else
            print_message $YELLOW "⚠️  JS 文件返回 $js_status"
        fi
    fi
    
    # 测试一个 CSS 文件
    local css_file=$(grep -o '/admin/assets/[^"]*\.css' static/admin/index.html | head -1 | sed 's|/admin/||')
    if [[ -n "$css_file" ]]; then
        local css_status=$(curl -s -k -o /dev/null -w "%{http_code}" "https://$domain/admin/$css_file" || echo "000")
        if [[ "$css_status" == "200" ]]; then
            print_message $GREEN "✅ CSS 文件访问正常 (200)"
        else
            print_message $YELLOW "⚠️  CSS 文件返回 $css_status"
        fi
    fi
else
    print_message $YELLOW "⚠️  无法测试访问（缺少 curl）"
fi

echo ""
print_message $GREEN "🎉 修复完成！"
echo ""
print_message $BLUE "📋 接下来的步骤："
echo "1. 清理浏览器缓存（非常重要！）"
echo "   - 按 Ctrl+Shift+R (Windows) 或 Cmd+Shift+R (Mac) 强制刷新"
echo "   - 或使用无痕模式访问"
echo ""
echo "2. 访问管理后台："
echo "   https://$domain/admin"
echo ""
echo "3. 如果仍有问题，请检查："
echo "   - 浏览器开发者工具的网络标签"
echo "   - 确认资源文件路径是否正确"
echo ""

print_message $YELLOW "💡 问题原因："
echo "   管理后台构建时缺少正确的 base 路径配置"
echo "   现在已经在 Dockerfile.builder 中添加了 VITE_BASE=/admin/"
echo "   这确保了所有资源文件都有正确的 /admin/ 前缀"

echo ""
print_message $BLUE "🔍 验证方法："
echo "   查看页面源代码，确认资源路径类似："
echo "   <script src=\"/admin/assets/index-xxx.js\"></script>"
echo "   <link href=\"/admin/assets/index-xxx.css\" rel=\"stylesheet\">"
