#!/bin/bash

# 端口配置检查脚本
# 检查项目中所有端口配置是否一致

set -e

echo "🔍 检查项目端口配置一致性..."

# 定义标准端口配置
BACKEND_PORT=3001
FRONTEND_PORT=3000
ADMIN_PORT=5173
DB_PORT=3306

# 检查结果
errors=()
warnings=()

# 检查函数
check_file_port() {
    local file="$1"
    local pattern="$2"
    local expected="$3"
    local description="$4"
    
    if [ -f "$file" ]; then
        local found=$(grep -n "$pattern" "$file" | head -5)
        if [ -n "$found" ]; then
            echo "📄 $description ($file):"
            echo "$found" | while read line; do
                echo "   $line"
                # 检查是否包含错误的端口
                if echo "$line" | grep -q "3000" && [ "$expected" != "3000" ]; then
                    if [ "$expected" = "3001" ]; then
                        echo "   ⚠️  发现3000端口，应该是3001"
                    fi
                elif echo "$line" | grep -q "3001" && [ "$expected" != "3001" ]; then
                    if [ "$expected" = "3000" ]; then
                        echo "   ⚠️  发现3001端口，应该是3000"
                    fi
                fi
            done
            echo ""
        fi
    fi
}

echo ""
echo "📊 标准端口配置:"
echo "   后端 (Go): $BACKEND_PORT"
echo "   前端 (Nuxt3): $FRONTEND_PORT"
echo "   管理后台 (Vue3): $ADMIN_PORT"
echo "   数据库 (MySQL): $DB_PORT"
echo ""

echo "🔍 检查配置文件..."

# 检查 docker-compose.yml
echo "📄 Docker Compose 配置:"
if [ -f "docker-compose.yml" ]; then
    echo "   后端端口映射:"
    grep -n "SERVER_PORT\|3001.*3001" docker-compose.yml | head -3
    echo "   前端端口映射:"
    grep -n "WEB_PORT\|3000.*3000" docker-compose.yml | head -3
    echo "   管理后台端口映射:"
    grep -n "ADMIN_PORT\|5173" docker-compose.yml | head -3
    echo ""
fi

# 检查环境变量文件
echo "📄 环境变量配置:"
if [ -f "docker.env" ]; then
    echo "   docker.env:"
    grep -n "SERVER_PORT\|WEB_PORT\|ADMIN_PORT" docker.env
    echo ""
fi

if [ -f "docker.env.example" ]; then
    echo "   docker.env.example:"
    grep -n "SERVER_PORT\|WEB_PORT\|ADMIN_PORT" docker.env.example
    echo ""
fi

# 检查后端配置
echo "📄 后端配置 (应该都是 $BACKEND_PORT):"
check_file_port "server-go/config.yaml" "port.*3" "$BACKEND_PORT" "Go配置文件"
check_file_port "server-go/cmd/main.go" "@host.*localhost:" "$BACKEND_PORT" "Swagger文档"
check_file_port "server-go/env.example" "PORT.*3" "$BACKEND_PORT" "环境变量示例"

# 检查前端配置
echo "📄 前端配置:"
echo "   Nuxt3 (应该API指向 $BACKEND_PORT):"
check_file_port "web/nuxt.config.ts" "localhost:" "$BACKEND_PORT" "Nuxt配置"

echo "   Vue3 管理后台 (应该API指向 $BACKEND_PORT，服务端口 $ADMIN_PORT):"
check_file_port "admin/vite.config.ts" "localhost:" "$BACKEND_PORT" "Vite配置"
check_file_port "admin/env.example" "localhost:" "$BACKEND_PORT" "环境变量示例"
check_file_port "admin/src/utils/request.ts" "localhost:" "$BACKEND_PORT" "请求配置"

# 检查 CORS 配置
echo "📄 CORS 配置:"
if [ -f "server-go/internal/router/router.go" ]; then
    echo "   允许的源地址:"
    grep -A 10 "AllowOrigins" server-go/internal/router/router.go | grep "localhost\|127.0.0.1"
    echo ""
fi

# 检查健康检查配置
echo "📄 健康检查配置:"
if [ -f "docker-compose.yml" ]; then
    echo "   健康检查端点:"
    grep -n "localhost:" docker-compose.yml | grep "health\|curl"
    echo ""
fi

# 端口冲突检查
echo "🔍 检查端口冲突..."
echo "   检查本地端口占用:"

ports=($BACKEND_PORT $FRONTEND_PORT $ADMIN_PORT $DB_PORT)
for port in "${ports[@]}"; do
    if command -v lsof >/dev/null 2>&1; then
        if lsof -i :$port >/dev/null 2>&1; then
            echo "   ⚠️  端口 $port 已被占用"
            lsof -i :$port | head -2
        else
            echo "   ✅ 端口 $port 可用"
        fi
    elif command -v netstat >/dev/null 2>&1; then
        if netstat -ln | grep ":$port " >/dev/null 2>&1; then
            echo "   ⚠️  端口 $port 已被占用"
        else
            echo "   ✅ 端口 $port 可用"
        fi
    else
        echo "   ❓ 无法检查端口 $port (缺少 lsof 或 netstat)"
    fi
done

echo ""

# 生成修复建议
echo "🔧 端口配置标准:"
echo ""
echo "   服务           | 端口  | 说明"
echo "   --------------|-------|------------------"
echo "   Go 后端       | 3001  | API 服务"
echo "   Nuxt3 前端    | 3000  | 企业官网"
echo "   Vue3 管理后台 | 5173  | 管理系统"
echo "   MySQL 数据库  | 3306  | 数据库服务"
echo "   Caddy HTTP    | 80    | 反向代理"
echo "   Caddy HTTPS   | 443   | SSL 终止"
echo ""

echo "📋 配置检查完成！"
echo ""
echo "🚀 相关命令:"
echo "   启动服务: docker-compose --env-file=docker.env up -d"
echo "   检查状态: docker-compose --env-file=docker.env ps"
echo "   查看日志: docker-compose --env-file=docker.env logs -f"
echo "   健康检查: ./scripts/test-health.sh"
