#!/bin/bash

# 健康检查监控脚本
# 持续监控所有服务的健康状态

set -e

echo "🔍 启动健康检查监控..."

# 读取环境变量
ENV_FILE="docker.env"
if [ -f "$ENV_FILE" ]; then
    source "$ENV_FILE"
else
    echo "⚠️  环境变量文件 $ENV_FILE 不存在，使用默认端口"
    SERVER_PORT=3001
    WEB_PORT=3000
    ADMIN_PORT=5173
fi

# 监控间隔（秒）
INTERVAL=${1:-30}

# 日志文件
LOG_FILE="logs/health-monitor.log"
mkdir -p logs

echo "📊 监控配置:"
echo "   监控间隔: ${INTERVAL}秒"
echo "   日志文件: $LOG_FILE"
echo "   按 Ctrl+C 停止监控"
echo ""

# 日志函数
log_message() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] $1" | tee -a "$LOG_FILE"
}

# 健康检查函数
check_health() {
    local name="$1"
    local url="$2"
    local timeout="$3"
    
    if curl -f -s -m "$timeout" "$url" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 服务状态检查
check_service_status() {
    local service_name="$1"
    
    if docker-compose --env-file="$ENV_FILE" ps "$service_name" 2>/dev/null | grep -q "Up"; then
        return 0
    else
        return 1
    fi
}

# 主监控循环
monitor_services() {
    local iteration=0
    
    while true; do
        iteration=$((iteration + 1))
        clear
        
        echo "🏥 健康检查监控 - 第 $iteration 次检查"
        echo "时间: $(date '+%Y-%m-%d %H:%M:%S')"
        echo "监控间隔: ${INTERVAL}秒"
        echo ""
        
        # 检查各服务状态
        local all_healthy=true
        
        # MySQL 数据库
        echo "📊 数据库服务:"
        if check_service_status "mysql"; then
            echo "  ✅ MySQL 容器运行正常"
            log_message "INFO: MySQL 容器运行正常"
        else
            echo "  ❌ MySQL 容器未运行"
            log_message "ERROR: MySQL 容器未运行"
            all_healthy=false
        fi
        
        echo ""
        
        # 后端服务
        echo "⚙️  后端服务:"
        if check_service_status "server"; then
            echo "  ✅ Go 后端容器运行正常"
            if check_health "Go 后端" "http://localhost:$SERVER_PORT/api/health" 5; then
                echo "  ✅ Go 后端健康检查通过"
                log_message "INFO: Go 后端服务健康"
            else
                echo "  ❌ Go 后端健康检查失败"
                log_message "ERROR: Go 后端健康检查失败"
                all_healthy=false
            fi
        else
            echo "  ❌ Go 后端容器未运行"
            log_message "ERROR: Go 后端容器未运行"
            all_healthy=false
        fi
        
        echo ""
        
        # 前端服务
        echo "🌐 前端服务:"
        if check_service_status "web"; then
            echo "  ✅ Nuxt3 前端容器运行正常"
            if check_health "Nuxt3 前端" "http://localhost:$WEB_PORT/api/health" 5; then
                echo "  ✅ Nuxt3 前端健康检查通过"
                log_message "INFO: Nuxt3 前端服务健康"
            else
                echo "  ❌ Nuxt3 前端健康检查失败"
                log_message "ERROR: Nuxt3 前端健康检查失败"
                all_healthy=false
            fi
        else
            echo "  ❌ Nuxt3 前端容器未运行"
            log_message "ERROR: Nuxt3 前端容器未运行"
            all_healthy=false
        fi
        
        echo ""
        
        # 管理后台
        echo "🔧 管理后台:"
        if check_service_status "admin"; then
            echo "  ✅ Vue3 管理后台容器运行正常"
            if check_health "Vue3 管理后台" "http://localhost:$ADMIN_PORT/health" 5; then
                echo "  ✅ Vue3 管理后台健康检查通过"
                log_message "INFO: Vue3 管理后台服务健康"
            else
                echo "  ❌ Vue3 管理后台健康检查失败"
                log_message "ERROR: Vue3 管理后台健康检查失败"
                all_healthy=false
            fi
        else
            echo "  ❌ Vue3 管理后台容器未运行"
            log_message "ERROR: Vue3 管理后台容器未运行"
            all_healthy=false
        fi
        
        echo ""
        
        # Caddy 代理（可选）
        if check_service_status "caddy"; then
            echo "🔄 Caddy 代理:"
            echo "  ✅ Caddy 代理容器运行正常"
            if check_health "Caddy 代理(8080)" "http://localhost:8080" 5; then
                echo "  ✅ Caddy 代理健康检查通过"
                log_message "INFO: Caddy 代理服务健康"
            else
                echo "  ❌ Caddy 代理健康检查失败"
                log_message "ERROR: Caddy 代理健康检查失败"
                all_healthy=false
            fi
            echo ""
        fi
        
        # 总体状态
        if [ "$all_healthy" = true ]; then
            echo "🎉 所有服务运行正常"
            log_message "INFO: 所有服务运行正常"
        else
            echo "⚠️  部分服务存在问题"
            log_message "WARNING: 部分服务存在问题"
        fi
        
        echo ""
        echo "下次检查: $(date -d "+${INTERVAL} seconds" '+%H:%M:%S')"
        echo "按 Ctrl+C 停止监控"
        
        # 等待下次检查
        sleep "$INTERVAL"
    done
}

# 信号处理
trap 'echo ""; log_message "INFO: 健康检查监控已停止"; exit 0' INT TERM

# 启动监控
log_message "INFO: 健康检查监控已启动，间隔: ${INTERVAL}秒"
monitor_services
