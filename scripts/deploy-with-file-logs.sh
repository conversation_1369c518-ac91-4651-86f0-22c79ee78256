#!/bin/bash

# 生产环境部署脚本 - 配置文件日志
# 确保后端服务只将日志写入文件，不输出到控制台

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 开始生产环境部署（文件日志模式）${NC}"

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 1. 设置日志目录
echo -e "${BLUE}📁 设置日志目录结构...${NC}"
./scripts/setup-logs.sh

# 2. 检查环境变量
echo -e "${BLUE}🔧 检查环境变量...${NC}"
if [[ ! -f "deployment/docker.env" ]]; then
    echo -e "${YELLOW}⚠️  创建环境变量文件...${NC}"
    cp deployment/docker.env.example deployment/docker.env
    echo -e "${RED}请编辑 deployment/docker.env 文件设置正确的环境变量${NC}"
fi

# 3. 验证生产配置
echo -e "${BLUE}✅ 验证生产配置...${NC}"

# 检查 Docker Compose 配置
if grep -q "APP_MODE: production" deployment/docker-compose.prod.yml; then
    echo -e "${GREEN}✓ APP_MODE 已设置为 production${NC}"
else
    echo -e "${RED}✗ APP_MODE 未设置为 production${NC}"
    exit 1
fi

# 检查日志目录映射
if grep -q "./logs/server:/app/logs" deployment/docker-compose.prod.yml; then
    echo -e "${GREEN}✓ 后端日志目录已映射${NC}"
else
    echo -e "${RED}✗ 后端日志目录映射配置错误${NC}"
    exit 1
fi

if grep -q "./logs/caddy:/var/log/caddy" deployment/docker-compose.prod.yml; then
    echo -e "${GREEN}✓ Caddy 日志目录已映射${NC}"
else
    echo -e "${RED}✗ Caddy 日志目录映射配置错误${NC}"
    exit 1
fi

# 4. 构建和部署
echo -e "${BLUE}🔨 构建和部署服务...${NC}"

# 停止现有服务
echo -e "${YELLOW}停止现有服务...${NC}"
docker-compose -f deployment/docker-compose.prod.yml down

# 构建镜像
echo -e "${YELLOW}构建镜像...${NC}"
docker-compose -f deployment/docker-compose.prod.yml build

# 启动服务
echo -e "${YELLOW}启动服务...${NC}"
docker-compose -f deployment/docker-compose.prod.yml up -d

# 5. 等待服务启动
echo -e "${BLUE}⏳ 等待服务启动...${NC}"
sleep 30

# 6. 检查服务状态
echo -e "${BLUE}🔍 检查服务状态...${NC}"
docker-compose -f deployment/docker-compose.prod.yml ps

# 7. 验证日志文件
echo -e "${BLUE}📊 验证日志文件...${NC}"

# 等待日志文件生成
sleep 10

if [[ -f "logs/server/app.log" ]]; then
    echo -e "${GREEN}✓ 后端日志文件已生成${NC}"
    echo -e "${BLUE}最新的后端日志:${NC}"
    tail -5 logs/server/app.log
else
    echo -e "${YELLOW}⚠️  后端日志文件尚未生成，请稍后检查${NC}"
fi

if [[ -f "logs/caddy/access.log" ]]; then
    echo -e "${GREEN}✓ Caddy 访问日志文件已生成${NC}"
else
    echo -e "${YELLOW}⚠️  Caddy 访问日志文件尚未生成${NC}"
fi

# 8. 显示日志查看命令
echo -e "${GREEN}✅ 部署完成！${NC}"
echo ""
echo -e "${BLUE}📊 日志查看命令:${NC}"
echo "# 查看后端日志"
echo "tail -f logs/server/app.log"
echo ""
echo "# 查看 Caddy 访问日志"
echo "tail -f logs/caddy/access.log"
echo ""
echo "# 使用日志查看脚本"
echo "./scripts/view-logs.sh server -f"
echo "./scripts/view-logs.sh caddy -a -f"
echo ""
echo -e "${BLUE}🔧 服务管理命令:${NC}"
echo "# 查看服务状态"
echo "docker-compose -f deployment/docker-compose.prod.yml ps"
echo ""
echo "# 重启服务"
echo "docker-compose -f deployment/docker-compose.prod.yml restart"
echo ""
echo "# 查看服务日志（如果文件日志未生成）"
echo "docker-compose -f deployment/docker-compose.prod.yml logs -f weizhi-server-prod"

# 9. 健康检查
echo -e "${BLUE}🏥 执行健康检查...${NC}"
sleep 5

# 检查后端服务
if curl -f http://localhost:3001/api/health >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 后端服务健康检查通过${NC}"
else
    echo -e "${YELLOW}⚠️  后端服务健康检查失败，请检查日志${NC}"
fi

# 检查前端服务
if curl -f http://localhost:3000 >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 前端服务健康检查通过${NC}"
else
    echo -e "${YELLOW}⚠️  前端服务健康检查失败，请检查日志${NC}"
fi

echo -e "${GREEN}🎉 生产环境部署完成！日志已配置为仅写入文件。${NC}"
