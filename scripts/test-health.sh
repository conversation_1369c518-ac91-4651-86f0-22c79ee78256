#!/bin/bash

# 健康检查测试脚本
# 用于测试所有服务的健康检查端点

set -e

echo "🏥 开始健康检查测试..."

# 读取环境变量
ENV_FILE="docker.env"
if [ -f "$ENV_FILE" ]; then
    source "$ENV_FILE"
else
    echo "⚠️  环境变量文件 $ENV_FILE 不存在，使用默认端口"
    SERVER_PORT=3001
    WEB_PORT=3000
fi

# 等待时间
WAIT_TIME=5

# 测试函数
test_endpoint() {
    local name="$1"
    local url="$2"
    local expected_status="$3"
    
    echo "🔍 测试 $name: $url"
    
    if curl -f -s -o /dev/null -w "%{http_code}" "$url" | grep -q "$expected_status"; then
        echo "✅ $name 健康检查通过"
        return 0
    else
        echo "❌ $name 健康检查失败"
        return 1
    fi
}

# 检查服务是否运行
check_service_running() {
    local service_name="$1"
    
    if docker-compose --env-file="$ENV_FILE" ps "$service_name" | grep -q "Up"; then
        echo "✅ $service_name 服务正在运行"
        return 0
    else
        echo "❌ $service_name 服务未运行"
        return 1
    fi
}

echo ""
echo "📊 检查服务运行状态..."

# 检查各服务是否运行（admin 现在是静态文件，不需要单独检查）
services=("mysql" "server" "web")
all_running=true

for service in "${services[@]}"; do
    if ! check_service_running "$service"; then
        all_running=false
    fi
done

if [ "$all_running" = false ]; then
    echo ""
    echo "⚠️  部分服务未运行，健康检查可能失败"
    echo "请先启动所有服务: docker-compose --env-file=$ENV_FILE up -d"
    echo ""
fi

echo ""
echo "🔍 等待 $WAIT_TIME 秒后开始健康检查..."
sleep $WAIT_TIME

echo ""
echo "🏥 开始健康检查测试..."

# 测试计数器
total_tests=0
passed_tests=0

# 测试后端健康检查
total_tests=$((total_tests + 1))
if test_endpoint "后端API" "http://localhost:$SERVER_PORT/api/health" "200"; then
    passed_tests=$((passed_tests + 1))
fi

# 测试前端健康检查
total_tests=$((total_tests + 1))
if test_endpoint "前端网站" "http://localhost:$WEB_PORT/api/health" "200"; then
    passed_tests=$((passed_tests + 1))
fi

# 测试管理后台静态文件（通过主 Caddy 或直接访问静态文件）
total_tests=$((total_tests + 1))
if [ -f "static/admin/index.html" ]; then
    echo "✅ 管理后台静态文件存在"
    passed_tests=$((passed_tests + 1))
else
    echo "❌ 管理后台静态文件不存在"
fi

# 测试 Caddy 代理健康检查 - 使用 8080 端口
total_tests=$((total_tests + 1))
if test_endpoint "Caddy代理(8080)" "http://localhost:8080" "200"; then
    passed_tests=$((passed_tests + 1))
else
    # 如果 8080 失败，尝试测试主域名
    if [ -n "$DOMAIN" ] && [ "$DOMAIN" != "localhost" ]; then
        if test_endpoint "Caddy代理(域名)" "https://$DOMAIN/health" "200"; then
            echo "✅ Caddy代理域名访问正常"
            passed_tests=$((passed_tests + 1))
        fi
    fi
fi

echo ""
echo "📊 健康检查结果汇总:"
echo "   总测试数: $total_tests"
echo "   通过测试: $passed_tests"
echo "   失败测试: $((total_tests - passed_tests))"

if [ $passed_tests -eq $total_tests ]; then
    echo ""
    echo "🎉 所有健康检查通过！"
    exit 0
else
    echo ""
    echo "⚠️  部分健康检查失败，请检查服务状态"
    echo ""
    echo "🔧 故障排除建议:"
    echo "   1. 检查服务日志: docker-compose --env-file=$ENV_FILE logs <service_name>"
    echo "   2. 检查服务状态: docker-compose --env-file=$ENV_FILE ps"
    echo "   3. 重启失败的服务: docker-compose --env-file=$ENV_FILE restart <service_name>"
    exit 1
fi
