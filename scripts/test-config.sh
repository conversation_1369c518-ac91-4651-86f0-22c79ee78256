#!/bin/bash

# 配置验证脚本
# 用于验证 Docker Compose 配置是否正确

set -e

echo "🔍 验证部署配置..."

# 检查必要文件是否存在
echo "📁 检查必要文件..."
files=(
    "docker-compose.yml"
    "docker.env"
    "server-go/Dockerfile"
    "web/Dockerfile"
    "admin/Dockerfile"
    "caddy/Caddyfile"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file 存在"
    else
        echo "❌ $file 不存在"
        exit 1
    fi
done

# 检查数据库初始化文件
echo "📁 检查数据库初始化文件..."
scripts=(
    "server-go/scripts/init_database.sql"
)

for script in "${scripts[@]}"; do
    if [ -f "$script" ]; then
        echo "✅ $script 存在"
    else
        echo "❌ $script 不存在"
        exit 1
    fi
done

# 验证 Docker Compose 配置语法
echo "🔧 验证 Docker Compose 配置语法..."
if docker-compose --env-file=docker.env config > /dev/null 2>&1; then
    echo "✅ Docker Compose 配置语法正确"
else
    echo "❌ Docker Compose 配置语法错误"
    docker-compose --env-file=docker.env config
    exit 1
fi

# 检查端口配置
echo "🔌 检查端口配置..."
echo "   前端端口: 3000"
echo "   管理后台端口: 5173"
echo "   后端端口: 3001"
echo "   数据库端口: 3306"
echo "   Caddy HTTP: 80"
echo "   Caddy HTTPS: 443"

# 显示环境变量配置
echo "🌍 环境变量配置预览:"
echo "   MYSQL_DATABASE: $(grep MYSQL_DATABASE docker.env | cut -d'=' -f2)"
echo "   SERVER_PORT: $(grep SERVER_PORT docker.env | cut -d'=' -f2)"
echo "   WEB_PORT: $(grep WEB_PORT docker.env | cut -d'=' -f2)"
echo "   ADMIN_PORT: $(grep ADMIN_PORT docker.env | cut -d'=' -f2)"
echo "   DOMAIN: $(grep DOMAIN docker.env | grep -v '#' | cut -d'=' -f2)"

echo ""
echo "✅ 配置验证完成！"
echo ""
echo "🚀 下一步操作："
echo "   1. 检查端口配置: ./scripts/check-ports.sh"
echo "   2. 确保 COS 配置已填写（如需要文件上传功能）"
echo "      - 运行 ./scripts/test-cos.sh 验证COS配置"
echo "   3. 运行 ./scripts/build-optimized.sh 优化构建"
echo "   4. 运行 ./scripts/deploy.sh 开始部署"
echo "   5. 或运行 docker-compose --env-file=docker.env up -d 手动部署"
echo "   6. 部署后运行 ./scripts/test-health.sh 测试健康检查"
echo "   7. 运行 ./scripts/analyze-build.sh 分析构建性能"
