#!/bin/bash

# Caddy 缓存配置测试脚本
# 功能：测试不同类型文件的缓存头配置是否正确

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：显示帮助信息
show_help() {
    echo "Caddy 缓存配置测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -d, --domain DOMAIN     指定测试域名 (默认从 docker.env 读取)"
    echo "  --verbose               显示详细输出"
    echo ""
    echo "示例:"
    echo "  $0                      # 使用默认域名测试"
    echo "  $0 -d example.com       # 测试指定域名"
    echo "  $0 --verbose            # 显示详细信息"
}

# 函数：测试单个 URL 的缓存头
test_cache_header() {
    local url="$1"
    local expected_cache="$2"
    local description="$3"
    local verbose="$4"
    
    if ! command -v curl >/dev/null 2>&1; then
        print_message $YELLOW "⚠️  跳过测试（缺少 curl）: $description"
        return
    fi
    
    print_message $BLUE "🔍 测试: $description"
    echo "   URL: $url"
    
    # 获取响应头
    local response=$(curl -s -k -I "$url" 2>/dev/null || echo "ERROR")
    
    if [[ "$response" == "ERROR" ]]; then
        print_message $RED "❌ 无法访问: $url"
        return
    fi
    
    # 检查 HTTP 状态码
    local status=$(echo "$response" | head -1 | grep -o '[0-9]\{3\}' || echo "000")
    
    if [[ "$status" != "200" ]]; then
        print_message $YELLOW "⚠️  HTTP $status: $url"
        if [[ "$verbose" == "true" ]]; then
            echo "$response" | head -5
        fi
        return
    fi
    
    # 检查 Cache-Control 头
    local cache_control=$(echo "$response" | grep -i "cache-control:" | cut -d' ' -f2- | tr -d '\r\n' || echo "未设置")
    
    echo "   实际缓存头: $cache_control"
    echo "   期望缓存头: $expected_cache"
    
    # 验证缓存头
    if [[ "$cache_control" == *"$expected_cache"* ]]; then
        print_message $GREEN "✅ 缓存头配置正确"
    else
        print_message $RED "❌ 缓存头配置不匹配"
    fi
    
    # 显示其他相关头信息
    if [[ "$verbose" == "true" ]]; then
        echo "   其他头信息:"
        echo "$response" | grep -i -E "(content-type|x-content-type-options|expires|pragma|etag):" | sed 's/^/     /'
    fi
    
    echo ""
}

# 函数：检查静态文件是否存在
check_static_files() {
    local domain="$1"
    
    print_message $BLUE "📁 检查静态文件..."
    
    # 检查管理后台文件
    if [[ -f "static/admin/index.html" ]]; then
        print_message $GREEN "✅ 管理后台文件存在"
        
        # 获取实际的资源文件名
        local js_files=$(grep -o 'assets/[^"]*\.js' static/admin/index.html | head -3)
        local css_files=$(grep -o 'assets/[^"]*\.css' static/admin/index.html | head -2)
        
        echo "   JS 文件:"
        for file in $js_files; do
            echo "     /admin/$file"
        done
        
        echo "   CSS 文件:"
        for file in $css_files; do
            echo "     /admin/$file"
        done
        
        # 返回第一个 JS 和 CSS 文件用于测试
        SAMPLE_JS=$(echo $js_files | awk '{print $1}')
        SAMPLE_CSS=$(echo $css_files | awk '{print $1}')
        
    else
        print_message $RED "❌ 管理后台文件不存在"
        SAMPLE_JS=""
        SAMPLE_CSS=""
    fi
    
    echo ""
}

# 主函数
main() {
    local domain=""
    local verbose=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -d|--domain)
                domain="$2"
                shift 2
                ;;
            --verbose)
                verbose=true
                shift
                ;;
            *)
                print_message $RED "❌ 未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 获取域名
    if [[ -z "$domain" ]]; then
        domain=$(grep "^DOMAIN=" docker.env 2>/dev/null | cut -d'=' -f2 || echo "localhost")
    fi
    
    print_message $BLUE "🧪 Caddy 缓存配置测试"
    print_message $BLUE "🌐 测试域名: $domain"
    echo ""
    
    # 检查静态文件
    check_static_files "$domain"
    
    # 测试用例
    print_message $BLUE "🔬 开始缓存头测试..."
    echo ""
    
    # 1. 测试管理后台 HTML - 应该不缓存
    test_cache_header "https://$domain/admin/" "no-cache" "管理后台首页 (HTML)" "$verbose"
    
    # 2. 测试管理后台 JS 文件 - 应该长期缓存
    if [[ -n "$SAMPLE_JS" ]]; then
        test_cache_header "https://$domain/admin/$SAMPLE_JS" "max-age=31536000" "管理后台 JS 文件 (带版本号)" "$verbose"
    else
        print_message $YELLOW "⚠️  跳过 JS 文件测试（文件不存在）"
    fi
    
    # 3. 测试管理后台 CSS 文件 - 应该长期缓存
    if [[ -n "$SAMPLE_CSS" ]]; then
        test_cache_header "https://$domain/admin/$SAMPLE_CSS" "max-age=31536000" "管理后台 CSS 文件 (带版本号)" "$verbose"
    else
        print_message $YELLOW "⚠️  跳过 CSS 文件测试（文件不存在）"
    fi
    
    # 4. 测试图标文件 - 应该短期缓存
    test_cache_header "https://$domain/admin/favicon.ico" "max-age=3600" "图标文件" "$verbose"
    
    # 5. 测试前端首页 - 应该不缓存
    test_cache_header "https://$domain/" "no-cache" "前端首页 (HTML)" "$verbose"
    
    # 6. 测试 API 接口 - 应该不缓存
    test_cache_header "https://$domain/api/health" "no-cache" "API 接口" "$verbose"
    
    # 总结
    print_message $BLUE "📊 测试总结"
    echo ""
    print_message $GREEN "✅ 推荐的缓存策略:"
    echo "   📄 HTML 文件: no-cache (确保内容及时更新)"
    echo "   📦 带版本号的 JS/CSS: max-age=31536000 (1年长期缓存)"
    echo "   🖼️  图标文件: max-age=3600 (1小时短期缓存)"
    echo "   🔌 API 接口: no-cache (动态内容不缓存)"
    echo ""
    
    print_message $YELLOW "💡 优化建议:"
    echo "   1. 确保 HTML 文件不被缓存，避免更新问题"
    echo "   2. 利用文件名哈希值进行长期缓存"
    echo "   3. 定期检查缓存配置是否生效"
    echo "   4. 监控缓存命中率和性能指标"
    echo ""
    
    print_message $BLUE "🔧 如果缓存配置不正确:"
    echo "   1. 检查 Caddy 配置文件: caddy/Caddyfile"
    echo "   2. 重启 Caddy 服务: docker-compose restart caddy"
    echo "   3. 清理浏览器缓存进行测试"
    echo "   4. 使用无痕模式验证配置"
}

# 执行主函数
main "$@"
