#!/bin/bash

# 域名测试脚本
# 用于验证多域名配置和重定向是否正常工作

echo "🌐 域名配置测试脚本"
echo "===================="

# 从环境变量读取域名
source docker.env
DOMAIN=${DOMAIN:-localhost}

echo "📋 测试域名: $DOMAIN"
echo "📋 www域名: www.$DOMAIN"
echo ""

# 检查DNS解析
echo "🔍 检查DNS解析..."
echo "主域名解析:"
nslookup $DOMAIN || echo "❌ 主域名解析失败"

echo ""
echo "www域名解析:"
nslookup www.$DOMAIN || echo "❌ www域名解析失败"

echo ""

# 检查服务状态
echo "🔍 检查Docker服务状态..."
docker-compose ps

echo ""

# 测试HTTP访问（如果是生产环境）
if [ "$DOMAIN" != "localhost" ]; then
    echo "🔍 测试HTTP访问..."
    
    echo "测试主域名HTTP访问:"
    curl -I http://$DOMAIN 2>/dev/null | head -1 || echo "❌ HTTP访问失败"
    
    echo "测试www域名HTTP访问:"
    curl -I http://www.$DOMAIN 2>/dev/null | head -1 || echo "❌ HTTP访问失败"
    
    echo ""
    echo "🔍 测试HTTPS访问..."
    
    echo "测试主域名HTTPS访问:"
    curl -I https://$DOMAIN 2>/dev/null | head -1 || echo "❌ HTTPS访问失败"
    
    echo "测试www域名HTTPS重定向:"
    curl -I https://www.$DOMAIN 2>/dev/null | grep -E "(HTTP|Location)" || echo "❌ HTTPS重定向失败"
    
    echo ""
    echo "🔍 测试API访问..."
    
    echo "测试API健康检查:"
    curl -I https://$DOMAIN/api/health 2>/dev/null | head -1 || echo "❌ API访问失败"
    
else
    echo "🔍 本地环境测试..."
    echo "测试本地访问:"
    curl -I http://localhost:80 2>/dev/null | head -1 || echo "❌ 本地访问失败"
fi

echo ""
echo "🔍 检查SSL证书（生产环境）..."
if [ "$DOMAIN" != "localhost" ]; then
    echo "主域名证书信息:"
    echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | openssl x509 -noout -dates 2>/dev/null || echo "❌ 证书检查失败"
    
    echo ""
    echo "www域名证书信息:"
    echo | openssl s_client -servername www.$DOMAIN -connect www.$DOMAIN:443 2>/dev/null | openssl x509 -noout -dates 2>/dev/null || echo "❌ www证书检查失败"
fi

echo ""
echo "📊 测试完成！"
echo ""
echo "💡 如果测试失败，请检查："
echo "   1. DNS解析是否正确"
echo "   2. 防火墙是否开放80、443端口"
echo "   3. Docker服务是否正常运行"
echo "   4. 域名是否已生效"
echo ""
echo "📖 详细配置说明请查看: DOMAIN_CONFIG.md" 