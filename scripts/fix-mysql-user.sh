#!/bin/bash
set -e

echo "=== MySQL用户检测和修复脚本 ==="

# 检查是否在正确的目录
if [ ! -f "production.env" ]; then
    echo "❌ production.env 文件不存在，请确保在正确的部署目录中执行"
    exit 1
fi

# 加载环境变量
set -a
source production.env
set +a

echo "✅ 已加载生产环境配置"
echo "数据库: $MYSQL_DATABASE"
echo "用户: $MYSQL_USER"
echo "端口: $MYSQL_PORT"

# 检查MySQL容器状态
echo ""
echo "🔍 检查MySQL容器状态..."
if docker compose --env-file production.env -f docker-compose.prod.yml ps mysql | grep -q "Up"; then
    echo "✅ MySQL容器正在运行"
else
    echo "❌ MySQL容器未运行，请先启动MySQL容器"
    exit 1
fi

# 检查root用户连接
echo ""
echo "🔍 测试root用户连接..."
if docker compose --env-file production.env -f docker-compose.prod.yml exec -T mysql mysql -u root -p"${MYSQL_ROOT_PASSWORD}" -e "SELECT 1;" &>/dev/null; then
    echo "✅ root用户密码认证成功"
elif docker compose --env-file production.env -f docker-compose.prod.yml exec -T mysql mysql -u root -e "SELECT 1;" &>/dev/null; then
    echo "⚠️ root用户使用空密码连接成功，正在设置密码..."
    docker compose --env-file production.env -f docker-compose.prod.yml exec -T mysql mysql -u root -e "ALTER USER 'root'@'localhost' IDENTIFIED BY '${MYSQL_ROOT_PASSWORD}'; FLUSH PRIVILEGES;"
    echo "✅ root密码设置成功"
else
    echo "❌ 无法连接到MySQL root用户"
    exit 1
fi

# 检查数据库是否存在
echo ""
echo "🔍 检查数据库..."
docker compose --env-file production.env -f docker-compose.prod.yml exec -T mysql mysql -u root -p"${MYSQL_ROOT_PASSWORD}" -e "CREATE DATABASE IF NOT EXISTS ${MYSQL_DATABASE} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
echo "✅ 数据库 ${MYSQL_DATABASE} 已准备就绪"

# 检查weizhi用户
echo ""
echo "🔍 检查weizhi用户..."
if docker compose --env-file production.env -f docker-compose.prod.yml exec -T mysql mysql -u ${MYSQL_USER} -p"${MYSQL_PASSWORD}" ${MYSQL_DATABASE} -e "SELECT 1;" &>/dev/null; then
    echo "✅ weizhi用户已存在且可以访问数据库"
else
    echo "⚠️ weizhi用户不存在或无法访问，正在重新创建..."
    
    # 删除现有用户（如果存在）
    docker compose --env-file production.env -f docker-compose.prod.yml exec -T mysql mysql -u root -p"${MYSQL_ROOT_PASSWORD}" -e "DROP USER IF EXISTS '${MYSQL_USER}'@'%';"
    
    # 创建新用户
    docker compose --env-file production.env -f docker-compose.prod.yml exec -T mysql mysql -u root -p"${MYSQL_ROOT_PASSWORD}" -e "CREATE USER '${MYSQL_USER}'@'%' IDENTIFIED BY '${MYSQL_PASSWORD}';"
    
    # 授权
    docker compose --env-file production.env -f docker-compose.prod.yml exec -T mysql mysql -u root -p"${MYSQL_ROOT_PASSWORD}" -e "GRANT ALL PRIVILEGES ON ${MYSQL_DATABASE}.* TO '${MYSQL_USER}'@'%'; FLUSH PRIVILEGES;"
    
    echo "✅ weizhi用户创建完成"
    
    # 验证用户
    if docker compose --env-file production.env -f docker-compose.prod.yml exec -T mysql mysql -u ${MYSQL_USER} -p"${MYSQL_PASSWORD}" ${MYSQL_DATABASE} -e "SELECT 1;" &>/dev/null; then
        echo "✅ weizhi用户创建成功并可以访问数据库"
    else
        echo "❌ weizhi用户创建失败"
        exit 1
    fi
fi

# 显示数据库信息
echo ""
echo "📊 数据库信息："
docker compose --env-file production.env -f docker-compose.prod.yml exec -T mysql mysql -u root -p"${MYSQL_ROOT_PASSWORD}" -e "
    SELECT user, host FROM mysql.user WHERE user = '${MYSQL_USER}';
    SHOW DATABASES LIKE '${MYSQL_DATABASE}';
"

echo ""
echo "✅ MySQL用户检测和修复完成！"
echo "现在可以重新启动server服务了"