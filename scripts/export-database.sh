#!/bin/bash

# 数据库导出脚本
# 用于导出完整的数据库结构和数据

set -e

echo "📊 开始导出数据库..."

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="root"
DB_PASSWORD="Ydb3344%"
DB_NAME="weizhi"

# 导出文件路径
EXPORT_DIR="server-go/scripts"
STRUCTURE_FILE="$EXPORT_DIR/database_structure.sql"
DATA_FILE="$EXPORT_DIR/database_data.sql"
FULL_FILE="$EXPORT_DIR/database_full.sql"

# 创建导出目录
mkdir -p "$EXPORT_DIR"

echo "🏗️  导出数据库结构..."

# 导出数据库结构（不包含数据）
mysqldump -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
    --no-data \
    --routines \
    --triggers \
    --single-transaction \
    --lock-tables=false \
    --add-drop-database \
    --skip-ssl \
    --databases "$DB_NAME" > "$STRUCTURE_FILE"

if [ $? -eq 0 ]; then
    echo "✅ 数据库结构导出成功: $STRUCTURE_FILE"
else
    echo "❌ 数据库结构导出失败"
    exit 1
fi

echo "📦 导出数据库数据..."

# 导出数据库数据（不包含结构）
mysqldump -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
    --no-create-info \
    --single-transaction \
    --lock-tables=false \
    --complete-insert \
    --extended-insert=false \
    --skip-ssl \
    "$DB_NAME" > "$DATA_FILE"

if [ $? -eq 0 ]; then
    echo "✅ 数据库数据导出成功: $DATA_FILE"
else
    echo "❌ 数据库数据导出失败"
    exit 1
fi

echo "🔄 合并完整数据库文件..."

# 创建完整的数据库文件
cat > "$FULL_FILE" << 'EOF'
-- =====================================================
-- 蔚之领域智能科技项目数据库完整导出文件
-- 包含表结构和初始数据
-- 生成时间: $(date '+%Y-%m-%d %H:%M:%S')
-- =====================================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

EOF

# 添加结构文件内容（跳过前几行注释）
tail -n +5 "$STRUCTURE_FILE" >> "$FULL_FILE"

# 添加分隔符
cat >> "$FULL_FILE" << 'EOF'

-- =====================================================
-- 数据插入部分
-- =====================================================

EOF

# 添加数据文件内容
cat "$DATA_FILE" >> "$FULL_FILE"

# 添加结尾
cat >> "$FULL_FILE" << 'EOF'

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 显示完成信息
SELECT '数据库初始化完成！' as message;
SELECT '管理员账号: admin' as admin_account;
SELECT '管理员密码: admin123' as admin_password;
EOF

echo "✅ 完整数据库文件生成成功: $FULL_FILE"

# 显示文件信息
echo ""
echo "📋 导出文件信息:"
echo "   结构文件: $STRUCTURE_FILE ($(wc -l < "$STRUCTURE_FILE") 行)"
echo "   数据文件: $DATA_FILE ($(wc -l < "$DATA_FILE") 行)"
echo "   完整文件: $FULL_FILE ($(wc -l < "$FULL_FILE") 行)"

# 显示文件大小
echo ""
echo "📊 文件大小:"
ls -lh "$STRUCTURE_FILE" "$DATA_FILE" "$FULL_FILE" | awk '{print "   " $9 ": " $5}'

echo ""
echo "🎉 数据库导出完成！"
echo ""
echo "📝 使用说明:"
echo "   1. 结构文件: $STRUCTURE_FILE"
echo "   2. 数据文件: $DATA_FILE"
echo "   3. 完整文件: $FULL_FILE (推荐用于部署)"
echo ""
echo "🚀 部署使用:"
echo "   将 $FULL_FILE 重命名为 init_database.sql"
echo "   在 docker-compose.yml 中引用该文件进行初始化"
