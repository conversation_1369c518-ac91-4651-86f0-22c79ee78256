#!/bin/bash

# 日志查看脚本
# 用于方便地查看各种服务的日志

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
LOGS_DIR="$PROJECT_ROOT/logs"

# 显示帮助信息
show_help() {
    echo -e "${GREEN}📊 日志查看工具${NC}"
    echo ""
    echo "用法: $0 [选项] [服务名]"
    echo ""
    echo "服务名:"
    echo "  server    - 后端服务日志"
    echo "  web       - 前端服务日志 (通过 Docker)"
    echo "  caddy     - Caddy 访问日志"
    echo "  mysql     - MySQL 数据库日志"
    echo "  all       - 所有服务日志"
    echo ""
    echo "选项:"
    echo "  -f, --follow    实时跟踪日志"
    echo "  -n, --lines N   显示最后 N 行 (默认: 100)"
    echo "  -e, --error     只显示错误日志"
    echo "  -a, --access    只显示访问日志 (仅 Caddy)"
    echo "  -h, --help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 server -f              # 实时查看后端日志"
    echo "  $0 caddy -a -n 50         # 查看最后50行访问日志"
    echo "  $0 mysql -e               # 查看MySQL错误日志"
    echo "  $0 all -f                 # 实时查看所有日志"
}

# 解析参数
FOLLOW=false
LINES=100
ERROR_ONLY=false
ACCESS_ONLY=false
SERVICE=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--follow)
            FOLLOW=true
            shift
            ;;
        -n|--lines)
            LINES="$2"
            shift 2
            ;;
        -e|--error)
            ERROR_ONLY=true
            shift
            ;;
        -a|--access)
            ACCESS_ONLY=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        server|web|caddy|mysql|all)
            SERVICE="$1"
            shift
            ;;
        *)
            echo -e "${RED}错误: 未知参数 $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# 如果没有指定服务，显示帮助
if [[ -z "$SERVICE" ]]; then
    show_help
    exit 1
fi

# 检查日志目录是否存在
if [[ ! -d "$LOGS_DIR" ]]; then
    echo -e "${RED}错误: 日志目录不存在: $LOGS_DIR${NC}"
    echo -e "${YELLOW}请先运行: ./scripts/setup-logs.sh${NC}"
    exit 1
fi

# 构建 tail 命令参数
TAIL_ARGS=""
if [[ "$FOLLOW" == true ]]; then
    TAIL_ARGS="-f"
else
    TAIL_ARGS="-n $LINES"
fi

# 查看服务日志
view_service_logs() {
    local service=$1
    
    case $service in
        server)
            echo -e "${BLUE}📱 查看后端服务日志...${NC}"
            if [[ -f "$LOGS_DIR/server/app.log" ]]; then
                if [[ "$ERROR_ONLY" == true ]]; then
                    tail $TAIL_ARGS "$LOGS_DIR/server/app.log" | grep -i "error\|exception\|panic\|fatal"
                else
                    tail $TAIL_ARGS "$LOGS_DIR/server/app.log"
                fi
            else
                echo -e "${YELLOW}日志文件不存在，尝试查看 Docker 日志...${NC}"
                docker-compose logs $TAIL_ARGS weizhi-server-prod
            fi
            ;;
        web)
            echo -e "${BLUE}🌐 查看前端服务日志...${NC}"
            docker-compose logs $TAIL_ARGS weizhi-web-prod
            ;;
        caddy)
            echo -e "${BLUE}🔄 查看 Caddy 日志...${NC}"
            if [[ "$ACCESS_ONLY" == true ]]; then
                if [[ -f "$LOGS_DIR/caddy/access.log" ]]; then
                    tail $TAIL_ARGS "$LOGS_DIR/caddy/access.log"
                else
                    echo -e "${YELLOW}访问日志文件不存在${NC}"
                fi
            elif [[ "$ERROR_ONLY" == true ]]; then
                if [[ -f "$LOGS_DIR/caddy/error.log" ]]; then
                    tail $TAIL_ARGS "$LOGS_DIR/caddy/error.log"
                else
                    echo -e "${YELLOW}错误日志文件不存在${NC}"
                fi
            else
                echo -e "${GREEN}=== 访问日志 ===${NC}"
                if [[ -f "$LOGS_DIR/caddy/access.log" ]]; then
                    tail -n 50 "$LOGS_DIR/caddy/access.log"
                fi
                echo -e "${RED}=== 错误日志 ===${NC}"
                if [[ -f "$LOGS_DIR/caddy/error.log" ]]; then
                    tail -n 50 "$LOGS_DIR/caddy/error.log"
                fi
            fi
            ;;
        mysql)
            echo -e "${BLUE}🗄️ 查看 MySQL 日志...${NC}"
            if [[ -f "$LOGS_DIR/mysql/error.log" ]]; then
                tail $TAIL_ARGS "$LOGS_DIR/mysql/error.log"
            else
                echo -e "${YELLOW}MySQL 日志文件不存在，尝试查看 Docker 日志...${NC}"
                docker-compose logs $TAIL_ARGS weizhi-mysql-prod
            fi
            ;;
        all)
            echo -e "${BLUE}📊 查看所有服务日志...${NC}"
            if [[ "$FOLLOW" == true ]]; then
                docker-compose logs -f
            else
                docker-compose logs --tail=$LINES
            fi
            ;;
    esac
}

# 执行日志查看
view_service_logs "$SERVICE"
