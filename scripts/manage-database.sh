#!/bin/bash

# 数据库管理脚本
# 提供数据库导出、导入、重置等功能

set -e

# 显示帮助信息
show_help() {
    echo "数据库管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  export    导出当前数据库到初始化文件"
    echo "  import    从初始化文件导入数据库"
    echo "  reset     重置数据库（删除并重新创建）"
    echo "  backup    备份当前数据库"
    echo "  status    显示数据库状态"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 export    # 导出数据库"
    echo "  $0 status    # 查看状态"
    echo "  $0 reset     # 重置数据库"
}

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="root"
DB_PASSWORD="Ydb3344%"
DB_NAME="weizhi"
TARGET_DB_NAME="weizhi"

# 文件路径
INIT_FILE="server-go/scripts/init_database.sql"
BACKUP_DIR="backups"

# 导出数据库
export_database() {
    echo "📊 开始导出数据库..."
    
    if ! ./scripts/export-database.sh; then
        echo "❌ 数据库导出失败"
        return 1
    fi
    
    if ! ./scripts/fix-database-name.sh; then
        echo "❌ 数据库名称修复失败"
        return 1
    fi
    
    echo "✅ 数据库导出完成！"
    echo "   初始化文件: $INIT_FILE"
}

# 导入数据库
import_database() {
    echo "📥 开始导入数据库..."
    
    if [ ! -f "$INIT_FILE" ]; then
        echo "❌ 初始化文件不存在: $INIT_FILE"
        echo "请先运行: $0 export"
        return 1
    fi
    
    echo "⚠️  这将删除现有的 $TARGET_DB_NAME 数据库并重新创建"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 操作已取消"
        return 1
    fi
    
    echo "🗑️  删除现有数据库..."
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
        -e "DROP DATABASE IF EXISTS $TARGET_DB_NAME;" 2>/dev/null || true
    
    echo "📥 导入数据库..."
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" < "$INIT_FILE"
    
    echo "✅ 数据库导入完成！"
}

# 重置数据库
reset_database() {
    echo "🔄 重置数据库..."
    
    echo "⚠️  这将完全删除并重新创建数据库"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 操作已取消"
        return 1
    fi
    
    import_database
}

# 备份数据库
backup_database() {
    echo "💾 备份数据库..."
    
    mkdir -p "$BACKUP_DIR"
    
    timestamp=$(date '+%Y%m%d_%H%M%S')
    backup_file="$BACKUP_DIR/weizhi_backup_$timestamp.sql"
    
    mysqldump -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
        --single-transaction \
        --routines \
        --triggers \
        --skip-ssl \
        "$DB_NAME" > "$backup_file"
    
    echo "✅ 数据库备份完成！"
    echo "   备份文件: $backup_file"
    echo "   文件大小: $(ls -lh "$backup_file" | awk '{print $5}')"
}

# 显示数据库状态
show_status() {
    echo "📊 数据库状态:"
    echo ""
    
    # 检查数据库是否存在
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
        -e "USE $DB_NAME; SELECT 1;" >/dev/null 2>&1; then
        echo "✅ 源数据库 ($DB_NAME) 存在"
        
        # 显示表数量
        table_count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
            -e "USE $DB_NAME; SHOW TABLES;" 2>/dev/null | wc -l)
        echo "   表数量: $((table_count - 1))"
        
        # 显示数据量
        echo "   主要表数据量:"
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
            -e "USE $DB_NAME; 
                SELECT 'admin_users' as table_name, COUNT(*) as count FROM admin_users
                UNION ALL
                SELECT 'swipers', COUNT(*) FROM swipers
                UNION ALL
                SELECT 'our_services', COUNT(*) FROM our_services
                UNION ALL
                SELECT 'partners', COUNT(*) FROM partners;" 2>/dev/null | column -t
    else
        echo "❌ 源数据库 ($DB_NAME) 不存在"
    fi
    
    echo ""
    
    # 检查目标数据库
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
        -e "USE $TARGET_DB_NAME; SELECT 1;" >/dev/null 2>&1; then
        echo "✅ 目标数据库 ($TARGET_DB_NAME) 存在"
    else
        echo "❌ 目标数据库 ($TARGET_DB_NAME) 不存在"
    fi
    
    # 检查初始化文件
    if [ -f "$INIT_FILE" ]; then
        echo "✅ 初始化文件存在"
        echo "   文件大小: $(ls -lh "$INIT_FILE" | awk '{print $5}')"
        echo "   修改时间: $(ls -l "$INIT_FILE" | awk '{print $6, $7, $8}')"
    else
        echo "❌ 初始化文件不存在"
    fi
}

# 主函数
main() {
    case "${1:-help}" in
        export)
            export_database
            ;;
        import)
            import_database
            ;;
        reset)
            reset_database
            ;;
        backup)
            backup_database
            ;;
        status)
            show_status
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            echo "❌ 未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
