#!/bin/bash

# 构建性能分析脚本
# 分析 Docker 构建的性能和优化建议

set -e

echo "📊 开始构建性能分析..."

# 读取环境变量
ENV_FILE="docker.env"
if [ ! -f "$ENV_FILE" ]; then
    ENV_FILE=""
fi

# 分析镜像大小
analyze_image_sizes() {
    echo "📏 镜像大小分析:"
    echo ""
    
    # 获取项目镜像
    images=$(docker images | grep "weishi_com_nuxt3web" | awk '{print $1":"$2"\t"$7}')
    
    if [ -z "$images" ]; then
        echo "   ⚠️  未找到项目镜像，请先构建项目"
        return 1
    fi
    
    echo "   项目镜像:"
    echo "$images" | while read line; do
        echo "     $line"
    done
    
    echo ""
    
    # 计算总大小
    total_mb=$(docker images | grep "weishi_com_nuxt3web" | awk '{
        size = $7
        if (size ~ /GB$/) {
            gsub(/GB/, "", size)
            size = size * 1024
        } else if (size ~ /MB$/) {
            gsub(/MB/, "", size)
        } else {
            size = 0
        }
        total += size
    } END {print total}')
    
    echo "   总大小: ${total_mb}MB"
    
    # 大小建议
    echo ""
    echo "   📋 大小优化建议:"
    if (( $(echo "$total_mb > 2000" | bc -l) )); then
        echo "     ⚠️  镜像总大小较大，建议优化"
        echo "     - 使用多阶段构建减少最终镜像大小"
        echo "     - 清理不必要的依赖和文件"
        echo "     - 使用 .dockerignore 排除无关文件"
    else
        echo "     ✅ 镜像大小合理"
    fi
}

# 分析构建层
analyze_build_layers() {
    echo "🏗️  构建层分析:"
    echo ""
    
    services=("server" "web" "admin")
    
    for service in "${services[@]}"; do
        image_name="weishi_com_nuxt3web-$service"
        
        if docker images | grep -q "$image_name"; then
            echo "   📦 $service 服务:"
            
            # 获取镜像历史
            layers=$(docker history "$image_name" --no-trunc --format "table {{.Size}}\t{{.CreatedBy}}" | tail -n +2 | head -10)
            
            echo "     最大的10个层:"
            echo "$layers" | while read line; do
                echo "       $line"
            done
            echo ""
        fi
    done
}

# 分析构建缓存
analyze_build_cache() {
    echo "💾 构建缓存分析:"
    echo ""
    
    # 检查构建缓存大小
    cache_size=$(docker system df | grep "Build Cache" | awk '{print $3}')
    
    if [ -n "$cache_size" ]; then
        echo "   当前缓存大小: $cache_size"
        
        # 缓存建议
        cache_mb=$(echo "$cache_size" | sed 's/GB/*1024/' | sed 's/MB//' | bc 2>/dev/null || echo "0")
        if (( $(echo "$cache_mb > 5000" | bc -l) )); then
            echo "   ⚠️  构建缓存较大，建议清理"
            echo "   清理命令: docker builder prune"
        else
            echo "   ✅ 构建缓存大小合理"
        fi
    else
        echo "   📊 无构建缓存数据"
    fi
    
    echo ""
}

# 分析 Dockerfile 优化
analyze_dockerfile_optimization() {
    echo "🔍 Dockerfile 优化分析:"
    echo ""
    
    dockerfiles=("server-go/Dockerfile" "web/Dockerfile" "admin/Dockerfile")
    
    for dockerfile in "${dockerfiles[@]}"; do
        if [ -f "$dockerfile" ]; then
            service=$(basename $(dirname "$dockerfile"))
            echo "   📄 $service ($dockerfile):"
            
            # 检查多阶段构建
            stages=$(grep -c "^FROM.*AS" "$dockerfile" || echo "0")
            if [ "$stages" -gt 0 ]; then
                echo "     ✅ 使用多阶段构建 ($stages 个阶段)"
            else
                echo "     ⚠️  未使用多阶段构建"
            fi
            
            # 检查 .dockerignore
            dockerignore_path=$(dirname "$dockerfile")/.dockerignore
            if [ -f "$dockerignore_path" ]; then
                echo "     ✅ 有 .dockerignore 文件"
            else
                echo "     ⚠️  缺少 .dockerignore 文件"
            fi
            
            # 检查层数
            layers=$(grep -c "^RUN\|^COPY\|^ADD" "$dockerfile" || echo "0")
            if [ "$layers" -lt 20 ]; then
                echo "     ✅ 层数合理 ($layers 层)"
            else
                echo "     ⚠️  层数较多 ($layers 层)，建议合并命令"
            fi
            
            echo ""
        fi
    done
}

# 性能测试
performance_test() {
    echo "⚡ 构建性能测试:"
    echo ""
    
    # 测试构建时间
    echo "   🕐 测试构建时间..."
    
    services=("web" "admin")  # 测试前端服务构建时间
    
    for service in "${services[@]}"; do
        echo "     测试 $service 服务构建时间..."
        
        start_time=$(date +%s)
        
        if docker-compose ${ENV_FILE:+--env-file=$ENV_FILE} build --no-cache "$service" > /dev/null 2>&1; then
            end_time=$(date +%s)
            duration=$((end_time - start_time))
            echo "     ✅ $service: ${duration}秒"
            
            # 性能评估
            if [ "$duration" -lt 120 ]; then
                echo "        🚀 构建速度很快"
            elif [ "$duration" -lt 300 ]; then
                echo "        ⚡ 构建速度正常"
            else
                echo "        🐌 构建速度较慢，建议优化"
            fi
        else
            echo "     ❌ $service: 构建失败"
        fi
        
        echo ""
    done
}

# 生成优化建议
generate_optimization_suggestions() {
    echo "💡 优化建议:"
    echo ""
    
    echo "   🏗️  构建优化:"
    echo "     1. 使用多阶段构建减少最终镜像大小"
    echo "     2. 合并 RUN 命令减少层数"
    echo "     3. 将变化频率低的操作放在前面"
    echo "     4. 使用 .dockerignore 排除不必要文件"
    echo ""
    
    echo "   💾 缓存优化:"
    echo "     1. 先复制依赖文件，再复制源代码"
    echo "     2. 使用 BuildKit 提高构建性能"
    echo "     3. 定期清理构建缓存"
    echo ""
    
    echo "   🚀 部署优化:"
    echo "     1. 使用并行构建提高效率"
    echo "     2. 在 CI/CD 中使用缓存策略"
    echo "     3. 考虑使用镜像仓库缓存"
    echo ""
    
    echo "   📊 监控建议:"
    echo "     1. 定期分析镜像大小变化"
    echo "     2. 监控构建时间趋势"
    echo "     3. 跟踪缓存命中率"
}

# 主函数
main() {
    echo "🔍 Docker 构建性能分析报告"
    echo "================================"
    echo ""
    
    analyze_image_sizes
    echo ""
    
    analyze_build_layers
    echo ""
    
    analyze_build_cache
    echo ""
    
    analyze_dockerfile_optimization
    echo ""
    
    # 询问是否进行性能测试
    read -p "是否进行构建性能测试？(这将重新构建部分服务) (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        performance_test
        echo ""
    fi
    
    generate_optimization_suggestions
    
    echo ""
    echo "📋 分析完成！"
    echo ""
    echo "🛠️  相关命令:"
    echo "   清理缓存: docker builder prune"
    echo "   清理镜像: docker image prune"
    echo "   系统清理: docker system prune"
    echo "   优化构建: ./scripts/build-optimized.sh"
}

# 显示帮助
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "构建性能分析脚本"
    echo ""
    echo "用法: $0"
    echo ""
    echo "功能:"
    echo "  - 分析镜像大小"
    echo "  - 分析构建层"
    echo "  - 分析构建缓存"
    echo "  - 分析 Dockerfile 优化"
    echo "  - 性能测试（可选）"
    echo "  - 生成优化建议"
    exit 0
fi

# 执行分析
main
