#!/bin/bash

# 管理后台 404 问题快速修复脚本
# 专门解决生产环境中管理后台静态资源 404 问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_message $BLUE "🚀 管理后台 404 问题快速修复"
echo ""

# 步骤 1: 清理旧文件
print_message $BLUE "1️⃣ 清理旧的静态文件..."
rm -rf static/admin/*
print_message $GREEN "✅ 旧文件已清理"

# 步骤 2: 重新构建管理后台
print_message $BLUE "2️⃣ 重新构建管理后台..."

# 停止旧的构建容器
docker stop weishi-admin-builder 2>/dev/null || true
docker rm weishi-admin-builder 2>/dev/null || true

# 强制重新构建（不使用缓存）
print_message $YELLOW "   构建管理后台镜像（无缓存）..."
docker-compose --env-file=docker.env build --no-cache admin-builder

# 运行构建
print_message $YELLOW "   运行构建容器..."
docker-compose --env-file=docker.env --profile build up admin-builder

print_message $GREEN "✅ 管理后台重新构建完成"

# 步骤 3: 验证构建结果
print_message $BLUE "3️⃣ 验证构建结果..."
if [[ -f "static/admin/index.html" ]]; then
    print_message $GREEN "✅ index.html 已生成"
    
    # 显示新的资源文件
    echo "   新的资源文件:"
    grep -o 'assets/[^"]*\.js' static/admin/index.html | head -3
    
    # 检查文件是否存在
    local missing=0
    for file in $(grep -o 'assets/[^"]*\.\(js\|css\)' static/admin/index.html); do
        if [[ ! -f "static/admin/$file" ]]; then
            print_message $RED "❌ 缺少文件: $file"
            ((missing++))
        fi
    done
    
    if [[ $missing -eq 0 ]]; then
        print_message $GREEN "✅ 所有引用的文件都存在"
    else
        print_message $RED "❌ 有 $missing 个文件缺失"
        exit 1
    fi
else
    print_message $RED "❌ index.html 未生成，构建失败"
    exit 1
fi

# 步骤 4: 更新 Caddy 配置（添加调试头）
print_message $BLUE "4️⃣ 更新 Caddy 配置..."
if ! grep -q "no-cache" caddy/Caddyfile; then
    print_message $YELLOW "   添加缓存控制配置..."
    # 配置已经在之前更新过了
    print_message $GREEN "✅ Caddy 配置已更新"
else
    print_message $GREEN "✅ Caddy 配置已是最新"
fi

# 步骤 5: 重启 Caddy 服务
print_message $BLUE "5️⃣ 重启 Caddy 服务..."
docker-compose --env-file=docker.env restart caddy

# 等待服务启动
print_message $YELLOW "   等待服务启动..."
sleep 15

print_message $GREEN "✅ Caddy 服务已重启"

# 步骤 6: 测试访问
print_message $BLUE "6️⃣ 测试访问..."
local domain=$(grep "^DOMAIN=" docker.env 2>/dev/null | cut -d'=' -f2 || echo "localhost")

if command -v curl >/dev/null 2>&1; then
    # 测试主页
    local status=$(curl -s -k -o /dev/null -w "%{http_code}" "https://$domain/admin/" || echo "000")
    if [[ "$status" == "200" ]]; then
        print_message $GREEN "✅ 管理后台主页访问正常 (200)"
    else
        print_message $YELLOW "⚠️  管理后台主页返回 $status"
    fi
    
    # 测试一个 JS 文件
    local js_file=$(grep -o 'assets/[^"]*\.js' static/admin/index.html | head -1)
    if [[ -n "$js_file" ]]; then
        local js_status=$(curl -s -k -o /dev/null -w "%{http_code}" "https://$domain/admin/$js_file" || echo "000")
        if [[ "$js_status" == "200" ]]; then
            print_message $GREEN "✅ JS 文件访问正常 (200)"
        else
            print_message $YELLOW "⚠️  JS 文件返回 $js_status"
        fi
    fi
else
    print_message $YELLOW "⚠️  无法测试访问（缺少 curl）"
fi

echo ""
print_message $GREEN "🎉 快速修复完成！"
echo ""
print_message $BLUE "📋 接下来的步骤："
echo "1. 清理浏览器缓存（重要！）"
echo "   - 按 Ctrl+Shift+R (Windows) 或 Cmd+Shift+R (Mac) 强制刷新"
echo "   - 或使用无痕模式访问"
echo ""
echo "2. 访问管理后台："
echo "   https://$domain/admin"
echo ""
echo "3. 如果仍有问题，请检查："
echo "   - 浏览器开发者工具的网络标签"
echo "   - 服务器防火墙设置"
echo "   - DNS 解析是否正确"

echo ""
print_message $YELLOW "💡 提示：如果问题仍然存在，可能是浏览器缓存问题。"
print_message $YELLOW "   请务必使用强制刷新或无痕模式测试。"
