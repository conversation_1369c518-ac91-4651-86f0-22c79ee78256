#!/bin/bash

# Caddy 缓存配置切换脚本
# 功能：在开发环境和生产环境的缓存配置之间切换

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：显示帮助信息
show_help() {
    echo "Caddy 缓存配置切换脚本"
    echo ""
    echo "用法: $0 [环境]"
    echo ""
    echo "环境:"
    echo "  dev, development        切换到开发环境配置（禁用缓存）"
    echo "  prod, production        切换到生产环境配置（启用缓存）"
    echo "  status                  显示当前配置状态"
    echo "  test                    测试当前缓存配置"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  --no-restart            不重启 Caddy 服务"
    echo ""
    echo "示例:"
    echo "  $0 dev                  # 切换到开发环境"
    echo "  $0 prod                 # 切换到生产环境"
    echo "  $0 status               # 查看当前状态"
    echo "  $0 test                 # 测试缓存配置"
}

# 函数：检查当前配置状态
check_status() {
    print_message $BLUE "🔍 检查当前配置状态..."
    echo ""
    
    if [[ -f "caddy/Caddyfile" ]]; then
        # 检查是否包含缓存配置
        if grep -q "max-age=31536000" caddy/Caddyfile; then
            print_message $GREEN "✅ 当前使用：生产环境配置（启用缓存）"
            echo "   - 带版本号的静态资源：1年缓存"
            echo "   - HTML 文件：不缓存"
            echo "   - 图标文件：1小时缓存"
        elif grep -q "no-cache, no-store, must-revalidate" caddy/Caddyfile; then
            if grep -q "X-Debug-Mode" caddy/Caddyfile; then
                print_message $YELLOW "⚠️  当前使用：开发环境配置（禁用缓存）"
                echo "   - 所有文件：不缓存"
                echo "   - 调试模式：启用"
            else
                print_message $BLUE "ℹ️  当前使用：混合配置"
                echo "   - HTML 文件：不缓存"
                echo "   - 静态资源：部分缓存"
            fi
        else
            print_message $YELLOW "⚠️  当前配置：未知状态"
        fi
        
        # 显示配置文件信息
        local file_size=$(du -h caddy/Caddyfile | cut -f1)
        local line_count=$(wc -l < caddy/Caddyfile)
        echo "   配置文件大小：$file_size"
        echo "   配置文件行数：$line_count"
        
    else
        print_message $RED "❌ Caddyfile 不存在"
    fi
    
    echo ""
    
    # 检查 Caddy 服务状态
    if docker ps | grep -q "weishi-caddy"; then
        print_message $GREEN "✅ Caddy 服务运行中"
    else
        print_message $YELLOW "⚠️  Caddy 服务未运行"
    fi
}

# 函数：切换到开发环境配置
switch_to_dev() {
    print_message $BLUE "🔧 切换到开发环境配置..."
    
    # 备份当前配置
    if [[ -f "caddy/Caddyfile" ]]; then
        cp caddy/Caddyfile caddy/Caddyfile.backup.$(date +%Y%m%d_%H%M%S)
        print_message $GREEN "✅ 当前配置已备份"
    fi
    
    # 检查开发环境配置文件是否存在
    if [[ -f "caddy/Caddyfile.dev" ]]; then
        cp caddy/Caddyfile.dev caddy/Caddyfile
        print_message $GREEN "✅ 已切换到开发环境配置"
        echo "   - 所有缓存已禁用"
        echo "   - 启用调试模式"
        echo "   - 详细日志记录"
    else
        print_message $RED "❌ 开发环境配置文件不存在: caddy/Caddyfile.dev"
        return 1
    fi
}

# 函数：切换到生产环境配置
switch_to_prod() {
    print_message $BLUE "🔧 切换到生产环境配置..."
    
    # 备份当前配置
    if [[ -f "caddy/Caddyfile" ]]; then
        cp caddy/Caddyfile caddy/Caddyfile.backup.$(date +%Y%m%d_%H%M%S)
        print_message $GREEN "✅ 当前配置已备份"
    fi
    
    # 检查是否有生产环境配置文件
    if [[ -f "caddy/Caddyfile.prod" ]]; then
        cp caddy/Caddyfile.prod caddy/Caddyfile
        print_message $GREEN "✅ 已切换到生产环境配置"
    else
        # 如果没有专门的生产环境配置，检查当前配置是否已经是生产环境配置
        if grep -q "max-age=31536000" caddy/Caddyfile 2>/dev/null; then
            print_message $GREEN "✅ 当前已是生产环境配置"
        else
            print_message $YELLOW "⚠️  没有找到专门的生产环境配置文件"
            print_message $BLUE "ℹ️  当前 Caddyfile 将被视为生产环境配置"
        fi
    fi
    
    print_message $GREEN "✅ 生产环境配置特性："
    echo "   - 带版本号的静态资源：1年缓存"
    echo "   - HTML 文件：不缓存"
    echo "   - 图标文件：1小时缓存"
    echo "   - 启用 GZIP 压缩"
    echo "   - 安全头配置"
}

# 函数：重启 Caddy 服务
restart_caddy() {
    print_message $BLUE "🔄 重启 Caddy 服务..."
    
    if docker ps | grep -q "weishi-caddy"; then
        docker-compose --env-file=docker.env restart caddy
        
        # 等待服务启动
        print_message $YELLOW "   等待服务启动..."
        sleep 10
        
        # 检查服务状态
        if docker ps | grep -q "weishi-caddy"; then
            print_message $GREEN "✅ Caddy 服务重启成功"
        else
            print_message $RED "❌ Caddy 服务重启失败"
            return 1
        fi
    else
        print_message $YELLOW "   启动 Caddy 服务..."
        docker-compose --env-file=docker.env up -d caddy
        
        # 等待服务启动
        sleep 15
        
        if docker ps | grep -q "weishi-caddy"; then
            print_message $GREEN "✅ Caddy 服务启动成功"
        else
            print_message $RED "❌ Caddy 服务启动失败"
            return 1
        fi
    fi
}

# 函数：测试缓存配置
test_cache_config() {
    print_message $BLUE "🧪 测试缓存配置..."
    
    if [[ -f "scripts/test-cache-headers.sh" ]]; then
        ./scripts/test-cache-headers.sh
    else
        print_message $YELLOW "⚠️  缓存测试脚本不存在"
        print_message $BLUE "ℹ️  手动测试方法："
        echo "   1. 打开浏览器开发者工具"
        echo "   2. 访问管理后台页面"
        echo "   3. 查看 Network 标签中的 Cache-Control 头"
    fi
}

# 主函数
main() {
    local environment=""
    local no_restart=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --no-restart)
                no_restart=true
                shift
                ;;
            dev|development)
                environment="dev"
                shift
                ;;
            prod|production)
                environment="prod"
                shift
                ;;
            status)
                environment="status"
                shift
                ;;
            test)
                environment="test"
                shift
                ;;
            *)
                print_message $RED "❌ 未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_message $BLUE "⚙️  Caddy 缓存配置管理"
    echo ""
    
    # 根据参数执行相应操作
    case $environment in
        "status")
            check_status
            ;;
        "test")
            test_cache_config
            ;;
        "dev")
            switch_to_dev
            if [[ "$no_restart" != "true" ]]; then
                restart_caddy
            fi
            echo ""
            check_status
            ;;
        "prod")
            switch_to_prod
            if [[ "$no_restart" != "true" ]]; then
                restart_caddy
            fi
            echo ""
            check_status
            ;;
        "")
            print_message $YELLOW "⚠️  请指定环境或操作"
            echo ""
            show_help
            ;;
        *)
            print_message $RED "❌ 未知环境: $environment"
            show_help
            exit 1
            ;;
    esac
    
    echo ""
    print_message $BLUE "💡 提示："
    echo "   - 开发环境：禁用缓存，便于调试"
    echo "   - 生产环境：启用缓存，提高性能"
    echo "   - 切换后建议清理浏览器缓存"
    echo "   - 使用 '$0 test' 验证配置是否生效"
}

# 执行主函数
main "$@"
