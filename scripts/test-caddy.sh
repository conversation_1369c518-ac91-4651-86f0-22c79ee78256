#!/bin/bash

# Caddy配置测试脚本

set -e

echo "🧪 测试Caddy配置..."

# 检查Caddyfile是否存在
if [ -f "caddy/Caddyfile" ]; then
    echo "✅ Caddyfile 文件存在"
else
    echo "❌ Caddyfile 文件不存在"
    exit 1
fi

# 使用Docker验证Caddyfile语法
echo "🔍 验证Caddyfile语法..."

if docker run --rm -v "$(pwd)/caddy/Caddyfile:/etc/caddy/Caddyfile:ro" caddy:2-alpine caddy validate --config /etc/caddy/Caddyfile; then
    echo "✅ Caddyfile 语法正确"
else
    echo "❌ Caddyfile 语法错误"
    exit 1
fi

# 测试Caddy服务配置
echo "🔍 测试Caddy服务配置..."

if docker-compose --env-file=docker.env --profile caddy config > /dev/null 2>&1; then
    echo "✅ Caddy服务配置正确"
else
    echo "❌ Caddy服务配置错误"
    exit 1
fi

echo "🎉 Caddy配置测试通过！"
echo ""
echo "📋 使用方法："
echo "   启动Caddy: docker-compose --env-file=docker.env --profile caddy up -d"
echo "   停止Caddy: docker-compose --env-file=docker.env --profile caddy down"
echo "   查看日志: docker-compose --env-file=docker.env logs caddy" 