#!/bin/bash

# Database Migration Script
# Usage: ./db-migrate.sh <action> <version> <backup_before> <compose_file> <env_file>

set -e

ACTION="$1"
VERSION="$2"
BACKUP_BEFORE="$3"
COMPOSE_FILE="$4"
ENV_FILE="$5"

echo "=== Database Migration Script ==="
echo "Action: ${ACTION}"
echo "Version: ${VERSION}"
echo "Backup before: ${BACKUP_BEFORE}"
echo "Compose file: ${COMPOSE_FILE}"
echo "Env file: ${ENV_FILE}"

# Basic checks
echo "=== Basic checks ==="
test -f "${COMPOSE_FILE}" || { echo "ERROR: ${COMPOSE_FILE} not found"; exit 1; }
test -f "${ENV_FILE}" || { echo "ERROR: ${ENV_FILE} not found"; exit 1; }
echo "Files found successfully"

# Check services
echo "=== Checking services ==="
docker compose -f "${COMPOSE_FILE}" --env-file "${ENV_FILE}" ps || true
echo "Service check completed"

# Backup functionality
echo "=== Backup functionality ==="
if [ "${BACKUP_BEFORE}" = "true" ] && { [ "${ACTION}" = "up" ] || [ "${ACTION}" = "down" ]; }; then
  echo "Backup is enabled, starting backup process"
  ts=$(date +%Y%m%d_%H%M%S)
  mkdir -p backups
  backup_file="backups/db_backup_${ts}.sql.gz"
  echo "Backup file: ${backup_file}"
  
  echo "Executing backup command..."
  docker compose -f "${COMPOSE_FILE}" --env-file "${ENV_FILE}" exec -T mysql \
    sh -lc 'mysqldump -u root -p"$MYSQL_ROOT_PASSWORD" "$MYSQL_DATABASE"' | gzip -c > "${backup_file}"
  backup_result=$?
  
  if [ $backup_result -eq 0 ]; then
    echo "SUCCESS: Backup completed successfully"
    ls -lh "${backup_file}"
  else
    echo "ERROR: Backup failed with exit code: $backup_result"
    exit 1
  fi
else
  echo "Backup skipped (BACKUP_BEFORE=${BACKUP_BEFORE}, ACTION=${ACTION})"
fi

# Migration file check and execution
echo "=== Migration file check and execution ==="
if [ -n "${VERSION}" ] && { [ "${ACTION}" = "up" ] || [ "${ACTION}" = "down" ]; }; then
  echo "Checking migration file..."
  migration_file="tmp/migration_${VERSION}.sql"
  
  if [ -f "${migration_file}" ]; then
    echo "Migration file found:"
    ls -lh "${migration_file}"
    echo "First 5 lines of migration file:"
    head -n 5 "${migration_file}"
    
    echo "Executing migration..."
    if cat "${migration_file}" | docker compose -f "${COMPOSE_FILE}" --env-file "${ENV_FILE}" exec -T mysql \
      sh -lc 'mysql -u root -p"$MYSQL_ROOT_PASSWORD" "$MYSQL_DATABASE"'; then
      echo "SUCCESS: Migration executed successfully"
    else
      echo "ERROR: Migration execution failed"
      exit 1
    fi
  else
    echo "WARNING: Migration file not found: ${migration_file}"
  fi
else
  echo "Migration execution skipped (VERSION=${VERSION}, ACTION=${ACTION})"
fi

# Status check
echo "=== Status check ==="
if docker compose -f "${COMPOSE_FILE}" --env-file "${ENV_FILE}" exec -T server \
  /bin/sh -lc 'cd /root/scripts && ENV=prod sh ./migrate-manager.sh status'; then
  echo "SUCCESS: Status check completed"
else
  echo "WARNING: Status check failed"
fi

# Cleanup
echo "=== Cleanup ==="
if [ -n "${VERSION}" ] && [ -f "tmp/migration_${VERSION}.sql" ]; then
  if rm -f "tmp/migration_${VERSION}.sql"; then
    echo "SUCCESS: Temporary file cleanup completed"
  else
    echo "WARNING: Temporary file cleanup failed"
  fi
fi

echo "=== Migration script completed successfully ==="
