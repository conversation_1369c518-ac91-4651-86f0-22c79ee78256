#!/bin/bash

# 优化构建脚本
# 使用多阶段构建和并行构建来提高构建效率

set -e

echo "🚀 开始优化构建..."

# 读取环境变量
ENV_FILE="docker.env"
if [ ! -f "$ENV_FILE" ]; then
    echo "⚠️  环境变量文件 $ENV_FILE 不存在，使用默认配置"
    ENV_FILE=""
fi

# 构建选项
PARALLEL_BUILD=${1:-true}
NO_CACHE=${2:-false}
SERVICES=${3:-"all"}

echo "📊 构建配置:"
echo "   并行构建: $PARALLEL_BUILD"
echo "   禁用缓存: $NO_CACHE"
echo "   构建服务: $SERVICES"
echo ""

# 构建参数
BUILD_ARGS=""
if [ "$NO_CACHE" = "true" ]; then
    BUILD_ARGS="--no-cache"
fi

if [ -n "$ENV_FILE" ]; then
    BUILD_ARGS="$BUILD_ARGS --env-file=$ENV_FILE"
fi

# 清理旧镜像函数
cleanup_images() {
    echo "🧹 清理旧镜像..."
    
    # 清理悬空镜像
    docker image prune -f
    
    # 清理旧的项目镜像（可选）
    read -p "是否清理项目的旧镜像？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker images | grep "weishi_com_nuxt3web" | awk '{print $3}' | xargs -r docker rmi -f || true
    fi
}

# 构建单个服务
build_service() {
    local service=$1
    echo "🔨 构建 $service 服务..."
    
    start_time=$(date +%s)
    
    if docker-compose $BUILD_ARGS build $service; then
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        echo "✅ $service 构建完成 (耗时: ${duration}秒)"
        return 0
    else
        echo "❌ $service 构建失败"
        return 1
    fi
}

# 并行构建函数
parallel_build() {
    local services=("$@")
    local pids=()
    local results=()
    
    echo "🔄 开始并行构建..."
    
    # 启动并行构建
    for service in "${services[@]}"; do
        (
            build_service "$service"
            echo $? > "/tmp/build_result_$service"
        ) &
        pids+=($!)
    done
    
    # 等待所有构建完成
    for pid in "${pids[@]}"; do
        wait $pid
    done
    
    # 检查构建结果
    local failed_services=()
    for service in "${services[@]}"; do
        if [ -f "/tmp/build_result_$service" ]; then
            result=$(cat "/tmp/build_result_$service")
            if [ "$result" != "0" ]; then
                failed_services+=("$service")
            fi
            rm -f "/tmp/build_result_$service"
        fi
    done
    
    if [ ${#failed_services[@]} -gt 0 ]; then
        echo "❌ 以下服务构建失败: ${failed_services[*]}"
        return 1
    else
        echo "✅ 所有服务构建成功"
        return 0
    fi
}

# 顺序构建函数
sequential_build() {
    local services=("$@")
    local failed_services=()
    
    echo "🔄 开始顺序构建..."
    
    for service in "${services[@]}"; do
        if ! build_service "$service"; then
            failed_services+=("$service")
        fi
    done
    
    if [ ${#failed_services[@]} -gt 0 ]; then
        echo "❌ 以下服务构建失败: ${failed_services[*]}"
        return 1
    else
        echo "✅ 所有服务构建成功"
        return 0
    fi
}

# 显示构建统计
show_build_stats() {
    echo ""
    echo "📊 构建统计:"
    
    # 显示镜像大小
    echo "   镜像大小:"
    docker images | grep "weishi_com_nuxt3web" | while read line; do
        echo "     $line"
    done
    
    # 显示总大小
    total_size=$(docker images | grep "weishi_com_nuxt3web" | awk '{print $7}' | sed 's/MB//' | sed 's/GB/*1024/' | bc 2>/dev/null | awk '{sum+=$1} END {print sum}' || echo "0")
    echo "   总大小: ${total_size}MB"
}

# 主构建流程
main() {
    # 清理选项
    read -p "是否在构建前清理旧镜像？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cleanup_images
    fi
    
    # 确定要构建的服务
    if [ "$SERVICES" = "all" ]; then
        services=("mysql" "server" "web" "admin")
    else
        IFS=',' read -ra services <<< "$SERVICES"
    fi
    
    echo "🎯 将构建以下服务: ${services[*]}"
    echo ""
    
    # 记录开始时间
    total_start_time=$(date +%s)
    
    # 执行构建
    if [ "$PARALLEL_BUILD" = "true" ] && [ ${#services[@]} -gt 1 ]; then
        if parallel_build "${services[@]}"; then
            build_success=true
        else
            build_success=false
        fi
    else
        if sequential_build "${services[@]}"; then
            build_success=true
        else
            build_success=false
        fi
    fi
    
    # 记录结束时间
    total_end_time=$(date +%s)
    total_duration=$((total_end_time - total_start_time))
    
    echo ""
    echo "⏱️  总构建时间: ${total_duration}秒"
    
    if [ "$build_success" = true ]; then
        show_build_stats
        echo ""
        echo "🎉 构建完成！"
        echo ""
        echo "🚀 下一步操作:"
        echo "   启动服务: docker-compose $BUILD_ARGS up -d"
        echo "   查看状态: docker-compose $BUILD_ARGS ps"
        echo "   查看日志: docker-compose $BUILD_ARGS logs -f"
        exit 0
    else
        echo ""
        echo "❌ 构建失败，请检查错误信息"
        echo ""
        echo "🔧 故障排除建议:"
        echo "   1. 检查 Dockerfile 语法"
        echo "   2. 确保网络连接正常"
        echo "   3. 尝试单独构建失败的服务"
        echo "   4. 使用 --no-cache 重新构建"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "优化构建脚本使用说明:"
    echo ""
    echo "用法: $0 [并行构建] [禁用缓存] [服务列表]"
    echo ""
    echo "参数:"
    echo "  并行构建    true/false (默认: true)"
    echo "  禁用缓存    true/false (默认: false)"
    echo "  服务列表    all 或 service1,service2,... (默认: all)"
    echo ""
    echo "示例:"
    echo "  $0                           # 默认并行构建所有服务"
    echo "  $0 false                     # 顺序构建所有服务"
    echo "  $0 true true                 # 并行构建，禁用缓存"
    echo "  $0 true false web,admin      # 并行构建web和admin服务"
    echo ""
    echo "可用服务: mysql, server, web, admin"
}

# 检查参数
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    show_help
    exit 0
fi

# 执行主流程
main
