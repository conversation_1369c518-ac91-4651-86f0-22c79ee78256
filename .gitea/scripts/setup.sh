#!/bin/bash

# Gitea Actions 快速设置脚本
# 用于初始化和配置 Gitea Actions CI/CD

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    echo "========================================"
    echo "    Gitea Actions CI/CD 设置向导"
    echo "========================================"
    echo
    print_info "本脚本将帮助您配置 Gitea Actions CI/CD 流水线"
    echo
}

# 检查项目结构
check_project() {
    print_info "检查项目结构..."
    
    local required_dirs=("web" "admin" "server-go" "deployment")
    local missing_dirs=()
    
    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            missing_dirs+=("$dir")
        fi
    done
    
    if [ ${#missing_dirs[@]} -gt 0 ]; then
        print_error "缺少必需的目录: ${missing_dirs[*]}"
        print_info "请确保在正确的项目根目录运行此脚本"
        exit 1
    fi
    
    print_success "项目结构检查通过"
}

# 检查 Dockerfile
check_dockerfiles() {
    print_info "检查 Dockerfile..."
    
    local dockerfiles=("deployment/Dockerfile.web" "deployment/Dockerfile.admin" "deployment/Dockerfile.server-go")
    local missing_files=()
    
    for dockerfile in "${dockerfiles[@]}"; do
        if [ ! -f "$dockerfile" ]; then
            missing_files+=("$dockerfile")
        fi
    done
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        print_error "缺少 Dockerfile: ${missing_files[*]}"
        print_info "请确保所有 Dockerfile 都已创建"
        exit 1
    fi
    
    print_success "Dockerfile 检查通过"
}

# 配置镜像仓库信息
configure_registry() {
    print_info "配置镜像仓库信息..."
    echo
    
    # 读取配置
    echo "请输入镜像仓库配置信息："
    echo
    
    read -p "镜像仓库地址 [registry.cn-hangzhou.aliyuncs.com]: " registry_url
    registry_url=${registry_url:-registry.cn-hangzhou.aliyuncs.com}
    
    read -p "命名空间 [vest]: " namespace
    namespace=${namespace:-vest}
    
    read -p "镜像仓库用户名: " registry_username
    read -s -p "镜像仓库密码: " registry_password
    echo
    
    if [ -z "$registry_username" ] || [ -z "$registry_password" ]; then
        print_error "用户名和密码不能为空"
        exit 1
    fi
    
    # 创建配置文件
    cat > .gitea/registry-config.env << EOF
# 镜像仓库配置
# 请将这些信息添加到 Gitea 仓库的 Secrets 中

REGISTRY_URL=$registry_url
NAMESPACE=$namespace
REGISTRY_USERNAME=$registry_username
REGISTRY_PASSWORD=$registry_password
EOF
    
    print_success "镜像仓库配置已保存到 .gitea/registry-config.env"
    print_warning "请不要将此文件提交到版本控制系统！"
}

# 测试 Docker 连接
test_docker() {
    print_info "测试 Docker 连接..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        return 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker 服务未运行，请启动 Docker"
        return 1
    fi
    
    print_success "Docker 连接正常"
}

# 测试镜像仓库连接
test_registry() {
    print_info "测试镜像仓库连接..."
    
    if [ ! -f ".gitea/registry-config.env" ]; then
        print_warning "未找到镜像仓库配置，跳过连接测试"
        return 0
    fi
    
    source .gitea/registry-config.env
    
    print_info "尝试登录镜像仓库: $REGISTRY_URL"
    
    if echo "$REGISTRY_PASSWORD" | docker login "$REGISTRY_URL" -u "$REGISTRY_USERNAME" --password-stdin; then
        print_success "镜像仓库连接成功"
        docker logout "$REGISTRY_URL" &> /dev/null
    else
        print_error "镜像仓库连接失败，请检查凭证"
        return 1
    fi
}

# 运行本地构建测试
run_build_test() {
    print_info "是否运行本地构建测试？"
    read -p "运行构建测试 (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "开始本地构建测试..."
        
        if [ -f ".gitea/scripts/local-build.sh" ]; then
            ./.gitea/scripts/local-build.sh --dry-run
            
            read -p "是否继续完整构建测试 (y/N): " -n 1 -r
            echo
            
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                ./.gitea/scripts/local-build.sh -s web
                print_success "构建测试完成"
            fi
        else
            print_error "构建测试脚本不存在"
        fi
    fi
}

# 生成 Secrets 配置指南
generate_secrets_guide() {
    print_info "生成 Secrets 配置指南..."
    
    cat > .gitea/SECRETS_SETUP.md << 'EOF'
# Gitea Secrets 配置指南

## 在 Gitea 中配置 Secrets

1. 进入你的 Gitea 仓库
2. 点击 "Settings" (设置)
3. 在左侧菜单中选择 "Secrets"
4. 点击 "Add Secret" 添加以下 Secrets：

### 必需的 Secrets

| Secret 名称 | 描述 | 示例值 |
|------------|------|--------|
| `REGISTRY_USERNAME` | 镜像仓库用户名 | your_username |
| `REGISTRY_PASSWORD` | 镜像仓库密码 | your_password |

### 可选的 Secrets

| Secret 名称 | 描述 | 默认值 |
|------------|------|--------|
| `REGISTRY_URL` | 镜像仓库地址 | registry.cn-hangzhou.aliyuncs.com |
| `NAMESPACE` | 镜像命名空间 | vest |

## 阿里云容器镜像服务配置

### 1. 创建命名空间
1. 登录阿里云控制台
2. 进入容器镜像服务
3. 创建命名空间（如：vest）

### 2. 创建镜像仓库
创建以下镜像仓库：
- `weizhi_web` - Web前端
- `weizhi_admin` - 管理后台
- `weizhi_server` - Go后端服务

### 3. 获取访问凭证
1. 在容器镜像服务中找到"访问凭证"
2. 设置固定密码
3. 记录用户名和密码

## 验证配置

配置完成后，推送代码到 main 分支测试自动构建：

```bash
git add .
git commit -m "feat: 配置 Gitea Actions CI/CD"
git push origin main
```

然后在 Gitea 的 Actions 页面查看构建状态。
EOF

    print_success "Secrets 配置指南已生成: .gitea/SECRETS_SETUP.md"
}

# 创建 .gitignore 规则
update_gitignore() {
    print_info "更新 .gitignore..."
    
    local gitignore_rules="
# Gitea Actions 相关
.gitea/registry-config.env
.gitea/config-report.md
build-report-*.md
"
    
    if [ -f ".gitignore" ]; then
        if ! grep -q ".gitea/registry-config.env" .gitignore; then
            echo "$gitignore_rules" >> .gitignore
            print_success ".gitignore 已更新"
        else
            print_info ".gitignore 已包含相关规则"
        fi
    else
        echo "$gitignore_rules" > .gitignore
        print_success ".gitignore 已创建"
    fi
}

# 显示完成信息
show_completion() {
    echo
    print_success "=== 设置完成 ==="
    echo
    print_info "下一步操作："
    echo "1. 查看 .gitea/SECRETS_SETUP.md 配置 Gitea Secrets"
    echo "2. 推送代码到 main 分支测试自动构建"
    echo "3. 在 Gitea Actions 页面监控构建状态"
    echo
    print_info "有用的命令："
    echo "- 检查配置: ./.gitea/scripts/check-config.sh"
    echo "- 本地构建测试: ./.gitea/scripts/local-build.sh"
    echo "- 查看工作流: ls .gitea/workflows/"
    echo
    print_warning "重要提醒："
    echo "- 不要提交 .gitea/registry-config.env 文件"
    echo "- 确保在 Gitea 中正确配置 Secrets"
    echo "- 首次构建可能需要较长时间"
}

# 主函数
main() {
    show_welcome
    
    check_project
    echo
    
    check_dockerfiles
    echo
    
    test_docker
    echo
    
    configure_registry
    echo
    
    test_registry
    echo
    
    run_build_test
    echo
    
    generate_secrets_guide
    echo
    
    update_gitignore
    echo
    
    show_completion
}

# 显示帮助
show_help() {
    echo "Gitea Actions 设置脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  --skip-test         跳过构建测试"
    echo "  --config-only       只生成配置文件"
    echo
    echo "功能:"
    echo "  - 检查项目结构"
    echo "  - 配置镜像仓库信息"
    echo "  - 测试 Docker 连接"
    echo "  - 生成配置指南"
    echo "  - 运行构建测试"
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    --skip-test)
        SKIP_TEST=true
        main
        ;;
    --config-only)
        configure_registry
        generate_secrets_guide
        update_gitignore
        ;;
    "")
        main
        ;;
    *)
        print_error "未知选项: $1"
        show_help
        exit 1
        ;;
esac
