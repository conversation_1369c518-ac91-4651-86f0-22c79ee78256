#!/bin/bash

# 本地构建测试脚本
# 用于在本地测试 Gitea Actions 的构建流程

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
REGISTRY_URL="${REGISTRY_URL:-registry.cn-hangzhou.aliyuncs.com}"
NAMESPACE="${NAMESPACE:-vest}"
VERSION="${VERSION:-local-$(date +%Y%m%d-%H%M%S)}"
BUILD_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ)

# 服务列表
SERVICES=("web" "admin" "server-go")

# 获取仓库名称
get_repo_name() {
    local service=$1
    case $service in
        "server-go")
            echo "weizhi_server"
            ;;
        "web")
            echo "weizhi_web"
            ;;
        "admin")
            echo "weizhi_admin"
            ;;
        *)
            echo "unknown"
            ;;
    esac
}

# 检查环境
check_environment() {
    print_info "检查构建环境..."
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装"
        exit 1
    fi
    
    print_success "Docker 已安装: $(docker --version)"
    
    # 检查 Docker 是否运行
    if ! docker info &> /dev/null; then
        print_error "Docker 服务未运行"
        exit 1
    fi
    
    print_success "Docker 服务正在运行"
    
    # 检查项目结构
    for service in "${SERVICES[@]}"; do
        if [ ! -f "deployment/Dockerfile.${service}" ]; then
            print_error "缺少 Dockerfile: deployment/Dockerfile.${service}"
            exit 1
        fi
    done
    
    print_success "项目结构检查通过"
}

# 构建单个服务
build_service() {
    local service=$1
    local repo_name=$(get_repo_name "$service")
    local image_name="${REGISTRY_URL}/${NAMESPACE}/${repo_name}"
    local dockerfile="deployment/Dockerfile.${service}"
    
    print_info "构建服务: $service"
    print_info "镜像名称: ${image_name}:${VERSION}"
    
    # 设置构建参数
    local build_args=""
    case $service in
        "server-go")
            build_args="--build-arg VERSION=${VERSION} --build-arg BUILD_TIME=${BUILD_TIME}"
            ;;
        "web")
            build_args="--build-arg NODE_ENV=production --build-arg NITRO_PRESET=node-server"
            ;;
        "admin")
            build_args="--build-arg NODE_ENV=production --build-arg VITE_API_BASE_URL=/api"
            ;;
    esac
    
    # 构建镜像
    print_info "开始构建..."
    if docker build \
        --platform linux/amd64 \
        -f "$dockerfile" \
        $build_args \
        -t "${image_name}:${VERSION}" \
        -t "${image_name}:local" \
        --progress=plain \
        .; then
        
        print_success "构建成功: $service"
        
        # 显示镜像信息
        local image_size=$(docker images "${image_name}:${VERSION}" --format "table {{.Size}}" | tail -n 1)
        print_info "镜像大小: $image_size"
        
        return 0
    else
        print_error "构建失败: $service"
        return 1
    fi
}

# 运行代码检查
run_lint_checks() {
    print_info "运行代码检查..."
    
    # Web 前端检查
    if [ -d "web" ] && [ -f "web/package.json" ]; then
        print_info "检查 Web 前端..."
        cd web
        if [ -f "package-lock.json" ]; then
            npm ci
        else
            npm install
        fi
        
        # 运行 lint（如果失败不中断）
        npm run lint || print_warning "Web lint 检查有警告"
        
        # 类型检查（如果存在）
        if npm run | grep -q "type-check"; then
            npm run type-check || print_warning "Web 类型检查有警告"
        fi
        
        cd ..
        print_success "Web 前端检查完成"
    fi
    
    # Admin 后台检查
    if [ -d "admin" ] && [ -f "admin/package.json" ]; then
        print_info "检查 Admin 后台..."
        cd admin
        if [ -f "package-lock.json" ]; then
            npm ci
        else
            npm install
        fi
        
        # 运行 lint
        npm run lint || print_warning "Admin lint 检查有警告"
        
        # 类型检查
        npm run type-check || print_warning "Admin 类型检查有警告"
        
        cd ..
        print_success "Admin 后台检查完成"
    fi
    
    # Go 后端检查
    if [ -d "server-go" ] && [ -f "server-go/go.mod" ]; then
        print_info "检查 Go 后端..."
        cd server-go
        
        # Go mod verify
        go mod verify
        
        # Go vet
        go vet ./...
        
        # Go test
        go test -v ./... || print_warning "Go 测试有失败"
        
        cd ..
        print_success "Go 后端检查完成"
    fi
}

# 清理镜像
cleanup_images() {
    if [ "$CLEANUP" = "true" ]; then
        print_info "清理本地镜像..."
        for service in "${SERVICES[@]}"; do
            local repo_name=$(get_repo_name "$service")
            local image_name="${REGISTRY_URL}/${NAMESPACE}/${repo_name}"
            docker rmi "${image_name}:${VERSION}" "${image_name}:local" 2>/dev/null || true
        done
        print_success "镜像清理完成"
    fi
}

# 生成构建报告
generate_build_report() {
    print_info "生成构建报告..."
    
    local report_file="build-report-${VERSION}.md"
    
    cat > "$report_file" << EOF
# 本地构建报告

- **构建时间**: ${BUILD_TIME}
- **版本**: ${VERSION}
- **Git 提交**: $(git rev-parse HEAD 2>/dev/null || echo "N/A")
- **Git 分支**: $(git branch --show-current 2>/dev/null || echo "N/A")

## 构建的镜像

EOF

    for service in "${SERVICES[@]}"; do
        local repo_name=$(get_repo_name "$service")
        local image_name="${REGISTRY_URL}/${NAMESPACE}/${repo_name}"
        
        if docker inspect "${image_name}:${VERSION}" >/dev/null 2>&1; then
            local image_size=$(docker images "${image_name}:${VERSION}" --format "table {{.Size}}" | tail -n 1)
            echo "- **${service}**: \`${image_name}:${VERSION}\` (${image_size})" >> "$report_file"
        else
            echo "- **${service}**: ❌ 构建失败" >> "$report_file"
        fi
    done
    
    cat >> "$report_file" << EOF

## 使用方法

### 运行镜像
\`\`\`bash
# Web 前端
docker run -p 3000:3000 ${REGISTRY_URL}/${NAMESPACE}/weizhi_web:${VERSION}

# Admin 后台
docker run -p 8080:80 ${REGISTRY_URL}/${NAMESPACE}/weizhi_admin:${VERSION}

# Go 后端
docker run -p 8000:8000 ${REGISTRY_URL}/${NAMESPACE}/weizhi_server:${VERSION}
\`\`\`

### 推送到仓库
\`\`\`bash
# 登录镜像仓库
docker login ${REGISTRY_URL}

# 推送镜像
docker push ${REGISTRY_URL}/${NAMESPACE}/weizhi_web:${VERSION}
docker push ${REGISTRY_URL}/${NAMESPACE}/weizhi_admin:${VERSION}
docker push ${REGISTRY_URL}/${NAMESPACE}/weizhi_server:${VERSION}
\`\`\`
EOF

    print_success "构建报告已生成: $report_file"
}

# 主函数
main() {
    print_info "=== 本地构建测试 ==="
    print_info "版本: $VERSION"
    print_info "镜像仓库: $REGISTRY_URL/$NAMESPACE"
    echo
    
    # 检查环境
    check_environment
    echo
    
    # 运行代码检查
    if [ "$SKIP_LINT" != "true" ]; then
        run_lint_checks
        echo
    fi
    
    # 构建所有服务
    local failed_services=()
    for service in "${SERVICES[@]}"; do
        if [ -n "$BUILD_SERVICE" ] && [ "$BUILD_SERVICE" != "$service" ]; then
            continue
        fi
        
        if ! build_service "$service"; then
            failed_services+=("$service")
        fi
        echo
    done
    
    # 检查构建结果
    if [ ${#failed_services[@]} -gt 0 ]; then
        print_error "以下服务构建失败: ${failed_services[*]}"
        exit 1
    fi
    
    # 生成报告
    generate_build_report
    
    # 清理镜像
    cleanup_images
    
    print_success "本地构建测试完成！"
}

# 显示帮助
show_help() {
    echo "本地构建测试脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  -s, --service SERVICE   只构建指定服务 (web|admin|server-go)"
    echo "  -v, --version VERSION   指定版本标签"
    echo "  -c, --cleanup           构建后清理镜像"
    echo "  --skip-lint             跳过代码检查"
    echo "  --dry-run               只检查环境，不构建"
    echo
    echo "环境变量:"
    echo "  REGISTRY_URL            镜像仓库地址"
    echo "  NAMESPACE               命名空间"
    echo "  VERSION                 版本标签"
    echo "  BUILD_SERVICE           指定构建的服务"
    echo "  CLEANUP                 是否清理镜像"
    echo "  SKIP_LINT               跳过代码检查"
    echo
    echo "示例:"
    echo "  $0                      # 构建所有服务"
    echo "  $0 -s web              # 只构建 web 服务"
    echo "  $0 -v v1.0.0 -c        # 指定版本并清理"
    echo "  $0 --dry-run           # 只检查环境"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -s|--service)
            BUILD_SERVICE="$2"
            shift 2
            ;;
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -c|--cleanup)
            CLEANUP="true"
            shift
            ;;
        --skip-lint)
            SKIP_LINT="true"
            shift
            ;;
        --dry-run)
            check_environment
            print_success "环境检查通过，可以进行构建"
            exit 0
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证服务名称
if [ -n "$BUILD_SERVICE" ] && [[ ! " ${SERVICES[@]} " =~ " ${BUILD_SERVICE} " ]]; then
    print_error "无效的服务名称: $BUILD_SERVICE"
    print_info "可用的服务: ${SERVICES[*]}"
    exit 1
fi

# 执行主函数
main
