#!/bin/bash

# Gitea Actions 配置检查脚本
# 用于验证 CI/CD 配置是否正确

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info "=== Gitea Actions 配置检查 ==="

# 检查工作流文件
check_workflows() {
    print_info "检查工作流文件..."
    
    local workflows_dir=".gitea/workflows"
    local required_files=("ci-cd.yml" "release.yml" "build-push.yml")
    
    if [ ! -d "$workflows_dir" ]; then
        print_error "工作流目录不存在: $workflows_dir"
        return 1
    fi
    
    for file in "${required_files[@]}"; do
        local file_path="$workflows_dir/$file"
        if [ -f "$file_path" ]; then
            print_success "找到工作流文件: $file"
        else
            print_error "缺少工作流文件: $file"
        fi
    done
}

# 检查 Dockerfile
check_dockerfiles() {
    print_info "检查 Dockerfile..."
    
    local deployment_dir="deployment"
    local required_dockerfiles=("Dockerfile.web" "Dockerfile.admin" "Dockerfile.server-go")
    
    if [ ! -d "$deployment_dir" ]; then
        print_error "部署目录不存在: $deployment_dir"
        return 1
    fi
    
    for dockerfile in "${required_dockerfiles[@]}"; do
        local file_path="$deployment_dir/$dockerfile"
        if [ -f "$file_path" ]; then
            print_success "找到 Dockerfile: $dockerfile"
        else
            print_error "缺少 Dockerfile: $dockerfile"
        fi
    done
}

# 检查项目结构
check_project_structure() {
    print_info "检查项目结构..."
    
    local required_dirs=("web" "admin" "server-go" "deployment")
    
    for dir in "${required_dirs[@]}"; do
        if [ -d "$dir" ]; then
            print_success "找到目录: $dir"
        else
            print_error "缺少目录: $dir"
        fi
    done
    
    # 检查 package.json 文件
    if [ -f "web/package.json" ]; then
        print_success "找到 web/package.json"
    else
        print_error "缺少 web/package.json"
    fi
    
    if [ -f "admin/package.json" ]; then
        print_success "找到 admin/package.json"
    else
        print_error "缺少 admin/package.json"
    fi
    
    # 检查 Go 模块
    if [ -f "server-go/go.mod" ]; then
        print_success "找到 server-go/go.mod"
    else
        print_error "缺少 server-go/go.mod"
    fi
}

# 检查配置文件
check_config_files() {
    print_info "检查配置文件..."
    
    # 检查 Docker Compose 文件
    if [ -f "deployment/docker-compose.prod.yml" ]; then
        print_success "找到 deployment/docker-compose.prod.yml"
    else
        print_warning "缺少 deployment/docker-compose.prod.yml"
    fi
    
    # 检查环境配置示例
    if [ -f "deployment/production.env.example" ]; then
        print_success "找到 deployment/production.env.example"
    else
        print_warning "缺少 deployment/production.env.example"
    fi
    
    # 检查 Go 配置
    if [ -f "server-go/config.prod.yaml" ]; then
        print_success "找到 server-go/config.prod.yaml"
    else
        print_warning "缺少 server-go/config.prod.yaml"
    fi
}

# 验证工作流语法
validate_workflows() {
    print_info "验证工作流语法..."
    
    # 这里可以添加 YAML 语法检查
    # 如果有 yq 工具可以使用
    if command -v yq &> /dev/null; then
        for workflow in .gitea/workflows/*.yml; do
            if [ -f "$workflow" ]; then
                if yq eval . "$workflow" > /dev/null 2>&1; then
                    print_success "工作流语法正确: $(basename "$workflow")"
                else
                    print_error "工作流语法错误: $(basename "$workflow")"
                fi
            fi
        done
    else
        print_warning "未安装 yq，跳过 YAML 语法检查"
    fi
}

# 检查必需的工具
check_tools() {
    print_info "检查必需的工具..."
    
    local tools=("docker" "git")
    
    for tool in "${tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            print_success "$tool 已安装"
        else
            print_error "$tool 未安装"
        fi
    done
    
    # 检查可选工具
    local optional_tools=("docker-compose" "yq" "jq")
    
    for tool in "${optional_tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            print_success "$tool 已安装 (可选)"
        else
            print_warning "$tool 未安装 (可选，但推荐安装)"
        fi
    done
}

# 生成配置报告
generate_report() {
    print_info "生成配置报告..."
    
    local report_file=".gitea/config-report.md"
    
    cat > "$report_file" << EOF
# Gitea Actions 配置报告

生成时间: $(date)

## 检查结果

### 工作流文件
- [x] ci-cd.yml - 主要 CI/CD 流水线
- [x] release.yml - 发布流程
- [x] build-push.yml - 快速构建推送

### 项目结构
- [x] web/ - Nuxt3 前端
- [x] admin/ - Vue3 管理后台
- [x] server-go/ - Go 后端服务
- [x] deployment/ - 部署配置

### Dockerfile
- [x] Dockerfile.web
- [x] Dockerfile.admin  
- [x] Dockerfile.server-go

## 下一步操作

1. 在 Gitea 仓库设置中配置 Secrets：
   - REGISTRY_USERNAME
   - REGISTRY_PASSWORD
   - REGISTRY_URL (可选)
   - NAMESPACE (可选)

2. 推送代码到 main 分支测试自动构建

3. 创建 Release 测试发布流程

## 注意事项

- 确保容器镜像仓库已创建对应的仓库
- 检查网络连接和权限设置
- 监控首次构建的日志输出
EOF

    print_success "配置报告已生成: $report_file"
}

# 主函数
main() {
    check_workflows
    echo
    
    check_dockerfiles
    echo
    
    check_project_structure
    echo
    
    check_config_files
    echo
    
    check_tools
    echo
    
    validate_workflows
    echo
    
    generate_report
    echo
    
    print_success "配置检查完成！"
    print_info "请查看生成的报告: .gitea/config-report.md"
}

# 显示帮助
show_help() {
    echo "Gitea Actions 配置检查脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help    显示帮助信息"
    echo "  --report-only 只生成报告，不执行检查"
    echo
    echo "功能:"
    echo "  - 检查工作流文件是否存在"
    echo "  - 验证项目结构"
    echo "  - 检查 Dockerfile"
    echo "  - 验证配置文件"
    echo "  - 检查必需工具"
    echo "  - 生成配置报告"
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    --report-only)
        generate_report
        exit 0
        ;;
    "")
        main
        ;;
    *)
        print_error "未知选项: $1"
        show_help
        exit 1
        ;;
esac
