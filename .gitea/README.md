# Gitea Actions CI/CD 配置

本项目使用 Gitea Actions 进行自动化构建和部署。

## 🚀 工作流概览

### 1. 构建并推送 (`build.yml`)
**触发方式**：
- ✅ 手动触发 - 完全可控
- ✅ Release 发布 - 自动触发

**功能特性**：
- 🎯 选择构建服务（web/admin/server-go/all）
- 🔄 双仓库支持（外部仓库 + Gitea 软件包仓库）
- 🏷️ 自定义标签和版本
- 📊 详细构建报告
- 🔧 支持 HTTP Registry（内网 Gitea）

### 2. 监控回滚 (`monitor-rollback.yml`)
- 服务监控和自动回滚功能

### 3. 自动化部署 (`deploy.yml`)
**触发方式**：
- ✅ 手动触发 - 选择服务进行部署

**功能特性**：
- 🔄 滚动更新和零停机部署
- 🏥 自动健康检查
- 📦 部署前自动备份
- 🔧 支持选择性服务部署

### 4. 服务回滚 (`rollback.yml`)
**回滚类型**：
- 🔄 回滚到上一版本
- 📋 回滚到指定备份
- 🚨 紧急停止服务

### 5. 监控回滚 (`monitor-rollback.yml`)
- 服务监控和自动回滚功能

### 6. 快速部署 (`quick-deploy.yml`)
- 快速部署功能

## ⚙️ 配置要求

### 外部镜像仓库配置 (Variables)
```
REGISTRY_URL=registry.cn-hangzhou.aliyuncs.com  # 外部仓库地址
NAMESPACE=vest                                  # 命名空间
```

### 外部镜像仓库认证 (Secrets)
```
REGISTRY_USERNAME=your_username    # 外部仓库用户名
REGISTRY_PASSWORD=your_password    # 外部仓库密码
```

### Gitea 软件包仓库配置
```
# Variables
PACKAGE_REGISTRY=1.94.51.79:6001  # Gitea 软件包仓库地址

# Secrets
PACKAGE_TOKEN=your_gitea_token     # Gitea 访问令牌 (需要 write:packages 权限)
```

### 🎯 简化配置管理

**新方案优势**：只需配置 **10-15 个关键变量**，系统自动生成完整配置！

#### 🔐 核心密钥 (Secrets)
```bash
# 数据库密码
MYSQL_ROOT_PASSWORD=Ydb3344%
MYSQL_PASSWORD=Ydb3344%

# 应用密钥
JWT_SECRET=your-super-secret-jwt-key-change-in-production-environment12312
ADMIN_PASSWORD=admin123

# SSH 连接密钥
PROD_SSH_PRIVATE_KEY=-----BEGIN OPENSSH PRIVATE KEY-----

# 腾讯云COS密钥（可选）
COS_SECRET_ID=your_cos_secret_id
COS_SECRET_KEY=your_cos_secret_key
```

#### 🌍 环境配置 (Variables)
```bash
# 镜像仓库（基于你当前配置）
REGISTRY_URL=registry.cn-hangzhou.aliyuncs.com
NAMESPACE=vest

# 服务器配置（需要你提供）
PROD_DEPLOY_HOST=your.production.server.ip
PROD_DEPLOY_USER=your_deploy_user
PROD_DEPLOY_PATH=/opt/weishi

# 域名配置（基于你当前配置）
PROD_DOMAIN=viclink.cn
SSL_EMAIL=<EMAIL>

# 数据库配置（基于你当前配置）
MYSQL_DATABASE=weizhi
MYSQL_USER=weishi
MYSQL_PORT=3307

# 应用配置（基于你当前配置）
ADMIN_USERNAME=admin
ADMIN_INIT_PASSWORD=true
IMPORT_SEED_DATA=false
IMPORT_ADMIN_DATA=false

# 文件上传配置（基于你当前配置）
MAX_FILE_SIZE=52428800
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp,application/pdf,text/plain

# 腾讯云COS配置（基于你当前配置）
COS_REGION=ap-nanjing
COS_BUCKET=upload-1305444037
COS_DOMAIN=your_cos_domain_if_any
```

> 📖 详细配置说明请参考：[简化配置管理指南](../docs/simplified-config-guide.md)
> 🔄 当前配置迁移请参考：[当前配置迁移指南](../docs/current-config-migration.md)

## 🎯 服务映射

| 服务 | 镜像名称 | 描述 |
|------|----------|------|
| web | weizhi-web | Nuxt3 前端应用 |
| admin | weizhi-admin | Vue3 管理后台 |
| server-go | weizhi-server | Go 后端服务 |

## 🚀 使用方法

### 手动构建
1. 进入仓库 Actions 页面
2. 选择 "构建并推送" 工作流
3. 点击 "Run workflow"
4. 配置选项：
   - 选择构建服务
   - 选择推送目标仓库
   - 自定义标签（可选）

### 自动构建
- 创建 Release 时自动触发
- 构建所有服务并推送到配置的仓库

### 自动化部署
1. 进入仓库 Actions 页面
2. 选择 "自动化部署" 工作流
3. 点击 "Run workflow"
4. 配置选项：
   - 选择部署服务
   - 指定镜像版本
   - 是否强制部署

### 服务回滚
1. 进入仓库 Actions 页面
2. 选择 "服务回滚" 工作流
3. 配置回滚选项：
   - 选择回滚类型（上一版本/指定备份/紧急停止）
   - 确认回滚操作

## 📦 构建产物

### 外部仓库镜像
```
{REGISTRY_URL}/{NAMESPACE}/weizhi-web:{VERSION}
{REGISTRY_URL}/{NAMESPACE}/weizhi-admin:{VERSION}
{REGISTRY_URL}/{NAMESPACE}/weizhi-server:{VERSION}
```

### Gitea 软件包仓库镜像
```
{PACKAGE_REGISTRY}/{repository_owner}/weizhi-web:{VERSION}
{PACKAGE_REGISTRY}/{repository_owner}/weizhi-admin:{VERSION}
{PACKAGE_REGISTRY}/{repository_owner}/weizhi-server:{VERSION}
```

## 🛠️ 辅助脚本

### `scripts/` 目录
- `setup.sh` - 环境设置脚本
- `local-build.sh` - 本地构建脚本
- `check-config.sh` - 配置检查脚本

## 📋 最佳实践

1. **版本管理**：使用语义化版本号（如 v1.0.0）
2. **安全配置**：定期更新访问令牌
3. **构建优化**：选择性构建变更的服务
4. **监控部署**：关注构建状态和部署结果

## 🔧 故障排除

### 常见问题
1. **HTTP Registry 连接问题**：已配置支持 HTTP 协议的 Gitea 仓库
2. **认证失败**：检查 Secrets 配置是否正确
3. **构建失败**：查看构建日志，检查 Dockerfile 和依赖

### 配置验证
工作流包含配置验证步骤，会在构建前检查必要的变量和密钥是否配置正确。
