name: 服务回滚

on:
  workflow_dispatch:
    inputs:
      environment:
        description: '回滚环境'
        required: true
        default: 'production'
        type: choice
        options:
          - production
      rollback_type:
        description: '回滚类型'
        required: true
        default: 'previous_version'
        type: choice
        options:
          - previous_version
          - specific_backup
          - emergency_stop
      backup_id:
        description: '备份ID（格式：YYYYMMDD_HHMMSS）'
        required: false
        type: string
      services:
        description: '回滚服务'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - web
          - caddy-admin
          - server-go
      confirm_rollback:
        description: '确认回滚操作'
        required: true
        default: false
        type: boolean

jobs:
  # 回滚确认
  confirm-rollback:
    runs-on: ubuntu-22.04
    steps:
      - name: 验证回滚确认
        run: |
          if [ "${{ inputs.confirm_rollback }}" != "true" ]; then
            echo "❌ 回滚操作未确认，退出"
            exit 1
          fi
          echo "✅ 回滚操作已确认"

      - name: 显示回滚信息
        run: |
          echo "🔄 准备执行回滚操作"
          echo "环境: ${{ inputs.environment }}"
          echo "类型: ${{ inputs.rollback_type }}"
          echo "服务: ${{ inputs.services }}"
          if [ "${{ inputs.rollback_type }}" = "specific_backup" ]; then
            echo "备份ID: ${{ inputs.backup_id }}"
          fi

  # 生产环境回滚
  rollback-production:
    if: inputs.confirm_rollback == true
    needs: confirm-rollback
    runs-on: ubuntu-22.04
    environment: production
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 配置回滚环境
        run: |
          echo "ENVIRONMENT=production" >> $GITHUB_ENV
          echo "ROLLBACK_TYPE=${{ inputs.rollback_type }}" >> $GITHUB_ENV
          echo "BACKUP_ID=${{ inputs.backup_id }}" >> $GITHUB_ENV
          echo "SERVICES=${{ inputs.services }}" >> $GITHUB_ENV
          echo "DEPLOY_HOST=${{ vars.PROD_DEPLOY_HOST }}" >> $GITHUB_ENV
          echo "DEPLOY_USER=${{ vars.PROD_DEPLOY_USER }}" >> $GITHUB_ENV
          echo "DEPLOY_PATH=${{ vars.PROD_DEPLOY_PATH }}" >> $GITHUB_ENV

      - name: 设置 SSH 密钥
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.PROD_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ${{ env.DEPLOY_HOST }} >> ~/.ssh/known_hosts

      - name: 验证服务器连接
        run: |
          ssh -o ConnectTimeout=10 ${{ env.DEPLOY_USER }}@${{ env.DEPLOY_HOST }} "echo '服务器连接成功'"

      - name: 创建回滚前备份
        run: |
          ssh ${{ env.DEPLOY_USER }}@${{ env.DEPLOY_HOST }} "
            cd ${{ env.DEPLOY_PATH }}
            
            # 创建回滚前备份
            ROLLBACK_BACKUP_DIR=backups/rollback_$(date +%Y%m%d_%H%M%S)
            mkdir -p \$ROLLBACK_BACKUP_DIR
            
            # 备份当前状态
            docker compose -f docker-compose.prod.yml ps > \$ROLLBACK_BACKUP_DIR/services_status.txt
            docker images --format 'table {{.Repository}}:{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}' | grep weizhi > \$ROLLBACK_BACKUP_DIR/current_images.txt || true
            
            if [ -f docker-compose.prod.yml ]; then
              cp docker-compose.prod.yml \$ROLLBACK_BACKUP_DIR/
            fi
            if [ -f production.env ]; then
              cp production.env \$ROLLBACK_BACKUP_DIR/
            fi
            
            echo '回滚前备份完成: '\$ROLLBACK_BACKUP_DIR
          "

      - name: 执行紧急停止
        if: inputs.rollback_type == 'emergency_stop'
        run: |
          ssh ${{ env.DEPLOY_USER }}@${{ env.DEPLOY_HOST }} "
            cd ${{ env.DEPLOY_PATH }}
            
            echo '🚨 执行紧急停止...'
            if [ '${{ env.SERVICES }}' = 'all' ]; then
              docker compose -f docker-compose.prod.yml down
            else
              case '${{ env.SERVICES }}' in
                'web')
                  docker compose -f docker-compose.prod.yml stop web
                  ;;
                'caddy-admin')
                  docker compose -f docker-compose.prod.yml stop caddy
                  ;;
                'server-go')
                  docker compose -f docker-compose.prod.yml stop server
                  ;;
              esac
            fi
            
            echo '✅ 紧急停止完成'
          "

      - name: 回滚到上一版本
        if: inputs.rollback_type == 'previous_version'
        run: |
          ssh ${{ env.DEPLOY_USER }}@${{ env.DEPLOY_HOST }} "
            cd ${{ env.DEPLOY_PATH }}
            
            echo '🔄 回滚到上一版本...'
            
            # 获取最新的备份
            LATEST_BACKUP=\$(ls -t backups/ | grep -E '^[0-9]{8}_[0-9]{6}$' | head -1)
            
            if [ -z \"\$LATEST_BACKUP\" ]; then
              echo '❌ 未找到可用的备份'
              exit 1
            fi
            
            echo \"使用备份: \$LATEST_BACKUP\"
            
            # 停止当前服务
            if [ '${{ env.SERVICES }}' = 'all' ]; then
              docker compose -f docker-compose.prod.yml down
            else
              case '${{ env.SERVICES }}' in
                'web')
                  docker compose -f docker-compose.prod.yml stop web
                  ;;
                'caddy-admin')
                  docker compose -f docker-compose.prod.yml stop caddy
                  ;;
                'server-go')
                  docker compose -f docker-compose.prod.yml stop server
                  ;;
              esac
            fi
            
            # 恢复配置文件
            if [ -f \"backups/\$LATEST_BACKUP/docker-compose.prod.yml\" ]; then
              cp \"backups/\$LATEST_BACKUP/docker-compose.prod.yml\" ./
            fi
            if [ -f \"backups/\$LATEST_BACKUP/production.env\" ]; then
              cp \"backups/\$LATEST_BACKUP/production.env\" ./
            fi
            
            # 启动服务
            if [ '${{ env.SERVICES }}' = 'all' ]; then
              docker compose -f docker-compose.prod.yml up -d
            else
              case '${{ env.SERVICES }}' in
                'web')
                  docker compose -f docker-compose.prod.yml up -d web
                  ;;
                'caddy-admin')
                  docker compose -f docker-compose.prod.yml up -d caddy
                  ;;
                'server-go')
                  docker compose -f docker-compose.prod.yml up -d server
                  ;;
              esac
            fi
            
            echo '✅ 回滚到上一版本完成'
          "

      - name: 回滚到指定备份
        if: inputs.rollback_type == 'specific_backup'
        run: |
          ssh ${{ env.DEPLOY_USER }}@${{ env.DEPLOY_HOST }} "
            cd ${{ env.DEPLOY_PATH }}
            
            echo '🔄 回滚到指定备份: ${{ env.BACKUP_ID }}'
            
            # 验证备份存在
            if [ ! -d \"backups/${{ env.BACKUP_ID }}\" ]; then
              echo '❌ 指定的备份不存在: ${{ env.BACKUP_ID }}'
              echo '可用的备份:'
              ls -la backups/
              exit 1
            fi
            
            # 停止当前服务
            if [ '${{ env.SERVICES }}' = 'all' ]; then
              docker compose -f docker-compose.prod.yml down
            else
              case '${{ env.SERVICES }}' in
                'web')
                  docker compose -f docker-compose.prod.yml stop web
                  ;;
                'caddy-admin')
                  docker compose -f docker-compose.prod.yml stop caddy
                  ;;
                'server-go')
                  docker compose -f docker-compose.prod.yml stop server
                  ;;
              esac
            fi
            
            # 恢复指定备份
            if [ -f \"backups/${{ env.BACKUP_ID }}/docker-compose.prod.yml\" ]; then
              cp \"backups/${{ env.BACKUP_ID }}/docker-compose.prod.yml\" ./
            fi
            if [ -f \"backups/${{ env.BACKUP_ID }}/production.env\" ]; then
              cp \"backups/${{ env.BACKUP_ID }}/production.env\" ./
            fi
            
            # 启动服务
            if [ '${{ env.SERVICES }}' = 'all' ]; then
              docker compose -f docker-compose.prod.yml up -d
            else
              case '${{ env.SERVICES }}' in
                'web')
                  docker compose -f docker-compose.prod.yml up -d web
                  ;;
                'caddy-admin')
                  docker compose -f docker-compose.prod.yml up -d caddy
                  ;;
                'server-go')
                  docker compose -f docker-compose.prod.yml up -d server
                  ;;
              esac
            fi
            
            echo '✅ 回滚到指定备份完成'
          "

      - name: 等待服务启动
        if: inputs.rollback_type != 'emergency_stop'
        run: |
          echo "等待服务启动..."
          sleep 30

      - name: 回滚后健康检查
        if: inputs.rollback_type != 'emergency_stop'
        run: |
          ssh ${{ env.DEPLOY_USER }}@${{ env.DEPLOY_HOST }} "
            cd ${{ env.DEPLOY_PATH }}
            
            echo '=== 回滚后服务状态 ==='
            docker compose -f docker-compose.prod.yml ps
            
            # 简单健康检查
            echo '=== 健康检查 ==='
            if curl -f -s http://localhost:3001/api/health > /dev/null; then
              echo '✅ 后端服务健康'
            else
              echo '⚠️ 后端服务可能有问题'
            fi
            
            if curl -f -s http://localhost:3000 > /dev/null; then
              echo '✅ 前端服务健康'
            else
              echo '⚠️ 前端服务可能有问题'
            fi
          "

      - name: 回滚完成通知
        run: |
          echo "🔄 回滚操作完成!"
          echo "环境: ${{ env.ENVIRONMENT }}"
          echo "类型: ${{ env.ROLLBACK_TYPE }}"
          echo "服务: ${{ env.SERVICES }}"
          if [ "${{ env.ROLLBACK_TYPE }}" = "specific_backup" ]; then
            echo "备份ID: ${{ env.BACKUP_ID }}"
          fi
          echo "时间: $(date)"



  # 回滚失败处理
  handle-rollback-failure:
    if: failure()
    needs: [rollback-production]
    runs-on: ubuntu-22.04
    steps:
      - name: 回滚失败通知
        run: |
          echo "❌ 回滚操作失败!"
          echo "请立即检查服务器状态并手动处理"
          echo "紧急联系方式: [添加紧急联系信息]"
