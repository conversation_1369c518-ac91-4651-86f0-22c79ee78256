name: 服务监控和回滚

on:
  schedule:
    # 每5分钟检查一次服务状态
    - cron: '*/5 * * * *'

env:
  # 只保留非敏感的Variables
  DEPLOY_PATH: ${{ vars.DEPLOY_PATH || '/opt/weizhi' }}

jobs:
  # 服务监控
  monitor-services:
    name: 监控服务状态
    runs-on: ubuntu-22.04
    if: github.event_name == 'schedule'
    outputs:
      web_status: ${{ steps.check.outputs.web_status }}
      admin_status: ${{ steps.check.outputs.admin_status }}  # caddy 提供 admin 静态资源，仅需检查反代可达性
      server_status: ${{ steps.check.outputs.server_status }}
      overall_status: ${{ steps.check.outputs.overall_status }}
    steps:
      - name: 设置SSH密钥
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.DEPLOY_KEY }}

      - name: 添加服务器到已知主机
        run: |
          ssh-keyscan -H ${{ secrets.DEPLOY_HOST }} >> ~/.ssh/known_hosts

      - name: 检查服务状态
        id: check
        run: |
          echo "检查服务状态..."
          
          # 检查Web服务
          if ssh ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} "curl -f -s http://localhost:3000/health > /dev/null 2>&1"; then
            echo "web_status=healthy" >> $GITHUB_OUTPUT
            echo "✅ Web服务正常"
          else
            echo "web_status=unhealthy" >> $GITHUB_OUTPUT
            echo "❌ Web服务异常"
          fi
          
          # 检查后端服务
          if ssh ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} "curl -f -s http://localhost:3001/health > /dev/null 2>&1"; then
            echo "server_status=healthy" >> $GITHUB_OUTPUT
            echo "✅ 后端服务正常"
          else
            echo "server_status=unhealthy" >> $GITHUB_OUTPUT
            echo "❌ 后端服务异常"
          fi
          
          # 检查管理后台
          # 管理后台由 Caddy 提供（域名 ADMIN_DOMAIN），可选：检查 https://$ADMIN_DOMAIN
          if ssh ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} "curl -f -s http://localhost:80/health > /dev/null 2>&1"; then
            echo "admin_status=healthy" >> $GITHUB_OUTPUT
            echo "✅ 管理后台正常"
          else
            echo "admin_status=unhealthy" >> $GITHUB_OUTPUT
            echo "❌ 管理后台异常"
          fi
          
          # 整体状态
          if [ "${{ steps.check.outputs.web_status }}" = "healthy" ] && \
             [ "${{ steps.check.outputs.server_status }}" = "healthy" ] && \
             [ "${{ steps.check.outputs.admin_status }}" = "healthy" ]; then
            echo "overall_status=healthy" >> $GITHUB_OUTPUT
            echo "✅ 所有服务正常"
          else
            echo "overall_status=unhealthy" >> $GITHUB_OUTPUT
            echo "❌ 存在服务异常"
          fi

      - name: 获取服务日志
        if: steps.check.outputs.overall_status == 'unhealthy'
        run: |
          echo "获取异常服务日志..."
          ssh ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} "cd ${{ env.DEPLOY_PATH }} && docker-compose -f docker-compose.prod.yml logs --tail=50" > service-logs.txt
          
          echo "=== 服务日志 ===" >> $GITHUB_STEP_SUMMARY
          cat service-logs.txt >> $GITHUB_STEP_SUMMARY

      - name: 发送告警通知
        if: steps.check.outputs.overall_status == 'unhealthy'
        run: |
          echo "发送告警通知..."
          
          # 构建告警消息
          message="🚨 Weizhi项目服务异常告警\n"
          message+="时间: $(date)\n"
          message+="服务器: ${{ secrets.DEPLOY_HOST }}\n"
          message+="\n服务状态:\n"
          message+="- Web服务: ${{ steps.check.outputs.web_status }}\n"
          message+="- 后端服务: ${{ steps.check.outputs.server_status }}\n"
          message+="- 管理后台(Caddy): ${{ steps.check.outputs.admin_status }}\n"
          
          # 发送到钉钉
          if [ -n "${{ secrets.ALERT_WEBHOOK }}" ]; then
            curl -H "Content-Type: application/json" \
                 -X POST \
                 -d "{\"text\":\"$message\"}" \
                 "${{ secrets.ALERT_WEBHOOK }}"
          fi

  # 自动回滚
  auto-rollback:
    name: 自动回滚
    runs-on: ubuntu-22.04
    needs: monitor-services
    if: needs.monitor-services.outputs.overall_status == 'unhealthy' && github.event_name == 'schedule'
    steps:
      - name: 设置SSH密钥
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.DEPLOY_KEY }}

      - name: 添加服务器到已知主机
        run: |
          ssh-keyscan -H ${{ secrets.DEPLOY_HOST }} >> ~/.ssh/known_hosts

      - name: 获取可用版本
        run: |
          echo "获取可用版本..."
          ssh ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} "cd ${{ env.DEPLOY_PATH }} && ls -la docker-compose.prod.yml.backup.* | tail -5" > available-versions.txt
          
          echo "可用版本:" >> $GITHUB_STEP_SUMMARY
          cat available-versions.txt >> $GITHUB_STEP_SUMMARY

      - name: 执行回滚
        run: |
          echo "执行自动回滚..."
          
          # 获取最新的备份版本
          latest_backup=$(ssh ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} "cd ${{ env.DEPLOY_PATH }} && ls -t docker-compose.prod.yml.backup.* | head -1")
          
          if [ -n "$latest_backup" ]; then
            echo "回滚到版本: $latest_backup"
            
            # 执行回滚
            ssh ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} "cd ${{ env.DEPLOY_PATH }} && \
              cp $latest_backup docker-compose.prod.yml && \
              docker-compose -f docker-compose.prod.yml down && \
              docker-compose -f docker-compose.prod.yml up -d && \
              sleep 10 && \
              docker-compose -f docker-compose.prod.yml ps"
            
            echo "✅ 自动回滚完成"
          else
            echo "❌ 没有找到可用的备份版本"
            exit 1
          fi

  # 手动回滚
  manual-rollback:
    name: 手动回滚
    runs-on: ubuntu-22.04
    if: github.event.inputs.action == 'rollback'
    steps:
      - name: 设置SSH密钥
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.DEPLOY_KEY }}

      - name: 添加服务器到已知主机
        run: |
          ssh-keyscan -H ${{ secrets.DEPLOY_HOST }} >> ~/.ssh/known_hosts

      - name: 验证目标版本
        run: |
          target_version="${{ github.event.inputs.target_version }}"
          if [ -z "$target_version" ]; then
            echo "❌ 请指定目标版本"
            exit 1
          fi
          
          echo "验证目标版本: $target_version"
          
          # 检查版本是否存在
          if ssh ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} "cd ${{ env.DEPLOY_PATH }} && test -f docker-compose.prod.yml.backup.$target_version"; then
            echo "✅ 目标版本存在"
          else
            echo "❌ 目标版本不存在"
            exit 1
          fi

      - name: 执行手动回滚
        run: |
          target_version="${{ github.event.inputs.target_version }}"
          echo "执行手动回滚到版本: $target_version"
          
          # 执行回滚
          ssh ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} "cd ${{ env.DEPLOY_PATH }} && \
            cp docker-compose.prod.yml.backup.$target_version docker-compose.prod.yml && \
            docker-compose -f docker-compose.prod.yml down && \
            docker-compose -f docker-compose.prod.yml up -d && \
            sleep 10 && \
            docker-compose -f docker-compose.prod.yml ps"
          
          echo "✅ 手动回滚完成"

      - name: 验证回滚结果
        run: |
          echo "验证回滚结果..."
          sleep 30
          
          # 检查服务状态
          if ssh ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} "curl -f -s http://localhost:3000/health > /dev/null 2>&1"; then
            echo "✅ Web服务回滚成功"
          else
            echo "❌ Web服务回滚失败"
            exit 1
          fi
          
          if ssh ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} "curl -f -s http://localhost:3001/health > /dev/null 2>&1"; then
            echo "✅ 后端服务回滚成功"
          else
            echo "❌ 后端服务回滚失败"
            exit 1
          fi

  # 监控报告
  generate-report:
    name: 生成监控报告
    runs-on: ubuntu-22.04
    needs: [monitor-services, auto-rollback]
    if: always()
    steps:
      - name: 生成监控报告
        run: |
          echo "## 📊 服务监控报告" >> $GITHUB_STEP_SUMMARY
          echo "- **检查时间**: $(date -u +%Y-%m-%dT%H:%M:%SZ)" >> $GITHUB_STEP_SUMMARY
          echo "- **服务器**: ${{ secrets.DEPLOY_HOST }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          echo "### 🔍 服务状态" >> $GITHUB_STEP_SUMMARY
          echo "- **Web服务**: ${{ needs.monitor-services.outputs.web_status }}" >> $GITHUB_STEP_SUMMARY
          echo "- **后端服务**: ${{ needs.monitor-services.outputs.server_status }}" >> $GITHUB_STEP_SUMMARY
          echo "- **管理后台(Caddy)**: ${{ needs.monitor-services.outputs.admin_status }}" >> $GITHUB_STEP_SUMMARY
          echo "- **整体状态**: ${{ needs.monitor-services.outputs.overall_status }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          if [ "${{ needs.auto-rollback.result }}" = "success" ]; then
            echo "### 🔄 自动回滚" >> $GITHUB_STEP_SUMMARY
            echo "- **状态**: 已执行自动回滚" >> $GITHUB_STEP_SUMMARY
            echo "- **时间**: $(date)" >> $GITHUB_STEP_SUMMARY
          elif [ "${{ needs.auto-rollback.result }}" = "failure" ]; then
            echo "### ❌ 自动回滚" >> $GITHUB_STEP_SUMMARY
            echo "- **状态**: 自动回滚失败" >> $GITHUB_STEP_SUMMARY
            echo "- **请手动检查**" >> $GITHUB_STEP_SUMMARY
          fi 