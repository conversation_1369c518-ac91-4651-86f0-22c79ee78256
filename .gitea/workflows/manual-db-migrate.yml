name: Manual Database Migration

on:
  workflow_dispatch:
    inputs:
      action:
        description: 'Migration action'
        required: true
        type: choice
        default: 'status'
        options: ['up', 'down', 'status', 'list']
      version:
        description: 'Migration version (e.g. 001/002, only for up/down)'
        required: false
        type: string
        default: ''
      backup_before:
        description: 'Backup database before migration (only for up/down)'
        required: false
        type: boolean
        default: true
      remote_dir:
        description: 'Remote deployment directory (contains docker-compose.prod.yml/production.env)'
        required: false
        type: string
        default: '/opt/weizhi'
      compose_file:
        description: 'Compose file name'
        required: false
        type: string
        default: 'docker-compose.prod.yml'
      env_file:
        description: 'Env file name'
        required: false
        type: string
        default: 'production.env'

jobs:
  migrate:
    name: Execute Database Migration
    runs-on: ubuntu-22.04

    steps:
      - name: Checkout repository (optional)
        uses: actions/checkout@v4

      - name: Setup SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.DEPLOY_SSH_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ${{ vars.DEPLOY_HOST }} >> ~/.ssh/known_hosts

      - name: Pre-check inputs
        run: |
          echo "Action: ${{ github.event.inputs.action }}"
          echo "Version: ${{ github.event.inputs.version }}"
          if [ "${{ github.event.inputs.action }}" != "up" ] && [ "${{ github.event.inputs.action }}" != "down" ]; then
            echo "Non up/down operation, ignoring version validation"
          else
            if [ -z "${{ github.event.inputs.version }}" ]; then
              echo "Version required (e.g. 001, 002)"
              exit 1
            fi
          fi

      - name: Execute remote migration
        env:
          REMOTE_DIR: ${{ github.event.inputs.remote_dir }}
          COMPOSE_FILE: ${{ github.event.inputs.compose_file }}
          ENV_FILE: ${{ github.event.inputs.env_file }}
          ACTION: ${{ github.event.inputs.action }}
          VERSION: ${{ github.event.inputs.version }}
          BACKUP_BEFORE: ${{ github.event.inputs.backup_before }}
        run: |
          set -euo pipefail

          # Parse migration files locally and upload to remote (only for up/down)
          ACTION="${{ github.event.inputs.action }}"
          VERSION="${{ github.event.inputs.version }}"
          MIGRATIONS_DIR="server-go/scripts/migrations"

          if [ "$ACTION" = "list" ]; then
            echo "Available migration files:"
            ls -1 "$MIGRATIONS_DIR"/*.sql || true
            # list action only outputs list locally
            exit 0
          fi

          if [ "$ACTION" = "up" ] || [ "$ACTION" = "down" ]; then
            if [ -z "$VERSION" ]; then
              echo "ERROR: up/down requires version (e.g. 001, 002)"; exit 1
            fi

            if [ "$ACTION" = "up" ]; then
              LOCAL_SQL_FILE=$(ls "$MIGRATIONS_DIR"/${VERSION}_*.sql 2>/dev/null | grep -v "rollback" | head -n1 || true)
            else
              LOCAL_SQL_FILE=$(ls "$MIGRATIONS_DIR"/${VERSION}_*_rollback.sql 2>/dev/null | head -n1 || true)
            fi

            if [ -z "$LOCAL_SQL_FILE" ]; then
              echo "ERROR: Migration file for version $VERSION not found in $MIGRATIONS_DIR"; exit 1
            fi

            echo "Migration file to execute: $LOCAL_SQL_FILE"

            # Ensure remote tmp directory exists
            ssh ${{ vars.DEPLOY_USER }}@${{ vars.DEPLOY_HOST }} "mkdir -p '${REMOTE_DIR}/tmp'"
            # Upload to remote fixed path
            scp "$LOCAL_SQL_FILE" ${{ vars.DEPLOY_USER }}@${{ vars.DEPLOY_HOST }}:"${REMOTE_DIR}/tmp/migration_${VERSION}.sql"
          fi


          # Upload migration script to remote
          scp "scripts/db-migrate.sh" ${{ vars.DEPLOY_USER }}@${{ vars.DEPLOY_HOST }}:"${REMOTE_DIR}/db-migrate.sh"

          # Execute remote migration script
          ssh ${{ vars.DEPLOY_USER }}@${{ vars.DEPLOY_HOST }} "cd '${REMOTE_DIR}' && chmod +x db-migrate.sh && ./db-migrate.sh '${{ github.event.inputs.action }}' '${{ github.event.inputs.version }}' '${{ github.event.inputs.backup_before }}' '${{ github.event.inputs.compose_file }}' '${{ github.event.inputs.env_file }}'"

      - name: Output results
        run: |
          echo "Migration operation has been executed, see logs above for details."

