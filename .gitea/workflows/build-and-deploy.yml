name: 构建并部署

on:
  workflow_dispatch:
    inputs:
      action_type:
        description: '操作类型'
        required: true
        default: 'build-and-deploy'
        type: choice
        options:
          - build-and-deploy
          - deploy-only
      services:
        description: '构建服务'
        required: true
        default: 'core'
        type: choice
        options:
          - core
          - all
          - web
          - caddy-admin
          - server-go
          - web,caddy-admin
          - web,server-go
          - caddy-admin,server-go
      version:
        description: '版本号（留空则读取仓库根 VERSION 文件或自动生成）'
        required: false
        default: ''
        type: string
      environment:
        description: '部署环境'
        required: true
        default: 'production'
        type: choice
        options:
          - production
      push_to_registry:
        description: '推送到外部镜像仓库'
        required: false
        default: true
        type: boolean
      push_to_gitea:
        description: '推送到 Gitea 包仓库'
        required: false
        default: true
        type: boolean
      force_deploy:
        description: '强制部署（跳过健康检查）'
        required: false
        default: false
        type: boolean
      first_deployment:
        description: '首次部署（启用数据导入）'
        required: false
        default: false
        type: boolean

env:
  REGISTRY_URL: ${{ vars.REGISTRY_URL }}
  NAMESPACE: ${{ vars.NAMESPACE }}
  PACKAGE_REGISTRY: ${{ vars.PACKAGE_REGISTRY }}

jobs:
  # 解析参数和生成版本号
  parse-params:
    runs-on: ubuntu-22.04
    outputs:
      action_type: ${{ steps.parse.outputs.action_type }}
      services: ${{ steps.parse.outputs.services }}
      version: ${{ steps.parse.outputs.version }}
      build_time: ${{ steps.parse.outputs.build_time }}
      push_to_registry: ${{ steps.parse.outputs.push_to_registry }}
      push_to_gitea: ${{ steps.parse.outputs.push_to_gitea }}
      environment: ${{ steps.parse.outputs.environment }}
      force_deploy: ${{ steps.parse.outputs.force_deploy }}
      first_deployment: ${{ steps.parse.outputs.first_deployment }}
      should_build: ${{ steps.parse.outputs.should_build }}
    steps:
      - name: 解析输入参数
        id: parse
        run: |
          echo "=== 解析输入参数 ==="

          ACTION_TYPE="${{ inputs.action_type }}"
          INPUT_VERSION="${{ inputs.version }}"

          echo "操作类型: $ACTION_TYPE"

          if [ "$ACTION_TYPE" = "build-and-deploy" ]; then
            # 构建并部署模式：优先使用手动输入，其次读取 VERSION 文件，最后自动生成
            get_version_from_file() {
              if [ -f VERSION ]; then
                local v
                v=$(grep -E '^[[:space:]]*VERSION[[:space:]]*=' VERSION | head -n1 | cut -d'=' -f2-)
                if [ -z "$v" ]; then
                  v=$(head -n1 VERSION)
                fi
                echo "$v"
              fi
            }

            if [ -n "$INPUT_VERSION" ]; then
              VERSION="$INPUT_VERSION"
              echo "🚀 构建并部署模式"
              echo "使用指定版本号: $VERSION"
            else
              FILE_VERSION="$(get_version_from_file || true)"
              if [ -n "$FILE_VERSION" ]; then
                VERSION="$FILE_VERSION"
                echo "🚀 构建并部署模式"
                echo "使用 VERSION 文件中的版本号: $VERSION"
              else
                VERSION="$(date +%Y%m%d-%H%M%S)-${GITHUB_SHA:0:8}"
                echo "🚀 构建并部署模式"
                echo "使用自动生成版本号: $VERSION"
              fi
            fi

            BUILD_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ)
            SHOULD_BUILD="true"

          elif [ "$ACTION_TYPE" = "deploy-only" ]; then
            # 仅部署模式：优先使用手动输入，其次读取 VERSION 文件
            get_version_from_file() {
              if [ -f VERSION ]; then
                local v
                v=$(grep -E '^[[:space:]]*VERSION[[:space:]]*=' VERSION | head -n1 | cut -d'=' -f2-)
                if [ -z "$v" ]; then
                  v=$(head -n1 VERSION)
                fi
                echo "$v"
              fi
            }

            if [ -n "$INPUT_VERSION" ]; then
              VERSION="$INPUT_VERSION"
            else
              FILE_VERSION="$(get_version_from_file || true)"
              if [ -n "$FILE_VERSION" ]; then
                VERSION="$FILE_VERSION"
              else
                echo "❌ deploy-only 模式未提供版本号，且未找到 VERSION 文件"
                exit 1
              fi
            fi

            BUILD_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ)
            SHOULD_BUILD="false"

            echo "📦 仅部署模式"
            echo "使用版本: $VERSION"

          else
            echo "❌ 未知操作类型: $ACTION_TYPE"
            exit 1
          fi

          # 输出参数
          echo "action_type=$ACTION_TYPE" >> $GITHUB_OUTPUT
          echo "services=${{ inputs.services }}" >> $GITHUB_OUTPUT
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "build_time=$BUILD_TIME" >> $GITHUB_OUTPUT
          echo "push_to_registry=${{ inputs.push_to_registry }}" >> $GITHUB_OUTPUT
          echo "push_to_gitea=${{ inputs.push_to_gitea }}" >> $GITHUB_OUTPUT
          echo "environment=${{ inputs.environment }}" >> $GITHUB_OUTPUT
          echo "force_deploy=${{ inputs.force_deploy }}" >> $GITHUB_OUTPUT
          echo "first_deployment=${{ inputs.first_deployment }}" >> $GITHUB_OUTPUT
          echo "should_build=$SHOULD_BUILD" >> $GITHUB_OUTPUT

          echo ""
          echo "✅ 参数解析完成"
          echo "操作类型: $ACTION_TYPE"
          echo "版本号: $VERSION"
          echo "构建时间: $BUILD_TIME"
          echo "构建服务: ${{ inputs.services }}"
          echo "部署环境: ${{ inputs.environment }}"
          echo "是否构建: $SHOULD_BUILD"

  # 构建阶段
  build:
    name: 构建服务
    runs-on: ubuntu-22.04
    needs: parse-params
    strategy:
      matrix:
        service: [web, caddy-admin, server-go]
      fail-fast: false
      max-parallel: 3
    if: needs.parse-params.outputs.should_build == 'true' && needs.parse-params.outputs.services != 'none'
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          config-inline: |
            [registry."${{ env.PACKAGE_REGISTRY }}"]
              http = true
              insecure = true

      - name: 并行构建状态
        run: |
          echo "🚀 开始构建服务: ${{ matrix.service }}"
          echo "构建开始时间: $(date '+%Y-%m-%d %H:%M:%S')"
          echo "版本号: ${{ needs.parse-params.outputs.version }}"

      - name: 检查是否需要构建此服务
        id: should_build
        run: |
          SERVICES="${{ needs.parse-params.outputs.services }}"
          SERVICE="${{ matrix.service }}"

          echo "=== 检查是否需要构建当前服务 ==="
          echo "请求构建的服务: $SERVICES"
          echo "当前服务: $SERVICE"

          # 检查是否应该构建当前服务
          if [ "$SERVICES" = "core" ] || [ "$SERVICES" = "all" ] || echo "$SERVICES" | grep -q "$SERVICE"; then
            echo "should_build=true" >> $GITHUB_OUTPUT
            echo "✅ 需要构建服务: $SERVICE"
          else
            echo "should_build=false" >> $GITHUB_OUTPUT
            echo "⏭️ 跳过服务: $SERVICE"
          fi

      - name: 设置仓库名称
        id: repo_name
        if: steps.should_build.outputs.should_build == 'true'
        run: |
          echo "=== 获取仓库名称 ==="
          echo "当前服务: '${{ matrix.service }}'"

          # 根据服务类型设置固定的镜像名
          case "${{ matrix.service }}" in
            "web")
              echo "name=weizhi_web" >> $GITHUB_OUTPUT
              echo "设置镜像名为: weizhi_web"
              ;;
            "caddy-admin")
              echo "name=weizhi_admin_caddy" >> $GITHUB_OUTPUT
              echo "设置镜像名为: weizhi_admin_caddy"
              ;;
            "server-go")
              echo "name=weizhi_server" >> $GITHUB_OUTPUT
              echo "设置镜像名为: weizhi_server"
              ;;
            *)
              echo "name=weizhi_${{ matrix.service }}" >> $GITHUB_OUTPUT
              echo "设置镜像名为: weizhi_${{ matrix.service }}"
              ;;
          esac

          echo "最终镜像名: $(echo 'weizhi_${{ matrix.service }}' | sed 's/server-go/server/' | sed 's/admin/admin/')"

      - name: 登录外部镜像仓库
        if: steps.should_build.outputs.should_build == 'true' && needs.parse-params.outputs.push_to_registry == 'true'
        uses: docker/login-action@v3
        with:
          registry: ${{ vars.REGISTRY_URL }}
          username: ${{ secrets.REGISTRY_USERNAME }}
          password: ${{ secrets.REGISTRY_PASSWORD }}

      - name: 登录 Gitea 包仓库
        if: steps.should_build.outputs.should_build == 'true' && needs.parse-params.outputs.push_to_gitea == 'true'
        uses: docker/login-action@v3
        with:
          registry: ${{ vars.PACKAGE_REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.PACKAGE_TOKEN }}

      - name: 构建并推送镜像
        if: steps.should_build.outputs.should_build == 'true'
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./deployment/Dockerfile.${{ matrix.service }}
          push: true
          tags: |
            ${{ needs.parse-params.outputs.push_to_registry == 'true' && format('{0}/{1}/{2}:{3}', vars.REGISTRY_URL, vars.NAMESPACE, steps.repo_name.outputs.name, needs.parse-params.outputs.version) || '' }}
            ${{ needs.parse-params.outputs.push_to_gitea == 'true' && format('{0}/{1}/{2}:{3}', vars.PACKAGE_REGISTRY, github.repository_owner, steps.repo_name.outputs.name, needs.parse-params.outputs.version) || '' }}
          platforms: linux/amd64,linux/arm64
          cache-from: type=gha,scope=${{ matrix.service }}
          cache-to: type=gha,mode=max,scope=${{ matrix.service }}

      - name: 生成构建元数据
        if: steps.should_build.outputs.should_build == 'true'
        run: |
          echo "✅ ${{ matrix.service }} 服务构建完成"
          echo "完成时间: $(date '+%Y-%m-%d %H:%M:%S')"
          
          mkdir -p artifacts/${{ matrix.service }}
          cat > artifacts/${{ matrix.service }}/metadata.json << EOF
          {
            "service": "${{ matrix.service }}",
            "version": "${{ needs.parse-params.outputs.version }}",
            "build_time": "${{ needs.parse-params.outputs.build_time }}",
            "commit_sha": "${{ github.sha }}",
            "branch": "${{ github.ref_name }}",
            "external_registry": {
              "enabled": ${{ needs.parse-params.outputs.push_to_registry }},
              "image": "${{ vars.REGISTRY_URL }}/${{ vars.NAMESPACE }}/${{ steps.repo_name.outputs.name }}:${{ needs.parse-params.outputs.version }}"
            },
            "gitea_registry": {
              "enabled": ${{ needs.parse-params.outputs.push_to_gitea }},
              "image": "${{ vars.PACKAGE_REGISTRY }}/${{ github.repository_owner }}/${{ steps.repo_name.outputs.name }}:${{ needs.parse-params.outputs.version }}"
            }
          }
          EOF

      - name: 上传构建元数据
        if: steps.should_build.outputs.should_build == 'true'
        uses: actions/upload-artifact@v3
        with:
          name: build-metadata-${{ matrix.service }}
          path: artifacts/${{ matrix.service }}/
          retention-days: 7

  # 创建部署制品
  create-deploy-package:
    name: 创建部署制品
    runs-on: ubuntu-22.04
    needs: [parse-params, build]
    if: needs.parse-params.outputs.should_build == 'true'
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 下载所有构建元数据
        uses: actions/download-artifact@v3
        with:
          path: build-artifacts

      - name: 创建部署包
        run: |
          echo "=== 创建部署制品包 ==="
          
          # 创建部署目录
          mkdir -p deploy-package
          
          # 复制部署配置（含 Caddy 与 env 示例）
          cp deployment/docker-compose.prod.yml deploy-package/
          mkdir -p deploy-package/caddy
          cp deployment/caddy/Caddyfile.prod deploy-package/caddy/
          if [ -f deployment/production.env.example ]; then
            cp deployment/production.env.example deploy-package/
          fi

          # 创建版本信息文件
          cat > deploy-package/VERSION << EOF
          VERSION=${{ needs.parse-params.outputs.version }}
          BUILD_TIME=${{ needs.parse-params.outputs.build_time }}
          COMMIT_SHA=${{ github.sha }}
          BRANCH=${{ github.ref_name }}
          REGISTRY_URL=${{ vars.REGISTRY_URL }}
          NAMESPACE=${{ vars.NAMESPACE }}
          EOF
          
          # 创建快速部署脚本
          cat > deploy-package/quick-deploy.sh << 'EOF'
          #!/bin/bash
          set -e
          
          echo "=== Weizhi 项目快速部署 ==="
          
          # 加载版本信息
          if [ -f VERSION ]; then
            source VERSION
            export VERSION REGISTRY_URL NAMESPACE
            echo "部署版本: $VERSION"
          else
            echo "❌ VERSION 文件不存在"
            exit 1
          fi
          
          # 加载生产环境配置
          if [ -f production.env ]; then
            set -a
            source production.env
            set +a
            echo "✅ 已加载生产环境配置"
          else
            echo "❌ production.env 文件不存在"
            exit 1
          fi
          
          # 拉取镜像
          echo "🔄 拉取 Docker 镜像..."
          docker compose --env-file production.env -f docker-compose.prod.yml pull

          # 启动服务
          echo "🚀 启动服务..."
          docker compose --env-file production.env -f docker-compose.prod.yml up -d

          # 检查服务状态
          echo "🔍 检查服务状态..."
          sleep 10
          docker compose --env-file production.env -f docker-compose.prod.yml ps
          
          echo "✅ 快速部署完成！"
          EOF
          
          chmod +x deploy-package/quick-deploy.sh
          
          # 打包
          tar -czf weizhi-deploy-${{ needs.parse-params.outputs.version }}.tar.gz -C deploy-package .

      - name: 上传部署制品包
        uses: actions/upload-artifact@v3
        with:
          name: weizhi-deploy-${{ needs.parse-params.outputs.version }}
          path: weizhi-deploy-${{ needs.parse-params.outputs.version }}.tar.gz
          retention-days: 30

  # 部署阶段
  deploy:
    name: 部署到生产环境
    runs-on: ubuntu-22.04
    needs: [parse-params, build, create-deploy-package]
    if: always() && !cancelled() && !failure()
    environment: production
    steps:
      - name: 检出代码 (仅部署模式需要)
        if: needs.parse-params.outputs.should_build == 'false'
        uses: actions/checkout@v4

      - name: 下载部署制品 (构建并部署模式)
        if: needs.parse-params.outputs.should_build == 'true'
        uses: actions/download-artifact@v3
        with:
          name: weizhi-deploy-${{ needs.parse-params.outputs.version }}
          path: ./

      - name: 创建部署制品 (仅部署模式)
        if: needs.parse-params.outputs.should_build == 'false'
        run: |
          echo "=== 仅部署模式：创建部署制品 ==="

          # 创建部署目录
          mkdir -p artifacts

          # 复制部署配置（含 Caddy 与 env 示例）
          cp deployment/docker-compose.prod.yml artifacts/
          mkdir -p artifacts/caddy
          cp deployment/caddy/Caddyfile.prod artifacts/caddy/
          if [ -f deployment/production.env.example ]; then
            cp deployment/production.env.example artifacts/
          fi

          # 创建版本信息文件
          cat > artifacts/VERSION << EOF
          VERSION=${{ needs.parse-params.outputs.version }}
          BUILD_TIME=${{ needs.parse-params.outputs.build_time }}
          COMMIT_SHA=${{ github.sha }}
          BRANCH=${{ github.ref_name }}
          REGISTRY_URL=${{ vars.REGISTRY_URL }}
          NAMESPACE=${{ vars.NAMESPACE }}
          EOF

          # 创建快速部署脚本
          cat > artifacts/quick-deploy.sh << 'EOF'
          #!/bin/bash
          set -e

          echo "=== Weizhi 项目快速部署 ==="

          # 加载版本信息
          if [ -f VERSION ]; then
            source VERSION
            export VERSION REGISTRY_URL NAMESPACE
            echo "部署版本: $VERSION"
          else
            echo "❌ VERSION 文件不存在"
            exit 1
          fi

          # 加载生产环境配置
          if [ -f production.env ]; then
            set -a
            source production.env
            set +a
            echo "✅ 已加载生产环境配置"
          else
            echo "❌ production.env 文件不存在"
            exit 1
          fi

          # 拉取镜像
          echo "🔄 拉取 Docker 镜像..."
          docker compose --env-file production.env -f docker-compose.prod.yml pull

          # 启动服务
          echo "🚀 启动服务..."
          docker compose --env-file production.env -f docker-compose.prod.yml up -d

          # 检查服务状态
          echo "🔍 检查服务状态..."
          sleep 10
          docker compose --env-file production.env -f docker-compose.prod.yml ps

          echo "✅ 快速部署完成！"
          EOF

          chmod +x artifacts/quick-deploy.sh

          # 打包制品
          tar -czf weizhi-deploy-${{ needs.parse-params.outputs.version }}.tar.gz -C artifacts .

          echo "✅ 仅部署模式制品创建完成"

      - name: 配置部署环境
        run: |
          echo "ENVIRONMENT=${{ needs.parse-params.outputs.environment }}" >> $GITHUB_ENV
          echo "SERVICES=${{ needs.parse-params.outputs.services }}" >> $GITHUB_ENV
          echo "VERSION=${{ needs.parse-params.outputs.version }}" >> $GITHUB_ENV
          echo "DEPLOY_HOST=${{ vars.DEPLOY_HOST }}" >> $GITHUB_ENV
          echo "DEPLOY_USER=${{ vars.DEPLOY_USER }}" >> $GITHUB_ENV
          echo "DEPLOY_PATH=${{ vars.DEPLOY_PATH }}" >> $GITHUB_ENV

      - name: 设置 SSH 密钥
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.DEPLOY_SSH_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ${{ env.DEPLOY_HOST }} >> ~/.ssh/known_hosts

      - name: 生成生产环境配置
        run: |
          echo "=== 生成生产环境配置 ==="

          cat > deployment.env << EOF
          # 镜像仓库配置
          REGISTRY_URL="${{ vars.REGISTRY_URL }}"
          NAMESPACE="${{ vars.NAMESPACE }}"
          VERSION="${{ needs.parse-params.outputs.version }}"

          # 数据库配置
          MYSQL_ROOT_PASSWORD="${{ secrets.MYSQL_ROOT_PASSWORD }}"
          MYSQL_PASSWORD="${{ secrets.MYSQL_PASSWORD }}"
          MYSQL_DATABASE="${{ vars.MYSQL_DATABASE }}"
          MYSQL_USER="${{ vars.MYSQL_USER }}"
          MYSQL_PORT="${{ vars.MYSQL_PORT }}"

          # 应用配置
          JWT_SECRET="${{ secrets.JWT_SECRET }}"
          ADMIN_PASSWORD="${{ secrets.ADMIN_PASSWORD }}"
          DOMAIN="${{ vars.DOMAIN }}"
          ADMIN_DOMAIN="${{ vars.ADMIN_DOMAIN }}"
          WEB_PORT="${{ vars.WEB_PORT }}"
          SERVER_PORT="${{ vars.SERVER_PORT }}"

          # 文件上传配置
          MAX_FILE_SIZE="${{ vars.MAX_FILE_SIZE }}"
          ALLOWED_FILE_TYPES="${{ vars.ALLOWED_FILE_TYPES }}"

          # 腾讯云COS配置
          COS_SECRET_ID="${{ secrets.COS_SECRET_ID }}"
          COS_SECRET_KEY="${{ secrets.COS_SECRET_KEY }}"
          COS_REGION="${{ vars.COS_REGION }}"
          COS_BUCKET="${{ vars.COS_BUCKET }}"
          COS_DOMAIN="${{ vars.COS_DOMAIN }}"

          # JWT配置
          JWT_EXPIRE_TIME="${{ vars.JWT_EXPIRE_TIME }}"

          # 管理员初始化
          ADMIN_USERNAME="${{ vars.ADMIN_USERNAME }}"
          ADMIN_INIT_PASSWORD="${{ secrets.ADMIN_INIT_PASSWORD }}"

          # 数据导入配置
          IMPORT_FULL_DATA="${{ needs.parse-params.outputs.first_deployment == 'true' && 'true' || 'false' }}"
          EOF

          echo "✅ 生产环境配置生成完成"

          # 设置环境变量用于验证
          MYSQL_ROOT_PASSWORD="${{ secrets.MYSQL_ROOT_PASSWORD }}"
          MYSQL_PASSWORD="${{ secrets.MYSQL_PASSWORD }}"
          MYSQL_USER="${{ vars.MYSQL_USER }}"
          MYSQL_DATABASE="${{ vars.MYSQL_DATABASE }}"

          # 验证关键环境变量
          echo "=== 验证关键环境变量 ==="
          echo "MYSQL_ROOT_PASSWORD 长度: ${#MYSQL_ROOT_PASSWORD}"
          echo "MYSQL_PASSWORD 长度: ${#MYSQL_PASSWORD}"
          echo "MYSQL_USER: $MYSQL_USER"
          echo "MYSQL_DATABASE: $MYSQL_DATABASE"

          # 调试：显示 Gitea Secrets 和 Variables 的原始值
          echo "=== Gitea 配置调试 ==="
          echo "secrets.MYSQL_ROOT_PASSWORD 长度: ${{ secrets.MYSQL_ROOT_PASSWORD != '' && '已设置' || '未设置' }}"
          echo "secrets.MYSQL_PASSWORD 长度: ${{ secrets.MYSQL_PASSWORD != '' && '已设置' || '未设置' }}"
          echo "vars.MYSQL_USER: ${{ vars.MYSQL_USER }}"
          echo "vars.MYSQL_DATABASE: ${{ vars.MYSQL_DATABASE }}"

          if [ -z "$MYSQL_ROOT_PASSWORD" ]; then
            echo "❌ MYSQL_ROOT_PASSWORD 未设置"
            exit 1
          fi

          if [ -z "$MYSQL_PASSWORD" ]; then
            echo "❌ MYSQL_PASSWORD 未设置"
            exit 1
          fi

          if [ -z "$MYSQL_USER" ]; then
            echo "❌ MYSQL_USER 未设置"
            exit 1
          fi

          echo "✅ 关键环境变量验证通过"

      - name: 上传部署制品
        run: |
          echo "=== 上传部署制品到服务器 ==="
          echo "当前版本: ${{ env.VERSION }}"

          # 检查制品文件是否存在
          ARTIFACT_FILE="weizhi-deploy-${{ env.VERSION }}.tar.gz"
          echo "查找制品文件: $ARTIFACT_FILE"
          
          if [ ! -f "$ARTIFACT_FILE" ]; then
            echo "❌ 制品文件不存在: $ARTIFACT_FILE"
            echo "当前目录内容:"
            ls -la
            exit 1
          fi

          echo "✅ 找到制品文件: $ARTIFACT_FILE"
          echo "文件大小: $(du -h $ARTIFACT_FILE | cut -f1)"

          # 上传制品到服务器
          scp "$ARTIFACT_FILE" ${{ env.DEPLOY_USER }}@${{ env.DEPLOY_HOST }}:${{ env.DEPLOY_PATH }}/

          # 上传环境配置文件
          scp deployment.env ${{ env.DEPLOY_USER }}@${{ env.DEPLOY_HOST }}:${{ env.DEPLOY_PATH }}/production.env

          echo "✅ 制品上传完成"

      - name: 解压并部署
        run: |
          echo "=== 在服务器上解压并部署 ==="

          ssh ${{ env.DEPLOY_USER }}@${{ env.DEPLOY_HOST }} "cd ${{ env.DEPLOY_PATH }} && VERSION='${{ env.VERSION }}' bash -s" << 'DEPLOY_SCRIPT'
            # 设置版本变量
            VERSION='${{ env.VERSION }}'
            echo "使用版本: $VERSION"

            # 备份当前配置
            echo '🔄 备份当前配置...'
            if [ -f docker-compose.prod.yml ]; then
              cp docker-compose.prod.yml docker-compose.prod.yml.backup
              echo '✅ 已备份当前配置'
            fi

            # 解压制品
            ARTIFACT_FILE="weizhi-deploy-${VERSION}.tar.gz"
            echo "查找制品文件: $ARTIFACT_FILE"
            ls -la *.tar.gz 2>/dev/null || echo "没有找到 .tar.gz 文件"

            if [ -f "$ARTIFACT_FILE" ]; then
              tar -xzf "$ARTIFACT_FILE"
              echo '✅ 制品解压完成'

              # 设置权限
              chmod +x quick-deploy.sh

              # 验证关键文件
              if [ -f docker-compose.prod.yml ]; then
                echo '✅ Docker Compose 配置文件存在'
              else
                echo '❌ Docker Compose 配置文件不存在'
                exit 1
              fi

              if [ -f VERSION ]; then
                echo '✅ 版本信息文件存在'
                cat VERSION
              else
                echo '❌ 版本信息文件不存在'
                exit 1
              fi

              # 清理压缩包
              rm "$ARTIFACT_FILE"
              echo '✅ 已清理压缩包'
            else
              echo "❌ 制品文件不存在: $ARTIFACT_FILE"
              exit 1
            fi
          DEPLOY_SCRIPT

      - name: 执行部署
        run: |
          echo "=== 执行服务部署 ==="

          ssh ${{ env.DEPLOY_USER }}@${{ env.DEPLOY_HOST }} "cd ${{ env.DEPLOY_PATH }} && VERSION='${{ env.VERSION }}' bash -s" << 'DEPLOY_SCRIPT'
            # 设置版本变量
            VERSION='${{ env.VERSION }}'
            echo "部署版本: $VERSION"
            # 验证环境配置文件
            echo '📋 验证生产环境配置...'
            if [ -f production.env ]; then
              echo '✅ production.env 文件存在'
              echo '📄 配置文件内容预览:'
              head -10 production.env
            else
              echo '❌ production.env 文件不存在'
              exit 1
            fi

            echo '🛑 停止所有现有服务...'

            # 1. 停止所有 weizhi 相关容器
            echo '停止 weizhi 相关容器...'
            docker stop $(docker ps -q --filter "name=weizhi") 2>/dev/null || true
            docker rm $(docker ps -aq --filter "name=weizhi") 2>/dev/null || true

            # 2. 停止占用关键端口的容器
            echo '停止占用端口的容器...'
            docker stop $(docker ps -q --filter "publish=3307") 2>/dev/null || true
            docker stop $(docker ps -q --filter "publish=3000") 2>/dev/null || true
            docker stop $(docker ps -q --filter "publish=3001") 2>/dev/null || true
            docker stop $(docker ps -q --filter "publish=80") 2>/dev/null || true

            # 3. 使用 docker compose 清理
            if [ '${{ needs.parse-params.outputs.first_deployment }}' = 'true' ]; then
              echo '🗑️ 首次部署：清理所有数据（包括数据库）'
              docker compose --env-file production.env -f docker-compose.prod.yml down --remove-orphans --volumes 2>/dev/null || true
            else
              echo '🔄 非首次部署：保留数据库数据'
              docker compose --env-file production.env -f docker-compose.prod.yml down --remove-orphans 2>/dev/null || true
            fi

            # 4. 清理网络
            docker network prune -f 2>/dev/null || true

            echo '✅ 现有服务已停止'

            echo '🔄 拉取 Docker 镜像...'
            docker compose --env-file production.env -f docker-compose.prod.yml pull

            echo '🚀 启动服务...'
            if [ '${{ needs.parse-params.outputs.force_deploy }}' = 'true' ]; then
              echo '⚠️ 强制部署模式，跳过健康检查'
              if docker compose --env-file production.env -f docker-compose.prod.yml up -d; then
                echo '✅ 服务启动命令执行成功'
              else
                echo '❌ 服务启动命令执行失败'
                docker compose --env-file production.env -f docker-compose.prod.yml logs --tail=50
                exit 1
              fi
            else
              echo '🔍 检查数据库配置...'
              echo "MYSQL_USER: $(grep '^MYSQL_USER=' production.env | cut -d'=' -f2)"
              echo "MYSQL_DATABASE: $(grep '^MYSQL_DATABASE=' production.env | cut -d'=' -f2)"
              if grep -q '^MYSQL_ROOT_PASSWORD=' production.env; then
                echo "MYSQL_ROOT_PASSWORD: [已设置]"
              else
                echo "MYSQL_ROOT_PASSWORD: [未设置]"
              fi
              if grep -q '^MYSQL_PASSWORD=' production.env; then
                echo "MYSQL_PASSWORD: [已设置]"
              else
                echo "MYSQL_PASSWORD: [未设置]"
              fi

              echo '🚀 启动服务（带健康检查）...'
              # 先启动 MySQL
              echo '🔧 先启动 MySQL 服务...'
              docker compose --env-file production.env -f docker-compose.prod.yml up -d mysql
              
              # 等待 MySQL 完全启动
              echo '⏳ 等待 MySQL 完全启动...'

              # 等待 MySQL 容器健康检查通过
              echo "等待 MySQL 容器健康检查通过..."
              for i in {1..30}; do
                container_status=$(docker compose --env-file production.env -f docker-compose.prod.yml ps mysql --format "table" | tail -n +2)
                if echo "$container_status" | grep -q "(healthy)"; then
                  echo "✅ MySQL 容器健康检查通过"
                  break
                else
                  echo "⏳ 等待 MySQL 健康检查... $i/30 (等待2秒)"
                  sleep 2
                fi
              done
              
              # 创建MySQL修复命令文件（供手动执行）
              echo '📝 创建MySQL修复命令文件...'
              echo '#!/bin/bash' > mysql-fix-commands.sh
              echo 'set -e' >> mysql-fix-commands.sh
              echo '' >> mysql-fix-commands.sh
              echo 'echo "=== MySQL用户检测和修复命令 ==="' >> mysql-fix-commands.sh
              echo '' >> mysql-fix-commands.sh
              echo '# 读取环境变量' >> mysql-fix-commands.sh
              echo 'source production.env' >> mysql-fix-commands.sh
              echo '' >> mysql-fix-commands.sh
              echo 'echo "1. 检查MySQL容器状态"' >> mysql-fix-commands.sh
              echo 'docker compose --env-file production.env -f docker-compose.prod.yml ps mysql' >> mysql-fix-commands.sh
              echo '' >> mysql-fix-commands.sh
              echo 'echo "2. 测试root用户连接（使用密码）"' >> mysql-fix-commands.sh
              echo 'docker compose --env-file production.env -f docker-compose.prod.yml exec -T mysql mysql -u root -p"${MYSQL_ROOT_PASSWORD}" -e "SELECT 1;"' >> mysql-fix-commands.sh
              echo '' >> mysql-fix-commands.sh
              echo 'echo "3. 验证weizhi用户是否可以连接数据库"' >> mysql-fix-commands.sh
              echo 'docker compose --env-file production.env -f docker-compose.prod.yml exec -T mysql mysql -u "${MYSQL_USER}" -p"${MYSQL_PASSWORD}" "${MYSQL_DATABASE}" -e "SELECT 1;"' >> mysql-fix-commands.sh
              echo '' >> mysql-fix-commands.sh
              echo 'echo "✅ MySQL修复命令执行完成"' >> mysql-fix-commands.sh
              
              chmod +x mysql-fix-commands.sh
              echo "✅ MySQL修复命令文件已创建: mysql-fix-commands.sh"
              echo "💡 请手动执行: ./mysql-fix-commands.sh"
              
              # 启动所有服务
              echo '🚀 启动所有服务...'
              if docker compose --env-file production.env -f docker-compose.prod.yml up -d --wait; then
                echo '✅ 所有服务启动并通过健康检查'
              else
                echo '❌ 服务启动失败，开始诊断...'
                echo '📋 服务状态:'
                docker compose --env-file production.env -f docker-compose.prod.yml ps
                echo '📋 服务日志:'
                docker compose --env-file production.env -f docker-compose.prod.yml logs --tail=50

              echo ''
              echo '🔍 Caddy 健康检查调试:'
              echo '测试 Caddy 健康检查端点...'

              # 测试主健康检查端点（从宿主机测试）
              echo '1. 测试 http://localhost:80/health'
              if curl -f -m 10 http://localhost:80/health 2>/dev/null; then
                echo '✅ 主健康检查端点正常'
              else
                echo '❌ 主健康检查端点失败'
              fi

              # 测试备用健康检查端点（从宿主机测试）
              echo '2. 测试 http://localhost:8080/health'
              if curl -f -m 10 http://localhost:8080/health 2>/dev/null; then
                echo '✅ 备用健康检查端点正常'
              else
                echo '⚠️ 备用健康检查端点失败（可能由于访问控制，不影响主要功能）'
              fi

              # 检查 Caddy 配置
              echo '3. 检查 Caddy 配置'
              docker compose --env-file production.env -f docker-compose.prod.yml exec -T caddy caddy validate --config /etc/caddy/Caddyfile

              # 显示 Caddy 监听的端口
              echo '4. 检查 Caddy 监听端口'
              docker compose --env-file production.env -f docker-compose.prod.yml exec -T caddy netstat -tlnp 2>/dev/null || echo '无法获取端口信息'

                echo '📝 MySQL修复命令文件已创建: mysql-fix-commands.sh'
                echo '💡 请手动执行: ./mysql-fix-commands.sh'
                echo '💡 然后重启服务: docker compose --env-file production.env -f docker-compose.prod.yml restart server'
                exit 1
              fi
            fi

            echo "✅ 部署完成"
          DEPLOY_SCRIPT

      - name: 首次部署数据导入
        if: needs.parse-params.outputs.first_deployment == 'true'
        run: |
          echo "=== 首次部署数据导入 ==="

          ssh ${{ env.DEPLOY_USER }}@${{ env.DEPLOY_HOST }} "
            cd ${{ env.DEPLOY_PATH }}

            echo '✅ 首次部署完成，server-go 将自动执行数据导入'
          "

      - name: 部署完成总结
        run: |
          echo "## 🎉 部署完成总结" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📋 部署信息" >> $GITHUB_STEP_SUMMARY
          echo "- **操作类型**: ${{ needs.parse-params.outputs.action_type }}" >> $GITHUB_STEP_SUMMARY
          echo "- **版本号**: ${{ needs.parse-params.outputs.version }}" >> $GITHUB_STEP_SUMMARY
          echo "- **部署环境**: ${{ needs.parse-params.outputs.environment }}" >> $GITHUB_STEP_SUMMARY
          echo "- **部署服务**: ${{ needs.parse-params.outputs.services }}" >> $GITHUB_STEP_SUMMARY
          echo "- **部署时间**: $(date '+%Y-%m-%d %H:%M:%S')" >> $GITHUB_STEP_SUMMARY
          echo "- **强制部署**: ${{ needs.parse-params.outputs.force_deploy }}" >> $GITHUB_STEP_SUMMARY
          echo "- **首次部署**: ${{ needs.parse-params.outputs.first_deployment }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          if [ "${{ needs.parse-params.outputs.action_type }}" = "build-and-deploy" ]; then
            echo "### 🚀 构建并部署" >> $GITHUB_STEP_SUMMARY
            echo "- 已构建新镜像并部署到生产环境" >> $GITHUB_STEP_SUMMARY
            echo "- 推送到外部仓库: ${{ needs.parse-params.outputs.push_to_registry }}" >> $GITHUB_STEP_SUMMARY
            echo "- 推送到 Gitea 仓库: ${{ needs.parse-params.outputs.push_to_gitea }}" >> $GITHUB_STEP_SUMMARY
          else
            echo "### 📦 仅部署" >> $GITHUB_STEP_SUMMARY
            echo "- 使用指定版本镜像进行部署" >> $GITHUB_STEP_SUMMARY
            echo "- 版本来源: 之前构建的镜像" >> $GITHUB_STEP_SUMMARY
          fi

          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🔗 访问地址" >> $GITHUB_STEP_SUMMARY
          echo "- **前端**: https://${{ vars.DOMAIN }}" >> $GITHUB_STEP_SUMMARY
          echo "- **管理后台**: https://${{ vars.ADMIN_DOMAIN }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### ✅ 部署成功" >> $GITHUB_STEP_SUMMARY
          echo "所有服务已成功部署并运行！" >> $GITHUB_STEP_SUMMARY