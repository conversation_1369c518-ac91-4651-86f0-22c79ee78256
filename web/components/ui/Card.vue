<template>
  <div class="bg-white rounded-lg shadow-md overflow-hidden transition-shadow duration-300 hover:shadow-lg">
    <div v-if="$slots.image" class="w-full">
      <slot name="image"></slot>
    </div>
    <div class="p-6">
      <div v-if="$slots.title" class="mb-4">
        <slot name="title"></slot>
      </div>
      <div v-if="$slots.content">
        <slot name="content"></slot>
      </div>
      <div v-if="$slots.footer" class="mt-4 pt-4 border-t border-gray-100">
        <slot name="footer"></slot>
      </div>
    </div>
  </div>
</template>

<script setup>
// 卡片组件逻辑
</script>

<style scoped>
/* 可以添加额外的样式 */
</style> 