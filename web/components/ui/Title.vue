<template>
  <div class="title-container bg-main-color" :class="{ 'with-line': withLine }">
    <h2 class="text-7 font-bold text-main-color mb-2">{{ title }}</h2>
    <p v-if="subtitle" class="text-gray-600">{{ subtitle }}</p>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true
  },
  subtitle: {
    type: String,
    default: ''
  },
  withLine: {
    type: Boolean,
    default: false
  }
});
</script>

<style scoped>
.title-container {
  margin-bottom: 2rem;
}

.with-line h2 {
  position: relative;
  padding-bottom: 0.75rem;
}

.with-line h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
}

@media (max-width: 768px) {
  .title-container {
    margin-bottom: 1.5rem;
  }
  
  h2 {
    font-size: 1.5rem;
  }
}
</style> 