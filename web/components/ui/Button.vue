<template>
  <button
    :class="[
      'px-6 py-3 rounded-md transition-all duration-300 font-medium text-4',
      type === 'primary' ? 'bg-main-color hover:bg-opacity-90 text-white' : '',
      type === 'secondary' ? 'bg-main1-color hover:bg-opacity-90 text-white' : '',
      type === 'outline' ? 'border border-main-color text-main-color hover:bg-main-color hover:text-white' : '',
      type === 'text' ? 'text-main-color hover:underline' : '',
      block ? 'w-full' : '',
      disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer',
      className
    ]"
    :disabled="disabled"
    @click="$emit('click')"
  >
    <slot></slot>
  </button>
</template>

<script setup>
defineProps({
  type: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'secondary', 'outline', 'text'].includes(value)
  },
  block: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  className: {
    type: String,
    default: ''
  }
});

defineEmits(['click']);
</script> 