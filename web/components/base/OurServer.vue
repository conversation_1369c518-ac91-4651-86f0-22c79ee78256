<template>
  <main class="flex justify-center flex-wrap mb-10">
    <MainTitle
      first-title="OUR"
      last-title="SERVICE"
      sub-title="我们的服务"
    />
    <div
      class="w-full grid lg:grid-cols-8 box-border px-6 xl:px-0 gap-4"
    >
      <!-- 设计开发 -->
      <div
        class="transition-all duration-500 transform hover:scale-105 lg:col-span-3 h-80 border border-solid bg-server cursor-pointer"
        :style="getBgStyle(services, 0)"
        @click="handleServiceClick('/ourserver/design_develop')"
      >
        <div class="w-full h-full relative" @mouseover="serviceHover(0)" @mouseleave="serviceHover(-1)">
          <Transition name="fade">
            <div
              class="w-full absolute h-24 text-white flex bg-blue-600 z-10"
              style="bottom: 0"
            >
              <div
                class="h-full box-border flex flex-col justify-center pl-10"
                style="width: 60%"
              >
                <div>Design and development</div>
                <div class="text-xl">设计开发</div>
              </div>
              <div class="w-full h-full flex items-center" style="width: 40%">
                <div
                  class="border border-solid flex items-center justify-center box-border"
                  style="width: 85%; height: 70%"
                >
                  了解详情
                  <!-- <div class="flex items-center" style="width: 40%">
                    <hr class="bg-white" style="width: 50%" />
                    <div
                      class="triangle-right"
                      style="border-top: 5px solid transparent; border-left: 12px solid white; border-bottom: 5px solid transparent;"
                    ></div>
                  </div> -->
                </div>
              </div>
            </div>
          </Transition>
          <!-- <div v-if="serviceCurrent !== 0" class="w-full h-24 flex items-center">
            <div
              class="w-4 h-4 border border-solid ml-6"
              style="border-radius: 40%"
            ></div>
            <div class="pl-4" style="font-size: 26px">设计开发</div>
          </div> -->
        </div>
      </div>

      <!-- 零件平台 -->
      <NuxtLink
        class="transition-all duration-500 transform hover:scale-105 lg:col-span-2 h-80 border border-solid relative bg-server cursor-pointer"
        :style="getBgStyle(services, 1)"
        to="/part"
      >
      <div class="w-full h-full relative" @mouseover="serviceHover(1)" @mouseleave="serviceHover(-1)">
          <Transition name="fade">
            <div
              class="w-full absolute h-24 text-white flex bg-blue-600 z-10"
              style="bottom: 0"
            >
              <div
                class="h-full box-border flex flex-col justify-center pl-10"
                style="width: 60%"
              >
                <div>Parts Platform</div>
                <div class="text-xl">零件平台</div>
              </div>
              <div class="w-full h-full flex items-center" style="width: 40%">
                <div
                  class="border border-solid flex items-center justify-center box-border"
                  style="width: 85%; height: 70%"
                >
                  了解详情
                  <!-- <div class="flex items-center" style="width: 40%">
                    <hr class="bg-white" style="width: 50%" />
                    <div
                      class="triangle-right"
                      style="border-top: 5px solid transparent; border-left: 12px solid white; border-bottom: 5px solid transparent;"
                    ></div>
                  </div> -->
                </div>
              </div>
            </div>
          </Transition>
          <!-- <div v-if="serviceCurrent !== 1" class="w-full h-24 flex items-center">
            <div
              class="w-4 h-4 border border-solid ml-6"
              style="border-radius: 40%"
            ></div>
            <div class="pl-4" style="font-size: 26px">试验验证</div>
          </div> -->
        </div>
      </NuxtLink>

      <!-- 试验验证 -->
      <NuxtLink
        class="transition-all duration-500 transform hover:scale-105 lg:col-span-3 h-80 border border-solid server2 cursor-pointer"
        :style="getBgStyle(services, 2)"
        to="/ourserver/ev"
      >
        <div class="w-full h-full relative" @mouseover="serviceHover(1)" @mouseleave="serviceHover(-1)">
          <Transition name="fade">
            <div
              class="w-full absolute h-24 text-white flex bg-blue-600 z-10"
              style="bottom: 0"
            >
              <div
                class="h-full box-border flex flex-col justify-center pl-10"
                style="width: 60%"
              >
                <div>Test and validation</div>
                <div class="text-xl">试验验证</div>
              </div>
              <div class="w-full h-full flex items-center" style="width: 40%">
                <div
                  class="border border-solid flex items-center justify-center box-border"
                  style="width: 85%; height: 70%"
                >
                  了解详情
                  <!-- <div class="flex items-center" style="width: 40%">
                    <hr class="bg-white" style="width: 50%" />
                    <div
                      class="triangle-right"
                      style="border-top: 5px solid transparent; border-left: 12px solid white; border-bottom: 5px solid transparent;"
                    ></div>
                  </div> -->
                </div>
              </div>
            </div>
          </Transition>
          <!-- <div v-if="serviceCurrent !== 1" class="w-full h-24 flex items-center">
            <div
              class="w-4 h-4 border border-solid ml-6"
              style="border-radius: 40%"
            ></div>
            <div class="pl-4" style="font-size: 26px">试验验证</div>
          </div> -->
        </div>
      </NuxtLink>

      <!-- 设备开发 -->
      <NuxtLink
        class="transition-all duration-500 transform hover:scale-105 lg:col-span-3 h-80 border border-solid server3 cursor-pointer"
        :style="getBgStyle(services, 3)"
        to="/ourserver/ed"
      >
        <div class="w-full h-full relative" @mouseover="serviceHover(2)" @mouseleave="serviceHover(-1)">
          <Transition name="fade">
            <div
              class="w-full absolute h-24 text-white flex bg-blue-600"
              style="bottom: 0"
            >
              <div
                class="h-full box-border flex flex-col justify-center pl-10"
                style="width: 60%"
              >
                <div>Equipment and development</div>
                <div class="text-xl">设备开发</div>
              </div>
              <div class="w-full h-full flex items-center" style="width: 40%">
                <div
                  class="border border-solid flex items-center justify-center box-border"
                  style="width: 85%; height: 70%"
                >
                  了解详情
                  <!-- <div class="flex items-center" style="width: 40%">
                    <hr class="bg-white" style="width: 50%" />
                    <div
                      class="triangle-right"
                      style="border-top: 5px solid transparent; border-left: 12px solid white; border-bottom: 5px solid transparent;"
                    ></div>
                  </div> -->
                </div>
              </div>
            </div>
          </Transition>
          <!-- <div v-if="serviceCurrent !== 2" class="w-full h-24 flex items-center">
            <div
              class="w-4 h-4 border border-solid ml-6"
              style="border-radius: 40%"
            ></div>
            <div class="pl-4" style="font-size: 26px">设备开发</div>
          </div> -->
        </div>
      </NuxtLink>

      <!-- 人力资源 -->
      <NuxtLink
        class="transition-all duration-500 transform hover:scale-105 lg:col-span-3 h-80 border border-solid server4 cursor-pointer"
        :style="getBgStyle(services, 4)"
        to="/join"
      >
        <div class="w-full h-full relative" @mouseover="serviceHover(3)" @mouseleave="serviceHover(-1)">
          <Transition name="fade">
            <div
              class="w-full absolute h-24 text-white flex bg-blue-600"
              style="bottom: 0"
            >
              <div
                class="h-full box-border flex flex-col justify-center pl-10"
                style="width: 60%"
              >
                <div>Human Resources</div>
                <div class="text-xl">人力资源</div>
              </div>
              <div class="w-full h-full flex items-center" style="width: 40%">
                <div
                  class="border border-solid flex items-center justify-center box-border"
                  style="width: 85%; height: 70%"
                >
                  了解详情
                  <!-- <div class="flex items-center" style="width: 40%">
                    <hr class="bg-white" style="width: 50%" />
                    <div
                      class="triangle-right"
                      style="border-top: 5px solid transparent; border-left: 12px solid white; border-bottom: 5px solid transparent;"
                    ></div>
                  </div> -->
                </div>
              </div>
            </div>
          </Transition>
          <!-- <div v-if="serviceCurrent !== 3" class="w-full h-24 flex items-center">
            <div
              class="w-4 h-4 border border-solid ml-6"
              style="border-radius: 40%"
            ></div>
            <div class="pl-4" style="font-size: 26px">人力资源</div>
          </div> -->
        </div>
      </NuxtLink>

      <!-- 在线服务 -->
      <NuxtLink
        class="transition-all duration-500 transform hover:scale-105 lg:col-span-2 h-80 border border-solid relative bg-server cursor-pointer overflow-hidden"
        style="background-color: #309cdb"
        to="/"
      >
        <div class="w-full h-24 flex items-center">
          <div
            class="w-4 h-4 border border-solid ml-6"
            style="border-radius: 40%"
          ></div>
          <div class="pl-4 text-white" style="font-size: 26px">在线服务</div>
        </div>
        <div class="absolute bottom-0 w-full" style="right: -60px">
          <img
            class="h-auto w-full"
            alt=""
            src="https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/cover/6.png"
          />
        </div>
      </NuxtLink>
    </div>
  </main>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import MainTitle from './MainTitle.vue'

const router = useRouter()
const serviceCurrent = ref(-1)

const serviceHover = (index: number) => {
  serviceCurrent.value = index
}

const handleServiceClick = async (path: string) => {
  if (!path) {
    console.warn('handleServiceClick 跳转 path 为空，已拦截');
    return;
  }
  try {
    await navigateTo(path)
  } catch (error) {
    console.error('路由跳转失败:', error)
  }
}

// 获取服务项目数据
const { data: services, fetchData, loading, error } = useServices()

// 页面元数据
useHead({
  title: '我们的服务 - 江苏蔚之领域智能科技有限公司',
  meta: [
    {
      name: 'description',
      content: '蔚之领域提供全面的汽车行业服务，包括设计开发、试验验证、设备开发等专业解决方案。',
    },
  ],
})

onMounted(async () => {
  await fetchData()
})

function getBgStyle(arr: any[], idx: number) {
  if (Array.isArray(arr) && arr[idx] && arr[idx].image) {
    return { backgroundImage: `url(${arr[idx].image})` }
  }
  return {}
}
</script>

<style lang="scss" scoped>
.bg-server {
  background-repeat: no-repeat;
  background-position: center top;
  background-size: cover;
}

.server2 {
  background-repeat: no-repeat;
  background-size: cover;
}

.server3 {
  background-repeat: no-repeat;
  background-size: cover;
}

.server4 {
  background-repeat: no-repeat;
  background-size: cover;
}

.triangle-right {
  width: 0;
  height: 0;
}

// 淡入淡出动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style> 