<template>
  <div class="header">
    <!-- 顶部欢迎栏 -->
    <!-- <div class="w-full bg-[#f5f5f5] justify-center md:flex hidden box-border md:px-6 xl:px-0">
      <div class="w-main-width h-full flex-between">
        <div>欢迎访问公司官网！</div>
        <div class="flex items-center text-main-color font-bold">
          <img
            class="h-5.5 w-auto"
            src="/images/phone.png"
            alt="phone"
            width="22"
            height="22"
          />
          <span class="pl-2 text-xl">400-0000-0000</span>
        </div>
      </div>
    </div> -->
    
    <!-- 主导航栏 -->
    <div class="w-full h-14 xl:h-16 bg-white flex justify-center">
      <div class="w-main-width flex-between sm:pl-6 xl:pl-0">
        <!-- Logo -->
        <NuxtLink to="/">
          <img
            class="w-auto h-9 xl:h-10"
            src="/images/Snipaste_2025-03-30_01-52-47.png"
            alt="logo"
            width="120"
            height="40"
          />
        </NuxtLink>
        
        <!-- 导航菜单 - 大屏幕 -->
        <div class="xl:flex items-center justify-end hidden h-full" style="width: 80%">
          <ul class="flex text-lg justify-between h-full" style="width: 90%">
            <li class="h-full flex items-center">
              <NuxtLink class="hover:text-main-color" to="/">首页</NuxtLink>
            </li>
            <li class="h-full flex items-center">
              <NuxtLink class="hover:text-main-color" to="/about">蔚之领域</NuxtLink>
            </li>
            <li class="h-full flex items-center relative" 
                @mouseover="showCoreMenu = true" 
                @mouseout="showCoreMenu = false">
              <span class="hover:text-main-color cursor-pointer flex items-center">
                核心业务
                <i class="i-heroicons-chevron-down ml-1 text-xs transition-transform duration-300" :class="{'rotate-180': showCoreMenu}"></i>
              </span>
              <div class="absolute" style="top: 100%; left: -50px; z-index: 999;">
                <div v-show="showCoreMenu" class="core-dropdown mt-1">
                  <div class="core-menu-item">
                    <NuxtLink to="/ourServer/design_develop" class="core-menu-link hover:bg-main1-color hover:text-main1-color">设计开发</NuxtLink>
                  </div>
                  <div class="core-menu-item">
                    <NuxtLink to="/ourServer/ev" class="core-menu-link hover:bg-main1-color hover:text-main1-color">试验验证</NuxtLink>
                  </div>
                  <div class="core-menu-item">
                    <NuxtLink to="/part" class="core-menu-link hover:bg-main1-color hover:text-main1-color">零件平台</NuxtLink>
                  </div>
                  <div class="core-menu-item">
                    <NuxtLink to="/ourServer/ed" class="core-menu-link hover:bg-main1-color hover:text-main1-color">设备开发</NuxtLink>
                  </div>
                  <div class="core-menu-item">
                    <NuxtLink to="/join" class="core-menu-link hover:bg-main1-color hover:text-main1-color">人力资源</NuxtLink>
                  </div>
                </div>
              </div>
            </li>
            <li class="h-full flex items-center">
              <NuxtLink class="hover:text-main-color" to="/">公司文化</NuxtLink>
            </li>
            <li class="h-full flex items-center">
              <NuxtLink class="hover:text-main-color" to="/news">新闻资讯</NuxtLink>
            </li>
            <li class="h-full flex items-center">
              <NuxtLink class="hover:text-main-color" to="/partners">合作伙伴</NuxtLink>
            </li>
            <li class="h-full flex items-center">
              <NuxtLink class="hover:text-main-color" to="/contact">联系我们</NuxtLink>
            </li>
          </ul>
          
          <!-- 搜索按钮 -->
          <button class="pl-2" @click="openSearch">
            <img
              class="w-auto h-8"
              src="/images/search.png"
              alt="搜索"
              width="32"
              height="32"
            />
          </button>
        </div>
        
        <!-- 汉堡菜单按钮 - 小屏幕 -->
        <div class="flex xl:hidden items-center justify-end h-10" @click="openSidebar">
          <i class="i-heroicons-bars-3 text-2xl mr-4 hover:text-main-color"></i>
        </div>
      </div>
    </div>
    
    <!-- 遮罩层 -->
    <div v-if="showSidebar" class="fixed inset-0 bg-black bg-opacity-50 z-40 mask" @click="showSidebar = false"></div>
    
    <!-- 侧边栏菜单 - 小屏幕 -->
    <div v-if="showSidebar" class="fixed w-72 h-full right-0 top-0 z-50 flex justify-end">
      <div class="w-12 h-12 box-border pt-4" @click="closeSidebar">
        <i class="i-heroicons-x-mark text-2xl text-black"></i>
      </div>
      <div class="w-64 bg-white">
        <div class="w-full h-16 flex-center">
          <button class="px-3 mx-2" @click="openSearch">
            <img
              class="w-auto h-8"
              src="/images/search.png"
              alt="搜索"
              width="32"
              height="32"
            />
          </button>
        </div>
        <ul class="text-lg box-border sidebar-list">
          <li class="border-b border-gray-100 py-3 text-center hover:bg-main1-color hover:text-white">
            <NuxtLink to="/">首页</NuxtLink>
          </li>
          <li class="border-b border-gray-100 py-3 text-center hover:bg-main1-color hover:text-white">
            <NuxtLink to="/about">蔚之领域·</NuxtLink>
          </li>
          <li class="border-b border-gray-100 py-3 text-center hover:bg-main1-color hover:text-white">
            <NuxtLink to="/">公司文化</NuxtLink>
          </li>
          <li class="border-b border-gray-100 py-3 text-center hover:bg-main1-color hover:text-white">
            <NuxtLink to="/news">新闻资讯</NuxtLink>
          </li>
          <li class="border-b border-gray-100 py-3 text-center hover:bg-main1-color hover:text-white">
            <NuxtLink to="/partners">合作伙伴</NuxtLink>
          </li>
          <li class="border-b border-gray-100 py-3 text-center hover:bg-main1-color hover:text-white">
            <NuxtLink to="/contact">联系我们</NuxtLink>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

// 状态变量
const showCoreMenu = ref(false);
const showSidebar = ref(false);

// 方法
const openSearch = () => {
  emit('openSearch');
};

const openSidebar = () => {
  showSidebar.value = true;
};

const closeSidebar = () => {
  showSidebar.value = false;
};

// 定义事件
const emit = defineEmits(['openSearch']);
</script>

<style scoped>
.sidebar-list li:hover a {
  color: white;
}

/* 导航菜单项样式 */
ul li {
  position: relative;
}

ul li a, ul li span {
  padding: 0 8px;
  display: flex;
  align-items: center;
  height: 100%;
  white-space: nowrap;
}

/* 下拉菜单样式 */
.core-dropdown {
  width: 160px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  padding: 8px;
}

.core-menu-item {
  margin-bottom: 4px;
}

.core-menu-item:last-child {
  margin-bottom: 0;
}

.core-menu-link {
  display: block;
  height: 36px;
  line-height: 36px;
  text-align: center;
  font-size: 14px;
  color: #333;
  border-radius: 4px;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9;
}

.nav-list {
  @apply flex items-center justify-end h-full;
  white-space: nowrap;
}

.nav-item {
  @apply relative flex items-center h-full px-4 cursor-pointer;
  white-space: nowrap;
}

.nav-link {
  @apply text-base text-gray-700 hover:text-blue-600;
  white-space: nowrap;
}

.dropdown-menu {
  @apply absolute top-full left-0 bg-white shadow-lg rounded-md py-2 z-50 min-w-[120px];
  white-space: nowrap;
}

.dropdown-item {
  @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100;
  white-space: nowrap;
}

/* 确保文字不换行 */
.text-lg {
  font-size: 1rem;
  white-space: nowrap;
}

/* 主导航栏下边框 */
.header > .w-full.h-14 {
  border-bottom: 1px solid #e5e7eb;
}
@media (min-width: 1280px) {
  .header > .w-full.xl\:h-16 {
    border-bottom: 1px solid #e5e7eb;
  }
}
</style> 