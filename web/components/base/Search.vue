<template>
  <div v-if="show" class="fixed top-0 w-screen h-screen bg-gray-900 bg-opacity-90 z-50" @click.self="closeSearch">
    <div class="flex justify-end p-6">
      <el-button @click="closeSearch" type="text" class="text-white text-2xl">&times;</el-button>
    </div>
    <div class="w-full max-w-2xl mx-auto mt-20 px-4">
      <div class="relative">
        <input 
          v-model="searchValue" 
          type="text" 
          class="w-full h-16 bg-transparent border-b-2 border-white text-white text-xl outline-none px-2"
          placeholder="请输入搜索内容..." 
          @keyup.enter="handleSearch"
        />
        <el-button 
          type="primary" 
          class="absolute right-0 top-0 h-16 w-20 flex justify-center items-center"
          @click="handleSearch"
        >
          <el-icon><Search /></el-icon>
        </el-button>
      </div>
      
      <div class="mt-10 text-white">
        <div v-if="searchResults.length > 0">
          <div class="text-xl mb-4">搜索结果：</div>
          <div v-for="(item, index) in searchResults" :key="index" class="mb-4 cursor-pointer hover:text-blue-400" @click="goToDetail(item)">
            <div class="text-lg">{{ item.title }}</div>
            <div class="text-sm text-gray-400">{{ item.summary }}</div>
          </div>
        </div>
        <div v-else-if="isSearched" class="text-center mt-20">
          <div class="text-xl">暂无搜索结果</div>
          <div class="mt-4">请尝试其他关键词</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Search } from '@element-plus/icons-vue'

const props = defineProps({
  show: Boolean
})

const emit = defineEmits(['close'])

const searchValue = ref('')
const searchResults = ref([])
const isSearched = ref(false)

const closeSearch = () => {
  emit('close')
  searchValue.value = ''
  searchResults.value = []
  isSearched.value = false
}

const handleSearch = () => {
  if (!searchValue.value.trim()) return
  
  isSearched.value = true
  
  // 这里应该调用实际的搜索API
  // 目前使用模拟数据
  if (searchValue.value.includes('新闻') || searchValue.value.includes('news')) {
    searchResults.value = [
      { 
        id: 1, 
        type: 'news',
        title: '蔚之领域公司新闻', 
        summary: '蔚之领域最新进展公告...'
      }
    ]
  } else if (searchValue.value.includes('服务') || searchValue.value.includes('service')) {
    searchResults.value = [
      { 
        id: 1, 
        type: 'service',
        title: '设计开发服务', 
        summary: '蔚之领域设计开发服务介绍...'
      }
    ]
  } else {
    searchResults.value = []
  }
}

const goToDetail = (item) => {
  if (!item) {
    console.warn('goToDetail 跳转 item 为空，已拦截');
    return;
  }
  if (item.type === 'news' && !item.id) {
    console.warn('goToDetail 跳转 news id 为空，已拦截');
    return;
  }
  if (item.type === 'news') {
    navigateTo(`/news/${item.id}`)
  } else if (item.type === 'service') {
    navigateTo('/ourServer/design_develop')
  }
  closeSearch()
}
</script>

<style scoped>
/* 可以添加过渡动画等样式 */
</style> 