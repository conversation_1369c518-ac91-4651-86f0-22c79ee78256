<script setup lang="ts">
import { onMounted } from 'vue';
import { useFriendLinks } from '~/composables/useWebData';

// 使用友情链接数据
const { data: friendLinks, fetchData } = useFriendLinks()

// 初始化数据
onMounted(async () => {
  await fetchData()
  // console.log('Footer.vue fetchData 后 friendLinks.value:', friendLinks.value)
})
</script>

<template>
  <footer>
    <div class="bg-[#1a1a1a]">
      <div class="max-w-[1200px] mx-auto px-4 xl:px-0 py-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- 联系信息 -->
          <div class="text-white">
            <div class="flex items-center text-xl mb-4">
              <img class="w-5 h-5 mr-2.5" alt="phone"
                src="https://images-1305444037.cos.ap-nanjing.myqcloud.com/icon/phone2.png" />
              服务热线
            </div>
            <div class="text-3xl font-semibold mb-4 text-blue-400">13813194120</div>
            <div class="text-[#9b9b9b] mb-2">邮箱: <EMAIL></div>
            <div class="text-[#9b9b9b]">
              地址: 江苏省扬州市广开科技园
            </div>
          </div>

          <!-- 导航链接和友情链接 -->
          <div>
            <!-- 导航链接 -->
            <nav class="mb-5">
              <ul class="text-white grid grid-cols-3 gap-x-4 gap-y-3">
                <li>
                  <NuxtLink to="/" class="hover:text-blue-400 transition-colors">首页</NuxtLink>
                </li>
                <li>
                  <NuxtLink to="/about" class="hover:text-blue-400 transition-colors">蔚之领域</NuxtLink>
                </li>
                <li>
                  <NuxtLink to="/" class="hover:text-blue-400 transition-colors">公司文化</NuxtLink>
                </li>
                <li>
                  <NuxtLink to="/news" class="hover:text-blue-400 transition-colors">新闻资讯</NuxtLink>
                </li>
                <li>
                  <NuxtLink to="/partners" class="hover:text-blue-400 transition-colors">合作伙伴</NuxtLink>
                </li>
                <li>
                  <NuxtLink to="/contact" class="hover:text-blue-400 transition-colors">联系我们</NuxtLink>
                </li>
              </ul>
            </nav>

            <!-- 友情链接 -->
            <div>
              <h3 class="text-white mb-3 font-medium">友情链接:</h3>
              <div class="flex flex-wrap gap-x-4 gap-y-2">
                <a v-for="(item, index) in friendLinks" :key="index" :href="item.url" target="_blank"
                  class="text-[#9b9b9b] hover:text-blue-400 transition-colors text-sm">{{ item.name }}</a>
              </div>
            </div>
          </div>

          <!-- 二维码区域 -->
          <div class="flex flex-col items-center md:items-end">
            <div class="text-center">
              <img class="w-28 h-28 mb-3" alt="微信二维码"
                src="https://images-1305444037.cos.ap-nanjing.myqcloud.com/other/qrcode.png" />
              <p class="text-white text-sm">扫码关注我们</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 版权信息 -->
    <div class="bg-[#131313] py-4">
      <div class="max-w-[1200px] mx-auto px-4 xl:px-0">
        <div class="flex flex-col md:flex-row justify-between items-center gap-3 text-white">
          <div class="text-sm">
            Copyright © {{ new Date().getFullYear() }} 江苏蔚之领域智能科技有限公司 版权所有
          </div>
          <div class="flex items-center gap-2">
            <img class="w-5 h-5" alt="备案图标" src="https://img.alicdn.com/tfs/TB1..50QpXXXXX7XpXXXXXXXXXX-40-40.png" />
            <a href="https://beian.miit.gov.cn/" target="_blank"
              class="text-sm hover:text-blue-400 transition-colors">苏ICP备2025181841</a>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<style lang="scss" scoped>
.max-w-\[1200px\] {
  max-width: 1200px;
}
</style>