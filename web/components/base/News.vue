<template>
  <main class="flex justify-center flex-wrap mb-10">
    <MainTitle first-title="INFORMATION" last-title="CENTRE" sub-title="新闻中心" />
    <div class="
        w-full
        max-w-7xl
        grid
        lg:grid-cols-2
        gap-6
        relative
        box-border
        px-6
        lg:px-0
      ">
      <div v-if="homeNews && homeNews.length > 0" class="
          h-[450px]
          border border-solid
          bg-cover
          cursor-pointer
          relative
          overflow-hidden
        " @click="toNewsDisplay(homeNews[0].id)">
        <!-- 占位背景 -->
        <div v-if="!imageLoadStates[0]" class="absolute inset-0 bg-gray-200 flex items-center justify-center">
          <div class="text-gray-400 text-center">
            <svg class="w-16 h-16 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
            </svg>
            <p class="text-sm">暂无图片</p>
          </div>
        </div>
        <!-- 背景图片 -->
        <img
          v-if="homeNews[0].image"
          :src="homeNews[0].image"
          :alt="homeNews[0].title"
          :data-index="0"
          class="absolute inset-0 w-full h-full object-cover"
          @error="handleImageError"
          @load="handleImageLoad"
        />
        <div class="
            absolute
            w-72
            h-52
            bg-blue-600
            -bottom-0
            left-0
            box-border
            p-8
            text-white
            leading-8
            opacity-90
          ">
          {{ truncateText(homeNews[0].content, 50) }}
        </div>
      </div>
      <div class="h-[450px] flex flex-col gap-4">
        <div v-if="homeNews && homeNews.length > 1" class="
            flex
            items-center
            hover:shadow-lg
            cursor-pointer
            h-[140px]
          " @click="toNewsDisplay(homeNews[1].id)">
          <div class="border border-solid h-full relative overflow-hidden" style="width: 150px">
            <!-- 占位背景 -->
            <div v-if="!imageLoadStates[1]" class="absolute inset-0 bg-gray-200 flex items-center justify-center">
              <svg class="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
              </svg>
            </div>
            <img
              v-if="homeNews[1].image"
              :src="homeNews[1].image"
              :alt="homeNews[1].title"
              :data-index="1"
              class="absolute inset-0 w-full h-full object-cover"
              @error="handleImageError"
              @load="handleImageLoad"
            />
          </div>
          <div class="h-full box-border py-4 flex items-center ml-3">
            <div class="text-center py-2 pr-4" style="
                border-right-width: 2px;
                border-right-style: solid;
                border-right-color: #d8d8d8;
              ">
              <div class="text-5xl" style="color: #6f6f6f">
                {{ formatDay((homeNews[1] as any).created_at) }}
              </div>
              <div class="text-sm">
                {{ formatYearMonth((homeNews[1] as any).created_at) }}
              </div>
            </div>
            <div class="ml-6">
              <div class="text-xl pb-3">
                {{ truncateText(homeNews[1].title, 10) }}
              </div>
              <div class="text-sm" style="color: #7f7f7f">
                {{ truncateText(homeNews[1].content, 20) }}
              </div>
            </div>
          </div>
        </div>
        <div v-if="homeNews && homeNews.length > 2" class="
            flex
            items-center
            hover:shadow-lg
            cursor-pointer
            h-[140px]
          " @click="toNewsDisplay(homeNews[2].id)">
          <div class="border border-solid h-full relative overflow-hidden" style="width: 150px">
            <!-- 占位背景 -->
            <div v-if="!imageLoadStates[2]" class="absolute inset-0 bg-gray-200 flex items-center justify-center">
              <svg class="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
              </svg>
            </div>
            <img
              v-if="homeNews[2].image"
              :src="homeNews[2].image"
              :alt="homeNews[2].title"
              :data-index="2"
              class="absolute inset-0 w-full h-full object-cover"
              @error="handleImageError"
              @load="handleImageLoad"
            />
          </div>
          <div class="h-full box-border py-4 flex items-center ml-3">
            <div class="text-center py-2 pr-4" style="
                border-right-width: 2px;
                border-right-style: solid;
                border-right-color: #d8d8d8;
              ">
              <div class="text-5xl" style="color: #6f6f6f">
                {{ formatDay((homeNews[2] as any).created_at) }}
              </div>
              <div class="text-sm">
                {{ formatYearMonth((homeNews[2] as any).created_at) }}
              </div>
            </div>
            <div class="ml-6">
              <div class="text-xl pb-3">
                {{ truncateText(homeNews[2].title, 10) }}
              </div>
              <div class="text-sm" style="color: #7f7f7f">
                {{ truncateText(homeNews[2].content, 20) }}
              </div>
            </div>
          </div>
        </div>
        <div v-if="homeNews && homeNews.length > 3" class="
            flex
            items-center
            hover:shadow-lg
            cursor-pointer
            h-[140px]
          " @click="toNewsDisplay(homeNews[3].id)">
          <div class="border border-solid h-full relative overflow-hidden" style="width: 150px">
            <!-- 占位背景 -->
            <div v-if="!imageLoadStates[3]" class="absolute inset-0 bg-gray-200 flex items-center justify-center">
              <svg class="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
              </svg>
            </div>
            <img
              v-if="homeNews[3].image"
              :src="homeNews[3].image"
              :alt="homeNews[3].title"
              :data-index="3"
              class="absolute inset-0 w-full h-full object-cover"
              @error="handleImageError"
              @load="handleImageLoad"
            />
          </div>
          <div class="h-full box-border py-4 flex items-center ml-3">
            <div class="text-center py-2 pr-4" style="
                border-right-width: 2px;
                border-right-style: solid;
                border-right-color: #d8d8d8;
              ">
              <div class="text-5xl" style="color: #6f6f6f">
                {{ formatDay((homeNews[3] as any).created_at) }}
              </div>
              <div class="text-sm">
                {{ formatYearMonth((homeNews[3] as any).created_at) }}
              </div>
            </div>
            <div class="ml-6">
              <div class="text-xl pb-3">
                {{ truncateText(homeNews[3].title, 10) }}
              </div>
              <div class="text-sm" style="color: #7f7f7f">
                {{ truncateText(homeNews[3].content, 20) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</template>

<script setup lang="ts">
import type { News } from '@weishi/types'
import { onMounted, ref } from 'vue'
import { useNews } from '~/composables/useWebData'
import MainTitle from './MainTitle.vue'

const { data: newsRaw, fetchData } = useNews({ isHomePage: true })
const homeNews = ref<News[]>([])
const imageLoadStates = ref<Record<number, boolean>>({})

onMounted(async () => {
  await fetchData()
  const val = newsRaw.value as any
  if (Array.isArray(val)) {
    homeNews.value = val
  } else if (Array.isArray(val?.list)) {
    homeNews.value = val.list
  } else {
    homeNews.value = []
  }

  // 初始化图片加载状态
  homeNews.value.forEach((_, index) => {
    imageLoadStates.value[index] = false
  })
})

function truncateText(text: string, length: number) {
  if (!text) return ''
  return text.length > length ? text.substring(0, length) + '...' : text
}

function formatDay(dateString: string) {
  return new Date(dateString).getDate()
}

function formatYearMonth(dateString: string) {
  const date = new Date(dateString)
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`
}

async function toNewsDisplay(id: number) {
  try {
    console.log('点击新闻，准备跳转，ID:', id, '类型:', typeof id)

    if (!id) {
      throw new Error('新闻ID不能为空')
    }

    if (typeof id !== 'number' || isNaN(id)) {
      throw new Error('新闻ID格式不正确')
    }

    console.log('正在跳转到:', `/news/${id}`)
    await navigateTo(`/news/${id}`)
  } catch (error) {
    console.error('跳转到新闻详情页失败:', error)
    // 可以添加用户友好的错误提示
    alert('跳转失败，请稍后重试')
  }
}

// 图片加载错误处理
function handleImageError(event: Event) {
  const img = event.target as HTMLImageElement
  if (img) {
    // 获取图片索引
    const index = parseInt(img.dataset.index || '0')
    imageLoadStates.value[index] = false
    img.style.display = 'none'
  }
}

// 图片加载成功处理
function handleImageLoad(event: Event) {
  const img = event.target as HTMLImageElement
  if (img) {
    // 获取图片索引
    const index = parseInt(img.dataset.index || '0')
    imageLoadStates.value[index] = true
  }
}


</script>

<style scoped>
.bg-cover {
  background-size: cover;
  background-position: center;
}
</style>