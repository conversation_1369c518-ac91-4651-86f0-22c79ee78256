<template>
  <div class="w-full flex flex-col items-center mt-20 mb-10">
    <div class="font-bold text-2xl pb-3">
      <span class="pr-5" style="color: #0071b0">{{ firstTitle }}</span>{{ lastTitle }}
    </div>
    <div class="flex justify-center items-center">
      <hr class="w-24" />
      <div class="mx-4">{{ subTitle }}</div>
      <hr class="w-24" />
    </div>
  </div>
</template>

<script setup>
defineProps({
  firstTitle: {
    type: String,
    default: ''
  },
  lastTitle: {
    type: String,
    default: ''
  },
  subTitle: {
    type: String,
    default: ''
  }
})
</script>

<style scoped></style> 