<template>
  <main
    class="w-full h-80 flex justify-center items-center relative player"
    style="background-color: #060e0e"
  >
    <hr class="w-full h-1" />
    <div
      class="
        h-36
        w-36
        rounded-full
        border border-solid
        absolute
        text-white
        flex flex-col
        justify-center
        items-center
        cursor-pointer
      "
      @click="open"
    >
      <div>在线</div>
      <svg
        t="1621600033690"
        class="icon"
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        p-id="1196"
        width="30"
        height="30"
      >
        <path
          d="M228.97664 141.85472A25.6 25.6 0 0 0 225.28 155.10528v713.79456a25.6 25.6 0 0 0 38.85056 21.90336l546.51392-330.60864c26.61376-16.1024 35.13856-50.72896 19.03616-77.34272a56.32 56.32 0 0 0-19.03616-19.03616L264.13056 133.20192a25.6 25.6 0 0 0-35.15392 8.6528zM174.08 868.89984V155.10528a76.8 76.8 0 0 1 11.08992-39.75168c21.95456-36.29056 69.1712-47.91296 105.46176-25.9584l546.51392 330.61376a107.52 107.52 0 0 1 36.34688 36.34176c30.73536 50.80576 14.464 116.9152-36.34688 147.65056l-546.5088 330.61376A76.8 76.8 0 0 1 250.88 945.69984c-42.41408 0-76.8-34.3808-76.8-76.8z"
          fill="#0071b0"
          p-id="1197"
        ></path>
      </svg>
      <div>观看</div>
    </div>
    <div v-if="show" class="mask z-40" @click="close"></div>
    <div
      v-if="show"
      class="fixed top-24 left-0 bottom-0 right-0 m-auto z-10 w-11/12 lg:w-2/5 z-50"
    >
      <video 
        ref="videoPlayer" 
        class="w-full" 
        controls 
        autoplay
      >
        <source :src="videoSrc" type="video/mp4">
        您的浏览器不支持视频播放
      </video>
      <div class="flex justify-center">
        <div 
          class="w-10 h-10 text-white flex justify-center items-center mt-2 rounded-full cursor-pointer" 
          style="background-color: #3b3b3b" 
          @click="close"
        >
          x
        </div>
      </div>
    </div>
  </main>
</template>

<script setup>
const show = ref(false)
const videoPlayer = ref(null)
// 使用源项目中的视频URL
const videoSrc = 'https://images-1305444037.cos.ap-nanjing.myqcloud.com/video/%E5%8A%A0%E6%B2%B9%E8%A7%86%E9%A2%91.mp4'

function open() {
  show.value = true
}

function close() {
  show.value = false
  if (videoPlayer.value) {
    videoPlayer.value.pause()
  }
}
</script>

<style scoped>
.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
}

.player {
  background-image: url('https://images-1305444037.cos.ap-nanjing.myqcloud.com/player/online_watch.png');
  background-repeat: no-repeat;
  background-size: cover;
}
</style> 