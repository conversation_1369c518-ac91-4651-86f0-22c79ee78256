<template>
  <section class="section">
    <LayoutContainer>
      <div v-if="title || subtitle" class="section-title">
        <h2 v-if="title" class="text-8 font-bold text-main-color mb-4">{{ title }}</h2>
        <p v-if="subtitle" class="text-gray-600 max-w-700px mx-auto">{{ subtitle }}</p>
      </div>
      <slot></slot>
    </LayoutContainer>
  </section>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default: ''
  },
  subtitle: {
    type: String,
    default: ''
  }
})
</script>

<style scoped>
.section {
  padding: 4rem 0;
}

.section-title {
  text-align: center;
  margin-bottom: 3rem;
}

@media (max-width: 991px) {
  .section {
    padding: 3rem 0;
  }
  
  .section-title h2 {
    font-size: 1.75rem;
  }
}

@media (max-width: 768px) {
  .section {
    padding: 2.5rem 0;
  }
  
  .section-title {
    margin-bottom: 2rem;
  }
  
  .section-title h2 {
    font-size: 1.5rem;
  }
}
</style> 