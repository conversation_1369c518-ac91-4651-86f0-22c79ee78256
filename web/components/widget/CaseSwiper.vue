<template>
  <div class="swiper-container">
    <ClientOnly>
      <swiper-container
        v-if="dataSource && dataSource.length > 0"
        ref="containerRef"
        class="case-swiper"
        :init="false"
        :loop="true"
        :slides-per-view="slidesPerView"
        :space-between="20"
        :centered-slides="false"
        :autoplay="{
          delay: 3000,
          disableOnInteraction: false
        }"
      >
        <swiper-slide 
          v-for="(item, index) in dataSource" 
          :key="index"
          class="flex justify-center bg-white"
        >
          <img 
            :src="item.url" 
            class="w-full h-auto object-contain py-4" 
            style="max-height: 200px;"
            alt="案例图片" 
          />
        </swiper-slide>
      </swiper-container>
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
import { useSwiper } from '#imports';
import { computed, nextTick, onMounted, ref } from 'vue';

// 定义props
const props = defineProps<{
  dataSource: Array<{ url: string }>
}>()

const containerRef = ref<any>(null)

// 根据屏幕宽度计算显示数量
const slidesPerView = computed(() => {
  if (typeof window === 'undefined') return 4 // SSR默认值
  const width = window.innerWidth
  if (width < 640) return 1
  if (width < 768) return 2
  if (width < 1024) return 3
  return 4
})

// 使用 useSwiper composable 来配置和控制 swiper
const swiper = useSwiper(containerRef)

// 初始化 Swiper
const initSwiper = async () => {
  if (!containerRef.value) return
  
  try {
    // @ts-ignore
    await containerRef.value.initialize()
    console.log('Swiper 初始化成功')
  } catch (error) {
    console.error('Swiper 初始化失败:', error)
  }
}

// 导出方法供父组件调用
defineExpose({
  prev: async () => {
    console.log('prev clicked')
    try {
      if (!containerRef.value) {
        console.warn('containerRef 未初始化')
        return
      }
      // @ts-ignore
      await containerRef.value.swiper.slidePrev()
      console.log('上一张切换成功')
    } catch (error) {
      console.error('切换上一张失败:', error)
    }
  },
  next: async () => {
    console.log('next clicked')
    try {
      if (!containerRef.value) {
        console.warn('containerRef 未初始化')
        return
      }
      // @ts-ignore
      await containerRef.value.swiper.slideNext()
      console.log('下一张切换成功')
    } catch (error) {
      console.error('切换下一张失败:', error)
    }
  }
})

onMounted(async () => {
  console.log('组件挂载完成')
  await nextTick()
  await initSwiper()
  
  // 监听窗口大小变化
  window.addEventListener('resize', async () => {
    if (!containerRef.value) return
    // @ts-ignore
    if (containerRef.value.swiper) {
      // @ts-ignore
      containerRef.value.swiper.params.slidesPerView = slidesPerView.value
      // @ts-ignore
      containerRef.value.swiper.update()
      console.log('更新 slidesPerView:', slidesPerView.value)
    }
  })
})
</script>

<style lang="scss" scoped>
.swiper-container {
  width: 100%;
  margin: 0;
  padding: 20px 30px;
  position: relative;
}

.case-swiper {
  width: 100%;
  height: 100%;
}

swiper-slide {
  display: flex;
  justify-content: center;
  align-items: center;
  background: white;
  height: auto;
  transition: transform 0.3s ease;
  
  img {
    width: 100%;
    height: auto;
    max-height: 200px;
    object-fit: contain;
  }
}

/* 隐藏默认导航按钮 */
:deep(.swiper-button-prev),
:deep(.swiper-button-next) {
  display: none;
}
</style> 