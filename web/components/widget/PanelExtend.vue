<template>
  <div class="recruitment-card w-full bg-white rounded-lg shadow-lg overflow-hidden mb-6 border border-gray-200 hover:shadow-xl transition-all duration-300">
    <!-- 卡片头部 -->
    <div
      class="recruitment-header w-full p-6 cursor-pointer bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 transition-all duration-300"
      @click="toggle"
    >
      <div class="flex items-center justify-between">
        <div class="flex-1">
          <div class="flex items-center space-x-4 mb-2">
            <h3 class="text-xl font-bold text-gray-800">{{ name }}</h3>
            <span v-if="position" class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full font-medium">
              {{ position }}
            </span>
          </div>
          <div class="flex items-center space-x-6 text-gray-600">
            <div class="flex items-center space-x-1">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              <span>{{ location }}</span>
            </div>
            <div v-if="department" class="flex items-center space-x-1">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
              </svg>
              <span>{{ department }}</span>
            </div>
            <div v-if="time" class="flex items-center space-x-1">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span>{{ formatTime(time) }}</span>
            </div>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-500">{{ isOpen ? '收起' : '查看详情' }}</span>
          <svg
            class="w-5 h-5 text-gray-400 transition-transform duration-300"
            :class="{ 'transform rotate-180': isOpen }"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </div>
      </div>
    </div>

    <!-- 展开内容 -->
    <div
      class="recruitment-content overflow-hidden transition-all duration-300"
      :class="isOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'"
    >
      <div class="p-6 border-t border-gray-100">
        <!-- 职位描述 -->
        <div v-if="description" class="mb-6">
          <h4 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            职位描述
          </h4>
          <div class="text-gray-700 leading-relaxed whitespace-pre-line">{{ description }}</div>
        </div>

        <!-- 岗位职责 -->
        <div v-if="content" class="mb-6">
          <h4 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
            <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
            </svg>
            岗位职责
          </h4>
          <div class="text-gray-700 leading-relaxed whitespace-pre-line">{{ content }}</div>
        </div>

        <!-- 任职要求 -->
        <div v-if="requirement" class="mb-6">
          <h4 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
            <svg class="w-5 h-5 mr-2 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
            </svg>
            任职要求
          </h4>
          <div class="text-gray-700 leading-relaxed whitespace-pre-line">{{ requirement }}</div>
        </div>

        <!-- 联系方式 -->
        <div class="bg-gray-50 rounded-lg p-4 mt-6">
          <h4 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
            <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            联系我们
          </h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
              </svg>
              <span>电话：13813194120</span>
            </div>
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              <span>邮箱：<EMAIL></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 定义属性
const props = defineProps({
  name: {
    type: String,
    required: true
  },
  location: {
    type: String,
    default: ''
  },
  time: {
    type: String,
    default: ''
  },
  content: {
    type: String,
    required: true
  },
  position: {
    type: String,
    default: ''
  },
  department: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  requirement: {
    type: String,
    default: ''
  }
})

// 是否展开
const isOpen = ref(false)

// 切换展开状态
const toggle = () => {
  isOpen.value = !isOpen.value
}

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return ''
  try {
    const date = new Date(timeStr)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    return timeStr
  }
}
</script>

<style scoped>
.recruitment-card {
  transition: all 0.3s ease;
}

.recruitment-card:hover {
  transform: translateY(-2px);
}

.recruitment-header {
  transition: all 0.3s ease;
}

.recruitment-content {
  transition: max-height 0.3s ease, opacity 0.3s ease;
}

/* 确保内容在收起时完全隐藏 */
.recruitment-content.max-h-0 {
  padding-top: 0;
  padding-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .recruitment-header .flex {
    flex-direction: column;
    align-items: flex-start;
  }

  .recruitment-header .flex > div:first-child {
    margin-bottom: 1rem;
  }

  .recruitment-header .flex > div:last-child {
    align-self: flex-end;
  }
}
</style>