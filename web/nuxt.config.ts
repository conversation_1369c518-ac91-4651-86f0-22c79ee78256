// https://nuxt.com/docs/api/configuration/nuxt-config
import { defineNuxtConfig } from 'nuxt/config'

export default defineNuxtConfig({
  // 设置兼容性日期，用于确保不同版本的 Nuxt 行为一致
  compatibilityDate: '2024-11-01',
  // 启用开发工具，便于调试
  devtools: { enabled: true },
  
  // 注册 Nuxt 模块
  modules: [
    '@unocss/nuxt',      // UnoCSS 原子化 CSS 框架
    'nuxt-swiper',       // Swiper 轮播图组件
    '@pinia/nuxt',       // Pinia 状态管理
    '@element-plus/nuxt' // Element Plus UI 组件库
  ],

  // UnoCSS 配置
  unocss: {
    preflight: true,     // 启用预设样式重置
    shortcuts: [],       // 自定义快捷方式
    rules: [],          // 自定义规则
  },

  // 运行时配置
  runtimeConfig: {
    // 私有配置(仅在服务器端可用)
    apiSecret: process.env.API_SECRET,
    
    // 公共配置(客户端和服务器端都可用)
    public: {
      // 使用环境变量配置API地址
      apiBase: process.env.NUXT_PUBLIC_API_BASE || 'http://localhost:3001'
    }
  },

  // 开发服务器配置
  devServer: {
    port: 4000,         // 开发服务器端口
    host: 'localhost'   // 只监听本地
  },

  // 生产服务器配置 (Nitro)
  // 端口和主机通过环境变量配置：
  // NUXT_HOST=0.0.0.0 NUXT_PORT=3000 (在 Docker 中设置)
  // 或者使用 HOST=0.0.0.0 PORT=3000

  // Swiper 轮播图配置
  swiper: {
    enableComposables: true,  // 启用组合式 API
    bundled: true,           // 使用捆绑版本
  },

  // Element Plus 配置
  elementPlus: {
    importStyle: 'css',      // 导入 CSS 样式
    themes: ['dark'],        // 启用暗色主题
    components: [            // 按需导入的组件
      'ElButton', 
      'ElInput', 
      'ElIcon', 
      'ElPagination'
    ]
  },

  // 全局 CSS 配置
  css: [
    '@unocss/reset/tailwind.css',  // UnoCSS 的 Tailwind 重置样式
  ],

  // 应用全局配置
  app: {
    baseURL: '/',            // 应用基础 URL
    buildAssetsDir: '/_nuxt/', // 构建资源目录
    cdnURL: '',              // CDN URL（为空表示不使用 CDN）
    // CSP配置移至Caddy层面统一管理，避免冲突
  },

  // 构建配置
  build: {
    transpile: ['vue-baidu-map-3x']  // 需要转译的依赖包
  },

  // Vue 编译器配置
  vue: {
    compilerOptions: {
      // 自定义元素配置，用于百度地图组件
      isCustomElement: (tag) => [
        'baidu-map',
        'bm-marker',
        'bm-info-window',
        'bm-navigation',
        'bm-scale'
      ].includes(tag)
    }
  }
})