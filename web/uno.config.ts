import transformerDirectives from '@unocss/transformer-directives'
import { defineConfig, presetAttributify, presetIcons, presetUno } from 'unocss'

export default defineConfig({
  transformers: [
    transformerDirectives(),
  ],
  presets: [
    presetUno(),
    presetAttributify(),
    presetIcons(),
  ],
  theme: {
    colors: {
      'main-color': '#0071b0',
      'main1-color': '#47b4dc',
    },
    width: {
      'main-width': '1200px',
    },
    fontSize: {
      'xs': '0.75rem',
      'sm': '0.875rem',
      'base': '1rem',
      'lg': '1.125rem',
      'xl': '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
      '5xl': '3rem',
      '6xl': '3.75rem',
      '7xl': '4.5rem',
      '8xl': '6rem',
      '9xl': '8rem',
      // 数字简写
      '3': '0.75rem',    // xs
      '3.5': '0.875rem', // sm
      '4': '1rem',       // base
      '4.5': '1.125rem', // lg
      '5': '1.25rem',    // xl
      '6': '1.5rem',     // 2xl
      '7': '1.75rem',    // 介于 2xl 和 3xl 之间
      '8': '2rem',       // 接近 3xl
      '9': '2.25rem'     // 4xl
    },
    boxShadow: {
      'sm': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      'DEFAULT': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
      'md': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      'lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      'xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
      '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
      'inner': 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
      'none': 'none'
    }
  },
  shortcuts: {
    // 定义常用的样式组合
    'flex-center': 'flex justify-center items-center',
    'flex-between': 'flex justify-between items-center',
    'container-width': 'max-w-main-width mx-auto px-4',
    'btn': 'px-6 py-3 rounded-md transition-all duration-300 font-medium',
    'btn-primary': 'bg-main-color hover:bg-opacity-90 text-white',
    'btn-secondary': 'bg-main1-color hover:bg-opacity-90 text-white',
    'btn-outline': 'border border-main-color text-main-color hover:bg-main-color hover:text-white',
    'card': 'bg-white rounded-lg shadow-md overflow-hidden transition-shadow duration-300 hover:shadow-lg',
    'section-padding': 'py-16',
    'title-lg': 'text-8 font-bold text-main-color',
    'title-md': 'text-6 font-bold text-main-color',
    'title-sm': 'text-5 font-bold text-main-color'
  }
})