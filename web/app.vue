<template>
  <div class="app-container">
    <BaseSearch :show="searchShow" @close="closeSearch" />
    <BaseHeader @openSearch="openSearch" />

    <main class="main-content">
      <!-- <NuxtLoadingIndicator color="#309cdb"/> -->
      <NuxtPage />
    </main>

    <BaseFooter />
  </div>
</template>

<script setup>
import { ref } from 'vue';

const searchShow = ref(false);

const openSearch = () => {
  searchShow.value = true;
};

const closeSearch = () => {
  searchShow.value = false;
};

// 使用 useHead 配置元标签
useHead({
  title: '江苏蔚之领域智能科技有限公司',
  htmlAttrs: {
    lang: 'zh-CN'
  },
  meta: [
    { charset: 'utf-8' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { name: 'keywords', content: '汽车设计, 汽车造型, 汽车设计公司, 整车设计' },
    { 
      name: 'description', 
      content: '蔚之领域是一家成立于2021年，集设计、开发、测试、设备和零部件服务为一体的动力储能系统领域的咨询公司。' 
    },
    { name: 'format-detection', content: 'telephone=021-66983689' }
  ],
  link: [
    { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
  ],
  script: [
  ]
})
</script>

<style>
/* 确保 html 和 body 占满全屏 */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
}

/* 应用容器使用 flexbox 布局 */
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 主内容区域自动扩展，占据剩余空间 */
.main-content {
  flex: 1;
}

/* 确保页面内容有最小高度 */
#__nuxt {
  min-height: 100vh;
}
</style>
