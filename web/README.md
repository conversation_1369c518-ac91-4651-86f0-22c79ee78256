# 蔚之领域官网项目

## 项目简介

这是一个基于 Nuxt 3 框架开发的现代化企业官网项目，采用 TypeScript 开发，使用 Prisma 作为 ORM 工具，集成了多个现代化的前端技术栈。项目旨在提供一个功能完整、性能优异、用户体验良好的企业官网解决方案。

## 技术栈

### 核心框架
- Nuxt 3
- Vue 3
- TypeScript
- Prisma (ORM)
- MySQL (数据库)

### UI 框架和样式
- Element Plus (UI 组件库)
- UnoCSS (原子化 CSS 框架)
- Swiper (轮播组件)

### 状态管理
- Pinia
- Pinia 持久化插件

### 地图服务
- 百度地图 (vue-baidu-map-3x)

## 项目结构

```
├── components/     # 组件目录
├── pages/         # 页面路由
├── layouts/       # 布局组件
├── composables/   # 组合式函数
├── stores/        # Pinia 状态管理
├── server/        # 服务器端代码
├── prisma/        # 数据库模型和迁移
├── public/        # 静态资源
├── plugins/       # 插件目录
├── utils/         # 工具函数
└── types/         # TypeScript 类型定义
```

## 功能特性

- 响应式布局设计
- 新闻资讯展示
- 产品展示
- 服务介绍
- 项目案例展示
- 招聘信息发布
- 合作伙伴展示
- 友情链接管理

## 快速开始

### 环境要求
- Node.js >= 16.0.0
- pnpm >= 8.0.0
- MySQL >= 8.0
- Docker (可选)

### 安装依赖
```bash
# npm
npm install

# pnpm
pnpm install

# yarn
yarn install

# bun
bun install
```

### 开发服务器
启动开发服务器在 `http://localhost:3000`:

```bash
# npm
npm run dev

# pnpm
pnpm dev

# yarn
yarn dev

# bun
bun run dev
```

### 生产环境构建
```bash
# npm
npm run build

# pnpm
pnpm build

# yarn
yarn build

# bun
bun run build
```

### 预览生产构建
```bash
# npm
npm run preview

# pnpm
pnpm preview

# yarn
yarn preview

# bun
bun run preview
```

## 数据库操作

```bash
# 生成 Prisma 客户端
pnpm prisma:generate

# 数据库迁移
pnpm prisma:deploy

# 数据库同步
pnpm db:sync

# 启动 Prisma Studio
pnpm studio
```

## 文档

详细文档请查看项目文档中心：

- [项目文档中心](../docs/readme.md) - 完整的项目文档
- [项目概述](../docs/01-project-overview.md) - 项目简介和架构
- [快速开始](../docs/02-quick-start.md) - 快速上手指南
- [开发环境配置](../docs/development/01-environment-setup.md) - 开发环境搭建
- [API 文档](../docs/api/01-api-overview.md) - API接口文档

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

[MIT](./LICENSE)
