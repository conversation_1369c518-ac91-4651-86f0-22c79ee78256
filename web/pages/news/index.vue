<template>
  <div class="w-full bg-gray-50 py-12">
    <div class="container mx-auto px-4">
      <div class="mb-8 text-center">
        <h1 class="text-3xl font-bold text-main-color mb-2">新闻资讯</h1>
        <p class="text-gray-500">了解我们的最新动态和行业资讯</p>
      </div>

      <div v-if="news.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div v-for="item in news" :key="item.id" class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all">
          <NuxtLink v-if="item.id" :to="`/news/${item.id}`" class="block h-full">
            <div class="h-52 overflow-hidden">
              <img 
                v-if="item.image" 
                :src="item.image" 
                :alt="item.title" 
                class="w-full h-full object-cover transition-transform hover:scale-105"
              >
              <div v-else class="w-full h-full bg-gray-200 flex items-center justify-center">
                <span class="text-gray-400">暂无图片</span>
              </div>
            </div>
            <div class="p-5">
              <h3 class="font-bold text-xl text-main-color mb-3 line-clamp-2">{{ item.title }}</h3>
              <p class="text-gray-600 mb-4 line-clamp-3">{{ item.content }}</p>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-500">{{ formatDate((item as any).created_at) }}</span>
                <span class="text-main-color text-sm font-medium">阅读更多 →</span>
              </div>
            </div>
          </NuxtLink>
        </div>
      </div>

      <div v-else class="text-center py-16">
        <p class="text-gray-500">暂无新闻</p>
      </div>

      <!-- 可以在这里添加分页控件 -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { useHead } from '#imports';
import { onMounted, ref } from 'vue';
import { useNews } from '~/composables/useWebData';

const { data: newsRaw, fetchData } = useNews()
const news = ref<any[]>([])

onMounted(async () => {
  await fetchData()
  const val = newsRaw.value as any
  if (Array.isArray(val)) {
    news.value = val
  } else if (Array.isArray(val?.list)) {
    news.value = val.list
  } else {
    news.value = []
  }
})

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
}

// SEO优化
useHead({
  title: '新闻资讯 - 威仕',
  meta: [
    { name: 'description', content: '威仕最新公司动态和行业资讯' }
  ]
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 