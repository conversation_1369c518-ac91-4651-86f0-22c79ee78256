<template>
  <div class="w-full bg-gray-50 py-12">
    <div class="container mx-auto px-4">
      <!-- 返回链接 -->
      <div class="mb-6">
        <NuxtLink to="/news" class="flex items-center text-main-color hover:text-main1-color">
          <span class="mr-1">←</span> 返回新闻列表
        </NuxtLink>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="text-center py-16 bg-white rounded-lg shadow">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-gray-500">正在加载新闻详情...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="text-center py-16 bg-white rounded-lg shadow">
        <div class="text-red-500 mb-4">
          <svg class="w-16 h-16 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
        <p class="text-red-600 mb-4">{{ error }}</p>
        <button
          @click="$router.go(-1)"
          class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          返回上一页
        </button>
      </div>

      <!-- 新闻内容 -->
      <div v-else-if="newsData" class="bg-white rounded-lg shadow-lg p-6 md:p-8">
        <h1 class="text-2xl md:text-3xl font-bold text-main-color mb-4">{{ newsData.title }}</h1>

        <div class="mb-6 text-gray-500 text-sm border-b pb-4 flex items-center">
          <span>发布时间: {{ formatDate((newsData as any).created_at) }}</span>
        </div>

        <!-- 新闻图片 -->
        <div v-if="newsData.image" class="mb-6 overflow-hidden rounded-lg">
          <img
            :src="newsData.image"
            :alt="newsData.title"
            class="w-full object-cover"
            @error="handleImageError"
          />
        </div>

        <!-- 新闻内容 -->
        <div class="prose max-w-none text-gray-700 leading-relaxed">
          {{ newsData.content }}
        </div>
      </div>

      <!-- 未找到新闻 -->
      <div v-else class="text-center py-16 bg-white rounded-lg shadow">
        <p class="text-gray-500">未找到新闻内容</p>
        <NuxtLink to="/news" class="text-blue-600 hover:text-blue-800 mt-4 inline-block">
          返回新闻列表
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useHead } from '#imports';
import type { News } from '@weishi/types';
import { ref } from 'vue';
import { useApi } from '~/utils/api';

const route = useRoute();
const newsId = Number(route.params.id);
const api = useApi();

// 添加加载状态和错误状态
const loading = ref(true);
const error = ref<string | null>(null);
const newsData = ref<News | null>(null);

// 获取新闻详情
try {
  console.log('正在获取新闻详情，ID:', newsId);

  if (!newsId || isNaN(newsId)) {
    throw new Error('无效的新闻ID');
  }

  const response = await api.getNewsDetail(newsId);
  console.log('API响应:', response);

  if (response?.data) {
    // API 直接返回新闻对象
    newsData.value = response.data as News;
  } else {
    throw new Error('未获取到新闻数据');
  }

  console.log('新闻数据:', newsData.value);
} catch (err) {
  console.error('获取新闻详情失败:', err);
  error.value = err instanceof Error ? err.message : '获取新闻详情失败';

  // 如果是无效ID或找不到新闻，重定向到新闻列表页
  if (error.value.includes('无效') || error.value.includes('未找到')) {
    await navigateTo('/news');
  }
} finally {
  loading.value = false;
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
}

// 图片加载错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  if (img) {
    img.style.display = 'none';
  }
}

// SEO优化
useHead({
  title: newsData.value?.title ? `${newsData.value.title} - 威仕` : '新闻详情 - 威仕',
  meta: [
    { 
      name: 'description', 
      content: newsData.value?.content ? newsData.value.content.substring(0, 150) + '...' : '威仕新闻详情'
    }
  ]
})
</script>

<style scoped>
.prose {
  font-size: 1.1rem;
  line-height: 1.8;
}

@media (max-width: 768px) {
  .prose {
    font-size: 1rem;
    line-height: 1.7;
  }
}
</style> 