<template>
  <div class="w-full h-full">
    <!-- 页面标题区域 -->
    <div class="relative h-96 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center">
      <!-- 背景图片 -->
      <div
        class="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style="background-image: url('https://images-1305444037.cos.ap-nanjing.myqcloud.com/background/5.jpg')"
      ></div>
      <!-- 覆盖层 -->
      <div class="absolute inset-0 bg-black bg-opacity-40"></div>
      <!-- 内容 -->
      <div class="relative z-10 text-center text-white">
        <h1 class="text-4xl md:text-6xl font-bold mb-4">联系我们</h1>
        <p class="text-lg md:text-xl">Contact Us</p>
        <div class="mt-6 w-24 h-1 bg-white mx-auto rounded"></div>
      </div>
    </div>

    <main class="w-full">
      <!-- 联系信息区域 -->
      <div class="bg-gray-50 py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <!-- 标题 -->
          <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">联系方式</h2>
            <p class="text-lg text-gray-600">我们期待与您的合作</p>
            <div class="flex items-center justify-center mt-6">
              <hr class="w-24 border-gray-300" />
              <div class="mx-4 text-gray-500">●</div>
              <hr class="w-24 border-gray-300" />
            </div>
          </div>

          <!-- 联系信息卡片 -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 主要联系信息 -->
            <div class="bg-white rounded-lg shadow-lg p-8">
              <div class="flex items-center mb-6">
                <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mr-4">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900">技术联系人</h3>
              </div>

              <div class="space-y-4">
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-blue-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                  <span class="text-gray-700">联系人：<span class="font-semibold">杨再峰</span></span>
                </div>

                <div class="flex items-center">
                  <svg class="w-5 h-5 text-blue-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                  </svg>
                  <span class="text-gray-700">移动电话：<span class="font-semibold text-blue-600">13813194120</span></span>
                </div>

                <div class="flex items-center">
                  <svg class="w-5 h-5 text-blue-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                  </svg>
                  <span class="text-gray-700">电子邮箱：<span class="font-semibold text-blue-600"><EMAIL></span></span>
                </div>

                <div class="flex items-start">
                  <svg class="w-5 h-5 text-blue-500 mr-3 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                  <span class="text-gray-700">通讯地址：<span class="font-semibold">江苏省扬州市意马路8号</span></span>
                </div>

                <div class="flex items-center">
                  <svg class="w-5 h-5 text-blue-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                  </svg>
                  <span class="text-gray-700">邮政编码：<span class="font-semibold">225000</span></span>
                </div>
              </div>
            </div>

            <!-- 公司信息 -->
            <div class="bg-white rounded-lg shadow-lg p-8">
              <div class="flex items-center mb-6">
                <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mr-4">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                  </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900">公司信息</h3>
              </div>

              <div class="space-y-4">
                <div class="flex items-start">
                  <svg class="w-5 h-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                  </svg>
                  <div>
                    <p class="font-semibold text-gray-900">江苏蔚之领域智能科技有限公司</p>
                    <p class="text-gray-600 text-sm mt-1">专注于汽车动力系统测试设备研发与制造</p>
                  </div>
                </div>

                <div class="flex items-center">
                  <svg class="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span class="text-gray-700">营业时间：<span class="font-semibold">周一至周五 9:00-18:00</span></span>
                </div>

                <div class="flex items-center">
                  <svg class="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span class="text-gray-700">服务范围：<span class="font-semibold">全国</span></span>
                </div>

                <div class="bg-gray-50 rounded-lg p-4 mt-6">
                  <h4 class="font-semibold text-gray-900 mb-2">核心业务</h4>
                  <ul class="text-sm text-gray-600 space-y-1">
                    <li>• EDS测试台架设备</li>
                    <li>• 动力单元测试设备</li>
                    <li>• 燃油系统检测设备</li>
                    <li>• CAE仿真分析服务</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 地图区域 -->
      <div class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">公司位置</h2>
            <p class="text-lg text-gray-600">欢迎您的到访</p>
          </div>

          <ClientOnly>
            <div class="bg-white rounded-lg shadow-lg overflow-hidden" style="height: 500px">
              <Suspense>
                <div v-if="show" class="w-full h-full">
                  <component
                    :is="BaiduMapComponent"
                    class="map w-full h-full"
                    ak="F9b5H7wTqvcNyA9bF1fZeOwwxt6CRBjy"
                    v="3.0"
                    type="API"
                    :center="center"
                    :zoom="zoom"
                    :scroll-wheel-zoom="true"
                    @ready="mapReady"
                  >
                    <component
                      :is="BmNavigationComponent"
                      anchor="BMAP_ANCHOR_BOTTOM_RIGHT"
                    ></component>
                    <component
                      :is="BmScaleComponent"
                      anchor="BMAP_ANCHOR_BOTTOM_LEFT"
                    ></component>
                    <component :is="BmMarkerComponent" :position="center">
                      <component
                        :is="BmInfoWindowComponent"
                        :show="infoWindow.show"
                      >
                        江苏蔚之领域智能科技有限公司
                      </component>
                    </component>
                  </component>
                </div>
                <template #fallback>
                  <div class="w-full h-full flex items-center justify-center bg-gray-100">
                    <div class="text-center">
                      <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      </svg>
                      <p class="text-gray-500">加载地图中...</p>
                    </div>
                  </div>
                </template>
              </Suspense>
            </div>
          </ClientOnly>
        </div>
      </div>

      <!-- 快速联系区域 -->
      <div class="bg-blue-600 py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 class="text-3xl font-bold text-white mb-4">立即联系我们</h2>
          <p class="text-xl text-blue-100 mb-8">专业的技术团队，为您提供优质的服务</p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="tel:13813194120"
               class="inline-flex items-center px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-200">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
              </svg>
              立即致电
            </a>
            <a href="mailto:<EMAIL>"
               class="inline-flex items-center px-6 py-3 bg-transparent border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-colors duration-200">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              发送邮件
            </a>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
useHead({
  title: '联系我们 - 江苏蔚之领域智能科技有限公司',
  meta: [
    {
      name: 'description',
      content: '联系江苏蔚之领域智能科技有限公司，专业的汽车动力系统测试设备研发与制造商。联系电话：13813194120，邮箱：<EMAIL>',
    },
    {
      name: 'keywords',
      content: '联系我们,蔚之领域,汽车测试设备,动力系统,扬州,江苏',
    },
  ],
})

// @ts-ignore: vue-baidu-map-3x 类型声明问题
const BaiduMapComponent = shallowRef()
const BmNavigationComponent = shallowRef()
const BmScaleComponent = shallowRef()
const BmMarkerComponent = shallowRef()
const BmInfoWindowComponent = shallowRef()

interface BMapInstance {
  BMap: any
  map: any
}

const show = ref(false)
const center = ref({ lng: 119.516388, lat: 32.362909 }) // 119.516388,32.362909
const zoom = ref(16)
const infoWindow = ref({
  show: true,
  contents: '江苏蔚之领域智能科技有限公司'
})

const mapReady = ({ BMap, map }: BMapInstance) => {
  console.log('百度地图加载完成')
  if (map) {
    map.enableScrollWheelZoom()
  }
}

onMounted(async () => {
  if (!import.meta.server) {
    const { BaiduMap, BmNavigation, BmScale, BmMarker, BmInfoWindow } = await import('vue-baidu-map-3x')
    BaiduMapComponent.value = BaiduMap
    BmNavigationComponent.value = BmNavigation
    BmScaleComponent.value = BmScale
    BmMarkerComponent.value = BmMarker
    BmInfoWindowComponent.value = BmInfoWindow
    show.value = true
  }
})
</script>

<style lang="scss" scoped>
// 地图容器样式
.map {
  width: 100%;
  height: 100%;
  border-radius: 0.5rem;
}

// 联系信息卡片悬停效果
.contact-card {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
}

// 按钮悬停效果
.contact-button {
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
  }
}

// 响应式调整
@media (max-width: 768px) {
  .contact-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

// 标题装饰线动画
.title-decoration {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    border-radius: 2px;
  }
}
</style>