<template>
  <div>
    <h1>Health Check</h1>
    <p>Status: OK</p>
    <p>Timestamp: {{ timestamp }}</p>
  </div>
</template>

<script setup>
// 设置页面元数据
definePageMeta({
  layout: false
})

// 生成时间戳
const timestamp = new Date().toISOString()

// 设置响应头
setResponseStatus(200)
setHeader('Content-Type', 'text/html')
</script>

<style scoped>
body {
  font-family: Arial, sans-serif;
  margin: 20px;
}
</style>
