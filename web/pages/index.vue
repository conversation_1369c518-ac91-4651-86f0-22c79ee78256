<template>
  <div class="w-full">
    <div class="main-centered-content">
      <ClientOnly>
        <!-- 悬浮按钮组 -->
        <div class="fixed bottom-6 right-6 z-50 flex flex-col gap-3">
          <!-- 电话按钮 -->
          <div 
            class="floating-btn phone-btn"
            @click="makeCall"
            title="联系我们"
          >
            <el-icon size="18"><Phone /></el-icon>
          </div>
          
          <!-- 返回顶部按钮 -->
          <div 
            v-if="showTopButton"
            class="floating-btn top-btn"
            @click="scrollToTop"
            title="返回顶部"
          >
            <el-icon size="18"><ArrowUp /></el-icon>
          </div>
        </div>
      </ClientOnly>
      <Suspense>
        <template #default>
          <BaseSwiper />
        </template>
        <template #fallback>
          <div class="loading-placeholder">
            <div class="loading-spinner"></div>
          </div>
        </template>
      </Suspense>
      <LazyBaseOurServer />
      <LazyBasePlayer />
      <LazyBaseNews />
    </div>
  </div>
</template>

<script setup>
import { ArrowUp, Phone } from '@element-plus/icons-vue';
import { useWindowScroll } from '@vueuse/core';
import { onMounted, ref, watch } from 'vue';
import BaseSwiper from '~/components/base/Swiper.vue';

const showTopButton = ref(false);
let y = ref(0);

onMounted(() => {
  const { y: windowY } = useWindowScroll();
  y = windowY;
  
  watch(y, (newY) => {
    showTopButton.value = newY > 300;
  });
});

// 返回顶部
function scrollToTop() {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  });
}

// 拨打电话
function makeCall() {
  window.open('tel:13813194120');
}

// 页面元数据
useHead({
  title: '江苏蔚之领域智能科技有限公司',
  meta: [
    {
      name: 'keywords',
      content: '汽车设计, 汽车造型, 汽车设计公司, 整车设计',
    },
    {
      name: 'description',
      content:
        '蔚之领域是一家成立于2016年，集设计、开发、测试、设备和零部件服务为一体的动力储能系统领域的咨询公司。随着国六阶段排放法规的升级和新能源汽车的迅速发展，蔚之领域提供了完整了国六及其插电混合动力/增程式电动车储能系统的设计、开发、生产、零部件和整车测试的解决方案。',
    },
  ],
});
</script>

<style scoped>
.loading-placeholder {
  width: 100%;
  height: 400px;
  background: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 悬浮按钮基础样式 */
.floating-btn {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.floating-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  transition: transform 0.3s ease;
  transform: scale(0);
}

.floating-btn:hover::before {
  transform: scale(1);
}

.floating-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

/* 电话按钮样式 */
.phone-btn {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.phone-btn::before {
  background: rgba(255, 255, 255, 0.1);
}

.phone-btn:hover {
  background: linear-gradient(135deg, #45a049, #3d8b40);
}

/* 返回顶部按钮样式 */
.top-btn {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
  animation: slideInUp 0.3s ease-out;
}

.top-btn::before {
  background: rgba(255, 255, 255, 0.1);
}

.top-btn:hover {
  background: linear-gradient(135deg, #1976D2, #1565C0);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-placeholder {
    height: 300px;
  }
  
  .floating-btn {
    width: 40px;
    height: 40px;
  }
}

.main-centered-content {
  width: 80%;
  margin-left: 10%;
  margin-right: 10%;
}
@media (max-width: 1200px) {
  .main-centered-content {
    width: 90%;
    margin-left: 5%;
    margin-right: 5%;
  }
}
@media (max-width: 768px) {
  .main-centered-content {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }
}
</style> 