<template>
  <div class="w-full h-full">
    <main
      class="w-full flex flex-col items-center main_images bg-contain bg-repeat"
    >
      <img
        class="w-full h-auto"
        alt=""
        src="https://images-1305444037.cos.ap-nanjing.myqcloud.com/background/2.jpg"
      />
      <div class="xl:w-main-width w-11/12 flex justify-center mt-56 mb-20 relative">
        <div
          class="border border-solid"
          style="width: 90%; border-color: #b3b3b3"
        >
          <div class="w-full flex justify-center pb-10" style="margin-top: 220px">
            <div class="text-left xl:block hidden" style="width: 30%">
              <div class="w-full flex justify-end">
                <img
                  style="width: 90%; height: auto; margin-top: 40%"
                  alt=""
                  src="https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/design-and-development/%E6%A0%87%E9%A2%98.png"
                />
              </div>

              <div
                class="w-full text-right mt-4"
                style="font-size: 25px; font-weight: 500"
              >
                设计开发
              </div>
            </div>
            <div class="flex flex-col xl:w-8/12 w-11/12" style="">
              <ul class="h-full box-border pl-10 flex flex-col justify-between">
                <li class="relative">
                  <div
                    class="absolute w-3 h-3 border-2 border-main1-color"
                    style="left: -30px; top: 6px"
                  ></div>
                  <p class="font-semibold">
                    产品相关行业法规、国内外标准、技术路线的咨询
                  </p>
                  <p>
                    Consulting for regulation, specification, technique solution
                    related to fuel system
                  </p>
                </li>
                <li class="relative">
                  <div
                    class="absolute w-3 h-3 border-2 border-main1-color"
                    style="left: -30px; top: 6px"
                  ></div>
                  <p class="font-semibold">
                    燃油系统及相关部件概念设计、功能要求及技术规定定义
                  </p>
                  <p>
                    Concept design, function requirements, technique
                    specifications for fuel system
                  </p>
                </li>
                <li class="relative">
                  <div
                    class="absolute w-3 h-3 border-2 border-main1-color"
                    style="left: -30px; top: 6px"
                  ></div>
                  <p class="font-semibold">
                    燃油系统及相关部件的详细设计、仿真模拟以及图纸
                  </p>
                  <p>Detail design, CAE and drawing for fuel system</p>
                </li>
                <li class="relative">
                  <div
                    class="absolute w-3 h-3 border-2 border-main1-color"
                    style="left: -30px; top: 6px"
                  ></div>
                  <p class="font-semibold">
                    燃油系统和整车级别及部件设计开发体系建立和管理
                  </p>
                  <p>
                    Vehicle / component level design and development system
                    foundation and management
                  </p>
                </li>
                <li class="relative">
                  <div
                    class="absolute w-3 h-3 border-2 border-main1-color"
                    style="left: -30px; top: 6px"
                  ></div>
                  <p class="font-semibold">
                    研究性课题开发、产品成本优化设计、技术方案评审及优化、技术问题分析与解决
                  </p>
                  <p>
                    New technology research,VA/VE,technical schema review and
                    optimization,issue solution
                  </p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div
          class="absolute z-10 bg-white bg-opacity-80"
          style="top: -0.5px; width: 65%; height: 1.5px"
        ></div>
        <div
          class="absolute"
          style="z-index: 10; width: 50%; height: 360px; top: -180px"
        >
          <div class="w-full h-full relative">
            <div
              class="absolute box1_images w-full h-full"
              style="z-index: 12"
            ></div>
            <div
              class="absolute"
              style="
                z-index: 11;
                height: 98%;
                width: 98%;
                background-color: #52bde1;
                bottom: -10px;
                right: -10px;
              "
            ></div>
          </div>
        </div>
      </div>
    </main>
    <div
      class="w-full flex flex-col items-center"
      style="background-color: #0071b0"
    >
      <div class="w-full xl:w-main-width flex flex-col items-center my-10">
        <div class="font-medium text-white text-2xl">INFORMATION CENTRE</div>
        <div class="flex items-center justify-center my-4">
          <hr class="w-24" />
          <div class="mx-4 text-white">项目示例</div>
          <hr class="w-24" />
        </div>
      </div>
      <div class="w-full flex justify-center mb-16">
        <div class="w-full xl:w-main-width relative px-20">
          <div class="absolute left-0 top-1/2 transform -translate-y-1/2 z-10" style="left: -10px;">
            <el-icon style="font-size: 48px; color: white;" class="cursor-pointer hover:text-gray-200 transition-colors duration-300" @click="caseSwiper?.prev()">
              <ArrowLeft />
            </el-icon>
          </div>
          <WidgetCaseSwiper
            ref="caseSwiper"
            v-if="urls.length > 0"
            :data-source="urls"
          />
          <div class="absolute right-0 top-1/2 transform -translate-y-1/2 z-10" style="right: -10px;">
            <el-icon style="font-size: 48px; color: white;" class="cursor-pointer hover:text-gray-200 transition-colors duration-300" @click="caseSwiper?.next()">
              <ArrowRight />
            </el-icon>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import type { ProjectCase } from '@weishi/types'
import { onMounted, ref, watchEffect } from 'vue'
import WidgetCaseSwiper from '~/components/widget/CaseSwiper.vue'
import { useProjectCases } from '~/composables/useWebData'
import { useApi } from '~/utils/api'

const api = useApi()

// 获取项目案例数据
const { data: projectData, fetchData } = useProjectCases()

const urls = ref<ProjectCase[]>([])
const caseSwiper = ref<InstanceType<typeof WidgetCaseSwiper> | null>(null)

onMounted(async () => {
  await fetchData()
  console.log('design_develop.vue fetchData 后 projectData.value:', projectData.value)
})

// 页面元数据
useHead({
  title: '设计开发服务 - 江苏蔚之领域智能科技有限公司',
  meta: [
    {
      name: 'description',
      content: '蔚之领域设计开发服务，专注于燃油系统及相关部件的设计开发、技术咨询、方案优化等服务。',
    },
  ],
})

// 监听数据变化
watchEffect(() => {
  const val = projectData.value as any
  if (val) {
    if (Array.isArray(val)) {
      urls.value = val
    } else if (Array.isArray(val.list)) {
      urls.value = val.list
    } else {
      urls.value = []
    }
  }
})
</script>

<style lang="scss" scoped>
.img {
  overflow: hidden;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  background-image: url('https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/design-and-development/bg2.png');
  filter: grayscale(30%);
}
.main_images {
  background-image: url('https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/design-and-development/bg1.png');
}
.box1_images {
  background-image: url('https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/cover/1.jpg');
  background-repeat: no-repeat;
  background-position: center top;
  background-size: cover;
}
</style> 