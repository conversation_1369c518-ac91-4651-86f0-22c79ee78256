<template>
  <div class="w-full h-full">
    <main class="w-full flex flex-col items-center">
      <img
        class="w-full h-auto"
        alt=""
        src="https://images-1305444037.cos.ap-nanjing.myqcloud.com/background/4.jpg"
      />
      <div class="w-full xl:w-7/12 flex justify-between mt-20 mb-16 px-2">
        <div class="w-36 h-48 border flex flex-col items-center">
          <div class="relative" style="width: 100%; height: 40%">
            <img
              class="absolute bottom-0 z-10 left-0 right-0"
              style="margin: 0 auto; width: 30%; height: auto"
              alt=""
              src="https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/experiment-and-verification/%E4%B8%93%E4%B8%9A.png"
            />
          </div>
          <div class="w-full text-center text-2xl mt-5">专业</div>
          <div
            class="mt-5"
            style="height: 5px; width: 30%; background-color: #0066a1"
          ></div>
        </div>
        <div class="w-36 h-48 border flex flex-col items-center">
          <div class="relative" style="width: 100%; height: 40%">
            <img
              class="absolute bottom-0 z-10 left-0 right-0"
              style="margin: 0 auto; width: 30%; height: auto"
              alt=""
              src="https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/experiment-and-verification/%E4%B8%A5%E8%B0%A8.png"
            />
          </div>
          <div class="w-full text-center text-2xl mt-5">严谨</div>
          <div
            class="mt-5"
            style="height: 5px; width: 30%; background-color: #0066a1"
          ></div>
        </div>
        <div class="w-36 h-48 border flex flex-col items-center">
          <div class="relative" style="width: 100%; height: 40%">
            <img
              class="absolute bottom-0 z-10 left-0 right-0"
              style="margin: 0 auto; width: 30%; height: auto"
              alt=""
              src="https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/experiment-and-verification/%E5%85%AC%E6%AD%A3.png"
            />
          </div>
          <div class="w-full text-center text-2xl mt-5">公正</div>
          <div
            class="mt-5"
            style="height: 5px; width: 30%; background-color: #0066a1"
          ></div>
        </div>
        <div class="w-36 h-48 border flex flex-col items-center">
          <div class="relative" style="width: 100%; height: 40%">
            <img
              class="absolute bottom-0 z-10 left-0 right-0"
              style="margin: 0 auto; width: 30%; height: auto"
              alt=""
              src="https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/experiment-and-verification/%E9%AB%98%E6%95%88.png"
            />
          </div>
          <div class="w-full text-center text-2xl mt-5">高效</div>
          <div
            class="mt-5"
            style="height: 5px; width: 30%; background-color: #0066a1"
          ></div>
        </div>
      </div>
      <div class="w-full text-center mb-10 px-5">
        <p class="font-semibold text-2xl">
          专注于汽车领域整车级别、系统级别、零部件级别的试验及实施管理
        </p>
        <p>
          To build a professional, fair and efficient testing service
          organization;
        </p>
        <p>
          Focus on the automotive fuel system parts, fuel system, vehicle
          integration of all kinds of tests
        </p>
      </div>
      <div
        class="w-full xl:w-main-width grid grid-cols-1 lg:grid-cols-2 gap-8 mb-20 px-5"
      >
        <!-- 加油性能试验 -->
        <div
          class="h-64 box-border p-4 flex justify-between cursor-pointer"
          style="background-color: #f7f6f7"
          @click="toEv(0)"
        >
          <div
            class="h-full bg-center bg-no-repeat bg-cover"
            :style="{ width: '40%', backgroundImage: `url(${img1})` }"
          ></div>
          <div style="width: 56%">
            <p class="font-semibold text-2xl">加油性能试验</p>
            <p class="my-2">
              容积类加油试验、市场加油枪兼容性加油试验、角度加油试验、不同加油速度加油试验、不同温度加油试验、ORVR、加油顺畅性试验等
            </p>
            <p class="text-sm overflow-ellipsis overflow-hidden">
              PSO/Well back/Spit back during refueling I Angle refueling,
              different nozzles, nozzles insert /angle status ORVR refueling I
              Dead volume, rated capacity, expansion volume etc. I V-H-R
            </p>
          </div>
        </div>
        <!-- 六轴试验 -->
        <div
          class="h-64 box-border p-4 flex justify-between cursor-pointer"
          style="background-color: #f7f6f7"
          @click="toEv(1)"
        >
          <div
            class="h-full bg-center bg-no-repeat bg-cover"
            :style="{ width: '40%', backgroundImage: `url(${img2})` }"
          ></div>
          <div style="width: 56%">
            <p class="font-semibold text-2xl">六轴试验</p>
            <p class="my-2">
              六自由度运动仿真试验，模拟不同的路谱，美国南山路谱、黄山路谱、顾客特殊要求路谱
            </p>
            <p class="text-sm">Six-axixs slosh test</p>
          </div>
        </div>
        <!-- ORVR加油排放性能试验 -->
        <div
          class="h-64 box-border p-4 flex justify-between cursor-pointer"
          style="background-color: #f7f6f7"
          @click="toEv(2)"
        >
          <div
            class="h-full bg-center bg-no-repeat bg-cover"
            :style="{ width: '40%', backgroundImage: `url(${img3})` }"
          ></div>
          <div style="width: 56%">
            <p class="font-semibold text-2xl">ORVR加油排放性能试验</p>
            <p class="my-2">
              加油排放性能试验，台架试验通过特殊方式评价加油排放性能，SHED通过精确的数值监控进行加油排放性能测量；可满足国六VII型试验标准要求、CARB、EPA等标准要求；
            </p>
            <p class="text-sm">
              On-board refuelling vapour recovery test, to meet China 6/CARB/EPA
              etc specifications.
            </p>
          </div>
        </div>
        <!-- 蒸发污染物排放试验 -->
        <div
          class="h-64 box-border p-4 flex justify-between cursor-pointer"
          style="background-color: #f7f6f7"
          @click="toEv(3)"
        >
          <div
            class="h-full bg-center bg-no-repeat bg-cover"
            :style="{ width: '40%', backgroundImage: `url(${img4})` }"
          ></div>
          <div style="width: 56%">
            <p class="font-semibold text-2xl">蒸发污染物排放试验</p>
            <p class="my-2">
              可满足国六VII型试验标准要求、CARB、EPA等标准要求；系统级别、零部件级别(加油管、阀门、油箱、炭罐、排气管、油管、接头、橡胶管等)、座椅、内饰件等
            </p>
            <p class="text-sm">
              To meet China 6/CARB/EPA etc specifications. Vehicle,different
              functional ystem, various of sub-components including filler
              pipe,valves,carbon canister,seats,tubes,interior carpetings,etc.
            </p>
          </div>
        </div>
        <!-- 声学类试验 -->
        <div
          class="h-64 box-border p-4 flex justify-between cursor-pointer"
          style="background-color: #f7f6f7"
          @click="toEv(4)"
        >
          <div
            class="h-full bg-center bg-no-repeat bg-cover"
            :style="{ width: '40%', backgroundImage: `url(${img5})` }"
          ></div>
          <div style="width: 56%">
            <p class="font-semibold text-2xl">声学类试验</p>
            <p class="my-2">
              噪音试验，燃油系统噪声一般是指车辆启动，拐弯及刹车工况下，燃油在油箱内部晃动并撞击油箱内壁的声音车辆在刹车工况下的噪声尤为突出
            </p>
            <p class="text-sm">Slosh noise, applied for fuel system</p>
          </div>
        </div>
        <!-- 机械性能类试验 -->
        <div
          class="h-64 box-border p-4 flex justify-between cursor-pointer"
          style="background-color: #f7f6f7"
          @click="toEv(5)"
        >
          <div
            class="h-full bg-center bg-no-repeat bg-cover"
            :style="{ width: '40%', backgroundImage: `url(${img6})` }"
          ></div>
          <div style="width: 56%">
            <p class="font-semibold text-2xl">机械性能类试验</p>
            <p class="my-2">
              真空压力交变试验、晃动耐久试验、振动试验、跌落试验、爆破试验、尖锤冲击试验
            </p>
            <p class="text-sm">
              Fuel System Mechanical Performance Test,Pressure and vacuum I
              Pressure resistance I Internal pressure Soaking under stable
              temperature I Slosh test, drop test, vibration test
            </p>
          </div>
        </div>
        <!-- 耐环境类试验 -->
        <div
          class="h-64 box-border p-4 flex justify-between cursor-pointer"
          style="background-color: #f7f6f7"
          @click="toEv(6)"
        >
          <div
            class="h-full bg-center bg-no-repeat bg-cover"
            :style="{ width: '40%', backgroundImage: `url(${img7})` }"
          ></div>
          <div style="width: 56%">
            <p class="font-semibold text-2xl">耐环境类试验</p>
            <p class="my-2">
              温度交变试验、高温老化试验、温度压力交变试验、盐雾试验
            </p>
            <p class="text-sm">
              Fuel System Environment Resistance tests,Soaking under variable
              temperature I High temperature deformation I Variable temperature
              deformation Thermal cycle
            </p>
          </div>
        </div>
        <!-- 可靠性/耐久性能类试验 -->
        <div
          class="h-64 box-border p-4 flex justify-between cursor-pointer"
          style="background-color: #f7f6f7"
          @click="toEv(7)"
        >
          <div
            class="h-full bg-center bg-no-repeat bg-cover"
            :style="{ width: '40%', backgroundImage: `url(${img8})` }"
          ></div>
          <div style="width: 56%">
            <p class="font-semibold text-2xl">可靠性/耐久性能类试验</p>
            <p class="my-2">
              插枪负载(不同的加油枪类型/不同的插入拔出次数/不同的加载重量)、加油枪插入力及耐久性试验、锁盖耐久试验等
            </p>
            <p class="text-sm">
              Filling nozzle pull in and pull out test for different filling
              nozzle and different laoding force,cap durability test etc.
            </p>
            </div>
            </div>
        <!-- GWC/BWC试验 -->
        <div
          class="h-64 box-border p-4 flex justify-between cursor-pointer"
          style="background-color: #f7f6f7"
          @click="toEv(8)"
        >
          <div
            class="h-full bg-center bg-no-repeat bg-cover"
            :style="{ width: '40%', backgroundImage: `url(${img9})` }"
          ></div>
          <div style="width: 56%">
            <p class="font-semibold text-2xl">GWC/BWC试验</p>
            <p class="my-2">
              容积类加油试验、市场加油枪兼容性加油试验、角度加油试验、不同加油速度加油试验、不同温度加油试验、ORVR、加油顺畅性试验等
            </p>
            <p class="text-sm">
              PSO/Well back/Spit back during refueling I Angle refueling,
              different nozzles, nozzles insert /angle status ORVR refueling I
              Dead volume, rated capacity, expansion volume etc. I V-H-R
            </p>
          </div>
        </div>
        <!-- 整车集成类试验 -->
        <div
          class="h-64 box-border p-4 flex justify-between cursor-pointer"
          style="background-color: #f7f6f7"
          @click="toEv(9)"
        >
          <div
            class="h-full bg-center bg-no-repeat bg-cover"
            :style="{ width: '40%', backgroundImage: `url(${img10})` }"
          ></div>
          <div style="width: 56%">
            <p class="font-semibold text-2xl">整车集成类试验</p>
            <p class="my-2">
              高低温跑车试验、整车改装性能试验、不同测试工况跑车试验等
            </p>
            <p class="text-sm">
              Vehicle integration tests，Hot trip, altitude, cold trip I Off
              road, highway and city road durability I Domestic testing of the
              various high-ring, I Vehicle retrofit and professional test driver
              I Lab test for the vehicle
            </p>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
// 在 Nuxt3 中使用 definePageMeta 代替 layout 属性
definePageMeta({
  layout: "main"
});

// 使用 ref 代替 data 方法
const search_show = ref(false);

// 图片资源
const img1 = ref("https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/experiment-and-verification/IMG_20190626_173334.jpg");
const img2 = ref("https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/experiment-and-verification/IMG_20200509_131810.jpg");
const img3 = ref("https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/experiment-and-verification/8_26_1.png");
const img4 = ref("https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/experiment-and-verification/8_26_2.png");
const img5 = ref("https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/experiment-and-verification/IMG_20181107_133956%20-%20%E5%89%AF%E6%9C%AC.jpg");
const img6 = ref("https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/experiment-and-verification/8_26_3.png");
const img7 = ref("https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/experiment-and-verification/8_26_4.png");
const img8 = ref("https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/experiment-and-verification/8_26_5.png");
const img9 = ref("https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/experiment-and-verification/8_26_6.png");
const img10 = ref("https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/experiment-and-verification/9.png");

// 页面跳转方法
const router = useRouter();
const toEv = (id: number) => {
  if (!id) {
    console.warn('toEv 跳转 id 为空，已拦截');
    return;
  }
  navigateTo(`/ourserver/ev/${id}`);
};

// 添加页面元数据
useHead({
  title: '试验验证服务 - 江苏蔚之领域智能科技有限公司',
  meta: [
    {
      name: 'description',
      content: '蔚之领域试验验证服务，专注于汽车领域整车级别、系统级别、零部件级别的试验及实施管理，提供专业、严谨、公正、高效的测试服务。',
    },
  ],
});
</script>

<style lang="scss" scoped>
.img {
  overflow: hidden;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-size: cover;
  background-image: url("https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/experiment-and-verification/ban.png");
}
</style>