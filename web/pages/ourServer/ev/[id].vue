<template>
  <div class="w-full">
    <div class="bg-gray-100 py-10">
      <div class="max-w-main-width mx-auto px-4">
        <div v-if="testService" class="bg-white p-6 rounded-lg shadow-md">
          <div class="mb-6">
            <NuxtLink to="/ourServer/ev" class="text-main-color hover:text-main1-color mb-4 inline-block">
              <el-icon><ArrowLeft /></el-icon> 返回服务列表
            </NuxtLink>
            <h1 class="text-3xl font-bold text-main-color mt-4">{{ testService.title }}</h1>
          </div>
          
          <div class="mb-8">
            <img :src="testService.image" :alt="testService.title" class="w-full rounded-lg">
          </div>
          
          <div class="space-y-6">
            <section>
              <h2 class="text-xl font-bold text-main-color mb-3">服务介绍</h2>
              <p class="text-gray-700 leading-relaxed">
                {{ testService.fullDescription }}
              </p>
            </section>
            
            <section>
              <h2 class="text-xl font-bold text-main-color mb-3">测试项目</h2>
              <ul class="list-disc list-inside text-gray-700 space-y-2">
                <li v-for="(item, index) in testService.testItems" :key="index">
                  {{ item }}
                </li>
              </ul>
            </section>
            
            <section>
              <h2 class="text-xl font-bold text-main-color mb-3">设备介绍</h2>
              <div class="grid md:grid-cols-2 gap-6">
                <div v-for="(equipment, index) in testService.equipments" :key="index" class="border p-4 rounded-lg">
                  <h3 class="font-bold text-lg mb-2">{{ equipment.name }}</h3>
                  <p class="text-gray-600">{{ equipment.description }}</p>
                </div>
              </div>
            </section>
            
            <section>
              <h2 class="text-xl font-bold text-main-color mb-3">应用案例</h2>
              <div class="mb-6 relative">
                <WidgetCaseSwiper 
                  ref="caseSwiper"
                  :data-source="formattedCases" 
                />
                <button 
                  class="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-white/80 p-2 rounded-full shadow hover:bg-white"
                  @click="caseSwiper?.prev()"
                >
                  <el-icon><ArrowLeft /></el-icon>
                </button>
                <button 
                  class="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-white/80 p-2 rounded-full shadow hover:bg-white"
                  @click="caseSwiper?.next()"
                >
                  <el-icon><ArrowRight /></el-icon>
                </button>
              </div>
            </section>
            
            <div class="flex justify-between items-center mt-10 border-t pt-6">
              <NuxtLink 
                v-if="prevService && prevService.id" 
                :to="`/ourServer/ev/${prevService.id}`" 
                class="text-main-color hover:text-main1-color"
              >
                <el-icon><ArrowLeft /></el-icon> {{ prevService.title }}
              </NuxtLink>
              <div v-else></div>
              
              <NuxtLink 
                v-if="nextService && nextService.id" 
                :to="`/ourServer/ev/${nextService.id}`" 
                class="text-main-color hover:text-main1-color"
              >
                {{ nextService.title }} <el-icon><ArrowRight /></el-icon>
              </NuxtLink>
              <div v-else></div>
            </div>
            
            <div class="flex justify-center mt-10">
              <el-button type="primary" @click="navigateTo('/contact')">联系咨询</el-button>
            </div>
          </div>
        </div>
        
        <div v-else class="bg-white p-6 rounded-lg shadow-md text-center">
          <el-empty description="未找到相关服务信息" />
          <el-button type="primary" @click="navigateTo('/ourServer/ev')" class="mt-4">
            返回服务列表
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
import { computed, ref } from 'vue';
import WidgetCaseSwiper from '~/components/widget/CaseSwiper.vue';

const route = useRoute();
const id = parseInt(route.params.id);

// 测试服务数据
const testServicesData = [
  {
    id: 1,
    title: '整车测试',
    description: '提供整车性能、耐久性、可靠性等方面的测试服务，确保整车品质。',
    fullDescription: '蔚之领域的整车测试服务涵盖了整车性能、耐久性、可靠性等多个方面的测试。我们拥有专业的整车测试场地和设备，能够模拟各种道路和使用条件，全面评估整车的性能和品质。我们的测试结果精确可靠，能够帮助客户发现和解决潜在问题，提升产品质量。',
    image: 'https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/test_1.jpg',
    testItems: [
      '整车性能测试：包括加速性能、制动性能、操控性能等',
      '整车耐久性测试：模拟各种道路条件下的长期使用情况',
      '整车可靠性测试：评估整车各系统在长期使用过程中的可靠性',
      '整车舒适性测试：评估整车NVH性能和乘坐舒适性',
      '整车经济性测试：测试整车的燃油经济性和使用成本'
    ],
    equipments: [
      {
        name: '整车测试跑道',
        description: '专业的整车测试跑道，包括高速路段、坡道、颠簸路面等多种路况'
      },
      {
        name: '整车性能测试设备',
        description: '先进的整车性能测试设备，能够精确测量整车的各项性能指标'
      },
      {
        name: '耐久性测试平台',
        description: '模拟各种使用条件的耐久性测试平台，评估整车的长期耐久性'
      },
      {
        name: '舒适性测试设备',
        description: '专业的NVH测试设备，评估整车的噪音、振动和舒适性'
      }
    ],
    cases: [
      {
        title: '某新能源汽车整车测试项目',
        description: '为某新能源汽车制造商提供整车性能、耐久性和可靠性测试服务，发现并解决多个潜在问题，提升产品质量。',
        image: 'https://images-1305444037.cos.ap-nanjing.myqcloud.com/case/case1.jpg',
        link: '#'
      },
      {
        title: '某豪华品牌汽车操控性能优化项目',
        description: '通过整车操控性能测试，为某豪华品牌汽车提供操控性能优化建议，显著提升了车辆的操控性能。',
        image: 'https://images-1305444037.cos.ap-nanjing.myqcloud.com/case/case2.jpg',
        link: '#'
      }
    ]
  },
  {
    id: 2,
    title: '动力系统测试',
    description: '针对发动机、变速箱等动力系统部件进行性能、耐久性和可靠性测试。',
    fullDescription: '蔚之领域的动力系统测试服务专注于发动机、变速箱等动力系统部件的性能、耐久性和可靠性测试。我们拥有专业的动力系统测试设备和经验丰富的技术团队，能够为客户提供全面、准确的测试服务，帮助客户优化动力系统性能，提升产品质量。',
    image: 'https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/test_2.jpg',
    testItems: [
      '发动机性能测试：测试发动机功率、扭矩、油耗等性能指标',
      '发动机耐久性测试：评估发动机在长期使用过程中的耐久性',
      '变速箱性能测试：测试变速箱的换挡质量、传动效率等性能指标',
      '变速箱耐久性测试：评估变速箱在长期使用过程中的耐久性',
      '动力系统匹配测试：评估发动机与变速箱的匹配性能'
    ],
    equipments: [
      {
        name: '发动机测试台架',
        description: '先进的发动机测试台架，能够精确测量发动机的各项性能指标'
      },
      {
        name: '变速箱测试台架',
        description: '专业的变速箱测试台架，评估变速箱的性能和耐久性'
      },
      {
        name: '动力系统耐久性测试设备',
        description: '模拟各种使用条件的动力系统耐久性测试设备'
      },
      {
        name: '排放测试设备',
        description: '先进的排放测试设备，测量发动机的排放指标'
      }
    ],
    cases: [
      {
        title: '某汽车品牌发动机优化项目',
        description: '通过发动机性能测试，为某汽车品牌提供发动机优化建议，显著提升了发动机的功率和燃油经济性。',
        image: 'https://images-1305444037.cos.ap-nanjing.myqcloud.com/case/case3.jpg',
        link: '#'
      },
      {
        title: '某新能源汽车电机系统测试项目',
        description: '为某新能源汽车制造商提供电机系统性能和耐久性测试服务，确保电机系统的性能和可靠性。',
        image: 'https://images-1305444037.cos.ap-nanjing.myqcloud.com/case/case4.jpg',
        link: '#'
      }
    ]
  },
  // 其他测试服务数据...
];

// 当前服务数据
const testService = computed(() => {
  return testServicesData.find(service => service.id === id);
});

// 上一个服务
const prevService = computed(() => {
  const index = testServicesData.findIndex(service => service.id === id);
  return index > 0 ? testServicesData[index - 1] : null;
});

// 下一个服务
const nextService = computed(() => {
  const index = testServicesData.findIndex(service => service.id === id);
  return index < testServicesData.length - 1 ? testServicesData[index + 1] : null;
});

// 案例轮播图实例
const caseSwiper = ref(null)

// 格式化案例数据
const formattedCases = computed(() => {
  if (!testService.value?.cases) return []
  return testService.value.cases.map(item => ({
    url: item.image
  }))
})

// 页面元数据
useHead({
  title: testService.value 
    ? `${testService.value.title} - 江苏蔚之领域智能科技有限公司` 
    : '试验验证服务 - 江苏蔚之领域智能科技有限公司',
  meta: [
    {
      name: 'description',
      content: testService.value 
        ? testService.value.description 
        : '蔚之领域试验验证服务，为汽车行业提供全面、专业的测试和验证解决方案。',
    },
  ],
});
</script> 