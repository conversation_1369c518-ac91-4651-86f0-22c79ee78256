<script setup lang="ts">
import type { Partner } from '@weishi/types';
import { onMounted, ref } from 'vue';
import { usePartners } from '~/composables/useWebData';

// 获取数据
const { data: partnersRaw, fetchData, loading } = usePartners()
const partners = ref<Partner[]>([])
const pending = ref(true)

onMounted(async () => {
  await fetchData()
  const val = partnersRaw.value as any
  if (Array.isArray(val)) {
    partners.value = val
  } else if (Array.isArray(val?.list)) {
    partners.value = val.list
  } else {
    partners.value = []
  }
  pending.value = false
})
</script>

<template>
  <div class="w-full h-full">
    <img class="w-full h-auto" alt=""
      src="https://images-1305444037.cos.ap-nanjing.myqcloud.com/swiper/%E5%90%88%E4%BD%9C%E4%BC%99%E4%BC%B4%E9%A6%96%E5%9B%BE.png" />
    <main class="w-full flex justify-center">
      <div class="w-main-width flex justify-center">
        <div class="w-full flex flex-col items-center mt-10">
          <div id="partners" class="font-black text-4xl py-10">合作伙伴</div>
          <div v-if="pending" class="w-full flex justify-center py-10">
            <div class="text-gray-500">加载中...</div>
          </div>
          <div v-else-if="partners.length === 0" class="w-full flex justify-center py-10">
            <div class="text-gray-500">暂无数据</div>
          </div>
          <div v-else class="w-full flex flex-wrap justify-between">
            <div class="box" v-for="(partner, index) in partners" :key="index">
              <img
                class="max-h-full max-w-full w-auto h-auto object-contain"
                :src="partner.logo"
                :alt="partner.name"
                :title="partner.name"
                loading="lazy"
              />
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<style lang="scss" scoped>
.img {
  overflow: hidden;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-size: cover;
  background-image: url('https://images-1305444037.cos.ap-nanjing.myqcloud.com/background/1_2.png');
}

.box {
  width: 30%;
  height: 150px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 20px 0;
  box-sizing: border-box;
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.box:hover {
  border-color: #ddd;
  background: #f5f5f5;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .box {
    width: 45%;
    height: 120px;
  }
}

@media (max-width: 768px) {
  .box {
    width: 100%;
    height: 100px;
    margin: 10px 0;
  }
}
</style>