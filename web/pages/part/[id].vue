<template>
  <div class="w-full h-full bg-gray-50">
    <main class="w-full flex flex-col items-center">
      <!-- 顶部背景图片 -->
      <div class="w-full h-64 md:h-80 relative overflow-hidden">
        <img
          class="w-full h-full object-cover"
          alt="零件详情背景"
          src="https://images-1305444037.cos.ap-nanjing.myqcloud.com/background/3.jpg"
        />
        <div class="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
          <h1 class="text-white text-3xl md:text-5xl font-bold tracking-wide">零件详情</h1>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="w-11/12 sm:w-main-width flex justify-center mt-8 relative">
        <div class="w-full flex flex-col items-center">
          <div v-if="partData" class="w-full">
            <div class="flex flex-col lg:flex-row gap-8">
              <!-- 左侧图片区域 -->
              <div class="lg:w-2/5">
                <div class="bg-white rounded-2xl shadow-2xl overflow-hidden">
                  <div class="aspect-square bg-gradient-to-br from-gray-50 to-gray-100 p-6">
                    <img
                      :src="partData.url"
                      :alt="partData.name"
                      class="w-full h-full object-contain rounded-lg transition-transform duration-300 hover:scale-105"
                      @error="handleImageError"
                    />
                  </div>
                </div>
              </div>

              <!-- 右侧信息区域 -->
              <div class="lg:w-3/5">
                <div class="bg-white rounded-2xl shadow-2xl p-8">
                  <!-- 产品标题 -->
                  <div class="border-b border-gray-200 pb-6 mb-6">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">{{ partData.name }}</h2>
                    <div class="flex items-center text-sm text-gray-500">
                      <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                      专业零件解决方案
                    </div>
                  </div>

                  <!-- 详细信息 -->
                  <div class="space-y-8">
                    <div class="info-section">
                      <h3 class="section-title">产品描述</h3>
                      <div class="section-content">{{ partData.description }}</div>
                    </div>

                    <div class="info-section">
                      <h3 class="section-title">技术参数</h3>
                      <div class="section-content">{{ partData.parameters }}</div>
                    </div>

                    <div class="info-section">
                      <h3 class="section-title">应用范围</h3>
                      <div class="section-content">{{ partData.applications }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 扩展数据展示 -->
            <div v-if="extensions.length > 0" class="mt-8 space-y-8">
              <div v-for="extension in extensions" :key="extension.id" class="bg-white rounded-2xl shadow-lg overflow-hidden">
                <!-- 章节标题 -->
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-8 py-6 border-b border-gray-200">
                  <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ extension.title }}</h3>
                  <p v-if="extension.description" class="text-gray-600">{{ extension.description }}</p>
                </div>

                <div class="p-8">
                  <!-- 章节图片 -->
                  <div v-if="extension.image_url" class="mb-8 flex justify-center">
                    <img
                      :src="extension.image_url"
                      :alt="extension.title"
                      class="max-w-full h-auto rounded-lg shadow-md"
                      @error="handleImageError"
                    />
                  </div>

                  <!-- 表格列表 -->
                  <div v-if="extension.tables && extension.tables.length > 0" class="space-y-8">
                    <div v-for="table in extension.tables" :key="table.id" class="table-section">
                      <h4 class="text-xl font-semibold text-gray-900 mb-4">{{ table.table_name }}</h4>

                      <!-- 表格内容 -->
                      <div v-if="table.columns && table.columns.length > 0 && table.rows && table.rows.length > 0"
                           class="overflow-x-auto bg-gray-50 rounded-lg p-4">
                        <table class="w-full border-collapse bg-white rounded-lg overflow-hidden shadow-sm">
                          <thead>
                            <tr class="bg-gradient-to-r from-gray-100 to-gray-200">
                              <th
                                v-for="column in table.columns"
                                :key="column.id"
                                class="px-4 py-3 text-left text-sm font-semibold text-gray-700 border-b border-gray-300"
                              >
                                {{ column.column_label }}
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr
                              v-for="(row, index) in getTableData(table)"
                              :key="index"
                              class="hover:bg-gray-50 transition-colors duration-200"
                              :class="{ 'bg-gray-25': index % 2 === 1 }"
                            >
                              <td
                                v-for="column in table.columns"
                                :key="column.id"
                                class="px-4 py-3 text-sm text-gray-700 border-b border-gray-200"
                              >
                                {{ getRowValue(row, column.column_name) }}
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div v-else class="text-gray-500 text-center py-8 bg-gray-50 rounded-lg">
                        暂无数据
                      </div>
                    </div>
                  </div>
                  <div v-else class="text-gray-500 text-center py-8">
                    暂无表格数据
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="w-full bg-white rounded-2xl shadow-lg p-12 text-center">
            <el-empty description="未找到相关零件信息" />
          </div>
        </div>
      </div>

      <!-- 底部间距 -->
      <div class="h-16"></div>
    </main>
  </div>
</template>

<script setup lang="ts">
import type { PartPlatform, PartPlatformExtension } from '@weishi/types'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useApi } from '~/utils/api'

const route = useRoute()
const api = useApi()
const partData = ref<PartPlatform | null>(null)
const extensions = ref<PartPlatformExtension[]>([])

const getPartDetail = async () => {
  const id = Number(route.params.id)
  const { data: response } = await api.getPartPlatformDetail(id)
  if (response) {
    // 后端现在返回 { platform, extensions } 结构
    const responseData = response as any
    if (responseData.platform) {
      partData.value = responseData.platform
      extensions.value = responseData.extensions || []
    } else {
      // 兼容旧的返回格式
      partData.value = response as PartPlatform
      extensions.value = []
    }
  }
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/placeholder-part.png' // 可以设置一个默认图片
}

// 获取表格数据
const getTableData = (table: any) => {
  if (!table.rows || table.rows.length === 0) return []

  return table.rows.map((row: any) => {
    try {
      return typeof row.row_data === 'string' ? JSON.parse(row.row_data) : row.row_data
    } catch {
      return {}
    }
  })
}

// 获取行数据中指定列的值
const getRowValue = (row: any, columnName: string) => {
  return row[columnName] || '-'
}

onMounted(() => {
  getPartDetail()
})

definePageMeta({
  layout: 'main'
})
</script>

<style lang="scss" scoped>
/* 信息区域样式 */
.info-section {
  position: relative;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #f3f4f6;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    border-radius: 1px;
  }
}

.section-content {
  color: #374151;
  line-height: 1.625;
  white-space: pre-wrap;
  font-size: 15px;
  line-height: 1.7;
}

/* 图片容器动画效果 */
.aspect-square {
  position: relative;
  overflow: hidden;
}

.aspect-square::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.aspect-square:hover::before {
  transform: translateX(100%);
}

/* 响应式字体调整 */
@media (max-width: 768px) {
  .section-title {
    font-size: 1.125rem;
  }

  .section-content {
    font-size: 14px;
  }
}

/* 卡片阴影动画 */
.bg-white {
  transition: all 0.3s ease;
}

.bg-white:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 背景渐变优化 */
.bg-gray-50 {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
}

/* 扩展数据表格样式 */
.table-section {
  margin-bottom: 2rem;
}

.table-section table {
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.table-section th {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-size: 0.75rem;
}

.table-section td {
  border-right: 1px solid #e5e7eb;
  transition: background-color 0.2s ease;
}

.table-section td:last-child {
  border-right: none;
}

.table-section tr:hover td {
  background-color: #f8fafc;
}

/* 章节标题样式 */
.extension-title {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 响应式表格 */
@media (max-width: 768px) {
  .table-section {
    overflow-x: auto;
  }

  .table-section table {
    min-width: 600px;
  }

  .table-section th,
  .table-section td {
    padding: 0.5rem;
    font-size: 0.875rem;
  }
}
</style>