<template>
  <div class="w-full h-full">
    <div class="w-full relative">
      <img
        class="w-full h-auto"
        alt=""
        src="https://images-1305444037.cos.ap-nanjing.myqcloud.com/background/6.jpg"
      />
    </div>
    <main
      class="w-full flex flex-col items-center main_images bg-contain bg-repeat"
    >
      <div class="w-full mt-10">
        <p class="w-full text-center font-semibold">
          人才供求信息的收集、整理、储存、发布和咨询服务；人才推荐；人才招聘；人才派遣。
        </p>
        <p class="w-full text-center text-sm my-1">
          Collection,collation,storage,release and consultation of talent supply
          and demand information;
        </p>
        <p class="w-full text-center text-sm">
          Talent recommendation;Talent recruitment;Talent dispatch.
        </p>
        <div class="w-full text-center text-2xl my-4">
          <i
            class="el-icon-d-arrow-right transform rotate-90"
            style="color: #0c77b3"
          ></i>
        </div>
      </div>
      <div
        class="w-full xl:w-main-width flex xl:flex-row flex-col items-stretch xl:justify-between mb-10"
      >
        <div
          class="box-border px-6 w-4/5 xl:w-31% mb-5 flex flex-col"
          style="background-color: #f6f6f6"
        >
          <div
            class="mt-16 rounded-xl flex justify-center items-center"
            style="background-color: #258ca8; width: 80px; height: 80px"
          >
            <img
              style="width: 40px; height: 40px"
              alt=""
              src="https://images-1305444037.cos.ap-nanjing.myqcloud.com/icon/%E4%BA%BA%E6%89%8D.png"
            />
          </div>
          <div class="text-2xl font-bold mt-4">人才定制培养</div>
          <div class="mt-5 text-justify pb-8 flex-grow">
            根据企业特定的用人需求，制定专项人才培养计划，在一定时间内完成相关专业知识、技能的传授。可在企业内部进行亦可在本司内进行。培训方式灵活多变，可线上可线下，亦可同时进行，理论结合实践，高速高效完成人才培养。
          </div>
        </div>
        <div
          class="box-border px-6 w-4/5 xl:w-31% mb-5 flex flex-col"
          style="background-color: #f6f6f6"
        >
          <div
            class="mt-16 rounded-xl flex justify-center items-center"
            style="background-color: #5eaf9c; width: 80px; height: 80px"
          >
            <img
              style="width: 40px; height: 40px"
              alt=""
              src="https://images-1305444037.cos.ap-nanjing.myqcloud.com/icon/%E7%94%B5%E8%A7%86%E5%89%A7.png"
            />
          </div>
          <div class="text-2xl font-bold mt-4">企业专项培训</div>
          <div class="mt-5 text-justify pb-8 flex-grow">
            根据企业专项需求，制定培训内容，培训内容可以是当前行业的新技术、新标准、测试方法等，也可以是相关使用"工具"，包括3D设计软件(UG/CATRIA/AUTOCAD/)、仿真模拟软件；
          </div>
        </div>
        <div
          class="box-border px-6 w-4/5 xl:w-31% mb-5 flex flex-col"
          style="background-color: #f6f6f6"
        >
          <div
            class="mt-16 rounded-xl flex justify-center items-center"
            style="background-color: #203292; width: 80px; height: 80px"
          >
            <img
              style="width: 40px; height: 40px"
              alt=""
              src="https://images-1305444037.cos.ap-nanjing.myqcloud.com/icon/%E5%8C%85.png"
            />
          </div>
          <div class="text-2xl font-bold mt-4">人才资源储备●外派</div>
          <div class="mt-5 text-justify pb-8 flex-grow">
            行业领域内专业高效的招聘求职平台,为行业内的求职者提供大量高薪职位,在线沟通,快速反馈!为企业招聘方提供免费招人服务,优质人才,精准推荐。同时储备自己的相关人力资源，根据企业需求派遣特定人员进行上门服务。
          </div>
        </div>
      </div>
      <div class="w-full flex flex-col items-center">
        <div class="w-main-width flex flex-col items-center my-10">
          <div class="font-medium text-2xl text-center mb-2">
            <span style="color: #0066a2">PROJECT</span> EXAMPLE
          </div>
          <div class="flex items-center justify-center my-4">
            <hr class="w-24" />
            <div class="mx-4 font-bold">项目示例</div>
            <hr class="w-24" />
          </div>
        </div>
        <div class="w-full flex justify-center mb-16 project-example">
          <div class="w-main-width">
            <div class="relative px-10">
              <!-- 左导航按钮 -->
              <button 
                class="absolute left-0 top-1/2 -translate-y-1/2 z-20 bg-gray-50 w-6 h-12 flex items-center justify-center border border-gray-200 focus:outline-none hover:bg-gray-100"
                @click="prevSlide"
              >
                &lt;
              </button>
              
              <!-- Swiper组件 -->
              <ClientOnly>
                <swiper-container
                  ref="containerRef"
                  :init="false"
                  :loop="true"
                  :slides-per-view="slidesPerView"
                  :space-between="15"
                  :autoplay="{
                    delay: 3000,
                    disableOnInteraction: false
                  }"
                  class="case-swiper"
                >
                  <swiper-slide
                    v-for="(item, index) in urls"
                    :key="index"
                    class="py-2"
                  >
                    <div class="flex items-center justify-center h-64">
                      <img 
                        :src="item.url" 
                        class="h-auto max-h-64 max-w-full object-contain"
                        :alt="`项目示例${index + 1}`" 
                      />
                    </div>
                  </swiper-slide>
                </swiper-container>
              </ClientOnly>
              
              <!-- 右导航按钮 -->
              <button 
                class="absolute right-0 top-1/2 -translate-y-1/2 z-20 bg-gray-50 w-6 h-12 flex items-center justify-center border border-gray-200 focus:outline-none hover:bg-gray-100"
                @click="nextSlide"
              >
                &gt;
              </button>
            </div>
          </div>
        </div>
      </div>
      <!-- 招聘信息标题 -->
      <div class="w-main-width mx-auto mb-8">
        <div class="text-center">
          <h2 class="text-3xl font-bold text-gray-800 mb-4">招聘信息</h2>
          <p class="text-lg text-gray-600">加入蔚之领域，共创美好未来</p>
          <div class="flex items-center justify-center my-6">
            <hr class="w-24 border-gray-300" />
            <div class="mx-4 text-gray-500">●</div>
            <hr class="w-24 border-gray-300" />
          </div>
        </div>
      </div>

      <!-- 招聘信息列表 -->
      <div class="w-main-width mx-auto">
        <div v-if="recruitmentData.length === 0" class="text-center py-12">
          <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <p class="text-gray-500 text-lg">暂无招聘信息</p>
          <p class="text-gray-400 text-sm mt-2">请关注我们的最新动态</p>
        </div>

        <PanelExtend
          v-for="(v, k) in recruitmentData"
          :key="k"
          :name="v.name || '未知职位'"
          :location="v.location || '未知地点'"
          :time="v.createdAt || v.time"
          :content="v.content || '暂无描述'"
          :position="v.position || ''"
          :department="v.department || ''"
          :description="v.description || ''"
          :requirement="v.requirement || ''"
        />
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { useSwiper } from '#imports'
import type { Recruitment } from '@weishi/types'
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useApi } from '~/utils/api'

// 导入组件
import PanelExtend from '~/components/widget/PanelExtend.vue'

// Swiper 相关配置
const containerRef = ref<any>(null)
const swiper = useSwiper(containerRef)

// 根据屏幕宽度计算显示数量
const slidesPerView = computed(() => {
  if (typeof window === 'undefined') return 3 // SSR默认值
  const width = window.innerWidth
  if (width < 768) return 1
  if (width < 992) return 2
  return 3
})

// 图片数据
const urls = ref([
  {
    url: "https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/project_case/7.png",
  },
  {
    url: "https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/project_case/8.png",
  },
  {
    url: "https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/project_case/9.png",
  },
  {
    url: "https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/project_case/10.png",
  },
  {
    url: "https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/project_case/11.png",
  },
  {
    url: "https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/project_case/12.png",
  },
])

// 初始化 Swiper
const initSwiper = async () => {
  if (!containerRef.value) return
  try {
    await containerRef.value.initialize()
    console.log('Swiper 初始化成功')
  } catch (error) {
    console.error('Swiper 初始化失败:', error)
  }
}

// 上一张/下一张切换方法
const prevSlide = async () => {
  if (!containerRef.value?.swiper) {
    console.warn('Swiper 未初始化')
    return
  }
  await containerRef.value.swiper.slidePrev()
}

const nextSlide = async () => {
  if (!containerRef.value?.swiper) {
    console.warn('Swiper 未初始化')
    return
  }
  await containerRef.value.swiper.slideNext()
}

// 使用新的 API 工具获取数据
const api = useApi()
const recruitmentData = ref<Recruitment[]>([])

// 获取招聘数据
const fetchRecruitmentData = async () => {
  try {
    const response = await api.getRecruitments()

    // 处理不同的响应格式
    if (response?.data) {
      // 如果是数组格式
      if (Array.isArray(response.data)) {
        recruitmentData.value = response.data
      }
      // 如果是分页格式
      else if ((response.data as any).list && Array.isArray((response.data as any).list)) {
        recruitmentData.value = (response.data as any).list
      }
      // 如果直接是对象
      else if ((response.data as any).data && Array.isArray((response.data as any).data)) {
        recruitmentData.value = (response.data as any).data
      }
      else {
        console.warn('未知的数据格式:', response.data)
        recruitmentData.value = []
      }
    } else if ((response as any)?.list && Array.isArray((response as any).list)) {
      // 直接是分页格式
      recruitmentData.value = (response as any).list
    } else {
      console.warn('API响应格式不正确:', response)
      recruitmentData.value = []
    }
  } catch (error) {
    console.error('获取招聘数据失败:', error)
    recruitmentData.value = []
  }
}

// 监听窗口大小变化
onMounted(async () => {
  await nextTick()
  await initSwiper()

  // 获取招聘数据
  await fetchRecruitmentData()

  window.addEventListener('resize', async () => {
    if (!containerRef.value?.swiper) return
    containerRef.value.swiper.params.slidesPerView = slidesPerView.value
    containerRef.value.swiper.update()
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', () => {})
})

// 页面配置
definePageMeta({
  layout: 'main'
})

// 页面元数据
useHead({
  title: '加入我们 - 江苏蔚之领域智能科技有限公司',
  meta: [
    {
      name: 'description',
      content: '蔚之领域人才招聘与培训服务，为企业提供专业的人才解决方案。',
    },
  ],
})
</script>

<style lang="scss" scoped>
.main_images {
  background-image: url("https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/design-and-development/bg1.png");
}

.swiper-container {
  width: 100%;
  margin: 0;
  padding: 20px 30px;
  position: relative;
}

.case-swiper {
  width: 100%;
  margin: 0 auto;
  padding: 10px 0;
}

swiper-slide {
  display: flex;
  justify-content: center;
  align-items: center;
  height: auto;
}

// 自定义宽度类
.xl\:w-31\% {
  @media (min-width: 1280px) {
    width: 31%;
  }
}

// 自定义宽度类
.w-main-width {
  width: 1200px;
  max-width: 90%;
}
</style> 