import { defineStore } from 'pinia'
import type { PersistenceOptions } from 'pinia-plugin-persistedstate'
import { computed, ref } from 'vue'

export const useMainStore = defineStore('main', () => {
  const loading = ref(false)
  const error = ref<string | null>(null)

  // getters
  const getLoading = computed(() => loading.value)
  const getError = computed(() => error.value)

  // actions
  function setLoading(newLoading: boolean) {
    loading.value = newLoading
  }

  function setError(newError: string | null) {
    error.value = newError
  }

  function clearError() {
    error.value = null
  }

  return {
    // state
    loading,
    error,
    // getters
    getLoading,
    getError,
    // actions
    setLoading,
    setError,
    clearError
  }
}, {
  persist: {
    key: 'main-store',
    storage: localStorage
  } as PersistenceOptions
}) 