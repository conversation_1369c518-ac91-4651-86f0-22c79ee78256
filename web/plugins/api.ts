export default defineNuxtPlugin((nuxtApp) => {
  const config = useRuntimeConfig()
  
  // 创建自定义的 fetch 实例
  const customFetch = $fetch.create({
    baseURL: config.public.apiBase as string,
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json'
    }
  })

  // 创建请求包装器
  const api = {
    async request<T>(url: string, options: any = {}): Promise<T> {
      try {
        // 在这里可以添加 token 等认证信息
        const response = await customFetch(url, options)
        return response as T
      } catch (error: any) {
        // 处理错误响应
        if (error.response?.status === 401) {
          // 未授权，可以在这里处理重定向等
          navigateTo('/login')
        }
        throw error
      }
    }
  }

  // 提供全局的 API 实例
  nuxtApp.provide('api', api)
}) 