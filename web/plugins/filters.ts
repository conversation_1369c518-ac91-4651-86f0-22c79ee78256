import { defineNuxtPlugin } from '#app';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import relativeTime from 'dayjs/plugin/relativeTime';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

// 加载插件
dayjs.extend(timezone);
dayjs.extend(utc);
dayjs.extend(relativeTime);
// 设置语言为中文
dayjs.locale('zh-cn');
// 设置默认时区为上海
dayjs.tz.setDefault('Asia/Shanghai');

// 日期格式化函数
export const useDateFilters = () => {
  const toDate = (val: string) => {
    return dayjs.tz(val, 'Asia/Shanghai').format('YYYYMMDDHHmmss');
  }

  const toDate1 = (val: string) => {
    return dayjs.tz(val, 'Asia/Shanghai').format('YYYY年MM月DD日 HH:mm:ss');
  }

  const toYear = (val: string) => {
    return val.slice(0, 4);
  }

  const toMonth = (val: string) => {
    return val.slice(4, 6);
  }

  const toDay = (val: string) => {
    return val.slice(6, 8);
  }

  return {
    toDate,
    toDate1,
    toYear,
    toMonth,
    toDay
  }
}

// 中文日期格式化
export function formatChineseDate(date: string | Date): string {
  return dayjs(date).format('YYYY年MM月DD日');
}

// 完整中文日期时间格式化
export function formatChineseDateTime(date: string | Date): string {
  return dayjs(date).format('YYYY年MM月DD日 HH:mm:ss');
}

// 相对时间（如：3小时前）
export function fromNow(date: string | Date): string {
  return dayjs(date).fromNow();
}

// 创建插件
export default defineNuxtPlugin((nuxtApp) => {
  const filters = useDateFilters();
  
  // 提供全局的过滤器函数
  nuxtApp.provide('filters', filters);

  return {
    provide: {
      formatDate: filters.toDate,
      getYear: filters.toYear,
      getMonth: filters.toMonth,
      getDay: filters.toDay,
      formatChineseDate,
      formatChineseDateTime,
      fromNow
    }
  };
}); 