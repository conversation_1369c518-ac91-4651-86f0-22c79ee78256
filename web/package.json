{"name": "weizhi-web", "version": "1.0.0", "private": true, "type": "module", "scripts": {"build": "nuxt build", "build:prod": "nuxi build --dotenv .env.production", "dev": "nuxi dev --dotenv .env.development", "start": "node .output/server/index.mjs", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/image": "^1.10.0", "@pinia/nuxt": "^0.10.1", "@vueuse/core": "^13.0.0", "dayjs": "^1.11.10", "nuxt": "^3.17.7", "nuxt-swiper": "^2.0.0", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.2.0", "vue": "^3.5.13", "vue-baidu-map-3x": "^1.0.40", "vue-router": "^4.5.0"}, "devDependencies": {"@element-plus/icons-vue": "2.3.1", "@element-plus/nuxt": "1.0.7", "@types/node": "^22.13.11", "@types/swiper": "^6.0.0", "@unocss/core": "^66.3.3", "@unocss/nuxt": "^66.3.3", "@unocss/preset-attributify": "^66.3.3", "@unocss/preset-icons": "^66.3.3", "@unocss/preset-mini": "^66.3.3", "@unocss/preset-uno": "^66.3.3", "@unocss/preset-web-fonts": "^66.3.3", "@unocss/preset-wind": "^66.3.3", "@unocss/transformer-directives": "^66.3.3", "element-plus": "2.4.4", "sass-embedded": "^1.86.0", "ts-node": "^10.9.2", "typescript": "^5.8.2", "unocss": "^66.3.3"}, "packageManager": "pnpm@10.6.1+sha512.40ee09af407fa9fbb5fbfb8e1cb40fbb74c0af0c3e10e9224d7b53c7658528615b2c92450e74cfad91e3a2dcafe3ce4050d80bda71d757756d2ce2b66213e9a3"}