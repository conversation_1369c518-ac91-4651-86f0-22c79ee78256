import { useRuntimeConfig } from '#app'
import type {
  ApiResponse,
  FriendLink,
  News,
  NewsQueryParams,
  Partner,
  PartPlatform,
  PartPlatformQueryParams,
  ProjectCase,
  Recruitment,
  Service,
  Swiper
} from '@weishi/types'

export const useApi = () => {
  const config = useRuntimeConfig()
  const baseURL = config.public.apiBase

  const request = async (endpoint: string, options: RequestInit = {}) => {
    // 更健壮的 URL 拼接，防止 // 或 baseURL 为空
    let url = ''
    if (baseURL) {
      if (baseURL.endsWith('/') && endpoint.startsWith('/')) {
        url = baseURL + endpoint.slice(1)
      } else if (!baseURL.endsWith('/') && !endpoint.startsWith('/')) {
        url = baseURL + '/' + endpoint
      } else {
        url = baseURL + endpoint
      }
    } else {
      url = endpoint
    }
    
    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return response.json()
    } catch (error: any) {
      console.error('API Request failed:', {
        url,
        endpoint,
        baseURL,
        error: error?.message || error
      })
      throw error
    }
  }

  // 友情链接
  const getFriendLinks = async (): Promise<ApiResponse<FriendLink[]>> => {
    return request('/api/friendlinks')
  }

  // 合作伙伴
  const getPartners = async (): Promise<ApiResponse<Partner[]>> => {
    return request('/api/partners')
  }

  // 零件平台
  const getPartPlatforms = async (params?: PartPlatformQueryParams): Promise<ApiResponse<PartPlatform[]>> => {
    const queryString = params ? `?${new URLSearchParams(params as any).toString()}` : ''
    return request(`/api/part-platforms${queryString}`)
  }

  // 零件平台详情
  const getPartPlatformDetail = async (id: string | number): Promise<ApiResponse<PartPlatform>> => {
    return request(`/api/part-platforms/${id}`)
  }

  // 项目案例
  const getProjectCases = async (): Promise<ApiResponse<ProjectCase[]>> => {
    return request('/api/project-cases')
  }

  // 服务配置
  const getServices = async (): Promise<ApiResponse<Service[]>> => {
    return request('/api/services')
  }

  // 轮播图
  const getSwipers = async (): Promise<ApiResponse<Swiper[]>> => {
    return request('/api/swipers')
  }

  // 新闻列表
  const getNews = async (params?: NewsQueryParams): Promise<ApiResponse<News[]>> => {
    const queryString = params ? `?${new URLSearchParams(params as any).toString()}` : ''
    return request(`/api/news${queryString}`)
  }

  // 新闻详情
  const getNewsDetail = async (id: string | number): Promise<ApiResponse<News>> => {
    return request(`/api/news/${id}`)
  }

  // 招聘信息
  const getRecruitments = async (): Promise<ApiResponse<Recruitment[]>> => {
    return request('/api/recruitments')
  }

  // 缓存版本
  const getCacheVersion = async (): Promise<any> => {
    return request('/api/cache/version')
  }

  return {
    get: (endpoint: string, options?: RequestInit) => 
      request(endpoint, { ...options, method: 'GET' }),
    
    post: (endpoint: string, data: any, options?: RequestInit) =>
      request(endpoint, {
        ...options,
        method: 'POST',
        body: JSON.stringify(data),
      }),
    
    put: (endpoint: string, data: any, options?: RequestInit) =>
      request(endpoint, {
        ...options,
        method: 'PUT',
        body: JSON.stringify(data),
      }),
    
    delete: (endpoint: string, options?: RequestInit) =>
      request(endpoint, { ...options, method: 'DELETE' }),

    // 封装的 API 方法
    getFriendLinks,
    getPartners,
    getPartPlatforms,
    getPartPlatformDetail,
    getProjectCases,
    getServices,
    getSwipers,
    getNews,
    getNewsDetail,
    getRecruitments,
    getCacheVersion
  }
} 