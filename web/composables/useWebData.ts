import { useApi } from '~/utils/api'

/**
 * Web数据获取组合函数
 * 提供各种数据获取的响应式接口
 */

/**
 * 友情链接数据
 */
export function useFriendLinks() {
  const api = useApi()
  const data = ref([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  const fetchData = async (force = false) => {
    if (loading.value && !force) return
    
    loading.value = true
    error.value = null
    
    try {
      const response = await api.getFriendLinks()
      if (response?.data) {
        const responseData: any = response.data
        if (Array.isArray(responseData)) {
          data.value = responseData
        } else if (Array.isArray(responseData.list)) {
          data.value = responseData.list
        } else {
          data.value = []
        }
      } else {
        data.value = []
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取友情链接失败'
      data.value = []
    } finally {
      loading.value = false
    }
  }
  
  const refresh = () => fetchData(true)
  
  return {
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    fetchData,
    refresh
  }
}

/**
 * 合作伙伴数据
 */
export function usePartners() {
  const api = useApi()
  const data = ref([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  const fetchData = async (force = false) => {
    if (loading.value && !force) return
    
    loading.value = true
    error.value = null
    
    try {
      const response = await api.getPartners()
      data.value = response?.data || []
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取合作伙伴失败'
      data.value = []
    } finally {
      loading.value = false
    }
  }
  
  const refresh = () => fetchData(true)
  
  return {
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    fetchData,
    refresh
  }
}

/**
 * 轮播图数据
 */
export function useSwipers() {
  const api = useApi()
  const data = ref([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  const fetchData = async (force = false) => {
    if (loading.value && !force) return
    
    loading.value = true
    error.value = null
    
    try {
      const response = await api.getSwipers()
      if (response?.data) {
        const responseData: any = response.data
        if (Array.isArray(responseData)) {
          data.value = responseData
        } else if (Array.isArray(responseData.list)) {
          data.value = responseData.list
        } else {
          data.value = []
        }
      } else {
        data.value = []
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取轮播图失败'
      data.value = []
    } finally {
      loading.value = false
    }
  }
  
  const refresh = () => fetchData(true)
  
  return {
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    fetchData,
    refresh
  }
}

/**
 * 新闻数据
 */
export function useNews(params?: any) {
  const api = useApi()
  const data = ref([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  const fetchData = async (force = false) => {
    if (loading.value && !force) return
    
    loading.value = true
    error.value = null
    
    try {
      const response = await api.getNews(params)
      data.value = response?.data || []
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取新闻失败'
      data.value = []
    } finally {
      loading.value = false
    }
  }
  
  const refresh = () => fetchData(true)
  
  return {
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    fetchData,
    refresh
  }
}

/**
 * 服务配置数据
 */
export function useServices() {
  const api = useApi()
  const data = ref([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  const fetchData = async (force = false) => {
    if (loading.value && !force) return
    
    loading.value = true
    error.value = null
    
    try {
      const response = await api.getServices()
      if (response?.data) {
        const responseData: any = response.data
        if (Array.isArray(responseData)) {
          data.value = responseData
        } else if (Array.isArray(responseData.list)) {
          data.value = responseData.list
        } else {
          data.value = []
        }
      } else {
        data.value = []
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取服务配置失败'
      data.value = []
    } finally {
      loading.value = false
    }
  }
  
  const refresh = () => fetchData(true)
  
  return {
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    fetchData,
    refresh
  }
}

/**
 * 项目案例数据
 */
export function useProjectCases() {
  const api = useApi()
  const data = ref([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  const fetchData = async (force = false) => {
    if (loading.value && !force) return
    
    loading.value = true
    error.value = null
    
    try {
      const response = await api.getProjectCases()
      data.value = response?.data || []
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取项目案例失败'
      data.value = []
    } finally {
      loading.value = false
    }
  }
  
  const refresh = () => fetchData(true)
  
  return {
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    fetchData,
    refresh
  }
}

/**
 * 零件平台数据
 */
export function usePartPlatforms(params?: any) {
  const api = useApi()
  const data = ref([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  const fetchData = async (force = false) => {
    if (loading.value && !force) return
    
    loading.value = true
    error.value = null
    
    try {
      const response = await api.getPartPlatforms(params)
      data.value = response?.data || []
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取零件平台失败'
      data.value = []
    } finally {
      loading.value = false
    }
  }
  
  const refresh = () => fetchData(true)
  
  return {
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    fetchData,
    refresh
  }
}

/**
 * 招聘信息数据
 */
export function useRecruitments() {
  const api = useApi()
  const data = ref([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  const fetchData = async (force = false) => {
    if (loading.value && !force) return
    
    loading.value = true
    error.value = null
    
    try {
      const response = await api.getRecruitments()
      data.value = response?.data || []
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取招聘信息失败'
      data.value = []
    } finally {
      loading.value = false
    }
  }
  
  const refresh = () => fetchData(true)
  
  return {
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    fetchData,
    refresh
  }
}
