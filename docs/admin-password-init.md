# 管理员密码初始化功能

## 功能概述

为了方便部署和管理，后端服务提供了管理员密码自动初始化功能。当服务启动时，可以根据配置自动将管理员密码重置为指定的值。

## 配置说明

### 环境变量配置

在 `docker.env` 文件中配置以下环境变量：

```bash
# 管理员配置
# 🔐 管理员密码初始化配置
# - ADMIN_INIT_PASSWORD=true：服务启动时自动将管理员密码重置为 ADMIN_PASSWORD
# - ADMIN_INIT_PASSWORD=false：不自动重置密码，保持数据库中的现有密码
# ⚠️ 生产环境建议设置为 false，避免意外重置密码
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
ADMIN_INIT_PASSWORD=true
```

### 配置参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `ADMIN_USERNAME` | string | `admin` | 要初始化密码的管理员用户名 |
| `ADMIN_PASSWORD` | string | `admin123` | 要设置的新密码 |
| `ADMIN_INIT_PASSWORD` | boolean | `true` | 是否启用密码初始化功能 |

## 使用场景

### 1. 开发环境

在开发环境中，建议启用此功能以确保管理员密码始终是已知的：

```bash
ADMIN_INIT_PASSWORD=true
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
```

### 2. 测试环境

在测试环境中，可以启用此功能以便测试人员使用固定的密码：

```bash
ADMIN_INIT_PASSWORD=true
ADMIN_USERNAME=admin
ADMIN_PASSWORD=test123
```

### 3. 生产环境

在生产环境中，建议禁用此功能以避免意外重置密码：

```bash
ADMIN_INIT_PASSWORD=false
```

## 工作原理

1. **服务启动时检查**：当后端服务启动时，会检查 `ADMIN_INIT_PASSWORD` 配置
2. **查找用户**：如果启用，会根据 `ADMIN_USERNAME` 查找对应的管理员用户
3. **创建或更新**：
   - 如果用户不存在：创建新的管理员用户并分配超级管理员角色
   - 如果用户存在：更新现有用户的密码
4. **密码加密**：使用 bcrypt 算法加密密码
5. **角色分配**：新创建的用户自动分配超级管理员角色
6. **记录日志**：在控制台输出初始化结果

## 日志示例

### 创建新用户时的日志：

```
2025/07/21 16:10:31 🔧 开始初始化管理员密码...
2025/07/21 16:10:31 /app/internal/repository/admin_repository.go:55 record not found
[0.514ms] [rows:0] SELECT * FROM `admin_users` WHERE username = 'admin' ORDER BY `admin_users`.`id` LIMIT 1
2025/07/21 16:10:31 /app/internal/repository/admin_repository.go:38
[7.755ms] [rows:1] INSERT INTO `admin_users` (...) VALUES (...)
✅ 管理员 admin 创建成功并分配超级管理员角色
```

### 更新现有用户密码时的日志：

```
2025/07/21 15:57:26 🔧 开始初始化管理员密码...
✅ 管理员 admin 密码更新成功
```

### 失败时的日志：

```
2025/07/21 15:57:26 🔧 开始初始化管理员密码...
⚠️ 管理员密码初始化失败: 查找超级管理员角色失败: record not found
```

## 安全注意事项

1. **生产环境**：生产环境中应该将 `ADMIN_INIT_PASSWORD` 设置为 `false`
2. **密码强度**：确保 `ADMIN_PASSWORD` 是强密码
3. **环境变量保护**：保护好包含密码的环境变量文件
4. **定期更改**：定期手动更改管理员密码

## 故障排除

### 问题：服务启动时没有看到密码初始化日志

**可能原因**：
- `ADMIN_INIT_PASSWORD` 设置为 `false`
- 环境变量没有正确传递到容器

**解决方案**：
1. 检查 `docker.env` 文件中的配置
2. 确保使用 `--env-file=docker.env` 参数启动服务
3. 检查容器内的环境变量：`docker exec weishi-server env | grep ADMIN`

### 问题：密码初始化失败

**可能原因**：
- 指定的用户名不存在
- 数据库连接问题

**解决方案**：
1. 检查数据库中是否存在对应的管理员用户
2. 确保数据库连接正常
3. 查看详细的错误日志

## 数据库初始化脚本修改

为了配合密码初始化功能，已对数据库初始化脚本进行了以下修改：

### 移除的数据
- **admin_users 表**：移除了预设的 admin 用户数据
- **admin_user_roles 表**：移除了 admin 用户的角色关联数据
- **admin_logs 表**：移除了 admin 用户的操作日志数据

### 保留的数据
- **admin_roles 表**：保留角色定义（包括 super_admin 角色）
- **admin_permissions 表**：保留权限定义
- **admin_role_permissions 表**：保留角色权限关联

这样确保了：
1. 部署时不会导入预设的 admin 用户数据
2. 系统启动时通过密码初始化功能创建管理员用户
3. 基础的角色权限体系仍然完整

## 相关文件

- `server-go/internal/config/config.go` - 配置定义
- `server-go/internal/service/admin_service.go` - 密码初始化逻辑
- `server-go/cmd/main.go` - 启动时调用
- `server-go/scripts/init_database.sql` - 数据库初始化脚本（已修改）
- `docker.env` - 环境变量配置
- `docker-compose.yml` - Docker 服务配置
