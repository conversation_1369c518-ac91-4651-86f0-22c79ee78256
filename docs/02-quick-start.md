# 🚀 快速开始指南

本指南将帮助你快速搭建和运行蔚之领域智能科技项目。

## 📋 前置要求

### 系统要求

- **操作系统**: Windows 10+、macOS 10.15+、Ubuntu 18.04+
- **内存**: 至少 8GB RAM
- **存储**: 至少 10GB 可用空间

### 软件依赖

| 软件 | 版本要求 | 下载地址 | 说明 |
|------|----------|----------|------|
| Node.js | >= 18.0.0 | [nodejs.org](https://nodejs.org/) | JavaScript运行时 |
| pnpm | >= 8.0.0 | [pnpm.io](https://pnpm.io/) | 包管理器 |
| Go | >= 1.23.0 | [golang.org](https://golang.org/) | Go语言环境 |
| MySQL | >= 8.0.0 | [mysql.com](https://www.mysql.com/) | 数据库服务 |
| Git | >= 2.30.0 | [git-scm.com](https://git-scm.com/) | 版本控制 |

### 可选软件

| 软件 | 用途 | 下载地址 |
|------|------|----------|
| VS Code | 代码编辑器 | [code.visualstudio.com](https://code.visualstudio.com/) |
| Postman | API测试 | [postman.com](https://www.postman.com/) |

## 🔧 环境安装

### 1. 安装 Node.js 和 pnpm

```bash
# 安装 Node.js (推荐使用 nvm)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18

# 安装 pnpm
npm install -g pnpm

# 验证安装
node --version  # 应该显示 v18.x.x
pnpm --version  # 应该显示 8.x.x
```

### 2. 安装 Go

```bash
# macOS (使用 Homebrew)
brew install go

# Ubuntu
sudo apt update
sudo apt install golang-go

# 验证安装
go version  # 应该显示 go1.23.x
```

### 3. 安装 MySQL

```bash
# macOS (使用 Homebrew)
brew install mysql
brew services start mysql

# Ubuntu
sudo apt update
sudo apt install mysql-server
sudo systemctl start mysql

# 设置 root 密码
sudo mysql_secure_installation
```

## 📥 项目获取

### 克隆项目

```bash
# 克隆项目仓库
git clone <项目仓库地址>
cd Nuxt3Web

# 查看项目结构
ls -la
```

### 项目结构概览

```
Nuxt3Web/
├── docs/                   # 📚 项目文档
├── web/                    # 🌐 企业官网
├── admin/                  # 🔧 管理后台
├── server-go/              # ⚙️ 后端服务
├── packages/               # 📦 共享包
├── scripts/                # 🔨 脚本文件
├── deployment/             # 🚀 生产部署配置
└── README.md              # 📖 项目说明
```

## 🗄️ 数据库配置

### 1. 创建数据库

```bash
# 登录 MySQL
mysql -u root -p

# 创建数据库
CREATE DATABASE weizhi CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 创建用户（可选）
CREATE USER 'weizhi'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON weizhi.* TO 'weizhi'@'localhost';
FLUSH PRIVILEGES;

# 退出 MySQL
EXIT;
```

### 2. 配置数据库连接

```bash
# 进入后端目录
cd server-go

# 复制配置文件
cp config.yaml.example config.yaml

# 编辑配置文件
vim config.yaml
```

配置示例：

```yaml
database:
  host: "localhost"
  port: "3306"
  username: "root"
  password: "your_password"
  database: "weizhi"
  charset: "utf8mb4"
```

### 3. 初始化数据库

```bash
# 在 server-go 目录下执行
make setup-database

# 或者分步执行
make create-database  # 创建数据库
make init-tables     # 创建表结构
make seed-data       # 导入初始数据
```

## 📦 依赖安装

### 1. 安装前端依赖

```bash
# 在项目根目录执行
pnpm install

# 验证安装
pnpm list
```

### 2. 安装后端依赖

```bash
# 进入后端目录
cd server-go

# 下载 Go 模块
go mod download

# 验证安装
go mod verify
```

## 🚀 启动服务

### 方式一：一键启动（推荐）

```bash
# 在项目根目录执行
pnpm dev:all
```

这将同时启动：
- 后端服务 (http://localhost:3001)
- 企业官网 (http://localhost:4000)
- 管理后台 (http://localhost:3001)

### 方式二：分别启动

#### 启动后端服务

```bash
cd server-go

# 开发模式启动（支持热重载）
make dev

# 或者直接运行
go run cmd/main.go
```

#### 启动企业官网

```bash
cd web

# 开发模式启动
pnpm dev
```

#### 启动管理后台

```bash
cd admin

# 开发模式启动
pnpm dev
```

## 🌐 访问应用

### 服务地址

| 服务 | 地址 | 说明 |
|------|------|------|
| 企业官网 | http://localhost:4000 | 前台展示网站 |
| 管理后台 | http://localhost:3001 | 后台管理系统 |
| 后端API | http://localhost:3001 | API服务 |
| API文档 | http://localhost:3001/swagger/index.html | Swagger文档 |

### 默认账户

**管理后台登录**:
- 用户名: `admin`
- 密码: `123456`

## ✅ 验证安装

### 1. 检查服务状态

```bash
# 检查后端服务
curl http://localhost:3001/api/health

# 检查前端服务
curl http://localhost:4000

# 检查管理后台
curl http://localhost:3001
```

### 2. 检查数据库连接

```bash
cd server-go

# 查看服务状态
make status

# 查看日志
make logs
```

### 3. 功能测试

1. **企业官网测试**:
   - 访问 http://localhost:4000
   - 检查首页是否正常显示
   - 测试新闻列表和详情页

2. **管理后台测试**:
   - 访问 http://localhost:3001
   - 使用默认账户登录
   - 测试内容管理功能

3. **API测试**:
   - 访问 http://localhost:3001/swagger/index.html
   - 测试API接口调用

## 🐛 常见问题

### 端口冲突

```bash
# 查看端口占用
lsof -i :3001
lsof -i :4000

# 杀死占用进程
kill -9 <PID>
```

### 数据库连接失败

1. 检查MySQL服务是否启动
2. 验证数据库配置信息
3. 确认数据库用户权限

### 依赖安装失败

```bash
# 清理缓存
pnpm store prune
rm -rf node_modules
pnpm install

# Go模块问题
go clean -modcache
go mod download
```

### 权限问题

```bash
# macOS/Linux 权限问题
sudo chown -R $(whoami) ~/.pnpm-store
sudo chown -R $(whoami) node_modules
```

## 🔄 开发工作流

### 1. 日常开发

```bash
# 启动开发环境
pnpm dev:all

# 代码修改后自动重载
# 前端: 保存文件自动刷新
# 后端: Air热重载自动重启
```

### 2. 代码提交

```bash
# 代码格式化
pnpm lint

# 提交代码
git add .
git commit -m "feat: 添加新功能"
git push
```

### 3. 构建测试

```bash
# 构建所有项目
pnpm build

# 测试构建结果
pnpm preview:admin
```

## 📚 下一步

- 📖 阅读 [开发指南](./development/01-environment-setup.md)
- 🏗️ 了解 [系统架构](./architecture/01-system-architecture.md)
- 📡 查看 [API文档](./api/01-api-overview.md)
- 🚀 学习 [部署指南](./deployment/01-deployment-guide.md)

---

*如果遇到问题，请查看 [常见问题解答](./development/05-troubleshooting.md) 或提交 Issue。*
