# 📚 蔚之领域智能科技 - 项目文档中心

欢迎来到蔚之领域智能科技项目的文档中心！这里包含了项目的完整技术文档，帮助您快速了解和使用本项目。

## 🎯 文档导航

### 🚀 入门指南
- [项目概述](./getting-started/project-overview.md) - 项目简介、技术栈、架构概览
- [快速开始指南](./02-quick-start.md) - 环境搭建、项目启动、基本使用
- [环境搭建](./development/01-environment-setup.md) - 详细的开发环境配置步骤

### 💻 开发文档
- [代码规范](./development/02-coding-standards.md) - 代码风格、命名规范、最佳实践
- [开发工具](./development/03-development-tools.md) - 推荐工具、IDE配置、调试技巧
- [Makefile指南](./development/04-makefile-guide.md) - Makefile使用指南
- [更新指南](./development/05-update-guide.md) - 项目更新和维护指南
- [TSX/JSX指南](./development/06-tsx-jsx-guide.md) - TSX/JSX开发指南
- [本地开发环境配置](./development/local-development.md) - 本地开发环境详细配置

### 📡 API文档
- [API概览](./api/01-api-overview.md) - API设计原则、通用规范
- [文件上传API](./api/02-file-upload-api.md) - 文件上传功能文档
- [API状态报告](./api/03-api-status.md) - API接口测试状态

### 🗄️ 数据库文档
- [数据库概览](./database/01-database-overview.md) - 数据库架构、连接配置
- [表结构变更](./database/02-table-structure-changes.md) - 数据库变更记录

### 🚀 部署运维
- [部署指南](./deployment/README.md) - 部署文档总入口
- [Docker部署](./deployment/02-docker-deployment.md) - 容器化部署方案
- [域名配置](./deployment/03-domain-configuration.md) - 域名和SSL配置
- [COS配置](./deployment/04-cos-configuration.md) - 腾讯云COS配置
- [构建优化](./deployment/05-build-optimization.md) - 构建过程优化指南
- [端口配置](./deployment/06-port-configuration.md) - 服务端口配置说明
- [数据库初始化](./deployment/07-database-initialization.md) - 数据库初始化流程
- [完整部署指南](./deployment/08-complete-deployment-guide.md) - 详细部署步骤
- [数据库命名](./deployment/09-database-naming.md) - 数据库命名规范
- [最新部署指南](./deployment/10-latest-deployment-guide.md) - 最新的部署文档
- [管理后台密码初始化](./admin-password-init.md) - 管理后台密码初始化指南

### 🔧 CI/CD配置
- [CI/CD配置与管理](./ci-cd/README.md) - CI/CD文档总入口
- [Gitea Actions配置](./ci-cd/gitea-actions-setup.md) - 完整的CI/CD配置指南
- [Gitea Packages设置](./ci-cd/gitea-packages-setup.md) - Gitea包管理配置
- [自动部署配置](./ci-cd/gitea-auto-deploy-setup.md) - 自动化部署设置
- [构建和部署工作流](./ci-cd/build-and-deploy-workflow.md) - CI/CD工作流详解
- [并行构建优化](./ci-cd/parallel-build-optimization.md) - 构建性能优化指南

### 📊 运维管理
- [Caddy缓存配置](./administration/caddy-cache-configuration.md) - Caddy反向代理缓存配置

### 📦 历史档案
- [项目历史记录](./archives/README.md) - 项目发展历程和重要变更记录

### 📖 参考资料
- [简化配置指南](./reference/simplified-config-guide.md) - 项目配置简化指南

## 🧭 快速导航

### 按角色查找
- **👨‍💻 开发人员**: [环境搭建](./development/01-environment-setup.md) → [代码规范](./development/02-coding-standards.md) → [API文档](./api/01-api-overview.md)
- **🔧 运维人员**: [部署指南](./deployment/README.md) → [Docker部署](./deployment/02-docker-deployment.md) → [域名配置](./deployment/03-domain-configuration.md)
- **📊 项目经理**: [项目概述](./getting-started/project-overview.md) → [项目进度](./archives/project-progress.md) → [变更记录](./archives/changelog.md)

### 按任务查找
- **🏗️ 环境搭建**: [快速开始指南](./02-quick-start.md)
- **🔧 功能开发**: [开发工具](./development/03-development-tools.md) + [API概览](./api/01-api-overview.md)
- **🚀 项目部署**: [部署指南](./deployment/README.md)
- **🐛 问题排查**: [API状态报告](./api/03-api-status.md) + [更新指南](./development/05-update-guide.md)
- **⚙️ CI/CD配置**: [Gitea Actions配置](./ci-cd/gitea-actions-setup.md)

## 📋 文档使用指南

### 阅读顺序建议

#### 新手入门
1. [项目概述](./getting-started/project-overview.md) - 了解项目背景
2. [快速开始指南](./02-quick-start.md) - 搭建开发环境
3. [环境搭建](./development/01-environment-setup.md) - 详细配置步骤

#### 深入开发
1. [代码规范](./development/02-coding-standards.md) - 学习编码标准
2. [开发工具](./development/03-development-tools.md) - 掌握开发工具
3. [API概览](./api/01-api-overview.md) - 了解API设计

#### 部署上线
1. [部署指南](./deployment/README.md) - 了解部署流程
2. [Docker部署](./deployment/02-docker-deployment.md) - 容器化部署
3. [域名配置](./deployment/03-domain-configuration.md) - 域名和SSL配置

#### CI/CD配置
1. [Gitea Actions配置](./ci-cd/gitea-actions-setup.md) - 配置自动化构建
2. [构建和部署工作流](./ci-cd/build-and-deploy-workflow.md) - 理解工作流程
3. [并行构建优化](./ci-cd/parallel-build-optimization.md) - 优化构建性能

### 文档约定

- 📁 **文件命名**: 使用小写字母和连字符
- 🔗 **链接格式**: 相对路径，便于维护
- 📝 **内容结构**: 统一的标题层级和格式
- 🎨 **图标使用**: 统一的emoji图标体系

## 🔧 文档维护

### 更新频率
- **项目进度**: 每周更新
- **API文档**: 代码变更时同步更新
- **部署文档**: 环境变化时更新
- **开发文档**: 工具或流程变化时更新
- **CI/CD文档**: 配置变更时更新

### 贡献指南
1. **发现问题**: 通过GitHub Issues报告文档问题
2. **提出改进**: 提交Pull Request改进文档
3. **新增内容**: 按照现有格式添加新文档
4. **保持同步**: 确保文档与代码保持一致

### 文档规范
- 使用Markdown格式
- 遵循统一的文档结构
- 添加必要的代码示例
- 保持文档的时效性

## 🔗 外部资源

### 技术文档
- [Nuxt 3 官方文档](https://nuxt.com/)
- [Vue 3 官方文档](https://vuejs.org/)
- [Go 官方文档](https://golang.org/doc/)
- [Docker 官方文档](https://docs.docker.com/)
- [Gitea Actions 文档](https://docs.gitea.com/usage/actions/overview)

### 在线工具
- [API文档 (Swagger)](http://localhost:3000/swagger/index.html)
- [项目仓库](https://github.com/your-org/weishi-project)

## 📞 获取帮助

### 技术支持
- **邮箱**: <EMAIL>
- **文档问题**: <EMAIL>
- **GitHub Issues**: [项目Issues页面](https://github.com/your-org/weishi-project/issues)

### 联系方式
- **开发团队**: 技术相关问题
- **项目经理**: 项目进度和规划
- **运维团队**: 部署和环境问题

---

**💡 提示**: 建议将此页面加入书签，方便快速访问项目文档。

*文档持续更新中，最后更新时间: 2025-08-01*
