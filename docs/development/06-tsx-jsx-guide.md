# 🎨 TSX/JSX 开发指南

本文档介绍在Vue 3项目中使用TSX/JSX的配置、最佳实践和使用场景。

## 🎯 概述

项目已成功集成TSX/JSX支持，为Vue 3项目提供更灵活的组件开发方式。TSX/JSX特别适用于：

- **复杂逻辑渲染**: 条件渲染、循环渲染等复杂场景
- **动态组件生成**: 根据数据动态生成组件
- **函数式组件**: 无状态的纯函数组件
- **类型安全**: 更强的TypeScript类型检查

## 🔧 环境配置

### 1. 依赖安装

```bash
# Vue JSX 插件
pnpm add -D @vitejs/plugin-vue-jsx
```

### 2. Vite 配置

**文件**: `vite.config.ts`

```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'

export default defineConfig({
  plugins: [
    vue(),
    vueJsx() // 添加 JSX 支持
  ]
})
```

### 3. TypeScript 配置

**文件**: `tsconfig.json`

```json
{
  "compilerOptions": {
    "jsx": "preserve",
    "jsxImportSource": "vue",
    "types": ["vite/client"]
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue"
  ]
}
```

## 📋 使用场景

### 🟢 推荐使用 TSX/JSX 的场景

1. **复杂条件渲染**
   ```tsx
   // 多层嵌套的条件渲染
   const renderContent = () => {
     if (loading.value) return <Loading />
     if (error.value) return <ErrorMessage error={error.value} />
     if (data.value.length === 0) return <EmptyState />
     return <DataList data={data.value} />
   }
   ```

2. **动态组件生成**
   ```tsx
   // 根据配置动态生成表单字段
   const renderFormFields = () => {
     return formConfig.map(field => {
       switch (field.type) {
         case 'input': return <ElInput {...field.props} />
         case 'select': return <ElSelect {...field.props} />
         case 'date': return <ElDatePicker {...field.props} />
         default: return null
       }
     })
   }
   ```

3. **函数式组件**
   ```tsx
   // 纯函数组件，无状态
   const StatusBadge = (props: { status: string; text: string }) => {
     const statusClass = {
       success: 'bg-green-100 text-green-800',
       warning: 'bg-yellow-100 text-yellow-800',
       error: 'bg-red-100 text-red-800'
     }[props.status]
     
     return <span class={`px-2 py-1 rounded ${statusClass}`}>{props.text}</span>
   }
   ```

### 🔵 推荐使用 Vue SFC 的场景

1. **简单页面和组件**
2. **需要样式隔离** (scoped CSS)
3. **模板逻辑相对简单**
4. **团队更熟悉模板语法**

## 🌟 实际示例

### 1. 状态卡片组件

**文件**: `src/components/StatusCard.tsx`

```tsx
import { defineComponent, computed } from 'vue'
import type { PropType } from 'vue'

interface StatusCardProps {
  status: 'success' | 'warning' | 'error' | 'info'
  title: string
  description?: string
  icon?: string
  actions?: Array<{
    label: string
    type?: 'primary' | 'success' | 'warning' | 'danger'
    onClick: () => void
  }>
}

export default defineComponent({
  name: 'StatusCard',
  props: {
    status: {
      type: String as PropType<StatusCardProps['status']>,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    description: String,
    icon: String,
    actions: {
      type: Array as PropType<StatusCardProps['actions']>,
      default: () => []
    }
  },
  setup(props) {
    const statusConfig = computed(() => {
      const configs = {
        success: {
          bgClass: 'bg-green-50 border-green-500',
          iconClass: 'text-green-500',
          titleClass: 'text-green-800',
          defaultIcon: 'i-ep-success-filled'
        },
        warning: {
          bgClass: 'bg-yellow-50 border-yellow-500',
          iconClass: 'text-yellow-500',
          titleClass: 'text-yellow-800',
          defaultIcon: 'i-ep-warning-filled'
        },
        error: {
          bgClass: 'bg-red-50 border-red-500',
          iconClass: 'text-red-500',
          titleClass: 'text-red-800',
          defaultIcon: 'i-ep-error-filled'
        },
        info: {
          bgClass: 'bg-blue-50 border-blue-500',
          iconClass: 'text-blue-500',
          titleClass: 'text-blue-800',
          defaultIcon: 'i-ep-info-filled'
        }
      }
      return configs[props.status]
    })

    const renderIcon = () => {
      const iconClass = props.icon || statusConfig.value.defaultIcon
      return (
        <div class={`w-8 h-8 flex items-center justify-center ${statusConfig.value.iconClass}`}>
          <i class={`${iconClass} text-xl`} />
        </div>
      )
    }

    const renderActions = () => {
      if (!props.actions?.length) return null
      
      return (
        <div class="flex gap-2 mt-4">
          {props.actions.map((action, index) => (
            <el-button
              key={index}
              type={action.type || 'default'}
              size="small"
              onClick={action.onClick}
            >
              {action.label}
            </el-button>
          ))}
        </div>
      )
    }

    return () => (
      <div class={`p-4 border-l-4 rounded-lg ${statusConfig.value.bgClass}`}>
        <div class="flex items-start gap-3">
          {renderIcon()}
          <div class="flex-1">
            <h3 class={`font-medium ${statusConfig.value.titleClass}`}>
              {props.title}
            </h3>
            {props.description && (
              <p class="mt-1 text-sm text-gray-600">
                {props.description}
              </p>
            )}
            {renderActions()}
          </div>
        </div>
      </div>
    )
  }
})
```

### 2. 动态表单组件

**文件**: `src/components/DynamicForm.tsx`

```tsx
import { defineComponent, ref, reactive } from 'vue'
import type { PropType } from 'vue'

interface FormField {
  key: string
  label: string
  type: 'input' | 'select' | 'date' | 'textarea' | 'number'
  required?: boolean
  options?: Array<{ label: string; value: any }>
  placeholder?: string
  rules?: any[]
}

export default defineComponent({
  name: 'DynamicForm',
  props: {
    fields: {
      type: Array as PropType<FormField[]>,
      required: true
    },
    initialValues: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['submit', 'change'],
  setup(props, { emit }) {
    const formRef = ref()
    const formData = reactive({ ...props.initialValues })

    const renderField = (field: FormField) => {
      const commonProps = {
        modelValue: formData[field.key],
        'onUpdate:modelValue': (value: any) => {
          formData[field.key] = value
          emit('change', { key: field.key, value, formData })
        },
        placeholder: field.placeholder
      }

      switch (field.type) {
        case 'input':
          return <el-input {...commonProps} />
        
        case 'textarea':
          return <el-input type="textarea" rows={4} {...commonProps} />
        
        case 'number':
          return <el-input-number {...commonProps} />
        
        case 'select':
          return (
            <el-select {...commonProps}>
              {field.options?.map(option => (
                <el-option
                  key={option.value}
                  label={option.label}
                  value={option.value}
                />
              ))}
            </el-select>
          )
        
        case 'date':
          return <el-date-picker {...commonProps} />
        
        default:
          return <el-input {...commonProps} />
      }
    }

    const handleSubmit = () => {
      formRef.value?.validate((valid: boolean) => {
        if (valid) {
          emit('submit', formData)
        }
      })
    }

    return () => (
      <el-form ref={formRef} model={formData} label-width="120px">
        {props.fields.map(field => (
          <el-form-item
            key={field.key}
            label={field.label}
            prop={field.key}
            rules={field.rules}
          >
            {renderField(field)}
          </el-form-item>
        ))}
        <el-form-item>
          <el-button type="primary" onClick={handleSubmit}>
            提交
          </el-button>
        </el-form-item>
      </el-form>
    )
  }
})
```

### 3. 数据表格组件

**文件**: `src/components/DataTable.tsx`

```tsx
import { defineComponent, computed } from 'vue'
import type { PropType } from 'vue'

interface TableColumn {
  key: string
  label: string
  width?: string | number
  align?: 'left' | 'center' | 'right'
  render?: (row: any, column: TableColumn, index: number) => any
  sortable?: boolean
}

export default defineComponent({
  name: 'DataTable',
  props: {
    data: {
      type: Array as PropType<any[]>,
      default: () => []
    },
    columns: {
      type: Array as PropType<TableColumn[]>,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    pagination: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['sort-change', 'page-change'],
  setup(props, { emit }) {
    const renderCell = (row: any, column: TableColumn, index: number) => {
      if (column.render) {
        return column.render(row, column, index)
      }
      return row[column.key]
    }

    const handleSortChange = (sort: any) => {
      emit('sort-change', sort)
    }

    const handlePageChange = (page: number) => {
      emit('page-change', page)
    }

    return () => (
      <div class="data-table">
        <el-table
          data={props.data}
          loading={props.loading}
          onSort-change={handleSortChange}
        >
          {props.columns.map(column => (
            <el-table-column
              key={column.key}
              prop={column.key}
              label={column.label}
              width={column.width}
              align={column.align}
              sortable={column.sortable}
              v-slots={{
                default: ({ row, $index }: any) => renderCell(row, column, $index)
              }}
            />
          ))}
        </el-table>
        
        {props.pagination && (
          <div class="flex justify-end mt-4">
            <el-pagination
              {...props.pagination}
              onCurrent-change={handlePageChange}
            />
          </div>
        )}
      </div>
    )
  }
})
```

## 🎨 样式处理

### 1. 内联样式

```tsx
// 动态样式
const dynamicStyle = computed(() => ({
  color: props.color,
  fontSize: `${props.size}px`
}))

return () => <div style={dynamicStyle.value}>内容</div>
```

### 2. CSS类

```tsx
// 条件类名
const classNames = computed(() => [
  'base-class',
  {
    'active': props.active,
    'disabled': props.disabled
  }
])

return () => <div class={classNames.value}>内容</div>
```

### 3. UnoCSS集成

```tsx
// 使用UnoCSS原子类
return () => (
  <div class="flex items-center gap-2 p-4 bg-white rounded-lg shadow">
    <span class="text-lg font-bold text-gray-800">{props.title}</span>
  </div>
)
```

## 🔧 最佳实践

### 1. 组件命名

```tsx
// ✅ 推荐：使用 PascalCase
export default defineComponent({
  name: 'UserProfile',
  // ...
})

// ❌ 避免：使用其他命名方式
export default defineComponent({
  name: 'userProfile', // 错误
  // ...
})
```

### 2. Props 类型定义

```tsx
// ✅ 推荐：使用 TypeScript 接口
interface UserCardProps {
  user: {
    id: number
    name: string
    email: string
  }
  showActions?: boolean
}

export default defineComponent({
  props: {
    user: {
      type: Object as PropType<UserCardProps['user']>,
      required: true
    },
    showActions: {
      type: Boolean,
      default: false
    }
  }
})
```

### 3. 事件处理

```tsx
// ✅ 推荐：明确的事件类型
const handleClick = (event: MouseEvent) => {
  event.preventDefault()
  // 处理逻辑
}

return () => (
  <button onClick={handleClick}>
    点击我
  </button>
)
```

## 🐛 常见问题

### 1. 类型错误

```tsx
// ❌ 问题：类型不匹配
const MyComponent = (props: { value: string }) => {
  return <div>{props.value}</div>
}

// ✅ 解决：使用 defineComponent
export default defineComponent({
  props: {
    value: String
  },
  setup(props) {
    return () => <div>{props.value}</div>
  }
})
```

### 2. 响应式丢失

```tsx
// ❌ 问题：解构导致响应式丢失
setup(props) {
  const { value } = props
  return () => <div>{value}</div> // 不会响应变化
}

// ✅ 解决：保持响应式
setup(props) {
  return () => <div>{props.value}</div> // 保持响应式
}
```

## 📚 学习资源

- [Vue 3 JSX 官方文档](https://vuejs.org/guide/extras/render-function.html#jsx-tsx)
- [TypeScript JSX 文档](https://www.typescriptlang.org/docs/handbook/jsx.html)
- [Element Plus TSX 示例](https://element-plus.org/zh-CN/guide/dev.html#tsx)

---

*TSX/JSX为Vue项目提供了更强大的组件开发能力，适合复杂场景使用。建议根据具体需求选择合适的开发方式。*
