# 🔧 Makefile 使用指南

本文档介绍项目中 Makefile 的使用方法，特别是数据库管理相关的命令。

## 📋 概述

项目已成功将数据库管理脚本集成到 Makefile 中，提供了更便捷的数据库管理方式。现在 Makefile 实现与原脚本完全一致。

## 🗄️ 数据库管理命令

### 核心数据库设置命令

#### 1. `make setup-database` - 完整设置数据库

完整的数据库初始化流程：

```bash
cd server-go
make setup-database
```

**执行步骤**：
- 使用 `scripts/create_database.sql` 创建数据库（会删除已存在的数据库）
- 使用 Go 程序执行 GORM 自动迁移初始化表结构
- 导入管理员数据
- 导入业务数据
- 显示管理员登录信息

#### 2. `make setup-database-skip-admin` - 设置数据库但跳过管理员数据

适用于已有管理员数据的情况：

```bash
cd server-go
make setup-database-skip-admin
```

### 细分数据库操作命令

#### 3. `make create-database` - 仅创建数据库

```bash
cd server-go
make create-database
```

使用 `scripts/create_database.sql` 创建数据库。

#### 4. `make init-tables` - 仅初始化表结构

```bash
cd server-go
make init-tables
```

使用 Go 程序执行 GORM 自动迁移。

#### 5. `make drop-database` - 删除数据库

```bash
cd server-go
make drop-database
```

⚠️ **危险操作**，有确认提示。

### 数据管理命令

#### 6. `make init-admin` - 初始化管理员数据

```bash
cd server-go
make init-admin
```

创建默认管理员账户。

#### 7. `make seed-data` - 导入种子数据

```bash
cd server-go
make seed-data
```

导入业务数据（新闻、服务、案例等）。

## 🚀 开发相关命令

### 应用运行

```bash
# 开发模式运行（使用 Air 热重载）
make dev

# 生产模式运行
make run

# 构建应用
make build
```

### 代码质量

```bash
# 代码格式化
make fmt

# 代码检查
make lint

# 运行测试
make test

# 测试覆盖率
make test-coverage
```

### API 文档

```bash
# 生成 Swagger 文档
make swagger

# 清理生成的文档
make clean-docs
```

## 📊 实际使用示例

### 首次项目设置

```bash
# 1. 进入后端目录
cd server-go

# 2. 安装依赖
go mod download

# 3. 完整设置数据库
make setup-database

# 4. 启动开发服务器
make dev
```

### 重置数据库

```bash
cd server-go

# 删除现有数据库
make drop-database

# 重新创建和设置
make setup-database
```

### 仅更新表结构

```bash
cd server-go

# 仅运行数据库迁移
make init-tables
```

### 添加测试数据

```bash
cd server-go

# 导入种子数据
make seed-data
```

## 🔧 Makefile 配置详解

### 数据库配置

Makefile 中的数据库配置：

```makefile
# 数据库配置
DB_HOST ?= localhost
DB_PORT ?= 3306
DB_USER ?= root
DB_PASSWORD ?= Ydb3344%
DB_NAME ?= weizhi

# 数据库连接字符串
DB_DSN = $(DB_USER):$(DB_PASSWORD)@tcp($(DB_HOST):$(DB_PORT))/$(DB_NAME)?charset=utf8mb4&parseTime=True&loc=Local
```

### 自定义配置

可以通过环境变量覆盖默认配置：

```bash
# 使用自定义数据库配置
DB_PASSWORD=your_password make setup-database

# 使用不同的数据库名
DB_NAME=test_db make create-database
```

## 🔍 故障排除

### 常见问题

#### 1. 数据库连接失败

```bash
# 检查 MySQL 服务状态
sudo systemctl status mysql

# 检查端口是否开放
netstat -tulpn | grep 3306

# 测试数据库连接
mysql -u root -p -h localhost
```

#### 2. 权限问题

```bash
# 确保用户有足够权限
mysql -u root -p
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost';
FLUSH PRIVILEGES;
```

#### 3. 表结构迁移失败

```bash
# 查看详细错误信息
make init-tables

# 手动运行迁移程序
go run cmd/migrate/main.go
```

### 调试技巧

#### 查看 Makefile 变量

```bash
# 查看数据库配置
make show-db-config

# 查看所有可用命令
make help
```

#### 详细日志输出

```bash
# 启用详细输出
make setup-database VERBOSE=1

# 查看 SQL 执行日志
make init-tables DEBUG=1
```

## 📋 命令速查表

| 命令 | 用途 | 危险性 |
|------|------|--------|
| `make setup-database` | 完整数据库设置 | ⚠️ 中等（删除现有数据） |
| `make setup-database-skip-admin` | 设置数据库跳过管理员 | ⚠️ 中等 |
| `make create-database` | 创建数据库 | ⚠️ 中等 |
| `make init-tables` | 初始化表结构 | ✅ 安全 |
| `make drop-database` | 删除数据库 | 🚨 危险 |
| `make init-admin` | 初始化管理员 | ✅ 安全 |
| `make seed-data` | 导入种子数据 | ✅ 安全 |
| `make dev` | 开发模式运行 | ✅ 安全 |
| `make build` | 构建应用 | ✅ 安全 |
| `make test` | 运行测试 | ✅ 安全 |

## 🎯 最佳实践

### 开发流程

1. **首次设置**：使用 `make setup-database` 完整初始化
2. **日常开发**：使用 `make dev` 启动开发服务器
3. **数据重置**：使用 `make drop-database` + `make setup-database`
4. **结构更新**：使用 `make init-tables` 应用新的表结构

### 安全建议

1. **生产环境**：不要使用 `make drop-database`
2. **备份数据**：重要操作前先备份数据库
3. **权限控制**：使用专用数据库用户，避免使用 root
4. **环境隔离**：开发、测试、生产使用不同的数据库

---

*Makefile 提供了便捷的项目管理方式，建议熟悉常用命令以提高开发效率。*
