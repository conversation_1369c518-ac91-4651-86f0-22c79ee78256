# 🔄 项目更新指南

本文档记录了项目文件整合与 Makefile 改进的详细过程和使用指南。

## 📋 整合内容

### 🗃️ SQL 文件重构

#### 之前的问题
- `init_admin.sql` 和 `scripts/seed_data.sql` 存在重复的管理员数据创建
- 两个文件都创建超级管理员账号，造成数据冲突
- 职责不清晰，难以维护

#### 现在的解决方案
- **`scripts/init_admin.sql`** - 专门负责管理员系统初始化
  - 管理员用户创建
  - 角色权限体系
  - RBAC 权限配置
  - 操作日志测试数据
  
- **`scripts/seed_data.sql`** - 专门负责业务数据初始化
  - 友情链接
  - 合作伙伴
  - 零件平台
  - 项目案例
  - 服务配置
  - 轮播图
  - 招聘信息
  - **已删除** 管理员相关重复数据

#### 🔄 目录结构优化
为了保持一致性，已将 `init_admin.sql` 移动到 `scripts/` 目录下：

```
server-go/scripts/
├── create_database.sql     # 数据库创建
├── init_admin.sql         # 管理员系统初始化
└── seed_data.sql          # 业务数据初始化
```

### 🔧 Makefile 命令改进

#### 新增命令

1. **`make setup-database`** - 一键完整数据库设置
   ```bash
   make setup-database
   ```
   - 创建数据库
   - 初始化表结构
   - 导入管理员数据
   - 导入业务数据
   - 显示登录信息

2. **`make setup-database-skip-admin`** - 设置数据库但跳过管理员
   ```bash
   make setup-database-skip-admin
   ```
   - 适用于已有管理员数据的情况

3. **`make create-database`** - 仅创建数据库
   ```bash
   make create-database
   ```

4. **`make init-tables`** - 仅初始化表结构
   ```bash
   make init-tables
   ```

5. **`make drop-database`** - 删除数据库（危险操作）
   ```bash
   make drop-database
   ```

#### 改进的现有命令

6. **`make init-admin`** - 修正了数据库名称参数
7. **`make seed-data`** - 修正了数据库名称参数

## 🚀 使用指南

### 首次项目设置

```bash
# 1. 克隆项目
git clone <repository>
cd server-go

# 2. 安装依赖
go mod download

# 3. 一键设置数据库
make setup-database

# 4. 启动开发服务器
make dev
```

### 重置数据库

```bash
# 完全重置（删除并重新创建）
make drop-database
make setup-database

# 或者使用一键重置（如果有的话）
make reset-database
```

### 仅更新表结构

```bash
# 当模型有变化时，仅更新表结构
make init-tables
```

### 添加测试数据

```bash
# 添加业务测试数据
make seed-data

# 重新初始化管理员（如果需要）
make init-admin
```

## 📊 数据库初始化流程

### 完整流程 (`make setup-database`)

```mermaid
graph TD
    A[开始] --> B[删除现有数据库]
    B --> C[创建新数据库]
    C --> D[运行 GORM 迁移]
    D --> E[导入管理员数据]
    E --> F[导入业务数据]
    F --> G[显示登录信息]
    G --> H[完成]
```

### 跳过管理员流程 (`make setup-database-skip-admin`)

```mermaid
graph TD
    A[开始] --> B[删除现有数据库]
    B --> C[创建新数据库]
    C --> D[运行 GORM 迁移]
    D --> F[导入业务数据]
    F --> H[完成]
```

## 🔍 文件职责说明

### `scripts/create_database.sql`
```sql
-- 数据库创建和基础配置
DROP DATABASE IF EXISTS weizhi;
CREATE DATABASE weizhi CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### `scripts/init_admin.sql`
```sql
-- 管理员系统完整初始化
-- 1. 用户表数据
-- 2. 角色表数据
-- 3. 权限表数据
-- 4. 用户角色关联
-- 5. 角色权限关联
-- 6. 操作日志示例
```

### `scripts/seed_data.sql`
```sql
-- 业务数据初始化
-- 1. 友情链接
-- 2. 合作伙伴
-- 3. 零件平台
-- 4. 项目案例
-- 5. 服务配置
-- 6. 轮播图
-- 7. 招聘信息
-- 8. 新闻数据
```

## 🎯 最佳实践

### 开发环境

1. **首次设置**：使用 `make setup-database`
2. **日常开发**：使用 `make dev` 启动服务
3. **表结构变更**：使用 `make init-tables`
4. **数据重置**：使用 `make drop-database` + `make setup-database`

### 生产环境

1. **谨慎操作**：避免使用 `make drop-database`
2. **备份优先**：重要操作前先备份
3. **分步执行**：使用细分命令而非一键命令
4. **权限控制**：使用专用数据库用户

### 团队协作

1. **统一命令**：团队成员使用相同的 Makefile 命令
2. **文档同步**：及时更新相关文档
3. **版本控制**：SQL 文件变更要提交到版本控制
4. **测试验证**：新的数据库脚本要经过测试

## 🐛 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查 MySQL 服务
sudo systemctl status mysql

# 检查配置
cat config.yaml | grep -A 10 database
```

#### 2. 权限不足
```bash
# 检查数据库用户权限
mysql -u root -p
SHOW GRANTS FOR 'root'@'localhost';
```

#### 3. 表结构冲突
```bash
# 清理并重新创建
make drop-database
make setup-database
```

#### 4. 数据重复
```bash
# 检查是否多次运行了初始化脚本
# 使用 setup-database-skip-admin 避免重复创建管理员
make setup-database-skip-admin
```

## 📈 性能优化建议

### 数据库优化

1. **索引优化**：为常用查询字段添加索引
2. **连接池**：合理配置数据库连接池
3. **查询优化**：避免 N+1 查询问题

### 脚本优化

1. **批量操作**：使用批量插入而非单条插入
2. **事务控制**：合理使用事务确保数据一致性
3. **错误处理**：添加适当的错误处理和回滚机制

## 🔮 未来规划

### 短期计划
- [ ] 添加数据库迁移版本控制
- [ ] 实现增量数据更新
- [ ] 添加数据验证脚本

### 长期计划
- [ ] 自动化数据库部署流程
- [ ] 集成 CI/CD 流水线
- [ ] 添加数据库监控和告警

---

*项目更新指南持续完善中，如有问题请参考相关文档或联系开发团队。*
