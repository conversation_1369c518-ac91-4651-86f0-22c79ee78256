# 📝 代码规范

本文档定义了蔚之领域智能科技项目的代码规范和最佳实践，确保代码质量和团队协作效率。

## 🎯 总体原则

### 代码质量原则

- **可读性优先**: 代码应该易于理解和维护
- **一致性**: 遵循统一的编码风格和命名规范
- **简洁性**: 避免不必要的复杂性和重复代码
- **可测试性**: 编写易于测试的代码
- **性能考虑**: 在保证可读性的前提下优化性能

### 文档化原则

- **注释清晰**: 为复杂逻辑添加必要注释
- **API文档**: 为所有公开接口编写文档
- **变更记录**: 重要修改需要记录变更原因
- **示例代码**: 提供使用示例和最佳实践

## 🌐 前端代码规范

### Vue.js / Nuxt.js 规范

#### 组件命名

```vue
<!-- ✅ 推荐：使用 PascalCase -->
<template>
  <div class="news-card">
    <NewsHeader :title="title" />
    <NewsContent :content="content" />
  </div>
</template>

<!-- ❌ 避免：使用 kebab-case 或其他格式 -->
<template>
  <div class="news-card">
    <news-header :title="title" />
    <newsContent :content="content" />
  </div>
</template>
```

#### 文件命名

```bash
# ✅ 推荐：组件文件使用 PascalCase
components/
├── NewsCard.vue
├── UserProfile.vue
├── ProjectCase.vue

# ✅ 推荐：页面文件使用 kebab-case
pages/
├── index.vue
├── about-us.vue
├── news/
│   ├── index.vue
│   └── [id].vue

# ✅ 推荐：工具文件使用 camelCase
utils/
├── formatDate.js
├── validateForm.js
├── apiClient.js
```

#### 组件结构

```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 1. 导入依赖
import { ref, computed, onMounted } from 'vue'
import type { News } from '@/types'

// 2. 定义 Props
interface Props {
  title: string
  content?: string
  published?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  content: '',
  published: false
})

// 3. 定义 Emits
const emit = defineEmits<{
  update: [value: string]
  delete: [id: number]
}>()

// 4. 响应式数据
const isLoading = ref(false)
const newsData = ref<News[]>([])

// 5. 计算属性
const filteredNews = computed(() => {
  return newsData.value.filter(item => item.published)
})

// 6. 方法
const handleUpdate = (value: string) => {
  emit('update', value)
}

// 7. 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style scoped>
/* 样式内容 */
</style>
```

### TypeScript 规范

#### 类型定义

```typescript
// ✅ 推荐：使用 interface 定义对象类型
interface User {
  id: number
  username: string
  email: string
  createdAt: Date
}

// ✅ 推荐：使用 type 定义联合类型
type Status = 'pending' | 'approved' | 'rejected'

// ✅ 推荐：使用泛型提高复用性
interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

// ✅ 推荐：导出类型
export type { User, Status, ApiResponse }
```

#### 函数定义

```typescript
// ✅ 推荐：明确的参数和返回值类型
const fetchNews = async (params: {
  page: number
  pageSize: number
  keyword?: string
}): Promise<ApiResponse<News[]>> => {
  // 实现逻辑
}

// ✅ 推荐：使用可选参数
const formatDate = (
  date: Date,
  format: string = 'YYYY-MM-DD'
): string => {
  // 实现逻辑
}
```

### CSS/UnoCSS 规范

#### 类名命名

```vue
<template>
  <!-- ✅ 推荐：使用语义化类名 -->
  <div class="news-card">
    <header class="news-card__header">
      <h2 class="news-card__title">{{ title }}</h2>
    </header>
    <main class="news-card__content">
      <p class="news-card__excerpt">{{ excerpt }}</p>
    </main>
  </div>
</template>

<style scoped>
/* ✅ 推荐：BEM 命名规范 */
.news-card {
  @apply bg-white rounded-lg shadow-md p-6;
}

.news-card__header {
  @apply mb-4;
}

.news-card__title {
  @apply text-xl font-bold text-gray-800;
}

.news-card__content {
  @apply text-gray-600;
}

.news-card__excerpt {
  @apply line-clamp-3;
}
</style>
```

## ⚙️ 后端代码规范 (Go)

### 包和文件组织

```go
// ✅ 推荐：清晰的包结构
package handler

import (
    "context"
    "net/http"
    
    "github.com/gin-gonic/gin"
    
    "weishi-server/internal/model"
    "weishi-server/internal/service"
    "weishi-server/pkg/response"
)
```

### 结构体定义

```go
// ✅ 推荐：使用 PascalCase 命名
type User struct {
    ID        uint      `json:"id" gorm:"primaryKey"`
    Username  string    `json:"username" gorm:"uniqueIndex;size:50"`
    Email     string    `json:"email" gorm:"uniqueIndex;size:100"`
    CreatedAt time.Time `json:"createdAt" gorm:"column:created_at"`
    UpdatedAt time.Time `json:"updatedAt" gorm:"column:updated_at"`
}

// ✅ 推荐：添加表名方法
func (User) TableName() string {
    return "users"
}
```

### 函数定义

```go
// ✅ 推荐：清晰的函数签名和注释
// GetUserByID 根据用户ID获取用户信息
func (s *userService) GetUserByID(ctx context.Context, id uint) (*model.User, error) {
    if id == 0 {
        return nil, errors.New("invalid user id")
    }
    
    user, err := s.userRepo.GetByID(ctx, id)
    if err != nil {
        return nil, fmt.Errorf("failed to get user: %w", err)
    }
    
    return user, nil
}
```

### 错误处理

```go
// ✅ 推荐：使用 errors.New 和 fmt.Errorf
func (h *userHandler) GetUser(c *gin.Context) {
    id, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        response.Error(c, http.StatusBadRequest, "invalid user id")
        return
    }
    
    user, err := h.userService.GetUserByID(c.Request.Context(), uint(id))
    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            response.Error(c, http.StatusNotFound, "user not found")
            return
        }
        response.Error(c, http.StatusInternalServerError, "internal server error")
        return
    }
    
    response.Success(c, user)
}
```

### API 注释规范

```go
// @Summary 获取用户信息
// @Description 根据用户ID获取用户详细信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Success 200 {object} response.Response{data=model.User}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/users/{id} [get]
func (h *userHandler) GetUser(c *gin.Context) {
    // 实现逻辑
}
```

## 🗄️ 数据库规范

### 表命名

```sql
-- ✅ 推荐：使用单数形式，小写+下划线
CREATE TABLE user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ✅ 推荐：关联表使用两个表名组合
CREATE TABLE user_role (
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, role_id)
);
```

### 字段命名

```sql
-- ✅ 推荐：使用描述性字段名
CREATE TABLE news (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL COMMENT '新闻标题',
    content TEXT NOT NULL COMMENT '新闻内容',
    author_id BIGINT NOT NULL COMMENT '作者ID',
    published_at DATETIME NULL COMMENT '发布时间',
    is_published TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否发布',
    view_count INT NOT NULL DEFAULT 0 COMMENT '浏览次数',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_author_id (author_id),
    INDEX idx_published (is_published, published_at),
    INDEX idx_created_at (created_at)
);
```

## 📝 注释规范

### 文件头注释

```go
// Package handler 提供HTTP请求处理器
// 
// 本包包含所有API端点的处理逻辑，负责：
// - 请求参数验证
// - 业务逻辑调用
// - 响应格式化
//
// Author: 开发团队
// Created: 2024-10-01
package handler
```

### 函数注释

```typescript
/**
 * 格式化日期字符串
 * 
 * @param date - 要格式化的日期对象
 * @param format - 格式化模板，默认为 'YYYY-MM-DD'
 * @returns 格式化后的日期字符串
 * 
 * @example
 * ```typescript
 * const formatted = formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss')
 * console.log(formatted) // '2024-01-15 14:30:00'
 * ```
 */
export const formatDate = (date: Date, format: string = 'YYYY-MM-DD'): string => {
  // 实现逻辑
}
```

## 🧪 测试规范

### 单元测试

```typescript
// ✅ 推荐：清晰的测试结构
describe('formatDate', () => {
  it('should format date with default format', () => {
    const date = new Date('2024-01-15T14:30:00Z')
    const result = formatDate(date)
    expect(result).toBe('2024-01-15')
  })
  
  it('should format date with custom format', () => {
    const date = new Date('2024-01-15T14:30:00Z')
    const result = formatDate(date, 'YYYY/MM/DD HH:mm')
    expect(result).toBe('2024/01/15 14:30')
  })
  
  it('should handle invalid date', () => {
    const date = new Date('invalid')
    expect(() => formatDate(date)).toThrow('Invalid date')
  })
})
```

### Go 测试

```go
// ✅ 推荐：表驱动测试
func TestUserService_GetUserByID(t *testing.T) {
    tests := []struct {
        name    string
        userID  uint
        want    *model.User
        wantErr bool
    }{
        {
            name:   "valid user id",
            userID: 1,
            want:   &model.User{ID: 1, Username: "test"},
            wantErr: false,
        },
        {
            name:    "invalid user id",
            userID:  0,
            want:    nil,
            wantErr: true,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            got, err := service.GetUserByID(context.Background(), tt.userID)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetUserByID() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("GetUserByID() = %v, want %v", got, tt.want)
            }
        })
    }
}
```

## 🔧 工具配置

### ESLint 配置

```json
{
  "extends": [
    "@nuxt/eslint-config",
    "prettier"
  ],
  "rules": {
    "vue/multi-word-component-names": "off",
    "vue/no-multiple-template-root": "off",
    "@typescript-eslint/no-unused-vars": "error",
    "prefer-const": "error",
    "no-var": "error"
  }
}
```

### Prettier 配置

```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 80,
  "endOfLine": "lf",
  "arrowParens": "avoid"
}
```

### Go 工具配置

```yaml
# .golangci.yml
linters:
  enable:
    - gofmt
    - goimports
    - govet
    - golint
    - ineffassign
    - misspell
    - deadcode
    - varcheck
    - structcheck
    - errcheck

linters-settings:
  gofmt:
    simplify: true
  goimports:
    local-prefixes: weishi-server
```

## 📋 代码审查清单

### 通用检查

- [ ] 代码符合项目编码规范
- [ ] 变量和函数命名清晰有意义
- [ ] 没有硬编码的魔法数字或字符串
- [ ] 错误处理完整且合理
- [ ] 添加了必要的注释和文档
- [ ] 没有调试代码或注释掉的代码
- [ ] 性能考虑合理

### 前端检查

- [ ] 组件结构清晰，职责单一
- [ ] Props 和 Emits 定义完整
- [ ] 响应式数据使用正确
- [ ] 样式使用 scoped 或模块化
- [ ] 无障碍性考虑
- [ ] 移动端适配

### 后端检查

- [ ] API 接口设计合理
- [ ] 数据验证完整
- [ ] 数据库操作安全
- [ ] 并发安全考虑
- [ ] 日志记录适当
- [ ] 测试覆盖充分

---

*代码规范持续完善中，请团队成员严格遵循。如有建议请及时反馈。*
