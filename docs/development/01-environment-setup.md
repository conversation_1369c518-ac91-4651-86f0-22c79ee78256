# 🔧 开发环境配置

本文档详细介绍如何配置蔚之领域智能科技项目的开发环境。

## 📋 环境要求

### 硬件要求

| 组件 | 最低要求 | 推荐配置 |
|------|----------|----------|
| CPU | 双核 2.0GHz | 四核 3.0GHz+ |
| 内存 | 8GB | 16GB+ |
| 存储 | 20GB 可用空间 | 50GB+ SSD |
| 网络 | 稳定的互联网连接 | 宽带连接 |

### 操作系统支持

- **Windows**: Windows 10 (1903+) 或 Windows 11
- **macOS**: macOS 10.15 (Catalina) 或更高版本
- **Linux**: Ubuntu 18.04+、CentOS 7+、Debian 10+

## 🛠️ 开发工具安装

### 1. Node.js 环境

#### 使用 nvm 安装（推荐）

**Windows (nvm-windows)**:
```bash
# 下载并安装 nvm-windows
# https://github.com/coreybutler/nvm-windows/releases

# 安装 Node.js
nvm install 18.19.0
nvm use 18.19.0
```

**macOS/Linux**:
```bash
# 安装 nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# 重新加载终端或执行
source ~/.bashrc

# 安装 Node.js
nvm install 18.19.0
nvm use 18.19.0
nvm alias default 18.19.0
```

#### 验证安装

```bash
node --version  # 应该显示 v18.19.0
npm --version   # 应该显示 10.x.x
```

### 2. pnpm 包管理器

```bash
# 全局安装 pnpm
npm install -g pnpm@latest

# 验证安装
pnpm --version  # 应该显示 8.x.x

# 配置 pnpm（可选）
pnpm config set store-dir ~/.pnpm-store
pnpm config set registry https://registry.npmmirror.com/
```

### 3. Go 开发环境

#### Windows

```bash
# 下载并安装 Go
# https://golang.org/dl/

# 或使用 Chocolatey
choco install golang

# 或使用 Scoop
scoop install go
```

#### macOS

```bash
# 使用 Homebrew
brew install go

# 或下载安装包
# https://golang.org/dl/
```

#### Linux

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install golang-go

# CentOS/RHEL
sudo yum install golang

# 或下载二进制包
wget https://golang.org/dl/go1.23.0.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.23.0.linux-amd64.tar.gz
```

#### 配置 Go 环境

```bash
# 添加到 ~/.bashrc 或 ~/.zshrc
export PATH=$PATH:/usr/local/go/bin
export GOPATH=$HOME/go
export GOBIN=$GOPATH/bin
export PATH=$PATH:$GOBIN

# 重新加载配置
source ~/.bashrc

# 验证安装
go version  # 应该显示 go1.23.x
```

#### Go 开发工具

```bash
# 安装常用工具
go install github.com/cosmtrek/air@latest           # 热重载
go install github.com/swaggo/swag/cmd/swag@latest   # API文档生成
go install golang.org/x/tools/cmd/goimports@latest # 导入整理
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest # 代码检查
```

### 4. MySQL 数据库

#### Windows

```bash
# 下载并安装 MySQL
# https://dev.mysql.com/downloads/mysql/

# 或使用 Chocolatey
choco install mysql

# 启动服务
net start mysql
```

#### macOS

```bash
# 使用 Homebrew
brew install mysql

# 启动服务
brew services start mysql

# 或使用 MySQL 安装包
# https://dev.mysql.com/downloads/mysql/
```

#### Linux

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# 启动服务
sudo systemctl start mysql
sudo systemctl enable mysql

# CentOS/RHEL
sudo yum install mysql-server
sudo systemctl start mysqld
sudo systemctl enable mysqld
```

#### 配置 MySQL

```bash
# 安全配置
sudo mysql_secure_installation

# 登录 MySQL
mysql -u root -p

# 创建开发用户
CREATE USER 'weishi'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON *.* TO 'weishi'@'localhost';
FLUSH PRIVILEGES;
```

## 🎨 IDE 和编辑器配置

### Visual Studio Code（推荐）

#### 安装 VS Code

- 下载地址: https://code.visualstudio.com/
- 支持所有主流操作系统

#### 推荐扩展

**通用扩展**:
```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml"
  ]
}
```

**Vue/Nuxt 开发**:
```json
{
  "recommendations": [
    "Vue.volar",
    "Vue.vscode-typescript-vue-plugin",
    "antfu.unocss",
    "johnsoncodehk.vscode-typescript-vue-plugin"
  ]
}
```

**Go 开发**:
```json
{
  "recommendations": [
    "golang.go",
    "ms-vscode.vscode-go",
    "golang.go-nightly"
  ]
}
```

#### VS Code 配置

创建 `.vscode/settings.json`:

```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "go.formatTool": "goimports",
  "go.lintTool": "golangci-lint",
  "go.lintOnSave": "package",
  "[vue]": {
    "editor.defaultFormatter": "Vue.volar"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[go]": {
    "editor.defaultFormatter": "golang.go"
  }
}
```

### 其他 IDE 选择

#### GoLand（JetBrains）

- 专业的 Go 开发 IDE
- 强大的调试和重构功能
- 需要付费许可证

#### WebStorm（JetBrains）

- 专业的前端开发 IDE
- 优秀的 Vue/TypeScript 支持
- 需要付费许可证

## 🔧 项目配置

### 1. 克隆项目

```bash
# 克隆项目
git clone <项目仓库地址>
cd Nuxt3Web

# 查看项目结构
tree -L 2
```

### 2. 环境变量配置

#### 前端环境变量

**web/.env.development**:
```env
# API 基础地址
NUXT_PUBLIC_API_BASE=http://localhost:3001

# 百度地图 API Key
NUXT_PUBLIC_BAIDU_MAP_KEY=your_baidu_map_key

# 其他配置
NUXT_PUBLIC_APP_NAME=蔚之领域智能科技
```

**admin/.env**:
```env
# API 基础地址
VITE_API_BASE_URL=http://localhost:3001

# 应用配置
VITE_APP_TITLE=蔚之领域管理后台
VITE_APP_VERSION=1.0.0
```

#### 后端环境变量

**server-go/config.yaml**:
```yaml
app:
  name: "weizhi-server"
  port: "3001"
  mode: "debug"

database:
  host: "localhost"
  port: "3306"
  username: "root"
  password: "your_password"
  database: "weizhi"
  charset: "utf8mb4"

jwt:
  secret: "your-jwt-secret-key"
  expire_time: 7200

log:
  level: "info"
  path: "./logs"

cos:
  secret_id: "your_cos_secret_id"
  secret_key: "your_cos_secret_key"
  region: "ap-nanjing"
  bucket: "your_bucket_name"
  domain: ""
```

### 3. 依赖安装

```bash
# 安装前端依赖
pnpm install

# 安装后端依赖
cd server-go
go mod download
cd ..
```

### 4. 数据库初始化

```bash
cd server-go

# 创建数据库和表结构
make setup-database

# 导入初始数据
make seed-data

# 创建管理员账户
make init-admin
```

## 🚀 开发服务器启动

### 方式一：一键启动

```bash
# 在项目根目录
pnpm dev:all
```

### 方式二：分别启动

```bash
# 终端1: 启动后端服务
cd server-go
make dev

# 终端2: 启动企业官网
cd web
pnpm dev

# 终端3: 启动管理后台
cd admin
pnpm dev
```

## 🔍 开发工具配置

### 1. Git 配置

```bash
# 配置用户信息
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# 配置编辑器
git config --global core.editor "code --wait"

# 配置别名
git config --global alias.st status
git config --global alias.co checkout
git config --global alias.br branch
git config --global alias.ci commit
```

### 2. 代码格式化配置

#### Prettier 配置

创建 `.prettierrc`:

```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 80,
  "endOfLine": "lf"
}
```

#### ESLint 配置

项目已包含 ESLint 配置，可根据需要调整。

### 3. 调试配置

#### VS Code 调试配置

创建 `.vscode/launch.json`:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Go",
      "type": "go",
      "request": "launch",
      "mode": "auto",
      "program": "${workspaceFolder}/server-go/cmd/main.go",
      "cwd": "${workspaceFolder}/server-go"
    },
    {
      "name": "Debug Nuxt",
      "type": "node",
      "request": "launch",
      "cwd": "${workspaceFolder}/web",
      "runtimeExecutable": "pnpm",
      "runtimeArgs": ["dev"]
    }
  ]
}
```

## ✅ 环境验证

### 检查清单

- [ ] Node.js 版本 >= 18.0.0
- [ ] pnpm 版本 >= 8.0.0
- [ ] Go 版本 >= 1.23.0
- [ ] MySQL 服务正常运行
- [ ] 项目依赖安装成功
- [ ] 数据库初始化完成
- [ ] 开发服务器启动成功
- [ ] 各服务访问正常

### 验证命令

```bash
# 检查版本
node --version
pnpm --version
go version
mysql --version

# 检查服务
curl http://localhost:3001/api/health
curl http://localhost:4000
curl http://localhost:3001

# 检查数据库
mysql -u root -p -e "SHOW DATABASES;"
```

## 🐛 常见问题

### Node.js 相关

**问题**: pnpm 安装失败
```bash
# 解决方案
npm cache clean --force
npm install -g pnpm@latest
```

### Go 相关

**问题**: Go 模块下载失败
```bash
# 解决方案
go env -w GOPROXY=https://goproxy.cn,direct
go mod download
```

### MySQL 相关

**问题**: 连接被拒绝
```bash
# 检查服务状态
sudo systemctl status mysql

# 重启服务
sudo systemctl restart mysql
```

---

*配置完成后，请继续阅读 [代码规范](./02-coding-standards.md) 文档。*
