# 🛠️ 开发工具指南

本文档介绍蔚之领域智能科技项目中使用的各种开发工具和配置。

## 🔥 Air - Go 热重载开发工具

### 什么是 Air

Air 是一个针对 Go 应用的实时重载工具，能够在开发过程中监听文件变化并自动重新构建和运行应用程序，极大提升开发效率。

### 主要功能

- **自动监听文件变化** - 监控 Go 源文件的修改
- **智能增量构建** - 只在文件变化时重新构建
- **热重载** - 无需手动重启应用程序
- **构建错误提示** - 实时显示编译错误信息
- **高度可配置** - 支持自定义构建命令、监听路径等

### 安装

```bash
# 安装 Air
go install github.com/air-verse/air@latest

# 验证安装
air -v
```

### 配置文件 (.air.toml)

项目已经配置了 `.air.toml` 文件：

```toml
root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
  bin = "./tmp/main"
  cmd = "go build -o ./tmp/main ./cmd/main.go"
  delay = 0
  exclude_dir = ["assets", "tmp", "vendor", "testdata", "logs", "bin"]
  exclude_regex = ["_test.go"]
  include_ext = ["go", "tpl", "tmpl", "html"]
  kill_delay = "0s"
  log = "build-errors.log"
  rerun_delay = 500

[color]
  build = "yellow"
  main = "magenta"
  runner = "green"
  watcher = "cyan"

[log]
  main_only = false
  time = false

[screen]
  clear_on_rebuild = false
  keep_scroll = true
```

### 使用方法

```bash
# 通过 Makefile 启动（推荐）
cd server-go
make dev

# 直接使用 air 命令
air

# 查看运行状态
make status

# 停止服务
Ctrl + C
```

### 工作流程

1. **启动监听** - Air 开始监听指定目录下的文件变化
2. **检测变化** - 当检测到 `.go` 文件修改时
3. **自动构建** - 执行构建命令
4. **重启服务** - 终止旧进程，启动新的可执行文件
5. **显示状态** - 在终端显示构建状态和运行日志

## 🎨 前端开发工具

### Vite

**用途**: 前端构建工具，用于管理后台项目

**特点**:
- 极快的热重载
- 原生 ES 模块支持
- 优化的生产构建
- 丰富的插件生态

**配置**: `admin/vite.config.ts`

### Nuxt DevTools

**用途**: Nuxt3 开发调试工具

**功能**:
- 页面结构分析
- 组件树查看
- 路由调试
- 性能分析

**启用**:
```typescript
// nuxt.config.ts
export default defineNuxtConfig({
  devtools: { enabled: true }
})
```

### Vue DevTools

**用途**: Vue 组件调试

**安装**:
- 浏览器扩展
- 独立应用

**功能**:
- 组件状态查看
- Vuex/Pinia 状态管理
- 事件追踪
- 性能分析

## 🔧 代码质量工具

### ESLint

**用途**: JavaScript/TypeScript 代码检查

**配置文件**:
- `web/.eslintrc.js`
- `admin/.eslintrc.js`

**常用命令**:
```bash
# 检查代码
pnpm lint

# 自动修复
pnpm lint --fix
```

### Prettier

**用途**: 代码格式化

**配置文件**: `.prettierrc`

```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 80,
  "endOfLine": "lf"
}
```

### golangci-lint

**用途**: Go 代码检查

**安装**:
```bash
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
```

**使用**:
```bash
cd server-go
make lint
```

## 📚 API 文档工具

### Swagger

**用途**: 自动生成 API 文档

**安装**:
```bash
go install github.com/swaggo/swag/cmd/swag@latest
```

**生成文档**:
```bash
cd server-go
make swagger
```

**访问文档**: http://localhost:3001/swagger/index.html

### 注释规范

```go
// @Summary 获取新闻列表
// @Description 获取所有新闻信息
// @Tags 内容管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]model.News}
// @Router /api/news [get]
func (h *contentHandler) GetAllNews(c *gin.Context) {
    // 实现代码
}
```

## 🗄️ 数据库工具

### MySQL Workbench

**用途**: 可视化数据库管理

**功能**:
- 数据库设计
- SQL 查询
- 数据导入导出
- 性能监控

### phpMyAdmin

**用途**: Web 端数据库管理

**Docker 启动**:
```bash
docker-compose -f docker-compose.dev.yml up -d
```

**访问地址**: http://localhost:8080

### 命令行工具

```bash
# 连接数据库
mysql -u root -p

# 导出数据库
mysqldump -u root -p weizhi > backup.sql

# 导入数据库
mysql -u root -p weizhi < backup.sql
```

## 🐳 容器化工具

### Docker

**用途**: 应用容器化

**常用命令**:
```bash
# 构建镜像
docker build -t weishi-web .

# 运行容器
docker run -p 3000:3000 weishi-web

# 查看容器
docker ps

# 查看日志
docker logs <container_id>
```

### Docker Compose

**用途**: 多容器编排

**配置文件**: `docker-compose.yml`

**常用命令**:
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 🔍 调试工具

### VS Code 调试配置

**Go 调试** (`.vscode/launch.json`):
```json
{
  "name": "Debug Go",
  "type": "go",
  "request": "launch",
  "mode": "auto",
  "program": "${workspaceFolder}/server-go/cmd/main.go",
  "cwd": "${workspaceFolder}/server-go"
}
```

**Node.js 调试**:
```json
{
  "name": "Debug Nuxt",
  "type": "node",
  "request": "launch",
  "cwd": "${workspaceFolder}/web",
  "runtimeExecutable": "pnpm",
  "runtimeArgs": ["dev"]
}
```

### 浏览器开发者工具

**Chrome DevTools**:
- 元素检查
- 网络监控
- 性能分析
- 控制台调试

**Vue DevTools**:
- 组件状态
- 路由信息
- Vuex/Pinia 状态

## 📊 性能分析工具

### Go 性能分析

```bash
# CPU 性能分析
go tool pprof http://localhost:3001/debug/pprof/profile

# 内存分析
go tool pprof http://localhost:3001/debug/pprof/heap

# 协程分析
go tool pprof http://localhost:3001/debug/pprof/goroutine
```

### 前端性能分析

**Lighthouse**:
- 性能评分
- SEO 检查
- 可访问性
- 最佳实践

**Bundle Analyzer**:
```bash
# 分析打包大小
pnpm build --analyze
```

## 🔧 开发环境管理

### nvm (Node Version Manager)

```bash
# 安装特定版本
nvm install 18.19.0

# 切换版本
nvm use 18.19.0

# 设置默认版本
nvm alias default 18.19.0
```

### gvm (Go Version Manager)

```bash
# 安装 gvm
bash < <(curl -s -S -L https://raw.githubusercontent.com/moovweb/gvm/master/binscripts/gvm-installer)

# 安装 Go 版本
gvm install go1.23.0

# 使用特定版本
gvm use go1.23.0 --default
```

## 📝 最佳实践

### 开发工作流

1. **启动开发环境**
   ```bash
   # 启动所有服务
   pnpm dev:all
   ```

2. **代码开发**
   - 使用热重载进行实时开发
   - 遵循代码规范和注释规范
   - 及时提交代码

3. **代码检查**
   ```bash
   # 前端代码检查
   pnpm lint
   
   # 后端代码检查
   cd server-go && make lint
   ```

4. **测试验证**
   ```bash
   # 运行测试
   pnpm test
   cd server-go && make test
   ```

5. **构建部署**
   ```bash
   # 构建项目
   pnpm build
   cd server-go && make build
   ```

### 工具配置建议

1. **统一开发环境**: 使用 Docker 确保环境一致性
2. **自动化流程**: 使用 Makefile 和 npm scripts 简化操作
3. **代码质量**: 配置 pre-commit hooks 确保代码质量
4. **文档同步**: 及时更新 API 文档和开发文档

---

*更多工具配置请参考各工具的官方文档。*
