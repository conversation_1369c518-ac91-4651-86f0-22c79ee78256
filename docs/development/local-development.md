# 🛠️ 本地开发环境配置

本项目开发环境不使用Docker容器，采用原生开发方式以提高开发效率。

## 📋 环境要求

### 必需软件
- **Node.js** >= 18.0.0
- **pnpm** >= 8.0.0
- **Go** >= 1.21
- **MySQL** >= 8.0

### 推荐软件
- **VS Code** - 代码编辑器
- **Postman** - API测试
- **MySQL Workbench** - 数据库管理

## 🚀 快速启动

### 1. 安装依赖
```bash
# 安装前端依赖
cd web && pnpm install

# 安装管理后台依赖
cd admin && pnpm install

# 安装Go依赖
cd server-go && go mod download

# 或使用根目录脚本一键安装前端依赖
npm run install:all
```

### 2. 配置数据库
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE weizhi CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 配置后端数据库连接
cp server-go/env.example server-go/.env
# 编辑 server-go/.env 配置数据库连接信息
```

### 3. 启动服务

#### 后端服务 (端口: 3001)
```bash
cd server-go
go run main.go
```

#### 前端网站 (端口: 3000)
```bash
cd web
pnpm dev
```

#### 管理后台 (端口: 5173)
```bash
cd admin
pnpm dev
```

## 🔧 开发配置

### 环境变量配置

#### 后端配置 (server-go/.env)
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_password
DB_DATABASE=weizhi

# JWT配置
JWT_SECRET=your-jwt-secret
JWT_EXPIRE_TIME=28800

# 服务配置
SERVER_PORT=3001
```

#### 前端配置 (web/.env.development)
```bash
# API地址
NUXT_PUBLIC_API_BASE_URL=http://localhost:3001
```

#### 管理后台配置 (admin/.env)
```bash
# API地址
VITE_API_BASE_URL=http://localhost:3001
```

## 📝 开发流程

### 1. 代码修改
- 前端代码修改后自动热重载
- 后端代码修改后需要重启服务
- 管理后台代码修改后自动热重载

### 2. 数据库迁移
```bash
cd server-go
# Go应用启动时会自动执行数据库迁移
```

### 3. API测试
- 后端API地址: http://localhost:3001
- 健康检查: http://localhost:3001/api/health
- API文档: http://localhost:3001/docs

### 4. 前端访问
- 企业官网: http://localhost:3000
- 管理后台: http://localhost:5173

## 🐛 常见问题

### 端口冲突
如果端口被占用，可以修改配置文件中的端口号：
- 后端: 修改 `server-go/.env` 中的 `SERVER_PORT`
- 前端: 修改 `web/nuxt.config.ts` 中的 `devServer.port`
- 管理后台: 修改 `admin/vite.config.ts` 中的 `server.port`

### 数据库连接失败
1. 确认MySQL服务已启动
2. 检查数据库连接配置
3. 确认数据库用户权限

### 依赖安装失败
```bash
# 清理缓存重新安装
pnpm store prune
rm -rf node_modules
pnpm install
```

## 🔄 生产部署

开发完成后，使用 `deployment/` 目录下的配置进行生产部署：

```bash
cd deployment
./prepare-server-deploy.sh --compress
```

详见 [生产部署文档](../deployment/README.md)。
