# 列配置组功能需求文档

## 📋 功能概述

### 背景问题
当前系统中每个动态表格都需要独立配置列结构，导致：
- **数据冗余严重**：相似的列配置在多个表格中重复存储
- **维护成本高**：修改通用列配置需要逐个表格更新
- **标准化困难**：缺乏行业标准配置模板
- **存储浪费**：大量重复的列配置记录

### 解决方案
实现**列配置组**功能，允许：
- 创建可复用的列配置组（如"液压阀门标准配置"）
- 多个表格引用同一个配置组
- 支持表格级的个性化覆盖和扩展
- 统一管理，一处修改全局生效

## 🎯 核心功能

### 1. 列配置组管理
- **创建配置组**：定义包含多个列的标准配置
- **编辑配置组**：修改配置组的列结构和属性
- **删除配置组**：删除不再使用的配置组
- **复制配置组**：基于现有配置组创建新的配置组

### 2. 表格引用配置组
- **选择配置组**：创建表格时选择基础配置组
- **自动应用**：自动应用配置组中的所有列配置
- **实时同步**：配置组更新时自动同步到引用表格

### 3. 个性化覆盖
- **隐藏列**：隐藏配置组中不需要的列
- **修改列**：覆盖列的标签、类型等属性
- **添加列**：在配置组基础上添加自定义列
- **重新排序**：调整列的显示顺序

## 🏗️ 数据库设计

### 核心表结构

#### 1. 列模板表 (`column_templates`)
```sql
CREATE TABLE `column_templates` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '字段名',
  `label` varchar(100) NOT NULL COMMENT '显示标签',
  `type` enum('text','number','date','boolean','textarea') NOT NULL,
  `category` varchar(50) NOT NULL COMMENT '分类',
  `description` text COMMENT '描述',
  `is_required` tinyint(1) DEFAULT '0' COMMENT '是否必填',
  `is_common` tinyint(1) DEFAULT '0' COMMENT '是否常用',
  `default_value` text COMMENT '默认值',
  `options` json COMMENT '选项配置',
  `validation_rules` json COMMENT '验证规则',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `is_system` tinyint(1) DEFAULT '1' COMMENT '是否系统预设',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`)
);
```

#### 2. 组合模板表 (`combination_templates`)
```sql
CREATE TABLE `combination_templates` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '组合名称',
  `description` text COMMENT '描述',
  `category` varchar(50) COMMENT '分类',
  `scenarios` json COMMENT '适用场景',
  `is_system` tinyint(1) DEFAULT '1' COMMENT '是否系统预设',
  `created_by` bigint unsigned COMMENT '创建者ID',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

#### 3. 组合模板列关联表 (`combination_template_columns`)
```sql
CREATE TABLE `combination_template_columns` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `combination_template_id` bigint unsigned NOT NULL,
  `column_template_id` bigint unsigned NOT NULL,
  `sort_order` int DEFAULT '0' COMMENT '在组合中的排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_combination_column` (`combination_template_id`,`column_template_id`)
);
```

#### 4. 表格配置表 (`part_platform_table`) - 新增字段
```sql
ALTER TABLE `part_platform_table` 
ADD COLUMN `combination_template_id` bigint unsigned NULL COMMENT '组合模板ID',
ADD COLUMN `custom_columns` json COMMENT '自定义列配置';
```

#### 5. 列覆盖配置表 (`part_platform_table_column_override`)
```sql
CREATE TABLE `part_platform_table_column_override` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `table_id` bigint unsigned NOT NULL COMMENT '表格ID',
  `column_template_id` bigint unsigned NOT NULL COMMENT '列模板ID',
  `action` varchar(20) NOT NULL COMMENT '操作类型: hide, modify, add',
  `custom_label` varchar(255) COMMENT '自定义标签',
  `custom_config` json COMMENT '自定义配置',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`)
);
```

### 预设数据

#### 列模板数据
```sql
-- 基础信息类
('model', '型号', 'text', '基础信息', '产品型号或编号', 1, 1, 1, 1),
('name', '产品名称', 'text', '基础信息', '产品的完整名称', 1, 1, 2, 1),
('brand', '品牌', 'text', '基础信息', '产品品牌', 0, 0, 3, 1),

-- 技术参数类
('flow_rate', '流量(L/min)', 'number', '技术参数', '流体流量参数', 0, 1, 10, 1),
('pressure', '压力(MPa)', 'number', '技术参数', '工作压力参数', 0, 1, 11, 1),
('power', '功率(W)', 'number', '技术参数', '额定功率', 0, 0, 14, 1),

-- 商务信息类
('price', '价格(元)', 'number', '商务信息', '产品价格', 0, 1, 40, 1),
('supplier', '供应商', 'text', '商务信息', '产品供应商', 0, 0, 41, 1)
```

#### 组合模板数据
```sql
-- 液压阀门标准配置：型号 + 流量 + 压力 + 材质 + 价格
-- 电机产品标准配置：型号 + 名称 + 功率 + 电压 + 重量 + 价格
-- 基础产品配置：型号 + 名称 + 品牌 + 规格 + 价格
```

## 💻 技术实现

### 后端架构

#### 1. Go模型定义
```go
// 列模板模型
type ColumnTemplate struct {
    BaseModel
    Name            string `gorm:"type:varchar(100);not null;uniqueIndex" json:"name"`
    Label           string `gorm:"type:varchar(100);not null" json:"label"`
    Type            string `gorm:"type:varchar(20);not null" json:"type"`
    Category        string `gorm:"type:varchar(50);not null" json:"category"`
    // ... 其他字段
}

// 组合模板模型
type CombinationTemplate struct {
    BaseModel
    Name        string           `gorm:"type:varchar(100);not null" json:"name"`
    Description string           `gorm:"type:text" json:"description"`
    Category    string           `gorm:"type:varchar(50)" json:"category"`
    Columns     []ColumnTemplate `gorm:"many2many:combination_template_columns;" json:"columns"`
    // ... 其他字段
}

// 表格模型 - 新增字段
type PartPlatformTable struct {
    BaseModel
    ExtensionID           uint                 `gorm:"not null" json:"extension_id"`
    Name                  string               `gorm:"type:varchar(255);not null" json:"table_name"`
    CombinationTemplateID *uint                `gorm:"column:combination_template_id" json:"combination_template_id"`
    CustomColumns         string               `gorm:"type:json" json:"custom_columns"`
    CombinationTemplate   *CombinationTemplate `gorm:"foreignKey:CombinationTemplateID" json:"combination_template"`
    // ... 其他字段
}

// 列覆盖配置模型
type PartPlatformTableColumnOverride struct {
    BaseModel
    TableID          uint            `gorm:"not null" json:"table_id"`
    ColumnTemplateID uint            `gorm:"not null" json:"column_template_id"`
    Action           string          `gorm:"type:varchar(20);not null" json:"action"` // 'hide', 'modify', 'add'
    CustomLabel      string          `gorm:"type:varchar(255)" json:"custom_label"`
    CustomConfig     string          `gorm:"type:json" json:"custom_config"`
    SortOrder        int             `gorm:"default:0" json:"sort_order"`
    ColumnTemplate   *ColumnTemplate `gorm:"foreignKey:ColumnTemplateID" json:"column_template"`
}
```

#### 2. API接口设计
```go
// 列模板管理
GET    /api/column-templates              // 获取列模板列表
POST   /api/column-templates              // 创建列模板
PUT    /api/column-templates/{id}         // 更新列模板
DELETE /api/column-templates/{id}         // 删除列模板

// 组合模板管理
GET    /api/combination-templates         // 获取组合模板列表
GET    /api/combination-templates/{id}    // 获取组合模板详情
POST   /api/combination-templates         // 创建组合模板
PUT    /api/combination-templates/{id}    // 更新组合模板
DELETE /api/combination-templates/{id}    // 删除组合模板

// 表格列配置（新的基于模板的接口）
GET    /api/platform-config/tables/{id}/columns-with-template  // 获取表格完整列配置
POST   /api/platform-config/table-column-overrides            // 创建列覆盖
PUT    /api/platform-config/table-column-overrides/{id}       // 更新列覆盖
DELETE /api/platform-config/table-column-overrides/{id}       // 删除列覆盖
```

### 前端实现

#### 1. 列配置组管理页面
- **路径**：`/admin/content/column-config-groups`
- **功能**：配置组的CRUD操作
- **组件**：`ColumnConfigGroups.vue`

#### 2. 表格创建流程更新
- **选择配置组**：在创建表格时选择基础配置组
- **预览配置**：显示配置组包含的列
- **自定义选项**：支持隐藏、修改、添加列

#### 3. 列配置界面更新
- **模板列显示**：区分模板列和自定义列
- **覆盖操作**：支持对模板列进行覆盖
- **同步提示**：当模板更新时提示用户

## 🔄 业务流程

### 1. 管理员配置流程
```
1. 创建列模板 → 定义可复用的列（型号、流量、压力等）
2. 创建配置组 → 组合相关列形成标准配置
3. 设置分类 → 按行业或产品类型分类
4. 发布配置 → 供用户使用
```

### 2. 用户使用流程
```
1. 创建表格 → 选择合适的配置组
2. 自动应用 → 系统自动应用配置组的列
3. 个性化调整 → 根据需要隐藏、修改或添加列
4. 保存配置 → 完成表格创建
```

### 3. 维护更新流程
```
1. 修改配置组 → 管理员更新标准配置
2. 自动同步 → 系统自动同步到引用表格
3. 冲突处理 → 处理与个性化配置的冲突
4. 通知用户 → 提醒用户配置已更新
```

## 📊 数据优化效果

### 存储优化
```
优化前：
- 100个表格 × 平均5列 = 500条列配置记录
- 每条记录约200字节 = 100KB

优化后：
- 100个表格引用 = 100条引用记录（每条50字节）
- 50个列模板 = 50条模板记录（每条200字节）
- 20个覆盖配置 = 20条覆盖记录（每条100字节）
- 总计：5KB + 10KB + 2KB = 17KB

数据减少：83KB → 17KB（减少80%）
```

### 维护效率
```
优化前：修改"流量"列标签需要更新50个表格
优化后：修改1个模板自动影响所有引用表格

维护工作量减少95%
```

## 🚀 实施计划

### 阶段1：数据库迁移 ✅
- [x] 创建列模板表
- [x] 创建组合模板表和关联表
- [x] 创建列覆盖表
- [x] 初始化基础数据

### 阶段2：后端API开发 🔄
- [ ] 实现Repository层方法
- [ ] 实现Service层业务逻辑
- [ ] 实现Handler层API接口
- [ ] 编写单元测试

### 阶段3：前端界面开发
- [ ] 列配置组管理页面
- [ ] 表格创建流程更新
- [ ] 列配置界面改造
- [ ] 用户体验优化

### 阶段4：数据迁移和测试
- [ ] 现有数据迁移脚本
- [ ] 功能集成测试
- [ ] 性能测试
- [ ] 用户验收测试

## 🎯 成功指标

### 技术指标
- 数据存储减少 > 80%
- API响应时间 < 200ms
- 配置同步成功率 > 99%

### 业务指标
- 表格创建效率提升 > 50%
- 配置维护工作量减少 > 90%
- 用户满意度 > 4.5/5

## 📝 注意事项

### 兼容性
- 保持现有API的向后兼容
- 支持渐进式迁移
- 提供数据回滚机制

### 安全性
- 权限控制：只有管理员可以修改配置组
- 数据校验：严格验证配置数据的完整性
- 操作日志：记录所有配置变更操作

### 性能
- 缓存策略：缓存常用配置组数据
- 懒加载：按需加载列配置详情
- 批量操作：支持批量更新和同步

## 🔧 实现细节

### Repository层方法
```go
// 列模板相关
GetColumnTemplates() ([]model.ColumnTemplate, error)
GetColumnTemplatesByCategory(category string) ([]model.ColumnTemplate, error)
CreateColumnTemplate(template *model.ColumnTemplate) error
UpdateColumnTemplate(template *model.ColumnTemplate) error
DeleteColumnTemplate(id uint) error

// 组合模板相关
GetCombinationTemplates() ([]model.CombinationTemplate, error)
GetCombinationTemplateByID(id uint) (*model.CombinationTemplate, error)
CreateCombinationTemplate(template *model.CombinationTemplate) error
UpdateCombinationTemplate(template *model.CombinationTemplate) error
DeleteCombinationTemplate(id uint) error

// 表格列配置相关（新的基于模板的方法）
GetTableColumnsWithTemplate(tableID uint) ([]model.ColumnTemplate, []model.PartPlatformTableColumnOverride, error)
CreateTableColumnOverride(override *model.PartPlatformTableColumnOverride) error
UpdateTableColumnOverride(override *model.PartPlatformTableColumnOverride) error
DeleteTableColumnOverride(id uint) error
```

### 核心业务逻辑
```go
// 获取表格的完整列配置（模板 + 覆盖）
func (s *service) GetTableCompleteColumns(tableID uint) ([]ColumnConfig, error) {
    // 1. 获取表格基础信息和关联的组合模板
    // 2. 获取模板中的列配置
    // 3. 获取表格的覆盖配置
    // 4. 合并模板配置和覆盖配置
    // 5. 返回最终的列配置
}

// 应用配置组到表格
func (s *service) ApplyTemplateToTable(tableID uint, templateID uint) error {
    // 1. 更新表格的 combination_template_id
    // 2. 清理现有的覆盖配置（可选）
    // 3. 返回操作结果
}
```

### 前端组件设计
```typescript
// 列配置组选择器
interface ColumnConfigGroupSelector {
  value: number | null           // 选中的配置组ID
  options: CombinationTemplate[] // 可选的配置组列表
  preview: boolean              // 是否显示预览
  onChange: (id: number) => void // 选择变化回调
}

// 列覆盖编辑器
interface ColumnOverrideEditor {
  tableId: number                    // 表格ID
  templateColumns: ColumnTemplate[]  // 模板列
  overrides: ColumnOverride[]        // 覆盖配置
  onSave: (overrides: ColumnOverride[]) => void
}
```

## 🧪 测试用例

### 单元测试
```go
// 测试组合模板创建
func TestCreateCombinationTemplate(t *testing.T) {
    // 1. 准备测试数据
    // 2. 调用创建方法
    // 3. 验证结果
    // 4. 清理测试数据
}

// 测试表格列配置获取
func TestGetTableColumnsWithTemplate(t *testing.T) {
    // 1. 创建测试表格和模板
    // 2. 添加覆盖配置
    // 3. 获取完整配置
    // 4. 验证合并结果
}
```

### 集成测试
```typescript
// 测试配置组应用流程
describe('Column Config Group Application', () => {
  it('should apply template to table correctly', async () => {
    // 1. 创建配置组
    // 2. 创建表格并应用配置组
    // 3. 验证列配置正确应用
    // 4. 测试覆盖功能
  })
})
```

## 📚 使用示例

### 创建配置组
```typescript
const templateData = {
  name: '液压阀门标准配置',
  description: '适用于各类液压阀门产品',
  category: '液压设备',
  column_ids: [1, 5, 6, 8, 12] // 型号、流量、压力、材质、价格
}

await columnTemplateApi.createCombinationTemplate(templateData)
```

### 应用配置组到表格
```typescript
const tableData = {
  extension_id: 1,
  table_name: '两位四通手控换向阀',
  combination_template_id: 1 // 引用液压阀门标准配置
}

await platformConfigApi.createTable(tableData)
```

### 添加列覆盖
```typescript
const override = {
  table_id: 1,
  column_template_id: 5, // 流量列
  action: 'modify',
  custom_label: '最大流量(L/min)', // 自定义标签
  sort_order: 2
}

await platformConfigApi.createTableColumnOverride(override)
```

---

**文档版本**：v1.0
**创建时间**：2025-01-19
**最后更新**：2025-01-19
**负责人**：开发团队
