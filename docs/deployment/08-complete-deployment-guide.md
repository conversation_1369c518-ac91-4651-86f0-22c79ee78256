# 🚀 完整部署指南

本文档提供详细的部署流程，包括开发环境和生产环境的完整部署步骤。

## 📋 部署概览

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nuxt3 前端    │    │   Vue3 管理后台  │    │    Go 后端      │
│   Port: 4000    │───▶│   Port: 5173    │───▶│   Port: 3001    │
│   (开发环境)     │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                                       ▼
                                              ┌─────────────────┐
                                              │  MySQL 数据库   │
                                              │   Port: 3306    │
                                              └─────────────────┘
```

### 服务端口分配
| 服务 | 开发端口 | 生产端口 | 说明 |
|------|----------|----------|------|
| Go 后端 | 3001 | 3001 | API 服务 |
| Nuxt3 前端 | 4000 | 3000 | 企业官网 |
| Vue3 管理后台 | 5173 | 5173 | 管理系统 |
| MySQL 数据库 | 3306 | 3306 | 数据存储 |

## 🛠️ 开发环境部署

### 1. 环境准备

#### 系统要求
- **操作系统**: macOS / Linux / Windows
- **Go**: 1.21+
- **Node.js**: 18+
- **pnpm**: 最新版本
- **MySQL**: 8.0+

#### 安装依赖
```bash
# 1. 安装 Go
# 访问 https://golang.org/dl/ 下载安装

# 2. 安装 Node.js 和 pnpm
# 访问 https://nodejs.org/ 下载安装
npm install -g pnpm

# 3. 安装 MySQL
# macOS: brew install mysql
# Ubuntu: sudo apt install mysql-server
# Windows: 下载 MySQL 安装包
```

### 2. 项目初始化

```bash
# 1. 克隆项目
git clone <repository-url>
cd Nuxt3Web

# 2. 安装项目依赖
pnpm install

# 3. 安装各服务依赖
cd web && pnpm install && cd ..
cd admin && pnpm install && cd ..
cd server-go && go mod tidy && cd ..
```

### 3. 数据库配置

#### 创建数据库
```bash
# 1. 启动 MySQL 服务
sudo systemctl start mysql  # Linux
brew services start mysql   # macOS

# 2. 登录 MySQL
mysql -u root -p

# 3. 创建数据库和用户
CREATE DATABASE weizhi CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'weishi'@'localhost' IDENTIFIED BY 'Ydb3344%';
GRANT ALL PRIVILEGES ON weizhi.* TO 'weishi'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

#### 设置管理员密码
```bash
# 方式1: 交互式设置
./scripts/setup-admin.sh

# 方式2: 环境变量设置
ADMIN_PASSWORD="your_secure_password" ./scripts/setup-admin.sh

# 方式3: 生成自定义初始化脚本
ADMIN_USERNAME="admin" ADMIN_PASSWORD="your_password" ./scripts/generate-init-sql.sh
```

#### 导入数据
```bash
# 导入初始化数据
mysql -u weishi -p weizhi < server-go/scripts/init_database.sql
```

### 4. 服务启动

#### 启动顺序
```bash
# 1. 启动后端服务 (终端1)
cd server-go
go run cmd/main.go
# 或使用热重载: air

# 2. 启动前端服务 (终端2)
cd web
pnpm dev

# 3. 启动管理后台 (终端3)
cd admin
pnpm dev
```

#### 验证服务
```bash
# 检查后端
curl http://localhost:3001/api/health

# 检查前端
curl http://localhost:4000

# 检查管理后台
curl http://localhost:5173
```

### 5. 开发工具

```bash
# 端口检查
./scripts/check-ports.sh

# 健康检查
./scripts/test-health.sh

# 数据库管理
./scripts/manage-database.sh status
```

## 🐳 生产环境部署 (Docker)

### 1. 环境准备

#### 系统要求
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **服务器**: 2GB+ RAM, 20GB+ 存储

#### 安装 Docker
```bash
# Ubuntu
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# CentOS
sudo yum install -y docker docker-compose

# 启动 Docker
sudo systemctl start docker
sudo systemctl enable docker
```

### 2. 配置文件准备

#### 复制配置模板
```bash
cp docker.env.example docker.env
```

#### 编辑配置文件
```bash
vim docker.env
```

**必须配置的项目**:
```env
# 数据库配置
MYSQL_ROOT_PASSWORD=your_secure_root_password
MYSQL_PASSWORD=your_secure_db_password
MYSQL_DATABASE=weizhi

# 管理员配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_admin_password

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# 域名配置 (生产环境)
DOMAIN=your-domain.com

# COS配置 (如需文件上传)
COS_SECRET_ID=your_cos_secret_id
COS_SECRET_KEY=your_cos_secret_key
COS_BUCKET=your_bucket_name
```

### 3. 生成初始化脚本

```bash
# 使用配置的管理员密码生成初始化脚本
source docker.env
./scripts/generate-init-sql.sh
```

### 4. 部署服务

#### 一键部署
```bash
./scripts/deploy.sh
```

#### 手动部署
```bash
# 1. 构建镜像
docker-compose --env-file=docker.env build

# 2. 启动服务
docker-compose --env-file=docker.env up -d

# 3. 查看状态
docker-compose --env-file=docker.env ps
```

#### 验证部署
```bash
# 健康检查
./scripts/test-health.sh

# 查看日志
docker-compose --env-file=docker.env logs -f
```

### 5. 生产环境配置

#### SSL 证书 (Caddy 自动)
```bash
# Caddy 会自动申请 Let's Encrypt 证书
# 确保域名解析正确指向服务器IP
# 确保防火墙开放 80 和 443 端口
```

#### 防火墙配置
```bash
# Ubuntu/Debian
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

## 🔐 管理员密码配置方案

### 配置方式优先级

1. **环境变量** (最高优先级)
2. **配置文件** (`server-go/config/admin.yaml`)
3. **数据库默认值** (最低优先级)

### 配置方法

#### 方法1: 环境变量 (推荐)
```bash
# 设置环境变量
export ADMIN_USERNAME="admin"
export ADMIN_PASSWORD="your_secure_password"

# 或在 docker.env 中配置
echo "ADMIN_USERNAME=admin" >> docker.env
echo "ADMIN_PASSWORD=your_secure_password" >> docker.env
```

#### 方法2: 配置文件
```bash
# 使用设置脚本
./scripts/setup-admin.sh

# 手动创建配置文件
mkdir -p server-go/config
cat > server-go/config/admin.yaml << EOF
admin:
  username: "admin"
  password: "your_secure_password"
  password_policy:
    min_length: 8
    require_uppercase: true
    require_lowercase: true
    require_numbers: true
  security:
    force_password_change: true
    password_expire_days: 90
EOF
```

#### 方法3: 初始化脚本
```bash
# 生成自定义初始化脚本
ADMIN_PASSWORD="your_password" ./scripts/generate-init-sql.sh
```

### 安全建议

1. **强密码策略**
   - 至少8位字符
   - 包含大小写字母、数字
   - 建议包含特殊字符

2. **首次登录**
   - 立即修改默认密码
   - 启用强制密码修改

3. **定期维护**
   - 定期更换密码
   - 监控登录日志
   - 限制登录尝试次数

## 🛠️ 常用管理命令

### 开发环境
```bash
# 重启后端 (修改配置后)
pkill -f "server-go.*main"
cd server-go && go run cmd/main.go

# 重新生成初始化脚本
./scripts/generate-init-sql.sh

# 重置数据库
./scripts/manage-database.sh reset
```

### 生产环境
```bash
# 查看服务状态
docker-compose --env-file=docker.env ps

# 重启服务
docker-compose --env-file=docker.env restart

# 查看日志
docker-compose --env-file=docker.env logs -f [service_name]

# 备份数据库
./scripts/manage-database.sh backup

# 更新服务
docker-compose --env-file=docker.env pull
docker-compose --env-file=docker.env up -d
```

## 🚨 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   ./scripts/check-ports.sh
   
   # 释放端口
   lsof -ti :3001 | xargs kill -9
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   ./scripts/manage-database.sh status
   
   # 重置数据库
   ./scripts/manage-database.sh reset
   ```

3. **CORS 错误**
   ```bash
   # 检查后端 CORS 配置
   grep -A 10 "AllowOrigins" server-go/internal/router/router.go
   
   # 重启后端服务
   ```

### 日志查看

```bash
# 开发环境
tail -f server-go/logs/*.log

# 生产环境
docker-compose --env-file=docker.env logs -f
```

---

*完整部署指南到此结束。如有问题，请参考相关文档或联系技术支持。*
