# 版本化部署指南

## 🎯 概述

现在部署工作流支持指定版本号，可以部署特定版本的构建制品，实现精确的版本控制和回滚功能。

## 📦 制品命名规则

### 构建制品命名
```bash
# 制品名称格式
weizhi-deploy-{version}

# 示例
weizhi-deploy-20241201-143022-a1b2c3d4
weizhi-deploy-20241201-150030-b2c3d4e5
weizhi-deploy-latest
```

### 版本号格式
```bash
# 动态版本号（推荐）
{YYYYMMDD}-{HHMMSS}-{git_sha_short}

# 示例
20241201-143022-a1b2c3d4
20241201-150030-b2c3d4e5

# 固定版本号
latest
v0.1.0
v0.2.0
```

## 🚀 部署使用方法

### 1. **手动部署指定版本**

#### 在 Gitea Actions 界面
```
Actions → 自动化部署 → Run workflow

输入参数：
- 部署环境: production
- 部署服务: all
- 版本号: 20241201-143022-a1b2c3d4  ← 指定具体版本
- 强制部署: false
- 首次部署: false
```

#### 版本号获取方式
1. **从构建日志获取**
   ```bash
   # 在构建工作流的输出中查看
   ✅ 构建完成
   版本号: 20241201-143022-a1b2c3d4
   ```

2. **从 Artifacts 列表获取**
   ```bash
   # 在 Actions → Artifacts 中查看
   weizhi-deploy-20241201-143022-a1b2c3d4
   weizhi-deploy-20241201-150030-b2c3d4e5
   ```

3. **从 Git 提交记录获取**
   ```bash
   # 版本号包含 git commit SHA
   git log --oneline -5
   a1b2c3d4 feat: 添加新功能
   b2c3d4e5 fix: 修复bug
   ```

### 2. **部署最新版本**

#### 使用 latest
```
版本号: latest
```

#### 留空自动获取
```
版本号: (留空)
```

### 3. **版本回滚**

#### 回滚到之前版本
```bash
# 1. 查看历史版本
Actions → Artifacts → 查看可用版本

# 2. 选择要回滚的版本
版本号: 20241130-120000-x1y2z3a4

# 3. 执行部署
强制部署: true  # 跳过健康检查，强制回滚
```

## 🔄 工作流程

### 完整部署流程
```mermaid
graph TD
    A[手动触发部署] --> B[输入版本号]
    B --> C[下载指定版本制品]
    C --> D[验证制品存在]
    D --> E[上传到服务器]
    E --> F[解压制品]
    F --> G[备份当前配置]
    G --> H[部署新版本]
    H --> I[健康检查]
    I --> J[部署完成]
    
    D --> K[制品不存在]
    K --> L[部署失败]
```

### 版本制品对应关系
```bash
# 构建时生成
构建工作流 → weizhi-deploy-20241201-143022-a1b2c3d4.tar.gz

# 部署时下载
部署工作流 → 下载 weizhi-deploy-20241201-143022-a1b2c3d4
           ↓
           解压到服务器: /opt/weizhi/
```

## 📋 版本管理最佳实践

### 1. **版本命名规范**
```bash
# 生产发布版本
v1.0.0, v1.1.0, v2.0.0

# 开发测试版本  
20241201-143022-a1b2c3d4

# 热修复版本
v1.0.1-hotfix, v1.1.1-patch
```

### 2. **部署策略**
```bash
# 正常部署
版本号: 20241201-143022-a1b2c3d4
强制部署: false

# 紧急回滚
版本号: 20241130-120000-x1y2z3a4  # 上一个稳定版本
强制部署: true                     # 跳过健康检查

# 测试部署
版本号: latest
强制部署: false
```

### 3. **版本追踪**
```bash
# 在服务器上查看当前版本
cat /opt/weizhi/VERSION

# 输出示例
VERSION=20241201-143022-a1b2c3d4
BUILD_TIME=2024-12-01T14:30:22Z
COMMIT_SHA=a1b2c3d4e5f6g7h8
BRANCH=main
```

## 🛠️ 故障排除

### 1. **制品不存在错误**
```bash
❌ 制品文件不存在: artifacts/weizhi-deploy-20241201-143022-a1b2c3d4.tar.gz

解决方案：
1. 检查版本号是否正确
2. 确认该版本的构建是否成功
3. 检查 Artifacts 保留期限（默认30天）
```

### 2. **版本号格式错误**
```bash
❌ 无效的版本号格式

正确格式：
- 20241201-143022-a1b2c3d4
- latest
- v1.0.0

错误格式：
- 2024-12-01 14:30:22
- commit-a1b2c3d4
- build-123
```

### 3. **权限问题**
```bash
❌ 无法下载制品

解决方案：
1. 检查 GITHUB_TOKEN 权限
2. 确认 Artifacts 访问权限
3. 验证工作流权限设置
```

## 🎯 使用示例

### 示例1：部署最新版本
```yaml
输入参数：
- 部署环境: production
- 部署服务: all  
- 版本号: latest
- 强制部署: false
- 首次部署: false
```

### 示例2：部署指定版本
```yaml
输入参数：
- 部署环境: production
- 部署服务: web,admin
- 版本号: 20241201-143022-a1b2c3d4
- 强制部署: false
- 首次部署: false
```

### 示例3：紧急回滚
```yaml
输入参数：
- 部署环境: production
- 部署服务: all
- 版本号: 20241130-120000-x1y2z3a4
- 强制部署: true
- 首次部署: false
```

---

通过版本化部署，你可以：
- ✅ 精确控制部署版本
- ✅ 快速回滚到稳定版本  
- ✅ 追踪部署历史
- ✅ 支持多环境部署
- ✅ 实现蓝绿部署策略
