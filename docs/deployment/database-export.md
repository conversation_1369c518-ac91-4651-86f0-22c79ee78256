# 数据库导出脚本使用指南

本文档介绍如何使用数据库导出脚本来导出蔚之领域项目的数据库数据，并自动排除 admin 用户相关数据。

## 📋 脚本概览

项目提供了三个数据库导出脚本：

1. **`export_database.sh`** - 功能完整的导出脚本（Linux/macOS）
2. **`quick_export.sh`** - 快速导出脚本（Linux/macOS）
3. **`export_database.bat`** - Windows 批处理脚本

## 🚀 快速开始

### Linux/macOS 用户

#### 方法一：使用快速导出脚本（推荐）

```bash
# 进入脚本目录
cd server-go/scripts

# 执行快速导出
./quick_export.sh
```

#### 方法二：使用完整导出脚本

```bash
# 使用 Docker 容器导出（推荐）
./export_database.sh --docker

# 或指定连接参数
./export_database.sh -H localhost -P 3307 -u root -p "Ydb3344%"
```

### Windows 用户

```cmd
# 进入脚本目录
cd server-go\scripts

# 执行导出脚本
export_database.bat
```

## 📊 导出内容

### 包含的数据

✅ **角色权限系统**：
- `admin_roles` - 管理员角色定义
- `admin_permissions` - 权限定义
- `admin_role_permissions` - 角色权限关联
- `admin_permission_groups` - 权限分组

✅ **内容数据**：
- `swipers` - 轮播图数据
- `news` - 新闻资讯数据
- `our_services` - 服务项目数据
- `project_cases` - 项目案例数据
- `partners` - 合作伙伴数据
- `friend_links` - 友情链接数据
- `recruitments` - 招聘信息数据
- `part_platform` - 平台数据

### 排除的数据

❌ **Admin 用户相关**：
- `admin_users` 表中 username = 'admin' 的用户数据
- `admin_user_roles` 表中 admin 用户的角色关联
- `admin_logs` 表中 admin 用户的操作日志

## 📁 输出文件

导出脚本会生成以下文件：

### 快速导出脚本输出

```
exports/
├── structure_20250721_161030.sql      # 数据库结构
├── data_no_admin_20250721_161030.sql  # 数据（排除admin）
└── full_no_admin_20250721_161030.sql  # 完整导出（结构+数据）
```

### 完整导出脚本输出

```
server-go/scripts/
├── database_structure.sql             # 数据库结构
├── database_data.sql                  # 数据（排除admin）
└── database_full.sql                  # 完整导出
```

## ⚙️ 详细配置

### export_database.sh 选项

```bash
# 显示帮助
./export_database.sh --help

# 仅导出结构
./export_database.sh --structure-only

# 仅导出数据
./export_database.sh --data-only

# 包含 admin 用户数据
./export_database.sh --include-admin

# 指定输出目录
./export_database.sh -o /path/to/output

# 使用 Docker 容器
./export_database.sh --docker

# 指定数据库连接参数
./export_database.sh -H localhost -P 3307 -u root -p "password" -d weizhi
```

### 环境变量配置

脚本会自动从 `docker.env` 文件读取数据库配置：

```bash
MYSQL_HOST=localhost
MYSQL_PORT=3307
MYSQL_DATABASE=weizhi
MYSQL_USER=weizhi
MYSQL_ROOT_PASSWORD=Ydb3344%
```

## 🔧 使用场景

### 1. 开发环境数据备份

```bash
# 快速备份当前开发数据
./quick_export.sh
```

### 2. 生产环境部署准备

```bash
# 导出用于生产部署的数据（自动排除admin用户）
./export_database.sh --docker
```

### 3. 数据迁移

```bash
# 导出完整数据用于迁移
./export_database.sh --docker -o /backup/migration
```

### 4. 测试环境初始化

```bash
# 导出测试数据
./export_database.sh --structure-only  # 仅结构
./export_database.sh --data-only       # 仅数据
```

## 🛡️ 安全特性

### 自动排除敏感数据

- **默认排除 admin 用户**：防止生产环境中的管理员密码泄露
- **保留角色权限结构**：确保权限系统完整性
- **清理操作日志**：避免敏感操作记录泄露

### 密码保护

- 支持从环境变量读取数据库密码
- 命令行密码参数会被隐藏
- 支持 Docker 容器内部连接（更安全）

## 📝 导入使用

### 导入结构

```bash
# 导入数据库结构
mysql -h localhost -P 3307 -u root -p weizhi < structure_20250721_161030.sql
```

### 导入数据

```bash
# 导入数据（排除admin用户）
mysql -h localhost -P 3307 -u root -p weizhi < data_no_admin_20250721_161030.sql
```

### 导入完整数据库

```bash
# 导入完整数据库
mysql -h localhost -P 3307 -u root -p weizhi < full_no_admin_20250721_161030.sql
```

### 使用 Docker 导入

```bash
# 复制文件到容器
docker cp full_no_admin_20250721_161030.sql weishi-mysql:/tmp/

# 在容器内导入
docker exec weishi-mysql mysql -u root -p"Ydb3344%" weizhi < /tmp/full_no_admin_20250721_161030.sql
```

## 🔍 故障排除

### 常见问题

#### 1. Docker 容器未运行

```bash
# 错误信息
❌ weishi-mysql 容器未运行

# 解决方案
docker-compose up -d mysql
```

#### 2. 数据库连接失败

```bash
# 错误信息
❌ 数据库连接失败

# 检查步骤
1. 确认数据库服务运行正常
2. 检查连接参数（主机、端口、用户名、密码）
3. 确认数据库存在
4. 检查网络连接
```

#### 3. 权限不足

```bash
# 错误信息
Permission denied

# 解决方案
chmod +x export_database.sh
chmod +x quick_export.sh
```

#### 4. 输出目录不存在

```bash
# 脚本会自动创建输出目录
# 如果仍有问题，手动创建：
mkdir -p server-go/scripts/exports
```

### 调试模式

```bash
# 启用详细输出
bash -x ./export_database.sh --docker

# 检查 Docker 容器状态
docker ps | grep weishi-mysql

# 测试数据库连接
docker exec weishi-mysql mysql -u root -p"Ydb3344%" -e "SELECT 1;" weizhi
```

## 💡 最佳实践

### 1. 定期备份

```bash
# 创建定时任务
crontab -e

# 每天凌晨2点备份
0 2 * * * /path/to/project/server-go/scripts/quick_export.sh
```

### 2. 版本控制

```bash
# 为导出文件添加版本标签
./export_database.sh --docker -o /backup/v1.0.0
```

### 3. 压缩存储

```bash
# 导出后压缩
./quick_export.sh
cd server-go/scripts/exports
tar -czf backup_$(date +%Y%m%d).tar.gz *.sql
```

### 4. 验证导出

```bash
# 导出后验证文件完整性
head -10 full_no_admin_*.sql
tail -10 full_no_admin_*.sql
grep -c "INSERT INTO" full_no_admin_*.sql
```

## 🔗 相关文档

- [管理员密码初始化](./admin-password-init.md)
- [数据库部署指南](./database-deployment.md)
- [Docker 部署文档](./docker-deployment.md)
