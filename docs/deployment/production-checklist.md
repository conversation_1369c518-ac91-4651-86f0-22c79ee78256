# 🚀 生产环境部署检查清单

本文档列出了将蔚之领域项目部署到生产环境前需要检查和修改的配置项。

## ✅ 已经配置正确的项目

### 1. 后端配置 ✅
- **数据库连接**: 使用环境变量，无硬编码
- **端口配置**: 通过环境变量配置
- **JWT 密钥**: 通过环境变量配置
- **应用模式**: 支持 production 模式

### 2. 前端配置 ✅
- **API 基础 URL**: 通过环境变量配置
- **Nuxt3 配置**: 支持生产环境部署
- **管理后台**: 构建时配置正确的 API 地址

### 3. Docker 配置 ✅
- **容器化**: 所有服务都已容器化
- **环境变量**: 通过 docker.env 文件配置
- **网络配置**: 使用 Docker 网络

## ⚠️ 需要修改的配置项

### 1. CORS 配置需要更新

**文件**: `server-go/internal/router/router.go`

**当前配置**:
```go
AllowOrigins: []string{
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:3003",
    "http://127.0.0.1:3003",
    "http://localhost:4000",
    "http://127.0.0.1:4000",
    "http://localhost:5173",
    "http://127.0.0.1:5173",
    "http://localhost",
    "https://localhost",
    "http://localhost:8080",
    "https://viclink.cn",        // 生产环境域名
    "https://www.viclink.cn",
},
```

**需要修改**:
- 添加你的实际生产域名
- 移除或注释掉 localhost 相关配置（生产环境不需要）

### 2. Swagger 文档配置

**文件**: `server-go/cmd/main.go`

**当前配置**:
```go
// @host localhost:3001
```

**需要修改**:
```go
// @host your-api-domain.com
```

### 3. 环境变量配置

**文件**: `docker.env`

**需要修改的配置**:

```env
# 生产环境数据库配置
MYSQL_ROOT_PASSWORD=your_secure_production_password
MYSQL_PASSWORD=your_secure_production_password

# 生产环境 JWT 密钥
JWT_SECRET=your_super_secure_production_jwt_key_at_least_32_characters

# 生产环境 API 配置
NUXT_PUBLIC_API_BASE=https://your-domain.com

# 生产环境域名配置
DOMAIN=your-domain.com

# 生产环境端口配置（如果使用标准端口）
CADDY_HTTP_PORT=80
CADDY_HTTPS_PORT=443
MYSQL_PORT=3306
SERVER_PORT=3001
WEB_PORT=3000

# 管理员密码初始化（生产环境建议关闭）
ADMIN_INIT_PASSWORD=false
```

### 4. Caddy 配置

**文件**: `caddy/Caddyfile`

**需要修改**:
```caddyfile
# 修改邮箱为你的邮箱（用于 Let's Encrypt）
{
    email <EMAIL>
}
```

## 🔧 生产环境部署步骤

### 1. 准备环境变量文件

```bash
# 复制环境变量模板
cp docker.env.example docker.env

# 编辑生产环境配置
nano docker.env
```

### 2. 修改 CORS 配置

```bash
# 编辑 CORS 配置文件
nano server-go/internal/router/router.go
```

更新 AllowOrigins 为你的生产域名：
```go
AllowOrigins: []string{
    "https://your-domain.com",
    "https://www.your-domain.com",
},
```

### 3. 修改 Swagger 配置

```bash
# 编辑 Swagger 配置
nano server-go/cmd/main.go
```

更新 @host 为你的 API 域名：
```go
// @host your-domain.com
```

### 4. 配置域名

```bash
# 使用域名配置脚本
./scripts/setup-domain.sh
```

选择选项 1，输入你的实际域名。

### 5. 构建和部署

```bash
# 构建所有服务
docker-compose --env-file=docker.env build

# 启动服务
docker-compose --env-file=docker.env --profile caddy up -d
```

## 🛡️ 安全配置检查

### 1. 密码安全
- ✅ 数据库密码：使用强密码
- ✅ JWT 密钥：至少 32 位随机字符
- ✅ 管理员密码：使用强密码

### 2. 网络安全
- ✅ CORS 配置：仅允许生产域名
- ✅ HTTPS：通过 Caddy 自动配置
- ✅ 防火墙：仅开放必要端口

### 3. 数据安全
- ✅ 数据库：不暴露到公网
- ✅ 日志：不包含敏感信息
- ✅ 备份：定期备份数据

## 📋 部署前检查清单

### 配置文件检查
- [ ] `docker.env` - 生产环境配置
- [ ] `server-go/internal/router/router.go` - CORS 配置
- [ ] `server-go/cmd/main.go` - Swagger 配置
- [ ] `caddy/Caddyfile` - 邮箱配置

### 安全检查
- [ ] 数据库密码已更改
- [ ] JWT 密钥已更改
- [ ] 管理员密码已设置
- [ ] CORS 仅允许生产域名
- [ ] HTTPS 证书配置正确

### 功能检查
- [ ] 数据库连接正常
- [ ] API 接口可访问
- [ ] 前端页面正常
- [ ] 管理后台可登录
- [ ] 文件上传功能正常

### 性能检查
- [ ] 数据库连接池配置
- [ ] 静态资源缓存配置
- [ ] 日志级别设置为 info 或 warn
- [ ] 监控和健康检查配置

## 🚨 常见问题

### 1. CORS 错误
**问题**: 前端无法访问 API
**解决**: 检查 CORS 配置是否包含正确的域名

### 2. 证书问题
**问题**: HTTPS 证书申请失败
**解决**: 检查域名 DNS 解析和邮箱配置

### 3. 数据库连接失败
**问题**: 后端无法连接数据库
**解决**: 检查数据库密码和网络配置

### 4. 管理员无法登录
**问题**: 管理后台登录失败
**解决**: 检查管理员密码初始化配置

## 🤖 自动化配置

### 使用生产环境配置脚本

我们提供了自动化脚本来简化生产环境配置：

```bash
# 检查当前配置
./scripts/setup-production.sh --check-only

# 交互式配置
./scripts/setup-production.sh --interactive

# 直接配置
./scripts/setup-production.sh -d your-domain.com -e <EMAIL>
```

### 脚本功能
- ✅ 自动备份现有配置
- ✅ 更新 CORS 配置
- ✅ 更新 Swagger 配置
- ✅ 更新环境变量
- ✅ 更新 Caddy 配置
- ✅ 生成部署说明

## 📚 相关文档

- [Docker 部署指南](./deployment/02-docker-deployment.md)
- [域名配置指南](./deployment/03-domain-configuration.md)
- [安全配置指南](./deployment/04-security-configuration.md)
- [监控和维护](./deployment/05-monitoring-maintenance.md)
- [数据库导出指南](./database-export.md)
- [管理员密码初始化](./admin-password-init.md)
