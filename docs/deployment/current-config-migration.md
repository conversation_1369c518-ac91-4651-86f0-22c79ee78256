# 🔄 当前配置迁移指南

## 📋 **基于你当前配置的 Gitea 设置**

根据你提供的线上环境变量，以下是需要在 Gitea 中配置的变量和密钥：

## 🔐 **Gitea Secrets 配置**

### **数据库密钥**
```bash
# 数据库密码（基于你当前的配置）
MYSQL_ROOT_PASSWORD=Ydb3344%
MYSQL_PASSWORD=Ydb3344%

# JWT 密钥
JWT_SECRET=your-super-secret-jwt-key-change-in-production-environment12312

# 管理员密码
ADMIN_PASSWORD=admin123
```

### **SSH 连接密钥**
```bash
# 生产服务器 SSH 私钥
PROD_SSH_PRIVATE_KEY=-----BEGIN OPENSSH PRIVATE KEY-----
# 你的 SSH 私钥内容
-----END OPENSSH PRIVATE KEY-----
```

### **可选的第三方服务密钥**
```bash
# 腾讯云COS密钥（如果使用）
COS_SECRET_ID=your_cos_secret_id
COS_SECRET_KEY=your_cos_secret_key
```

## 🌍 **Gitea Variables 配置**

### **镜像仓库配置**
```bash
# 基于你当前的配置
REGISTRY_URL=registry.cn-hangzhou.aliyuncs.com
NAMESPACE=vest
```

### **服务器配置**
```bash
# 生产服务器信息（需要你提供）
DEPLOY_HOST=your.production.server.ip
DEPLOY_USER=your_deploy_user
DEPLOY_PATH=/opt/weishi
```

### **域名配置**
```bash
# 基于你当前的配置
PROD_DOMAIN=viclink.cn
SSL_EMAIL=<EMAIL>
```

### **数据库配置**
```bash
# 基于你当前的配置
MYSQL_DATABASE=weizhi
MYSQL_USER=weishi
MYSQL_PORT=3307
```

### **应用配置**
```bash
# 基于你当前的配置
ADMIN_USERNAME=admin
ADMIN_INIT_PASSWORD=true
IMPORT_SEED_DATA=false
IMPORT_ADMIN_DATA=false
```

### **文件上传配置**
```bash
# 基于你当前的配置
MAX_FILE_SIZE=52428800
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp,application/pdf,text/plain
```

### **腾讯云COS配置**
```bash
# 基于你当前的配置
COS_REGION=ap-nanjing
COS_BUCKET=upload-1305444037
COS_DOMAIN=your_cos_domain_if_any
```

## 🚀 **迁移步骤**

### **1. 在 Gitea 中配置 Secrets**
1. 进入仓库设置 → Secrets
2. 添加上述 5 个核心 Secrets
3. 确保密钥值正确无误

### **2. 在 Gitea 中配置 Variables**
1. 进入仓库设置 → Variables  
2. 添加上述 Variables
3. 根据你的实际服务器信息调整 `PROD_DEPLOY_*` 配置

### **3. 测试配置生成**
```bash
# 在本地测试配置生成
export REGISTRY_URL="registry.cn-hangzhou.aliyuncs.com"
export NAMESPACE="vest"
export PROD_DOMAIN="viclink.cn"
export SSL_EMAIL="<EMAIL>"
export MYSQL_DATABASE="weizhi"
export MYSQL_USER="weishi"
export MYSQL_PORT="3307"

# 生成配置文件
deployment/scripts/generate-config.sh production -o test-production.env

# 检查生成的配置
cat test-production.env
```

### **4. 部署测试**
1. 确保服务器 SSH 连接正常
2. 运行 "自动化部署" 工作流
3. 选择要部署的服务
4. 监控部署过程

## 🔍 **配置对比**

| 配置项 | 当前值 | 新系统处理方式 |
|--------|--------|----------------|
| REGISTRY_URL | registry.cn-hangzhou.aliyuncs.com | Gitea Variable |
| NAMESPACE | vest | Gitea Variable |
| MYSQL_PORT | 3307 | Gitea Variable |
| DOMAIN | viclink.cn | Gitea Variable |
| MYSQL_ROOT_PASSWORD | Ydb3344% | Gitea Secret |
| JWT_SECRET | your-super-secret... | Gitea Secret |
| 其他配置 | 自动生成 | 配置模板 |

## ⚠️ **注意事项**

### **1. 密钥安全**
- 确保 Gitea Secrets 中的密码与当前生产环境一致
- 不要在配置模板中硬编码敏感信息

### **2. 服务器配置**
- 确保部署服务器的 SSH 连接正常
- 确保部署用户有 Docker 操作权限

### **3. 域名配置**
- 确保域名解析指向正确的服务器
- SSL 证书会自动申请和续期

### **4. 数据库**
- 新系统会使用你现有的数据库配置
- 确保数据库连接信息正确

## 🎯 **优势**

使用新的配置管理系统后：

- ✅ **配置统一管理** - 所有配置在 Gitea 中集中管理
- ✅ **自动化部署** - 一键部署，减少人工错误
- ✅ **配置验证** - 自动检查配置完整性
- ✅ **版本控制** - 配置变更可追踪
- ✅ **安全隔离** - 敏感信息与代码分离

## 📞 **需要确认的信息**

请提供以下信息以完成配置：

1. **生产服务器信息**：
   - 服务器 IP 地址
   - SSH 用户名
   - SSH 私钥

2. **腾讯云COS**（如果使用）：
   - COS_SECRET_ID
   - COS_SECRET_KEY
   - 自定义域名（如果有）

3. **其他第三方服务**（如果使用）：
   - 短信服务配置
   - 邮件服务配置

配置完成后，你就可以享受自动化部署的便利了！🚀
