# 🐳 Docker 容器化部署

本文档详细介绍如何使用 Docker 和 Docker Compose 部署蔚之领域智能科技项目。

## 📋 Docker 架构概览

### 容器架构图

```mermaid
graph TB
    subgraph "Docker Host"
        subgraph "Web Container"
            A[Nuxt3 App]
            A1[Node.js Runtime]
        end
        
        subgraph "Admin Container"
            B[Vue3 App]
            B1[Nginx Server]
        end
        
        subgraph "Server Container"
            C[Go API Server]
            C1[Gin Framework]
        end
        
        subgraph "Database Container"
            D[MySQL 8.0]
            D1[Data Volume]
        end
        
        subgraph "Proxy Container"
            E[Caddy Server]
            E1[SSL Termination]
        end
    end
    
    E --> A
    E --> B
    E --> C
    C --> D
    D1 --> D
```

### 容器特性

| 容器 | 基础镜像 | 端口 | 数据卷 | 重启策略 |
|------|----------|------|--------|----------|
| web | node:18-alpine | 4000 | - | unless-stopped |
| admin | nginx:alpine | 3001 | - | unless-stopped |
| server-go | alpine:latest | 3001 | logs/ | unless-stopped |
| mysql | mysql:8.0 | 3306 | mysql_data | unless-stopped |
| caddy | caddy:alpine | 80,443 | caddy_data | unless-stopped |

## 🏗️ Dockerfile 配置

### Web 前端 Dockerfile

**文件位置**: `web/Dockerfile`

```dockerfile
# 构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制 package 文件
COPY package*.json ./
COPY pnpm-lock.yaml ./

# 安装 pnpm
RUN npm install -g pnpm

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 构建应用
RUN pnpm build

# 生产阶段
FROM node:18-alpine AS runner

WORKDIR /app

# 创建非 root 用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nuxt

# 复制构建产物
COPY --from=builder --chown=nuxt:nodejs /app/.output ./.output
COPY --from=builder --chown=nuxt:nodejs /app/package.json ./package.json

# 切换到非 root 用户
USER nuxt

# 暴露端口
EXPOSE 4000

# 设置环境变量
ENV NODE_ENV=production
ENV NUXT_HOST=0.0.0.0
ENV NUXT_PORT=4000

# 启动应用
CMD ["node", ".output/server/index.mjs"]
```

### 管理后台 Dockerfile

**文件位置**: `admin/Dockerfile`

```dockerfile
# 构建阶段
FROM node:18-alpine AS builder

WORKDIR /app

# 复制 package 文件
COPY package*.json ./
COPY pnpm-lock.yaml ./

# 安装 pnpm
RUN npm install -g pnpm

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 构建应用
RUN pnpm build

# 生产阶段
FROM nginx:alpine AS runner

# 复制构建产物到 nginx 目录
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制 nginx 配置
COPY nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 启动 nginx
CMD ["nginx", "-g", "daemon off;"]
```

**Nginx 配置** (`admin/nginx.conf`):

```nginx
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    sendfile        on;
    keepalive_timeout  65;
    
    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;
        
        # 处理 SPA 路由
        location / {
            try_files $uri $uri/ /index.html;
        }
        
        # API 代理
        location /api/ {
            proxy_pass http://server-go:3001;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

### 后端服务 Dockerfile

**文件位置**: `server-go/Dockerfile`

```dockerfile
# 构建阶段
FROM golang:1.23-alpine AS builder

# 安装必要工具
RUN apk add --no-cache git ca-certificates tzdata

# 设置工作目录
WORKDIR /app

# 复制 go mod 文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o main cmd/main.go

# 生产阶段
FROM alpine:latest

# 安装必要工具
RUN apk --no-cache add ca-certificates tzdata

# 设置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' > /etc/timezone

WORKDIR /root/

# 复制二进制文件和配置
COPY --from=builder /app/main .
COPY --from=builder /app/config.yaml .

# 创建必要目录
RUN mkdir -p logs

# 暴露端口
EXPOSE 3001

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:3001/api/health || exit 1

# 启动应用
CMD ["./main"]
```

## 📝 Docker Compose 配置

### 开发环境配置

**文件位置**: `docker-compose.dev.yml`

```yaml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: weishi-mysql-dev
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    ports:
      - "3306:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./server-go/scripts:/docker-entrypoint-initdb.d:ro
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - weishi-network

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: weishi-phpmyadmin-dev
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: ${MYSQL_ROOT_PASSWORD}
    ports:
      - "8080:80"
    depends_on:
      - mysql
    networks:
      - weishi-network

  redis:
    image: redis:7-alpine
    container_name: weishi-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - weishi-network

volumes:
  mysql_dev_data:
  redis_dev_data:

networks:
  weishi-network:
    driver: bridge
```

### 生产环境配置

**文件位置**: `docker-compose.yml`

```yaml
version: '3.8'

services:
  web:
    build:
      context: ./web
      dockerfile: Dockerfile
    container_name: weishi-web
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - NUXT_PUBLIC_API_BASE=http://server-go:3001
    ports:
      - "${WEB_PORT:-4000}:4000"
    depends_on:
      - server-go
    networks:
      - weishi-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.web.rule=Host(`${DOMAIN}`)"

  admin:
    build:
      context: ./admin
      dockerfile: Dockerfile
    container_name: weishi-admin
    restart: unless-stopped
    environment:
      - NODE_ENV=production
    ports:
      - "${ADMIN_PORT:-3001}:80"
    depends_on:
      - server-go
    networks:
      - weishi-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.admin.rule=Host(`admin.${DOMAIN}`)"

  server-go:
    build:
      context: ./server-go
      dockerfile: Dockerfile
    container_name: weishi-server
    restart: unless-stopped
    environment:
      - GO_ENV=production
    ports:
      - "${SERVER_PORT:-3001}:3001"
    volumes:
      - ./server-go/logs:/root/logs
      - ./server-go/config.yaml:/root/config.yaml:ro
    depends_on:
      - mysql
    networks:
      - weishi-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.api.rule=Host(`api.${DOMAIN}`)"

  mysql:
    image: mysql:8.0
    container_name: weishi-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./server-go/scripts:/docker-entrypoint-initdb.d:ro
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - weishi-network

  caddy:
    image: caddy:alpine
    container_name: weishi-caddy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./caddy/Caddyfile:/etc/caddy/Caddyfile:ro
      - caddy_data:/data
      - caddy_config:/config
    depends_on:
      - web
      - admin
      - server-go
    networks:
      - weishi-network
    profiles:
      - caddy

volumes:
  mysql_data:
  caddy_data:
  caddy_config:

networks:
  weishi-network:
    driver: bridge
```

## 🔧 环境变量配置

### 环境变量文件

**文件位置**: `docker.env`

```env
# 应用配置
NODE_ENV=production
GO_ENV=production

# 数据库配置
MYSQL_ROOT_PASSWORD=your_secure_root_password
MYSQL_DATABASE=weizhi
MYSQL_USER=weishi
MYSQL_PASSWORD=your_secure_db_password

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# 服务端口配置
WEB_PORT=4000
ADMIN_PORT=3001
SERVER_PORT=3001

# 域名配置
DOMAIN=your-domain.com

# 文件存储配置
COS_SECRET_ID=your_cos_secret_id
COS_SECRET_KEY=your_cos_secret_key
COS_REGION=ap-nanjing
COS_BUCKET=your_bucket_name
```

### 环境变量说明

| 变量名 | 默认值 | 说明 | 必需 |
|--------|--------|------|------|
| MYSQL_ROOT_PASSWORD | - | MySQL root 密码 | ✅ |
| MYSQL_DATABASE | weizhi | 数据库名称 | ✅ |
| MYSQL_USER | weishi | 数据库用户名 | ✅ |
| MYSQL_PASSWORD | - | 数据库密码 | ✅ |
| JWT_SECRET | - | JWT 签名密钥 | ✅ |
| WEB_PORT | 4000 | 前端服务端口 | ❌ |
| ADMIN_PORT | 3001 | 管理后台端口 | ❌ |
| SERVER_PORT | 3001 | 后端服务端口 | ❌ |
| DOMAIN | localhost | 域名 | ❌ |

## 🚀 部署操作

### 1. 快速部署

```bash
# 克隆项目
git clone <repository_url>
cd Nuxt3Web

# 配置环境变量
cp docker.env .env
vim .env  # 编辑配置

# 一键部署
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

### 2. 手动部署

```bash
# 构建并启动所有服务
docker compose --env-file=.env up -d --build

# 查看服务状态
docker compose ps

# 查看日志
docker compose logs -f
```

### 3. 分步部署

```bash
# 1. 启动数据库
docker compose up -d mysql

# 2. 等待数据库就绪
docker compose logs mysql

# 3. 启动后端服务
docker compose up -d server-go

# 4. 启动前端服务
docker compose up -d web admin

# 5. 启动反向代理（可选）
docker compose --profile caddy up -d caddy
```

## 🔍 容器管理

### 常用命令

```bash
# 查看容器状态
docker compose ps

# 查看容器日志
docker compose logs <service_name>
docker compose logs -f --tail=100 <service_name>

# 进入容器
docker compose exec <service_name> sh

# 重启服务
docker compose restart <service_name>

# 停止服务
docker compose stop <service_name>

# 删除服务
docker compose down <service_name>

# 重新构建镜像
docker compose build --no-cache <service_name>
```

### 数据管理

```bash
# 备份数据库
docker compose exec mysql mysqldump -u root -p${MYSQL_ROOT_PASSWORD} weizhi > backup.sql

# 恢复数据库
docker compose exec -T mysql mysql -u root -p${MYSQL_ROOT_PASSWORD} weizhi < backup.sql

# 查看数据卷
docker volume ls

# 备份数据卷
docker run --rm -v weishi_mysql_data:/data -v $(pwd):/backup alpine tar czf /backup/mysql_backup.tar.gz -C /data .

# 恢复数据卷
docker run --rm -v weishi_mysql_data:/data -v $(pwd):/backup alpine tar xzf /backup/mysql_backup.tar.gz -C /data
```

## 📊 监控和调试

### 容器监控

```bash
# 查看容器资源使用
docker stats

# 查看容器详细信息
docker inspect <container_name>

# 查看容器进程
docker compose top

# 查看网络信息
docker network ls
docker network inspect weishi_weishi-network
```

### 健康检查

```bash
# 检查服务健康状态
curl http://localhost:3001/api/health
curl http://localhost:4000
curl http://localhost:3001

# 检查数据库连接
docker compose exec mysql mysql -u root -p${MYSQL_ROOT_PASSWORD} -e "SELECT 1"

# 检查容器日志
docker compose logs --tail=50 server-go
```

### 故障排除

```bash
# 查看构建日志
docker compose build --no-cache --progress=plain

# 查看容器启动失败原因
docker compose logs <service_name>

# 检查配置文件
docker compose config

# 清理无用资源
docker system prune -a
docker volume prune
```

## 🔒 安全配置

### 容器安全

```yaml
# 在 docker-compose.yml 中添加安全配置
services:
  server-go:
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
    user: "1001:1001"
```

### 网络安全

```yaml
# 网络隔离
networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
    internal: true
```

### 资源限制

```yaml
services:
  server-go:
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
```

---

*Docker 部署完成后，请参考 [域名配置文档](./03-domain-configuration.md) 进行域名和SSL配置。*
