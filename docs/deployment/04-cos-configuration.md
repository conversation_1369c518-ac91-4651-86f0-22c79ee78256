# 📁 腾讯云COS文件存储配置指南

本文档介绍如何配置腾讯云COS（Cloud Object Storage）用于项目的文件上传和存储功能。

## 📋 概述

项目集成了腾讯云COS服务，提供：
- **文件上传**: 支持图片、文档等多种文件类型
- **文件管理**: 自动文件命名、大小限制、类型验证
- **CDN加速**: 可配置自定义域名和CDN加速
- **安全控制**: 基于JWT的上传权限控制

## 🔧 配置步骤

### 1. 创建腾讯云账号和COS服务

1. **注册腾讯云账号**
   - 访问 [腾讯云官网](https://cloud.tencent.com/)
   - 注册并完成实名认证

2. **开通COS服务**
   - 登录腾讯云控制台
   - 搜索"对象存储COS"并开通服务

### 2. 创建存储桶

1. **进入COS控制台**
   - 访问 [COS控制台](https://console.cloud.tencent.com/cos)

2. **创建存储桶**
   - 点击"创建存储桶"
   - 填写存储桶名称（如：`weishi-files`）
   - 选择地域（建议选择离服务器最近的地域）
   - 访问权限选择"私有读写"或"公有读私有写"

3. **记录存储桶信息**
   - 存储桶名称：`your-bucket-name`
   - 地域：`ap-nanjing`（根据实际选择）

### 3. 获取API密钥

1. **进入访问管理**
   - 访问 [访问管理控制台](https://console.cloud.tencent.com/cam)

2. **创建API密钥**
   - 点击"API密钥管理"
   - 点击"新建密钥"
   - 记录SecretId和SecretKey

### 4. 配置项目环境变量

编辑 `docker.env` 文件，填写COS配置：

```env
# 腾讯云COS配置（文件存储）
COS_SECRET_ID=AKIDxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
COS_SECRET_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
COS_REGION=ap-nanjing
COS_BUCKET=your-bucket-name
COS_DOMAIN=your-custom-domain.com

# 文件上传配置
MAX_FILE_SIZE=52428800
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp,application/pdf,text/plain
```

### 5. 配置自定义域名（可选）

1. **绑定自定义域名**
   - 在COS控制台选择存储桶
   - 点击"域名与传输管理" -> "自定义CDN加速域名"
   - 添加自定义域名

2. **配置DNS解析**
   - 在域名服务商处添加CNAME记录
   - 指向腾讯云提供的CDN域名

3. **更新配置**
   ```env
   COS_DOMAIN=files.your-domain.com
   ```

## 🔍 配置验证

### 1. 运行配置检查脚本

```bash
./scripts/test-cos.sh
```

### 2. 检查项目

- ✅ COS配置完整性
- ✅ 后端服务状态
- ✅ 文件上传API端点
- ✅ 配置参数格式

### 3. 测试文件上传

1. **启动项目**
   ```bash
   docker-compose --env-file=docker.env up -d
   ```

2. **访问管理后台**
   - 地址：http://localhost:5173
   - 登录管理员账号

3. **测试上传功能**
   - 在内容管理中上传图片
   - 检查COS存储桶中是否有文件

## 📊 配置参数说明

| 参数 | 说明 | 示例值 |
|------|------|--------|
| COS_SECRET_ID | 腾讯云API密钥ID | AKIDxxxxxxxx |
| COS_SECRET_KEY | 腾讯云API密钥Key | xxxxxxxx |
| COS_REGION | COS地域 | ap-nanjing |
| COS_BUCKET | 存储桶名称 | weishi-files |
| COS_DOMAIN | 自定义域名（可选） | files.domain.com |
| MAX_FILE_SIZE | 最大文件大小（字节） | 52428800 (50MB) |
| ALLOWED_FILE_TYPES | 允许的文件类型 | image/jpeg,image/png |

## 🔒 安全建议

### 1. 访问权限控制

- **存储桶权限**: 建议设置为"私有读写"
- **API密钥**: 定期更换SecretKey
- **跨域配置**: 仅允许项目域名访问

### 2. 成本控制

- **生命周期管理**: 设置文件自动删除策略
- **存储类型**: 根据访问频率选择合适的存储类型
- **CDN配置**: 合理配置CDN缓存策略

### 3. 监控告警

- **用量监控**: 设置存储用量告警
- **费用监控**: 设置费用告警阈值
- **访问日志**: 开启访问日志记录

## 🚨 故障排除

### 常见问题

1. **上传失败：权限不足**
   - 检查SecretId和SecretKey是否正确
   - 确认存储桶访问权限设置

2. **上传失败：网络错误**
   - 检查服务器网络连接
   - 确认COS地域配置正确

3. **文件访问失败**
   - 检查存储桶访问权限
   - 确认自定义域名配置

### 调试命令

```bash
# 检查COS配置
./scripts/test-cos.sh

# 查看后端日志
docker-compose logs server

# 测试API连通性
curl -X GET http://localhost:3001/api/health
```

## 📚 相关文档

- [腾讯云COS官方文档](https://cloud.tencent.com/document/product/436)
- [COS API文档](https://cloud.tencent.com/document/product/436/7751)
- [项目文件上传API文档](../api/02-file-upload-api.md)

---

*配置完成后，请运行 `./scripts/test-cos.sh` 验证配置是否正确。*
