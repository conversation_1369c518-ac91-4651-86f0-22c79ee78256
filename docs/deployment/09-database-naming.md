# 🗄️ 数据库命名统一说明

本文档说明项目中数据库命名的统一规范。

## 📋 统一命名

### 数据库名称
项目统一使用 **`weizhi`** 作为数据库名称。

### 历史变更
- **之前**: 混合使用 `weishi_db`、`weishi_db1`、`weizhi`
- **现在**: 统一使用 `weizhi`
- **原因**: 保持与现有数据的一致性，简化配置管理

## 🔧 配置文件统一

### 1. Go 后端配置
```yaml
# server-go/config.yaml
database:
  host: "localhost"
  port: "3306"
  username: "root"
  password: "Ydb3344%"
  database: "weizhi"  # ✅ 统一使用 weizhi
  charset: "utf8mb4"
```

### 2. 环境变量配置
```env
# docker.env
MYSQL_DATABASE=weizhi  # ✅ 统一使用 weizhi

# docker.env.example
MYSQL_DATABASE=weizhi  # ✅ 统一使用 weizhi
```

### 3. 数据库初始化脚本
```sql
-- server-go/scripts/init_database.sql
CREATE DATABASE weizhi CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE weizhi;
```

## 🛠️ 管理脚本统一

### 1. 导出脚本
```bash
# scripts/export-database.sh
DB_NAME="weizhi"  # ✅ 源数据库名

# scripts/generate-init-sql.sh
# 不再需要数据库名称转换，直接使用 weizhi
```

### 2. 管理脚本
```bash
# scripts/manage-database.sh
DB_NAME="weizhi"        # ✅ 源数据库名
TARGET_DB_NAME="weizhi" # ✅ 目标数据库名（统一）

# scripts/setup-admin.sh
DB_NAME="weizhi"  # ✅ 统一使用 weizhi
```

## 📊 部署环境统一

### 开发环境
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE weizhi CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'weizhi'@'localhost' IDENTIFIED BY 'Ydb3344%';
GRANT ALL PRIVILEGES ON weizhi.* TO 'weizhi'@'localhost';

# 导入数据
mysql -u weizhi -p weizhi < server-go/scripts/init_database.sql
```

### 生产环境 (Docker)
```yaml
# docker-compose.yml
services:
  mysql:
    environment:
      MYSQL_DATABASE: weizhi  # ✅ 统一使用 weizhi
    volumes:
      - ./server-go/scripts/init_database.sql:/docker-entrypoint-initdb.d/01-init_database.sql
```

## 🔄 迁移指南

如果你之前使用了其他数据库名称，需要进行迁移：

### 1. 备份现有数据
```bash
# 备份现有数据库
mysqldump -u root -p weishi_db > backup_weishi_db.sql
mysqldump -u root -p weishi_db1 > backup_weishi_db1.sql
```

### 2. 创建新数据库
```bash
# 创建 weizhi 数据库
mysql -u root -p
CREATE DATABASE weizhi CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
EXIT;
```

### 3. 导入数据
```bash
# 方式1: 从备份导入
mysql -u root -p weizhi < backup_weishi_db.sql

# 方式2: 使用初始化脚本
mysql -u root -p weizhi < server-go/scripts/init_database.sql
```

### 4. 更新配置
```bash
# 更新 Go 后端配置
vim server-go/config.yaml
# 确保 database: "weizhi"

# 更新环境变量
vim docker.env
# 确保 MYSQL_DATABASE=weizhi
```

### 5. 删除旧数据库（可选）
```bash
mysql -u root -p
DROP DATABASE IF EXISTS weishi_db;
DROP DATABASE IF EXISTS weishi_db1;
EXIT;
```

## 🔍 验证统一性

### 1. 配置检查
```bash
# 运行配置验证
./scripts/test-config.sh

# 检查数据库状态
./scripts/manage-database.sh status
```

### 2. 手动验证
```bash
# 检查配置文件
grep -r "database.*:" server-go/config.yaml
grep -r "MYSQL_DATABASE" docker.env*

# 检查脚本文件
grep -r "DB_NAME" scripts/
grep -r "weizhi\|weishi_db" scripts/
```

### 3. 数据库连接测试
```bash
# 测试数据库连接
mysql -u weishi -p weizhi -e "SHOW TABLES;"

# 检查管理员账号
mysql -u weishi -p weizhi -e "SELECT username FROM admin_users;"
```

## 📝 最佳实践

### 1. 命名规范
- **数据库名**: 使用项目简称，小写字母
- **表名**: 使用下划线分隔的小写字母
- **字段名**: 使用下划线分隔的小写字母

### 2. 配置管理
- 所有配置文件使用统一的数据库名称
- 环境变量优先级高于配置文件
- 生产环境和开发环境使用相同的数据库名称

### 3. 脚本管理
- 所有管理脚本使用统一的数据库名称
- 避免硬编码数据库名称，使用变量
- 提供数据库名称验证功能

## 🚨 注意事项

### 1. 数据一致性
- 确保所有环境使用相同的数据库名称
- 迁移时注意数据完整性
- 备份重要数据

### 2. 配置同步
- 修改配置后重启相关服务
- 验证配置生效
- 更新相关文档

### 3. 团队协作
- 通知团队成员数据库名称变更
- 更新开发环境配置
- 同步部署文档

## 📋 检查清单

部署前请确认：

- [ ] 所有配置文件使用 `weizhi` 数据库名
- [ ] 环境变量配置正确
- [ ] 初始化脚本使用正确的数据库名
- [ ] 管理脚本配置统一
- [ ] 数据库连接测试通过
- [ ] 备份现有数据（如有）
- [ ] 团队成员已同步更新

---

*数据库命名统一后，所有环境将使用 `weizhi` 作为数据库名称，简化了配置管理和部署流程。*
