# 🚀 部署指南

本文档介绍蔚之领域智能科技项目的完整部署流程，包括开发环境、测试环境和生产环境的部署方案。

## 📋 部署概览

### 部署架构

```mermaid
graph TB
    subgraph "用户访问层"
        A[用户浏览器]
        B[移动端应用]
    end
    
    subgraph "负载均衡层"
        C[Caddy反向代理]
        D[SSL证书]
    end
    
    subgraph "应用服务层"
        E[企业官网 - Nuxt3]
        F[管理后台 - Vue3]
        G[后端API - Go]
    end
    
    subgraph "数据存储层"
        H[MySQL数据库]
        I[文件存储 - COS]
        J[日志存储]
    end
    
    A --> C
    B --> C
    C --> D
    C --> E
    C --> F
    C --> G
    G --> H
    G --> I
    G --> J
```

### 部署方式对比

| 部署方式 | 适用场景 | 优势 | 劣势 |
|----------|----------|------|------|
| Docker Compose | 开发/测试环境 | 简单快速、环境一致 | 单机部署、扩展性有限 |
| Kubernetes | 生产环境 | 高可用、自动扩缩容 | 复杂度高、学习成本大 |
| 传统部署 | 简单场景 | 直接控制、调试方便 | 环境依赖、维护复杂 |

## 🔧 环境要求

### 硬件要求

#### 最低配置
- **CPU**: 2核心
- **内存**: 4GB
- **存储**: 20GB
- **网络**: 10Mbps

#### 推荐配置
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **存储**: 50GB以上 SSD
- **网络**: 100Mbps以上

### 软件要求

| 软件 | 版本要求 | 用途 |
|------|----------|------|
| Docker | >= 20.0 | 容器运行时 |
| Docker Compose | >= 2.0 | 容器编排 |
| Git | >= 2.30 | 代码管理 |
| 操作系统 | Ubuntu 20.04+ / CentOS 8+ | 服务器环境 |

### 网络要求

- **域名**: 已备案的域名（生产环境）
- **SSL证书**: 有效的SSL证书
- **端口开放**: 80、443、3001、3306等

## 🐳 Docker 部署

### 1. 环境准备

#### 安装 Docker

**Ubuntu/Debian**:
```bash
# 更新包索引
sudo apt update

# 安装依赖
sudo apt install apt-transport-https ca-certificates curl gnupg lsb-release

# 添加 Docker 官方 GPG 密钥
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 添加 Docker 仓库
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 安装 Docker
sudo apt update
sudo apt install docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 启动 Docker 服务
sudo systemctl start docker
sudo systemctl enable docker

# 添加用户到 docker 组
sudo usermod -aG docker $USER
```

**CentOS/RHEL**:
```bash
# 安装依赖
sudo yum install -y yum-utils

# 添加 Docker 仓库
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo

# 安装 Docker
sudo yum install docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 启动 Docker 服务
sudo systemctl start docker
sudo systemctl enable docker
```

#### 验证安装

```bash
# 检查 Docker 版本
docker --version

# 检查 Docker Compose 版本
docker compose version

# 测试 Docker 运行
docker run hello-world
```

### 2. 项目部署

#### 获取项目代码

```bash
# 克隆项目
git clone <项目仓库地址>
cd Nuxt3Web

# 检查项目结构
ls -la
```

#### 配置环境变量

```bash
# 复制环境变量模板
cp docker.env .env

# 编辑环境变量
vim .env
```

**关键环境变量配置**:

```env
# 数据库配置
MYSQL_ROOT_PASSWORD=your_secure_password
MYSQL_DATABASE=weizhi
MYSQL_USER=weishi
MYSQL_PASSWORD=your_db_password

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# 服务端口
SERVER_PORT=3001
WEB_PORT=4000
ADMIN_PORT=3001

# 域名配置（生产环境）
DOMAIN=your-domain.com
```

#### 一键部署

```bash
# 给部署脚本执行权限
chmod +x scripts/deploy.sh

# 执行部署
./scripts/deploy.sh
```

#### 手动部署

```bash
# 构建并启动所有服务
docker compose --env-file=.env up -d --build

# 查看服务状态
docker compose --env-file=.env ps

# 查看日志
docker compose --env-file=.env logs -f
```

### 3. 服务验证

#### 检查服务状态

```bash
# 查看所有容器状态
docker compose ps

# 查看特定服务日志
docker compose logs web
docker compose logs server-go
docker compose logs mysql

# 检查服务健康状态
curl http://localhost:3001/api/health
curl http://localhost:4000
```

#### 访问服务

| 服务 | 地址 | 说明 |
|------|------|------|
| 企业官网 | http://localhost:4000 | 前台展示网站 |
| 管理后台 | http://localhost:3001 | 后台管理系统 |
| 后端API | http://localhost:3001/api | API服务 |
| API文档 | http://localhost:3001/swagger/index.html | Swagger文档 |
| 数据库 | localhost:3306 | MySQL数据库 |

## 🌐 生产环境部署

### 1. 服务器配置

#### 系统优化

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 配置防火墙
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443

# 配置时区
sudo timedatectl set-timezone Asia/Shanghai

# 优化内核参数
echo 'net.core.somaxconn = 65535' | sudo tee -a /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 65535' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

#### 安装必要软件

```bash
# 安装基础工具
sudo apt install -y curl wget git vim htop

# 安装 Docker 和 Docker Compose
# (参考上面的安装步骤)

# 安装 Nginx (可选，用于反向代理)
sudo apt install -y nginx
```

### 2. 域名和SSL配置

#### 域名解析

```bash
# 配置 A 记录指向服务器IP
# 主域名: your-domain.com -> 服务器IP
# 子域名: api.your-domain.com -> 服务器IP
# 子域名: admin.your-domain.com -> 服务器IP
```

#### SSL证书配置

**使用 Let's Encrypt**:

```bash
# 安装 Certbot
sudo apt install -y certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com -d api.your-domain.com -d admin.your-domain.com

# 设置自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. 生产环境配置

#### 环境变量配置

```env
# 生产环境配置
NODE_ENV=production
GO_ENV=production

# 数据库配置（使用强密码）
MYSQL_ROOT_PASSWORD=your_very_secure_root_password
MYSQL_PASSWORD=your_very_secure_db_password

# JWT 配置（使用强密钥）
JWT_SECRET=your-super-long-and-complex-jwt-secret-key

# 域名配置
DOMAIN=your-domain.com
API_DOMAIN=api.your-domain.com
ADMIN_DOMAIN=admin.your-domain.com

# 文件存储配置
COS_SECRET_ID=your_cos_secret_id
COS_SECRET_KEY=your_cos_secret_key
COS_REGION=ap-nanjing
COS_BUCKET=your_bucket_name
```

#### Docker Compose 生产配置

创建 `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  web:
    build:
      context: ./web
      dockerfile: Dockerfile
    restart: unless-stopped
    environment:
      - NODE_ENV=production
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.web.rule=Host(`${DOMAIN}`)"
      - "traefik.http.routers.web.tls.certresolver=letsencrypt"

  admin:
    build:
      context: ./admin
      dockerfile: Dockerfile
    restart: unless-stopped
    environment:
      - NODE_ENV=production
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.admin.rule=Host(`${ADMIN_DOMAIN}`)"
      - "traefik.http.routers.admin.tls.certresolver=letsencrypt"

  server-go:
    build:
      context: ./server-go
      dockerfile: Dockerfile
    restart: unless-stopped
    environment:
      - GO_ENV=production
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.api.rule=Host(`${API_DOMAIN}`)"
      - "traefik.http.routers.api.tls.certresolver=letsencrypt"

  mysql:
    image: mysql:8.0
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./server-go/scripts:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password

volumes:
  mysql_data:
```

#### 部署到生产环境

```bash
# 使用生产配置部署
docker compose -f docker-compose.prod.yml --env-file=.env up -d --build

# 查看服务状态
docker compose -f docker-compose.prod.yml ps

# 查看日志
docker compose -f docker-compose.prod.yml logs -f
```

## 📊 监控和维护

### 1. 日志管理

```bash
# 查看容器日志
docker compose logs -f --tail=100

# 查看特定服务日志
docker compose logs web -f
docker compose logs server-go -f

# 日志轮转配置
sudo vim /etc/docker/daemon.json
```

```json
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
```

### 2. 备份策略

#### 数据库备份

```bash
# 创建备份脚本
cat > backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup"
mkdir -p $BACKUP_DIR

# 备份数据库
docker compose exec mysql mysqldump -u root -p$MYSQL_ROOT_PASSWORD weizhi > $BACKUP_DIR/weizhi_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/weizhi_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
EOF

chmod +x backup.sh

# 设置定时备份
crontab -e
# 添加：每天凌晨2点备份
0 2 * * * /path/to/backup.sh
```

### 3. 性能监控

#### 系统监控

```bash
# 安装监控工具
sudo apt install -y htop iotop nethogs

# 查看系统资源
htop
docker stats

# 查看磁盘使用
df -h
du -sh /var/lib/docker
```

#### 应用监控

```bash
# 查看容器资源使用
docker stats

# 查看容器详细信息
docker inspect <container_name>

# 查看网络连接
netstat -tulpn
```

## 🔧 故障排除

### 常见问题

#### 1. 容器启动失败

```bash
# 查看容器日志
docker compose logs <service_name>

# 检查配置文件
docker compose config

# 重新构建镜像
docker compose build --no-cache <service_name>
```

#### 2. 数据库连接失败

```bash
# 检查数据库容器状态
docker compose ps mysql

# 进入数据库容器
docker compose exec mysql mysql -u root -p

# 检查网络连接
docker compose exec server-go ping mysql
```

#### 3. 端口冲突

```bash
# 查看端口占用
netstat -tulpn | grep :3001

# 停止冲突服务
sudo systemctl stop <service_name>

# 修改端口配置
vim .env
```

### 应急处理

#### 服务重启

```bash
# 重启所有服务
docker compose restart

# 重启特定服务
docker compose restart web
docker compose restart server-go

# 强制重新创建容器
docker compose up -d --force-recreate
```

#### 数据恢复

```bash
# 恢复数据库备份
docker compose exec -T mysql mysql -u root -p$MYSQL_ROOT_PASSWORD weizhi < backup.sql

# 检查数据完整性
docker compose exec mysql mysql -u root -p$MYSQL_ROOT_PASSWORD -e "SELECT COUNT(*) FROM weizhi.news;"
```

---

*部署完成后，请参考 [监控运维文档](./04-monitoring.md) 进行系统监控配置。*
