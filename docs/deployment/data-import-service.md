# 数据导入服务使用说明

## 概述

数据导入服务是一个独立的容器服务，用于在 Go 应用启动完成后自动导入初始数据。它会等待 server 服务完全启动并创建数据库表后，再执行数据导入操作。

## 特性

- ✅ **安全启动**：等待 server 服务完全启动后再执行
- ✅ **智能检测**：自动检测现有数据，避免重复导入
- ✅ **灵活配置**：支持选择性导入不同类型的数据
- ✅ **环境变量配置**：所有敏感数据通过环境变量传递
- ✅ **容器化部署**：独立容器，不影响主服务
- ✅ **一次性运行**：导入完成后自动退出，不重启
- ✅ **首次部署集成**：与 Gitea Actions 工作流深度集成
- ✅ **自动化部署**：支持在部署工作流中自动执行数据导入

## 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MySQL 数据库   │    │   Go 后端服务    │    │  数据导入服务    │
│                │    │                │    │                │
│ 1. 启动数据库    │    │ 2. 连接数据库    │    │ 4. 等待服务就绪  │
│ 2. 健康检查     │◄───┤ 3. 创建数据表    │◄───┤ 5. 检测现有数据  │
│                │    │ 4. 启动API服务   │    │ 6. 执行数据导入  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 环境变量配置

### 服务连接配置
```bash
SERVER_HOST=server                    # Go 服务主机名
SERVER_PORT=3001                     # Go 服务端口
SERVER_HEALTH_ENDPOINT=/api/health   # 健康检查端点
```

### 数据库配置
```bash
DB_HOST=mysql                        # 数据库主机
DB_PORT=3306                         # 数据库端口
DB_USERNAME=weizhi                   # 数据库用户名
DB_PASSWORD=your_password            # 数据库密码
DB_DATABASE=weizhi                   # 数据库名称
```

### 数据导入选项
```bash
IMPORT_FULL_DATA=false               # 导入完整数据
# 注意：IMPORT_SEED_DATA 和 IMPORT_ADMIN_DATA 已废弃
```

### 等待配置
```bash
MAX_WAIT_TIME=300                    # 最大等待时间（秒）
CHECK_INTERVAL=10                    # 检查间隔（秒）
```

## 使用方法

### 方法一：通过 Gitea Actions 首次部署（推荐）

1. **构建镜像时启用首次部署**：
   - 在 Gitea 仓库中，进入 Actions 页面
   - 选择"构建并推送"工作流
   - 点击"Run workflow"
   - 勾选"首次部署（启用数据导入）"选项
   - 选择要构建的服务（建议选择 "all"）
   - 点击运行

2. **部署时启用首次部署**：
   - 在 Gitea 仓库中，进入 Actions 页面
   - 选择"自动化部署"工作流
   - 点击"Run workflow"
   - 勾选"首次部署（启用数据导入）"选项
   - 选择部署环境和服务
   - 点击运行

### 方法二：手动启用数据导入服务

1. **在 `.env` 文件中设置**：
```bash
# 启用数据导入
IMPORT_SEED_DATA=true
IMPORT_ADMIN_DATA=true

# 可选：调整等待时间
DATA_IMPORT_MAX_WAIT=300
DATA_IMPORT_CHECK_INTERVAL=10
```

2. **使用 Docker Compose Profile 启动**：
```bash
# 启动包含数据导入服务的完整环境
docker-compose --profile data-import up -d

# 或者只启动数据导入服务
docker-compose --profile data-import up data-import
```

3. **查看导入日志**：
```bash
# 查看数据导入服务日志
docker-compose logs -f data-import

# 查看所有服务日志
docker-compose logs -f
```

## 首次部署说明

### 什么是首次部署？

首次部署是指在全新的服务器环境中第一次部署应用时，需要初始化数据库并导入基础数据的过程。这包括：

- **种子数据**：应用正常运行所需的基础配置数据
- **管理员数据**：默认管理员账户和权限设置
- **示例数据**：演示用的样例内容（可选）

### 为什么需要首次部署选项？

1. **避免重复导入**：只在首次部署时导入数据，后续更新不会重复执行
2. **自动化流程**：与 CI/CD 工作流集成，无需手动操作
3. **安全可靠**：确保在正确的时机执行数据导入
4. **灵活控制**：可以选择性地启用或禁用数据导入

### 首次部署工作流程

```mermaid
graph TD
    A[开始部署] --> B{是否首次部署?}
    B -->|是| C[构建所有镜像]
    B -->|否| D[正常部署流程]
    C --> E[部署基础服务]
    E --> F[等待服务启动]
    F --> G[健康检查]
    G --> H[启动数据导入服务]
    H --> I[等待数据导入完成]
    I --> J[清理导入容器]
    J --> K[部署完成]
    D --> K
```

### 使用场景

1. **新环境搭建**：在新的服务器上首次部署应用
2. **开发环境初始化**：为开发团队搭建标准的开发环境
3. **测试环境重置**：重新初始化测试环境的数据
4. **演示环境准备**：为客户演示准备包含样例数据的环境

## 数据文件结构

数据导入服务会查找以下 SQL 文件：

```
server-go/scripts/
├── seed_data.sql          # 种子数据（基础配置数据）
├── admin_data.sql         # 管理员数据（管理员账户等）
├── database_full.sql      # 完整数据（包含所有数据）
└── import-data.sh         # 数据导入脚本
```

## 导入逻辑

1. **等待依赖服务**：
   - 等待 MySQL 数据库连接成功
   - 等待 Go 服务健康检查通过
   - 等待数据库表创建完成

2. **检测现有数据**：
   - 检查关键表是否已有数据
   - 如有数据则跳过导入，避免重复

3. **执行数据导入**：
   - 根据环境变量选择导入类型
   - 执行相应的 SQL 文件
   - 记录导入统计信息

4. **完成并退出**：
   - 显示最终数据统计
   - 服务自动退出（不重启）

## 故障排除

### 常见问题

1. **服务连接超时**
   ```bash
   # 检查服务状态
   docker-compose ps
   
   # 检查网络连接
   docker-compose exec data-import ping server
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库密码配置
   docker-compose exec data-import env | grep DB_
   
   # 手动测试数据库连接
   docker-compose exec data-import mysql -h mysql -u weizhi -p
   ```

3. **SQL 文件不存在**
   ```bash
   # 检查数据文件是否存在
   docker-compose exec data-import ls -la /app/scripts/
   ```

### 调试模式

启用详细日志：
```bash
# 设置调试环境变量
export DEBUG=true
export CHECK_INTERVAL=5

# 重新启动服务
docker-compose --profile data-import up data-import
```

## 安全注意事项

1. **敏感数据保护**：
   - 所有数据库密码通过环境变量传递
   - 不在代码中硬编码任何敏感信息

2. **数据完整性**：
   - 导入前检查现有数据
   - 使用事务确保数据一致性

3. **权限控制**：
   - 使用最小权限的数据库用户
   - 限制容器资源使用

## 生产环境建议

1. **资源限制**：
   ```yaml
   deploy:
     resources:
       limits:
         memory: 128M
         cpus: '0.2'
   ```

2. **监控告警**：
   - 监控导入服务的执行状态
   - 设置导入失败的告警通知

3. **备份策略**：
   - 导入前自动备份现有数据
   - 保留导入日志用于审计

## 更新和维护

### 更新数据文件
1. 修改 `server-go/scripts/` 目录下的 SQL 文件
2. 重新构建 data-import 镜像
3. 重新部署服务

### 版本管理
- 数据导入服务与主应用使用相同的版本标签
- 确保数据文件与应用版本兼容
