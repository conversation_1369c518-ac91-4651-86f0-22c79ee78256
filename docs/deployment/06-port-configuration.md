# 🔌 端口配置指南

本文档详细说明项目中各服务的端口配置和管理。

## 📊 端口分配表

| 服务 | 端口 | 协议 | 说明 | 访问地址 |
|------|------|------|------|----------|
| Go 后端 | 3001 | HTTP | API 服务 | http://localhost:3001 |
| Nuxt3 前端 | 3000 | HTTP | 企业官网 | http://localhost:3000 |
| Vue3 管理后台 | 5173 | HTTP | 管理系统 | http://localhost:5173 |
| MySQL 数据库 | 3306 | TCP | 数据库服务 | localhost:3306 |
| Caddy HTTP | 80 | HTTP | 反向代理 | http://localhost |
| Caddy HTTPS | 443 | HTTPS | SSL 终止 | https://localhost |

## 🔧 配置文件说明

### 1. 环境变量配置

**docker.env**
```env
# 服务端口配置
SERVER_PORT=3001    # Go 后端端口
WEB_PORT=3000       # Nuxt3 前端端口
ADMIN_PORT=5173     # Vue3 管理后台端口
MYSQL_PORT=3306     # MySQL 数据库端口
```

### 2. Docker Compose 配置

**docker-compose.yml**
```yaml
services:
  server:
    ports:
      - "${SERVER_PORT:-3001}:3001"
  
  web:
    ports:
      - "${WEB_PORT:-3000}:3000"
  
  admin:
    ports:
      - "${ADMIN_PORT:-5173}:80"
  
  mysql:
    ports:
      - "${MYSQL_PORT:-3306}:3306"
```

### 3. 后端配置

**server-go/config.yaml**
```yaml
app:
  port: "3001"  # Go 应用端口
```

**server-go/cmd/main.go**
```go
// @host localhost:3001  # Swagger 文档地址
```

### 4. 前端配置

**web/nuxt.config.ts**
```typescript
runtimeConfig: {
  public: {
    apiBase: process.env.NUXT_PUBLIC_API_BASE || 'http://localhost:3001'
  }
}
```

**admin/vite.config.ts**
```typescript
server: {
  port: 5173,
  proxy: {
    '/api': {
      target: 'http://localhost:3001',
      changeOrigin: true,
    },
  },
}
```

## 🌐 网络通信

### 开发环境

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nuxt3 前端    │    │   Vue3 管理后台  │    │    Go 后端      │
│   Port: 3000    │───▶│   Port: 5173    │───▶│   Port: 3001    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                                       ▼
                                              ┌─────────────────┐
                                              │  MySQL 数据库   │
                                              │   Port: 3306    │
                                              └─────────────────┘
```

### 生产环境 (通过 Caddy)

```
┌─────────────────┐    ┌─────────────────┐
│      用户       │    │     Caddy       │
│                 │───▶│   Port: 80/443  │
└─────────────────┘    └─────────────────┘
                                │
                ┌───────────────┼───────────────┐
                ▼               ▼               ▼
       ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
       │ Nuxt3 前端  │ │ Vue3 管理后台│ │  Go 后端    │
       │ Port: 3000  │ │ Port: 5173  │ │ Port: 3001  │
       └─────────────┘ └─────────────┘ └─────────────┘
```

## 🔍 端口检查工具

### 1. 端口配置检查

```bash
# 检查所有端口配置是否一致
./scripts/check-ports.sh
```

### 2. 端口占用检查

```bash
# 检查端口是否被占用
lsof -i :3001  # 检查后端端口
lsof -i :3000  # 检查前端端口
lsof -i :5173  # 检查管理后台端口
lsof -i :3306  # 检查数据库端口
```

### 3. 服务状态检查

```bash
# 检查 Docker 服务状态
docker-compose ps

# 检查服务健康状态
./scripts/test-health.sh
```

## 🚨 常见问题

### 1. 端口冲突

**问题**: 启动服务时提示端口已被占用

**解决方案**:
```bash
# 查找占用端口的进程
lsof -i :3001

# 终止进程
kill -9 <PID>

# 或者修改端口配置
# 编辑 docker.env 文件，修改相应端口
```

### 2. API 请求失败

**问题**: 前端无法访问后端 API

**检查清单**:
- [ ] 后端服务是否正常运行 (端口 3001)
- [ ] 前端 API 配置是否正确
- [ ] CORS 配置是否包含前端地址
- [ ] 防火墙是否阻止了端口访问

**调试命令**:
```bash
# 测试后端 API
curl http://localhost:3001/api/health

# 检查 CORS 配置
curl -H "Origin: http://localhost:3000" \
     -H "Access-Control-Request-Method: GET" \
     -X OPTIONS http://localhost:3001/api/health
```

### 3. 管理后台无法访问

**问题**: 管理后台页面无法加载

**检查清单**:
- [ ] 管理后台服务是否运行 (端口 5173)
- [ ] API 代理配置是否正确
- [ ] 后端服务是否可访问

### 4. 数据库连接失败

**问题**: 后端无法连接数据库

**检查清单**:
- [ ] MySQL 服务是否运行 (端口 3306)
- [ ] 数据库配置是否正确
- [ ] 网络连接是否正常

## 🔒 安全考虑

### 1. 生产环境端口

- **不要暴露**: 数据库端口 (3306) 不应对外暴露
- **使用代理**: 通过 Caddy 代理访问，只暴露 80/443 端口
- **防火墙**: 配置防火墙规则，限制端口访问

### 2. 开发环境

- **本地访问**: 开发环境端口仅绑定到 localhost
- **临时暴露**: 如需外部访问，使用临时端口转发
- **定期检查**: 定期检查端口占用情况

## 📋 端口管理最佳实践

### 1. 标准化

- 使用统一的端口分配策略
- 在文档中明确记录端口用途
- 避免使用系统保留端口

### 2. 环境隔离

- 开发、测试、生产环境使用不同端口
- 使用环境变量管理端口配置
- 避免硬编码端口号

### 3. 监控

- 定期检查端口占用情况
- 监控服务健康状态
- 记录端口变更历史

## 🛠️ 相关工具

```bash
# 端口配置检查
./scripts/check-ports.sh

# 健康检查
./scripts/test-health.sh

# 配置验证
./scripts/test-config.sh

# 服务监控
./scripts/monitor-health.sh
```

---

*定期运行 `./scripts/check-ports.sh` 来验证端口配置的一致性。*
