# 🌐 域名配置指南

本文档介绍如何配置域名、DNS解析和SSL证书，实现生产环境的域名访问。

## 📋 概述

项目使用 Caddy 作为反向代理服务器，支持：
- 自动 HTTPS 证书申请和续期
- 多域名配置和重定向
- 负载均衡和健康检查
- 自动压缩和缓存

## 🔧 域名配置

### 1. 环境变量配置

在 `docker.env` 文件中设置域名相关变量：

```bash
# 主域名配置
DOMAIN=viclink.cn

# 子域名配置（可选）
API_DOMAIN=api.viclink.cn
ADMIN_DOMAIN=admin.viclink.cn
```

### 2. 多域名支持

当前配置支持以下域名访问：

| 域名 | 用途 | 重定向 |
|------|------|--------|
| `https://viclink.cn` | 主站 | - |
| `https://www.viclink.cn` | WWW域名 | → `https://viclink.cn` |
| `https://api.viclink.cn` | API服务 | - |
| `https://admin.viclink.cn` | 管理后台 | - |

### 3. 重定向策略

```bash
# WWW重定向配置
www.viclink.cn {
    redir https://viclink.cn{uri} permanent
}
```

**重定向原因**：
- SEO最佳实践，避免重复内容问题
- 保持URL的一致性和权重集中
- 提升用户体验

## 🌍 DNS 配置

### 必需的DNS记录

确保以下DNS记录已正确配置：

```dns
# A记录 - 指向服务器IP
viclink.cn        A    YOUR_SERVER_IP
www.viclink.cn    A    YOUR_SERVER_IP
api.viclink.cn    A    YOUR_SERVER_IP
admin.viclink.cn  A    YOUR_SERVER_IP

# 或者使用CNAME记录（推荐）
www.viclink.cn    CNAME    viclink.cn
api.viclink.cn    CNAME    viclink.cn
admin.viclink.cn  CNAME    viclink.cn
```

### DNS配置验证

```bash
# 验证DNS解析
nslookup viclink.cn
nslookup www.viclink.cn
nslookup api.viclink.cn

# 或使用dig命令
dig viclink.cn
dig www.viclink.cn
```

## 🔒 SSL 证书

### 自动证书申请

Caddy 会自动为以下域名申请 Let's Encrypt SSL 证书：
- `viclink.cn`
- `www.viclink.cn`
- `api.viclink.cn`
- `admin.viclink.cn`

### 证书申请条件

1. **域名解析正确**: 域名已正确解析到服务器IP
2. **端口开放**: 服务器防火墙开放 80 和 443 端口
3. **网络连接**: 服务器可以访问互联网
4. **域名有效**: 域名未过期且可正常访问

### 证书验证

```bash
# 检查SSL证书
openssl s_client -connect viclink.cn:443 -servername viclink.cn

# 查看证书详情
curl -vI https://viclink.cn

# 在线SSL检查
# https://www.ssllabs.com/ssltest/
```

## 🔧 Caddy 配置

### 基础配置文件

**文件位置**: `caddy/Caddyfile`

```caddyfile
# 主站配置
{$DOMAIN} {
    reverse_proxy web:4000
    
    # 启用压缩
    encode gzip
    
    # 静态文件缓存
    @static {
        path *.css *.js *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2
    }
    header @static Cache-Control "public, max-age=31536000"
    
    # 安全头
    header {
        X-Content-Type-Options nosniff
        X-Frame-Options DENY
        X-XSS-Protection "1; mode=block"
        Referrer-Policy strict-origin-when-cross-origin
    }
}

# WWW重定向
www.{$DOMAIN} {
    redir https://{$DOMAIN}{uri} permanent
}

# API服务
api.{$DOMAIN} {
    reverse_proxy server-go:3001
    
    # API特定配置
    header {
        Access-Control-Allow-Origin *
        Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
        Access-Control-Allow-Headers "Content-Type, Authorization"
    }
}

# 管理后台
admin.{$DOMAIN} {
    reverse_proxy admin:80
    
    # 管理后台安全配置
    header {
        X-Frame-Options SAMEORIGIN
        Content-Security-Policy "default-src 'self'"
    }
}
```

### 高级配置

```caddyfile
# 负载均衡配置
{$DOMAIN} {
    reverse_proxy {
        to web:4000
        health_uri /health
        health_interval 30s
        health_timeout 5s
    }
}

# 限流配置
{$DOMAIN} {
    rate_limit {
        zone static_ip 10r/s
        key {remote_host}
    }
    reverse_proxy web:4000
}
```

## 🚀 部署配置

### 1. 生产环境部署

```bash
# 设置域名环境变量
export DOMAIN=viclink.cn

# 启动服务
docker-compose --env-file=docker.env up -d

# 检查服务状态
docker-compose ps
```

### 2. 验证部署

```bash
# 检查域名访问
curl -I https://viclink.cn
curl -I https://www.viclink.cn
curl -I https://api.viclink.cn

# 检查重定向
curl -I https://www.viclink.cn | grep Location

# 检查SSL证书
curl -vI https://viclink.cn 2>&1 | grep -E "(subject|issuer|expire)"
```

## 🔍 故障排除

### 常见问题

#### 1. 域名无法访问

**检查步骤**：
```bash
# 1. 验证DNS解析
nslookup viclink.cn

# 2. 检查服务状态
docker-compose ps

# 3. 查看Caddy日志
docker-compose logs caddy

# 4. 检查防火墙
sudo ufw status
```

#### 2. SSL证书申请失败

**可能原因**：
- DNS解析未生效
- 防火墙阻止80/443端口
- 域名已有其他SSL证书

**解决方案**：
```bash
# 清除Caddy数据重新申请
docker-compose down
docker volume rm $(docker volume ls -q | grep caddy)
docker-compose up -d
```

#### 3. 重定向循环

**检查配置**：
```bash
# 检查Caddyfile配置
cat caddy/Caddyfile

# 验证重定向
curl -I https://www.viclink.cn
```

### 日志查看

```bash
# Caddy访问日志
docker-compose logs -f caddy

# 应用服务日志
docker-compose logs -f web
docker-compose logs -f server-go

# 系统日志
sudo journalctl -u docker
```

## 📊 性能优化

### 1. 缓存配置

```caddyfile
{$DOMAIN} {
    # 静态资源缓存
    @static path *.css *.js *.png *.jpg *.jpeg *.gif *.ico *.svg
    header @static Cache-Control "public, max-age=31536000, immutable"
    
    # HTML缓存
    @html path *.html /
    header @html Cache-Control "public, max-age=3600"
    
    reverse_proxy web:4000
}
```

### 2. 压缩配置

```caddyfile
{$DOMAIN} {
    # 启用Gzip压缩
    encode {
        gzip 6
        minimum_length 1024
    }
    
    reverse_proxy web:4000
}
```

### 3. 安全配置

```caddyfile
{$DOMAIN} {
    # 安全头
    header {
        # 防止XSS攻击
        X-XSS-Protection "1; mode=block"
        
        # 防止MIME类型嗅探
        X-Content-Type-Options nosniff
        
        # 防止点击劫持
        X-Frame-Options DENY
        
        # HSTS
        Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
        
        # CSP
        Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
    }
    
    reverse_proxy web:4000
}
```

## 📋 检查清单

部署前请确认以下项目：

- [ ] DNS记录已正确配置
- [ ] 域名已解析到服务器IP
- [ ] 防火墙已开放80和443端口
- [ ] 环境变量已正确设置
- [ ] Caddyfile配置无语法错误
- [ ] 服务容器正常运行
- [ ] SSL证书申请成功
- [ ] 域名可正常访问
- [ ] 重定向工作正常
- [ ] 静态资源加载正常

---

*域名配置完成后，请参考 [监控运维文档](./04-monitoring.md) 进行系统监控配置。*
