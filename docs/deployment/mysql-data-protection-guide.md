# MySQL数据保护使用指南

## 概述

为了防止部署过程中意外清理MySQL数据，CI/CD工作流现在支持根据部署类型来决定是否保留数据库数据。

## 部署类型

### 🆕 首次部署
- **何时使用**：全新环境部署、需要重置所有数据
- **设置方法**：`首次部署（启用数据导入）` = `true`
- **行为**：清理所有数据卷，重新初始化数据库
- **风险**：⚠️ 会删除所有现有数据

### 🔄 常规部署
- **何时使用**：日常更新、功能发布、bug修复
- **设置方法**：`首次部署（启用数据导入）` = `false`（默认）
- **行为**：保留MySQL数据卷，只更新应用代码
- **风险**：✅ 数据安全，无数据丢失风险

## 操作步骤

### 在Gitea中触发部署

1. 进入项目的 Actions 页面
2. 选择 "构建并部署" 工作流
3. 点击 "Run workflow"
4. 配置参数：

#### 常规部署（推荐）
```
操作类型: build-and-deploy
构建服务: core
版本号: v0.1.0
部署环境: production
首次部署（启用数据导入）: false  ← 重要！保持默认值
```

#### 首次部署或数据重置
```
操作类型: build-and-deploy
构建服务: core
版本号: v0.1.0
部署环境: production
首次部署（启用数据导入）: true   ← 注意！会清理数据
```

## 安全检查清单

### 部署前检查

- [ ] 确认部署类型是否正确
- [ ] 如果是首次部署，确认是否需要备份现有数据
- [ ] 检查版本号是否正确
- [ ] 确认构建服务选择是否合适

### 首次部署前必做

- [ ] 备份现有数据库数据
- [ ] 确认初始化脚本是否最新
- [ ] 通知相关人员数据将被重置
- [ ] 准备好重新录入关键数据的计划

## 数据备份方法

### 手动备份

```bash
# 连接到服务器
ssh user@your-server

# 进入部署目录
cd /opt/weishi

# 备份数据库
docker exec weizhi-mysql-prod mysqldump \
  -u root -p"$MYSQL_ROOT_PASSWORD" \
  --single-transaction \
  --routines \
  --triggers \
  weizhi > backup_$(date +%Y%m%d_%H%M%S).sql

# 压缩备份文件
gzip backup_*.sql
```

### 使用备份脚本

```bash
# 使用项目提供的备份脚本
./deployment/scripts/backup.sh
```

## 故障恢复

### 如果意外清理了数据

1. **停止服务**
   ```bash
   docker compose --env-file production.env -f docker-compose.prod.yml down
   ```

2. **恢复备份**
   ```bash
   # 解压备份文件
   gunzip backup_YYYYMMDD_HHMMSS.sql.gz
   
   # 启动MySQL服务
   docker compose --env-file production.env -f docker-compose.prod.yml up -d mysql
   
   # 等待MySQL启动完成
   sleep 30
   
   # 恢复数据
   docker exec -i weizhi-mysql-prod mysql -u root -p"$MYSQL_ROOT_PASSWORD" weizhi < backup_YYYYMMDD_HHMMSS.sql
   ```

3. **重启所有服务**
   ```bash
   docker compose --env-file production.env -f docker-compose.prod.yml up -d
   ```

## 监控和验证

### 部署后验证

1. **检查服务状态**
   ```bash
   docker compose --env-file production.env -f docker-compose.prod.yml ps
   ```

2. **验证数据完整性**
   ```bash
   # 连接数据库
   docker exec -it weizhi-mysql-prod mysql -u root -p
   
   # 检查关键表
   USE weizhi;
   SELECT COUNT(*) FROM admin_users;
   SELECT COUNT(*) FROM swipers;
   ```

3. **测试应用功能**
   - 访问前端网站
   - 登录管理后台
   - 检查关键功能是否正常

## 最佳实践

### 1. 定期备份
- 建议每日自动备份数据库
- 保留至少30天的备份文件
- 定期测试备份文件的可用性

### 2. 部署策略
- 生产环境部署前先在测试环境验证
- 重要更新建议在低峰期进行
- 准备回滚计划

### 3. 团队协作
- 部署前通知团队成员
- 记录部署日志和变更内容
- 建立部署审批流程

## 常见问题

### Q: 如何确认当前是否为首次部署？
A: 检查服务器上是否存在MySQL数据卷：
```bash
docker volume ls | grep mysql
```

### Q: 误设置了首次部署怎么办？
A: 如果还未执行，可以取消工作流。如果已执行，需要从备份恢复数据。

### Q: 数据库结构更新如何处理？
A: 应用会自动处理数据库迁移，无需手动干预。使用常规部署即可。

## 相关文档

- [数据库初始化文档](07-database-initialization.md)
- [部署指南](01-deployment-guide.md)
- [故障排除指南](../troubleshooting/common-issues.md)
