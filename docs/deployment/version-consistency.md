# 镜像版本一致性管理

## 🎯 概述

确保构建的 Docker 镜像版本与生产环境 Docker Compose 使用的版本完全一致，这是自动化部署的关键要求。

## 🔄 版本流程

### 1. 版本生成策略

#### 自动生成版本（默认）
```bash
# 格式：YYYYMMDD-HHMMSS-{git_sha_8位}
# 示例：20241201-143022-a1b2c3d4
VERSION="$(date +%Y%m%d-%H%M%S)-${GITHUB_SHA:0:8}"
```

#### 自定义版本
```bash
# 在 Gitea Actions 中手动指定
# 示例：v1.0.0, release-2024.12, hotfix-001
VERSION="用户输入的自定义标签"
```

### 2. 构建阶段版本使用

在构建工作流中，版本号用于：

```yaml
# 镜像标签
tags: |
  ${REGISTRY_URL}/${NAMESPACE}/weizhi_server:${VERSION}
  ${REGISTRY_URL}/${NAMESPACE}/weizhi_server:latest

# 构建参数
build-args: |
  VERSION=${VERSION}
  BUILD_TIME=${BUILD_TIME}
```

### 3. 部署阶段版本传递

#### 环境配置文件生成
```bash
# deployment.env
REGISTRY_URL=registry.cn-hangzhou.aliyuncs.com
NAMESPACE=vest
VERSION=20241201-143022-a1b2c3d4  # 与构建时完全一致
```

#### Docker Compose 版本引用
```yaml
# docker-compose.prod.yml
services:
  server:
    image: ${REGISTRY_URL}/${NAMESPACE}/weizhi_server:${VERSION:-latest}
  web:
    image: ${REGISTRY_URL}/${NAMESPACE}/weizhi_web:${VERSION:-latest}
  caddy:
    image: ${REGISTRY_URL}/${NAMESPACE}/weizhi_admin_caddy:${VERSION:-latest}
```

## 🔍 版本验证机制

### 1. 构建时验证
```bash
# 显示构建的镜像标签
echo "构建镜像: ${REGISTRY_URL}/${NAMESPACE}/${IMAGE_NAME}:${VERSION}"
echo "构建镜像: ${REGISTRY_URL}/${NAMESPACE}/${IMAGE_NAME}:latest"
```

### 2. 部署前验证
```bash
# 验证镜像是否存在
docker images | grep "${NAMESPACE}" | grep "weizhi"

# 检查特定版本镜像
if docker images | grep -q "${REGISTRY_URL}/${NAMESPACE}/weizhi_server:${VERSION}"; then
  echo "✅ 后端镜像版本正确"
else
  echo "❌ 后端镜像版本不匹配"
fi
```

### 3. 运行时验证
```bash
# 检查容器使用的镜像版本
docker compose ps --format "table {{.Service}}\t{{.Image}}\t{{.Status}}"
```

## 📋 版本一致性检查清单

### 构建阶段
- [ ] 版本号正确生成（自动或自定义）
- [ ] 所有服务镜像使用相同版本标签
- [ ] 镜像成功推送到仓库
- [ ] 构建元数据包含正确版本信息

### 部署阶段
- [ ] 环境配置文件包含正确版本号
- [ ] Docker Compose 文件引用版本变量
- [ ] 镜像拉取使用正确版本
- [ ] 容器启动使用正确镜像版本

### 验证阶段
- [ ] 拉取的镜像版本与期望一致
- [ ] 运行的容器使用正确镜像
- [ ] 服务健康检查通过
- [ ] 版本信息在日志中可见

## 🛠️ 故障排除

### 常见问题

#### 1. 版本不匹配
```bash
# 问题：部署时找不到指定版本的镜像
# 原因：构建失败或版本号传递错误
# 解决：检查构建日志，确认镜像是否成功推送

# 检查镜像仓库中的版本
docker search ${REGISTRY_URL}/${NAMESPACE}/weizhi_server
```

#### 2. 环境变量未生效
```bash
# 问题：Docker Compose 使用 latest 而不是指定版本
# 原因：环境变量未正确加载
# 解决：确认 production.env 文件存在且格式正确

# 验证环境变量
source production.env
echo "VERSION: $VERSION"
```

#### 3. 镜像拉取失败
```bash
# 问题：无法拉取指定版本镜像
# 原因：网络问题或认证失败
# 解决：检查网络连接和仓库认证

# 手动拉取测试
docker pull ${REGISTRY_URL}/${NAMESPACE}/weizhi_server:${VERSION}
```

## 🎯 最佳实践

### 1. 版本命名规范
- **开发版本**：`dev-YYYYMMDD-HHMMSS-{sha}`
- **测试版本**：`test-YYYYMMDD-HHMMSS-{sha}`
- **生产版本**：`prod-YYYYMMDD-HHMMSS-{sha}` 或 `v1.0.0`
- **热修复版本**：`hotfix-{issue}-YYYYMMDD`

### 2. 版本管理策略
- 每次构建生成唯一版本号
- 保留多个历史版本用于回滚
- 使用语义化版本号标记重要发布
- 定期清理过期版本镜像

### 3. 部署安全性
- 始终验证镜像版本一致性
- 部署前进行镜像安全扫描
- 保留部署前的镜像备份
- 实施分阶段部署策略

### 4. 监控和告警
- 监控版本部署状态
- 设置版本不匹配告警
- 记录版本变更历史
- 建立版本回滚机制

## 📊 版本追踪

### 构建元数据示例
```json
{
  "service": "server-go",
  "version": "20241201-143022-a1b2c3d4",
  "build_time": "2024-12-01T14:30:22Z",
  "commit_sha": "a1b2c3d4e5f6g7h8",
  "branch": "main",
  "external_registry": {
    "enabled": true,
    "image": "registry.cn-hangzhou.aliyuncs.com/vest/weizhi_server:20241201-143022-a1b2c3d4"
  }
}
```

### 部署记录示例
```bash
# 部署日志
2024-12-01 14:35:00 - 开始部署版本: 20241201-143022-a1b2c3d4
2024-12-01 14:35:30 - 镜像拉取完成
2024-12-01 14:36:00 - 服务启动完成
2024-12-01 14:36:30 - 健康检查通过
2024-12-01 14:37:00 - 部署成功
```

---

通过严格的版本一致性管理，确保生产环境运行的始终是经过验证的正确版本镜像。
