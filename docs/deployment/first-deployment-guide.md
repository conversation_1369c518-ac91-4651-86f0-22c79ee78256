# 首次部署快速指南

## 🚀 快速开始

### 步骤 1：准备环境

确保你的服务器已经安装了：
- Docker
- Docker Compose
- Git

### 步骤 2：配置 Gitea Variables 和 Secrets

在 Gitea 仓库设置中配置以下变量：

#### Variables（公开配置）
```
REGISTRY_URL=registry.cn-hangzhou.aliyuncs.com
NAMESPACE=vest
DOMAIN=viclink.cn
ADMIN_DOMAIN=admin.viclink.cn
MYSQL_DATABASE=weizhi
MYSQL_USER=weizhi
WEB_PORT=3000
SERVER_PORT=3001
MYSQL_PORT=3307
```

#### Secrets（敏感配置）
```
REGISTRY_USERNAME=你的镜像仓库用户名
REGISTRY_PASSWORD=你的镜像仓库密码
MYSQL_PASSWORD=数据库密码
MYSQL_ROOT_PASSWORD=数据库root密码
JWT_SECRET=JWT密钥
ADMIN_PASSWORD=管理员密码
DEPLOY_HOST=服务器IP地址
DEPLOY_USER=服务器用户名
DEPLOY_SSH_KEY=SSH私钥
```

### 步骤 3：首次部署

1. **进入 Gitea Actions 页面**
2. **选择"构建并推送"工作流**
3. **点击"Run workflow"**
4. **配置参数**：
   - 要构建的服务：选择 `all`
   - 推送到外部镜像仓库：✅
   - 推送到 Gitea 软件包仓库：✅（可选）
   - 创建制品包：✅
   - **首次部署（启用数据导入）**：✅ **重要！**
5. **点击运行**

### 步骤 4：等待构建完成

构建过程包括：
- 构建 web 前端镜像
- 构建 admin 管理后台镜像  
- 构建 server-go 后端镜像
- 构建 data-import 数据导入镜像
- 推送镜像到仓库

### 步骤 5：执行部署

构建完成后：

1. **选择"自动化部署"工作流**
2. **点击"Run workflow"**
3. **配置参数**：
   - 部署环境：`production`
   - 部署服务：`all`
   - 镜像版本：`latest`（或指定版本）
   - **首次部署（启用数据导入）**：✅ **重要！**
4. **点击运行**

### 步骤 6：验证部署

部署完成后，检查以下内容：

1. **服务状态**：
   ```bash
   docker compose -f docker-compose.prod.yml ps
   ```

2. **访问应用**：
   - 前端网站：`https://你的域名`
   - 管理后台：`https://admin.你的域名`
   - API健康检查：`https://你的域名/api/health`

3. **检查数据**：
   - 登录管理后台验证管理员账户
   - 查看前端页面是否显示基础数据

## 🔧 故障排除

### 常见问题

1. **构建失败**
   ```bash
   # 检查构建日志
   # 在 Gitea Actions 页面查看详细错误信息
   ```

2. **部署失败**
   ```bash
   # SSH 到服务器检查
   ssh user@server
   cd /path/to/deploy
   docker compose logs
   ```

3. **数据导入失败**
   ```bash
   # 查看数据导入日志
   docker compose logs data-import
   
   # 手动重新导入
   docker compose --profile data-import up data-import
   ```

4. **服务无法访问**
   ```bash
   # 检查防火墙设置
   sudo ufw status
   
   # 检查端口占用
   netstat -tlnp | grep :80
   netstat -tlnp | grep :443
   ```

### 重新部署

如果需要重新进行首次部署：

1. **清理现有环境**：
   ```bash
   docker compose -f docker-compose.prod.yml down -v
   docker system prune -f
   ```

2. **重新执行首次部署流程**

## 📋 检查清单

### 部署前检查
- [ ] 服务器环境准备完成
- [ ] Gitea Variables 配置完成
- [ ] Gitea Secrets 配置完成
- [ ] 域名 DNS 解析配置完成
- [ ] SSL 证书配置（如果需要）

### 部署中检查
- [ ] 构建工作流成功完成
- [ ] 所有镜像推送成功
- [ ] 部署工作流成功完成
- [ ] 数据导入服务执行成功

### 部署后检查
- [ ] 所有容器运行正常
- [ ] 前端网站可以访问
- [ ] 管理后台可以访问
- [ ] API 健康检查通过
- [ ] 管理员账户可以登录
- [ ] 基础数据显示正常

## 🎯 最佳实践

1. **备份策略**：
   - 首次部署前备份服务器配置
   - 定期备份数据库和上传文件

2. **监控告警**：
   - 设置服务状态监控
   - 配置部署失败告警

3. **版本管理**：
   - 使用语义化版本标签
   - 保留多个版本的镜像

4. **安全配置**：
   - 定期更新密码和密钥
   - 限制服务器访问权限
   - 启用防火墙和安全组

## 📞 获取帮助

如果在首次部署过程中遇到问题：

1. 查看 Gitea Actions 的详细日志
2. 检查服务器上的容器日志
3. 参考 `docs/data-import-service.md` 详细文档
4. 联系技术支持团队

---

**注意**：首次部署选项只应在全新环境中使用。在已有数据的环境中启用可能会导致数据冲突或重复。
