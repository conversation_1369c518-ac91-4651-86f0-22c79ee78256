# 服务器部署制品说明

## 📦 最终上传到服务器的内容

### 🎯 概述

通过 Gitea Actions 自动化部署，最终会将以下内容上传到生产服务器：

```
/opt/weizhi/                          # 部署目录
├── weizhi-deploy-{version}.tar.gz    # 构建制品压缩包（临时）
├── production.env                    # 生产环境配置文件
├── docker-compose.prod.yml           # Docker Compose 配置
├── VERSION                           # 版本信息文件
├── quick-deploy.sh                   # 快速部署脚本
├── config.prod.yaml                  # Go 后端配置（如果存在）
└── backups/                          # 备份目录
    ├── 20241201_143000/              # 按时间戳的备份
    └── ...
```

---

## 📋 详细文件说明

### 1. **构建制品压缩包** `weizhi-deploy-{version}.tar.gz`

#### 上传过程
```bash
# 1. 从 Gitea Actions Artifacts 下载
# 2. 上传到服务器
scp weizhi-deploy-20241201-143022-a1b2c3d4.tar.gz user@server:/opt/weizhi/

# 3. 在服务器上解压
tar -xzf weizhi-deploy-20241201-143022-a1b2c3d4.tar.gz

# 4. 清理压缩包
rm weizhi-deploy-20241201-143022-a1b2c3d4.tar.gz
```

#### 压缩包内容
- `docker-compose.prod.yml` - 生产环境容器编排配置
- `VERSION` - 版本和构建信息
- `quick-deploy.sh` - 快速部署脚本
- `config.prod.yaml` - Go 后端配置文件（如果存在）

### 2. **生产环境配置文件** `production.env`

#### 文件内容示例
```bash
# 镜像仓库配置
REGISTRY_URL=registry.cn-hangzhou.aliyuncs.com
NAMESPACE=vest
VERSION=20241201-143022-a1b2c3d4

# 数据库配置
MYSQL_ROOT_PASSWORD=your_root_password
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=weizhi
MYSQL_USER=weizhi
MYSQL_PORT=3307

# 应用配置
JWT_SECRET=your_jwt_secret
ADMIN_PASSWORD=your_admin_password
DOMAIN=viclink.cn
ADMIN_DOMAIN=admin.viclink.cn
WEB_PORT=3000
SERVER_PORT=8080

# 文件上传配置
MAX_FILE_SIZE=10MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# 腾讯云COS配置
COS_SECRET_ID=your_secret_id
COS_SECRET_KEY=your_secret_key
COS_REGION=ap-guangzhou
COS_BUCKET=your-bucket
COS_DOMAIN=your-cos-domain.com

# JWT配置
JWT_EXPIRE_TIME=24h

# 管理员初始化
ADMIN_USERNAME=admin
ADMIN_INIT_PASSWORD=your_init_password

# 数据导入配置
IMPORT_SEED_DATA=false
IMPORT_ADMIN_DATA=false
```

#### 生成过程
```yaml
# 在 Gitea Actions 中动态生成
cat > deployment.env << EOF
REGISTRY_URL=${{ vars.REGISTRY_URL }}
NAMESPACE=${{ vars.NAMESPACE }}
VERSION=${{ env.VERSION }}
# ... 其他配置
EOF

# 上传到服务器
scp deployment.env user@server:/opt/weizhi/production.env
```

### 3. **Docker Compose 配置** `docker-compose.prod.yml`

#### 关键特性
- 使用环境变量引用镜像版本：`${VERSION:-latest}`
- 包含所有服务定义：web, server, caddy, mysql, data-import
- 生产环境优化配置：重启策略、健康检查、资源限制

#### 版本引用示例
```yaml
services:
  server:
    image: ${REGISTRY_URL}/${NAMESPACE}/weizhi_server:${VERSION:-latest}
  web:
    image: ${REGISTRY_URL}/${NAMESPACE}/weizhi_web:${VERSION:-latest}
  caddy:
    image: ${REGISTRY_URL}/${NAMESPACE}/weizhi_admin_caddy:${VERSION:-latest}
```

### 4. **版本信息文件** `VERSION`

#### 文件内容
```bash
VERSION=20241201-143022-a1b2c3d4
BUILD_TIME=2024-12-01T14:30:22Z
COMMIT_SHA=a1b2c3d4e5f6g7h8
BRANCH=main
REGISTRY_URL=registry.cn-hangzhou.aliyuncs.com
NAMESPACE=vest
```

#### 用途
- 版本追踪和审计
- 快速部署脚本使用
- 问题排查时的版本确认

### 5. **快速部署脚本** `quick-deploy.sh`

#### 脚本功能
```bash
#!/bin/bash
set -e

echo "=== Weizhi 项目快速部署 ==="

# 加载版本信息
source VERSION
export VERSION REGISTRY_URL NAMESPACE

# 加载生产环境配置
source production.env

# 验证文件存在
# 拉取镜像
# 启动服务
# 检查状态

echo "✅ 快速部署完成！"
```

#### 使用方法
```bash
# 在服务器上直接执行
cd /opt/weizhi
./quick-deploy.sh
```

---

## 🔄 部署流程

### 1. **制品下载阶段**
```yaml
- name: 下载构建制品
  uses: actions/download-artifact@v3
  with:
    name: weizhi-deploy-${{ needs.check-deploy-conditions.outputs.version }}
    path: ./artifacts
```

### 2. **制品上传阶段**
```bash
# 检查制品文件
ARTIFACT_FILE="artifacts/weizhi-deploy-${{ env.VERSION }}.tar.gz"

# 上传到服务器
scp "$ARTIFACT_FILE" user@server:/opt/weizhi/
scp deployment.env user@server:/opt/weizhi/production.env
```

### 3. **制品解压阶段**
```bash
ssh user@server "
  cd /opt/weizhi
  
  # 备份当前配置
  cp docker-compose.prod.yml docker-compose.prod.yml.backup
  
  # 解压制品
  tar -xzf weizhi-deploy-$VERSION.tar.gz
  
  # 设置权限
  chmod +x quick-deploy.sh
  
  # 清理压缩包
  rm weizhi-deploy-$VERSION.tar.gz
"
```

### 4. **服务部署阶段**
```bash
# 加载环境配置
source production.env

# 拉取镜像
docker compose -f docker-compose.prod.yml pull

# 启动服务
docker compose -f docker-compose.prod.yml up -d
```

---

## 🎯 关键优势

### 1. **版本一致性**
- 制品包含确切的版本信息
- 镜像版本与构建版本完全匹配
- 可追踪的部署历史

### 2. **部署可靠性**
- 制品经过构建验证
- 包含完整的部署配置
- 支持快速回滚

### 3. **环境隔离**
- 生产环境配置独立生成
- 敏感信息安全传输
- 配置版本化管理

### 4. **操作简化**
- 一键部署脚本
- 自动化配置管理
- 标准化部署流程

---

## 🛠️ 故障排除

### 常见问题

#### 1. 制品下载失败
```bash
# 检查制品是否存在
# 验证版本号是否正确
# 确认 Artifacts 保留期限
```

#### 2. 配置文件错误
```bash
# 验证环境变量配置
# 检查 Secrets 和 Variables 设置
# 确认配置文件格式
```

#### 3. 权限问题
```bash
# 检查 SSH 密钥配置
# 验证服务器用户权限
# 确认目录访问权限
```

---

通过这种基于制品的部署方式，确保了部署的一致性、可靠性和可追踪性，符合现代 CI/CD 的最佳实践。
