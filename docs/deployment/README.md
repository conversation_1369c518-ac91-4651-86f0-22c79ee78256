# 🚀 部署与管理

本项目支持多种部署方式，包括本地开发部署、Docker容器化部署和Gitea自动化部署。

## 📋 目录

### 部署指南
- [部署指南](./01-deployment-guide.md) - 完整的部署流程和配置
- [Docker部署](./02-docker-deployment.md) - 容器化部署方案
- [域名配置](./03-domain-configuration.md) - 域名和SSL配置
- [COS配置](./04-cos-configuration.md) - 腾讯云COS配置
- [构建优化](./05-build-optimization.md) - 构建过程优化指南
- [端口配置](./06-port-configuration.md) - 服务端口配置说明
- [数据库初始化](./07-database-initialization.md) - 数据库初始化流程
- [完整部署指南](./08-complete-deployment-guide.md) - 详细部署步骤
- [数据库命名](./09-database-naming.md) - 数据库命名规范
- [最新部署指南](./10-latest-deployment-guide.md) - 最新的部署文档

### 专项部署
- [首次部署指南](./first-deployment-guide.md) - 首次部署详细步骤
- [版本化部署](./versioned-deployment.md) - 版本管理和部署
- [版本一致性](./version-consistency.md) - 确保版本一致性
- [当前配置迁移](./current-config-migration.md) - 配置迁移指南
- [服务器部署制品](./server-deployment-artifacts.md) - 部署制品管理
- [数据库导出](./database-export.md) - 数据库导出和导入
- [数据导入服务](./data-import-service.md) - 数据导入服务配置

### 检查清单
- [生产部署检查清单](./production-checklist.md) - 生产环境部署前检查项目

## 🚀 快速部署

### 1. 本地开发部署

```bash
# 安装依赖
pnpm install

# 配置数据库
cd server-go
make create-database
make init-admin
make seed-data

# 启动服务
pnpm dev:all
```

### 2. Docker容器化部署

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

### 3. Gitea自动化部署

配置Gitea Actions后，代码推送到main分支即可自动触发构建和部署。

## 📊 部署架构

```mermaid
graph TB
    subgraph "负载均衡"
        A[Caddy反向代理]
    end
    
    subgraph "应用层"
        B[Nuxt3企业官网]
        C[Vue3管理后台]
        D[Go API服务]
    end
    
    subgraph "数据层"
        E[MySQL数据库]
        F[Redis缓存]
        G[文件存储COS]
    end
    
    A --> B
    A --> C
    A --> D
    B --> D
    C --> D
    D --> E
    D --> F
    D --> G
```

## 🔧 环境配置

### 开发环境
- **前端端口**: 4000 (web), 3001 (admin)
- **后端端口**: 3000 (server-go)
- **数据库**: 本地MySQL Docker容器

### 生产环境
- **前端端口**: 80 (HTTP), 443 (HTTPS)
- **后端端口**: 3000-3001
- **数据库**: 生产MySQL实例

## 📝 部署注意事项

1. **环境变量**: 确保所有环境变量正确配置
2. **数据库**: 检查数据库连接和权限设置
3. **文件权限**: 确保上传目录有正确的写权限
4. **SSL证书**: 生产环境必须配置SSL证书
5. **监控**: 配置日志记录和监控告警

## 🆘 故障排除

常见部署问题及解决方案：
- 端口冲突：检查端口占用情况
- 数据库连接失败：验证数据库配置和权限
- 静态资源无法访问：检查Nginx/Caddy配置
- 权限问题：检查文件和目录权限

---

*最后更新: 2025-08-01*