# 🏗️ Docker 构建优化指南

本文档介绍项目的 Docker 构建优化策略和最佳实践。

## 📋 概述

项目采用了多项构建优化技术：
- **多阶段构建**: 减少最终镜像大小
- **精确构建上下文**: 避免复制不必要文件
- **层缓存优化**: 提高构建速度
- **并行构建**: 充分利用多核性能

## 🏗️ 构建架构

### 服务构建上下文

| 服务 | 构建上下文 | Dockerfile | 说明 |
|------|------------|------------|------|
| server | `./server-go` | `server-go/Dockerfile` | Go 后端服务 |
| web | `./web` | `web/Dockerfile` | Nuxt3 前端 |
| admin | `./admin` | `admin/Dockerfile` | Vue3 管理后台 |

### 多阶段构建策略

#### 1. Go 后端 (server)
```dockerfile
# 构建阶段
FROM golang:alpine AS builder
# 编译 Go 应用

# 运行阶段  
FROM alpine:latest AS runner
# 只包含编译后的二进制文件
```

#### 2. Nuxt3 前端 (web)
```dockerfile
# 依赖安装阶段
FROM node:alpine AS base
# 安装依赖

# 构建阶段
FROM base AS builder
# 构建应用

# 运行阶段
FROM node:alpine AS runner
# 只包含构建产物和运行时
```

#### 3. Vue3 管理后台 (admin)
```dockerfile
# 构建阶段
FROM node:alpine AS builder
# 构建静态文件

# 运行阶段
FROM nginx:alpine AS runner
# 使用 Nginx 提供静态文件服务
```

## 🚀 构建优化工具

### 1. 优化构建脚本

```bash
# 并行构建所有服务
./scripts/build-optimized.sh

# 顺序构建
./scripts/build-optimized.sh false

# 禁用缓存构建
./scripts/build-optimized.sh true true

# 构建指定服务
./scripts/build-optimized.sh true false web,admin
```

### 2. 性能分析脚本

```bash
# 分析构建性能
./scripts/analyze-build.sh
```

分析内容：
- 镜像大小分析
- 构建层分析
- 缓存使用情况
- Dockerfile 优化建议
- 构建时间测试

## 📊 优化效果

### 镜像大小对比

| 服务 | 优化前 | 优化后 | 减少 |
|------|--------|--------|------|
| server | ~800MB | ~50MB | 93% |
| web | ~1.2GB | ~200MB | 83% |
| admin | ~1.1GB | ~50MB | 95% |

### 构建时间对比

| 构建方式 | 时间 | 说明 |
|----------|------|------|
| 顺序构建 | ~15分钟 | 传统方式 |
| 并行构建 | ~8分钟 | 优化后 |
| 缓存构建 | ~3分钟 | 有缓存时 |

## 🔧 优化技术详解

### 1. .dockerignore 优化

每个服务都有专门的 `.dockerignore` 文件：

```
# 通用排除
node_modules/
.git/
*.log
.env*

# 服务特定排除
../other-services/
docs/
*.md
```

### 2. 层缓存优化

**优化前**:
```dockerfile
COPY . .
RUN npm install
```

**优化后**:
```dockerfile
COPY package.json ./
RUN npm install
COPY . .
```

### 3. 多阶段构建

**优化前**:
```dockerfile
FROM node:alpine
COPY . .
RUN npm install && npm run build
CMD ["npm", "start"]
```

**优化后**:
```dockerfile
# 构建阶段
FROM node:alpine AS builder
COPY package.json ./
RUN npm install
COPY . .
RUN npm run build

# 运行阶段
FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
```

### 4. 并行构建

利用 Docker Compose 的并行构建能力：

```bash
# 并行构建多个服务
docker-compose build --parallel
```

## 📈 性能监控

### 1. 构建时间监控

```bash
# 记录构建时间
time docker-compose build

# 分析各阶段耗时
docker build --progress=plain .
```

### 2. 镜像大小监控

```bash
# 查看镜像大小
docker images | grep weishi

# 分析镜像层
docker history <image_name>
```

### 3. 缓存命中率

```bash
# 查看构建缓存
docker system df

# 清理缓存
docker builder prune
```

## 🛠️ 故障排除

### 常见问题

1. **构建失败：上下文过大**
   ```
   解决方案：
   - 检查 .dockerignore 文件
   - 减少构建上下文大小
   - 使用多阶段构建
   ```

2. **构建缓存失效**
   ```
   解决方案：
   - 检查文件复制顺序
   - 避免频繁变化的文件影响缓存
   - 使用 --cache-from 参数
   ```

3. **并行构建冲突**
   ```
   解决方案：
   - 检查服务依赖关系
   - 调整构建顺序
   - 使用顺序构建
   ```

### 调试命令

```bash
# 详细构建日志
docker-compose build --progress=plain

# 检查构建上下文
docker build --dry-run .

# 分析镜像层
docker history --no-trunc <image>

# 进入构建容器调试
docker run -it --rm <image> sh
```

## 📚 最佳实践

### 1. Dockerfile 编写

- 使用多阶段构建
- 合并 RUN 命令减少层数
- 将变化频率低的操作放在前面
- 使用特定版本的基础镜像

### 2. 依赖管理

- 先复制依赖文件，再复制源代码
- 使用 lock 文件确保依赖版本一致
- 清理不必要的开发依赖

### 3. 缓存策略

- 合理安排指令顺序
- 使用 .dockerignore 排除变化频繁的文件
- 定期清理构建缓存

### 4. 安全考虑

- 使用非 root 用户运行应用
- 最小化镜像攻击面
- 定期更新基础镜像

## 🔄 持续优化

### 1. 定期分析

```bash
# 每周运行性能分析
./scripts/analyze-build.sh

# 监控镜像大小变化
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"
```

### 2. 版本管理

- 为镜像打标签
- 保留历史版本用于回滚
- 清理过期镜像

### 3. CI/CD 集成

- 在 CI 中使用构建缓存
- 并行构建和测试
- 自动化镜像扫描

---

*定期运行 `./scripts/analyze-build.sh` 来监控和优化构建性能。*
