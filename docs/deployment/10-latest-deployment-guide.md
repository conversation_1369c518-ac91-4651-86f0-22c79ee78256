# 🚀 最新部署指南 (2024)

本文档基于最新的架构，包含管理后台静态文件部署方案。

## 📊 最新架构

```
主 Caddy (80/443)
├── /admin/* → 静态文件 (./static/admin/)
├── /api/*   → server:3001 (Go 后端)
└── /*       → web:3000 (Nuxt3 前端)
```

## 🛠️ 生产环境部署

### 1. 服务器准备

```bash
# 连接服务器
ssh root@your-server-ip

# 安装 Docker 和 Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 配置防火墙
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw --force enable
```

### 2. 代码部署

```bash
# 创建项目目录
sudo mkdir -p /opt/weishi
sudo chown $USER:$USER /opt/weishi
cd /opt/weishi

# 克隆代码
git clone <your-repository-url> .

# 或上传代码包
# scp -r ./Nuxt3Web root@your-server:/opt/weishi/
```

### 3. 环境配置

```bash
# 复制配置模板
cp docker.env.example docker.env

# 编辑配置文件
vim docker.env
```

**必须配置的项目**：
```env
# 数据库配置
MYSQL_ROOT_PASSWORD=your_very_secure_root_password_2024
MYSQL_PASSWORD=your_secure_db_password_2024
MYSQL_DATABASE=weizhi

# 管理员配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_admin_password_2024

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-production-$(openssl rand -hex 32)

# 域名配置
DOMAIN=your-domain.com

# 腾讯云 COS 配置（可选）
COS_SECRET_ID=your_cos_secret_id
COS_SECRET_KEY=your_cos_secret_key
COS_REGION=ap-nanjing
COS_BUCKET=your_bucket_name
```

### 4. 一键部署

```bash
# 运行部署脚本
./scripts/deploy.sh
```

**部署脚本会自动执行**：
1. 构建管理后台静态文件
2. 生成数据库初始化脚本
3. 验证配置文件
4. 构建 Docker 镜像
5. 启动所有服务
6. 运行健康检查

### 5. 配置主 Caddy

在你的主 Caddyfile 中添加：

```caddyfile
your-domain.com {
    # 管理后台静态文件
    handle /admin* {
        root * /opt/weishi/static
        file_server
        try_files {path} /admin/index.html
        
        # 静态资源缓存
        @static {
            path *.js *.css *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2
        }
        header @static Cache-Control "public, max-age=31536000"
    }
    
    # API 接口
    handle /api* {
        reverse_proxy localhost:3001
    }
    
    # 前端网站
    reverse_proxy localhost:3000
}
```

### 6. 重启 Caddy

```bash
# 重新加载 Caddy 配置
sudo systemctl reload caddy

# 或重启 Caddy
sudo systemctl restart caddy
```

## 🔍 验证部署

### 1. 检查服务状态

```bash
# 查看 Docker 服务
docker-compose --env-file=docker.env ps

# 运行健康检查
./scripts/test-health.sh
```

### 2. 测试访问

```bash
# 测试前端
curl -I https://your-domain.com

# 测试管理后台
curl -I https://your-domain.com/admin

# 测试 API
curl -I https://your-domain.com/api/health
```

### 3. 管理员登录

- **访问地址**：https://your-domain.com/admin
- **用户名**：你在 docker.env 中设置的 ADMIN_USERNAME
- **密码**：你在 docker.env 中设置的 ADMIN_PASSWORD

## 🔄 更新部署

### 代码更新

```bash
cd /opt/weishi

# 拉取最新代码
git pull origin main

# 重新构建管理后台
./scripts/build-admin.sh

# 重新部署服务
docker-compose --env-file=docker.env build
docker-compose --env-file=docker.env up -d

# 验证更新
./scripts/test-health.sh
```

### 管理后台单独更新

```bash
# 只更新管理后台静态文件
./scripts/build-admin.sh

# 不需要重启其他服务
```

## 🛠️ 开发环境部署

### 1. 本地开发

```bash
# 安装依赖
pnpm install
cd web && pnpm install && cd ..
cd admin && pnpm install && cd ..
cd server-go && go mod tidy && cd ..

# 创建数据库
mysql -u root -p
CREATE DATABASE weizhi CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 设置管理员密码并导入数据
ADMIN_PASSWORD="your_password" ./scripts/setup-admin.sh
mysql -u root -p weizhi < server-go/scripts/init_database.sql

# 启动服务
# 终端1: 后端
cd server-go && go run cmd/main.go

# 终端2: 前端
cd web && pnpm dev

# 终端3: 管理后台
cd admin && pnpm dev
```

### 2. 访问地址

- **前端网站**：http://localhost:4000
- **管理后台**：http://localhost:5173 (开发环境) / https://your-domain.com/admin (生产环境)
- **API 文档**：http://localhost:3001/swagger/index.html

## 🚨 故障排除

### 常见问题

1. **静态文件 404**
   ```bash
   # 检查静态文件是否存在
   ls -la static/admin/
   
   # 重新构建
   ./scripts/build-admin.sh
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   ./scripts/manage-database.sh status
   
   # 重置数据库
   ./scripts/manage-database.sh reset
   ```

3. **服务启动失败**
   ```bash
   # 查看日志
   docker-compose --env-file=docker.env logs -f
   
   # 重启服务
   docker-compose --env-file=docker.env restart
   ```

## 📋 部署检查清单

- [ ] 服务器环境配置完成
- [ ] 代码部署到服务器
- [ ] docker.env 配置完成
- [ ] 管理后台静态文件构建成功
- [ ] Docker 服务启动正常
- [ ] 主 Caddy 配置更新
- [ ] 域名访问正常
- [ ] 管理员登录成功
- [ ] API 接口正常
- [ ] SSL 证书申请成功

## 📞 技术支持

如有问题，请查看：
- [完整部署指南](08-complete-deployment-guide.md)
- [数据库初始化](07-database-initialization.md)
- [端口配置](06-port-configuration.md)

---

*本文档基于最新架构编写，适用于 2024 年部署。*
