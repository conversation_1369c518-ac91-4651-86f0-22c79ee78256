# 🗄️ 数据库初始化指南

本文档介绍项目的数据库初始化配置和管理。

## 📋 概述

项目使用 MySQL 8.0 数据库，包含：
- **完整的表结构**：用户管理、内容管理、权限系统等
- **初始数据**：管理员账号、基础配置、示例数据
- **自动初始化**：Docker 容器启动时自动执行

## 🏗️ 数据库结构

### 核心表

| 表名 | 说明 | 记录数 |
|------|------|--------|
| admin_users | 管理员用户 | 1 |
| admin_roles | 角色管理 | 3 |
| admin_permissions | 权限管理 | 22 |
| swipers | 轮播图 | 4 |
| our_services | 服务配置 | 5 |
| partners | 合作伙伴 | 13 |
| friend_links | 友情链接 | 9 |
| recruitments | 招聘信息 | 3 |
| part_platform | 零件平台 | 8 |
| project_cases | 项目案例 | 5 |

### 系统表

| 表名 | 说明 |
|------|------|
| admin_user_roles | 用户角色关联 |
| admin_role_permissions | 角色权限关联 |
| admin_logs | 操作日志 |
| cache_versions | 缓存版本 |
| file_uploads | 文件上传记录 |

## 🚀 自动初始化

### Docker 容器初始化

项目配置了 Docker 容器启动时自动初始化数据库：

```yaml
# docker-compose.yml
mysql:
  volumes:
    - mysql_data:/var/lib/mysql
    - ./server-go/scripts/init_database.sql:/docker-entrypoint-initdb.d/01-init_database.sql
```

### 初始化流程

1. **容器启动**：MySQL 容器首次启动
2. **检查数据**：如果数据目录为空，执行初始化
3. **执行脚本**：自动执行 `init_database.sql`
4. **创建数据库**：创建 `weizhi` 数据库
5. **创建表结构**：创建所有必要的表
6. **插入数据**：插入初始数据和配置

## 👤 默认账号

### 管理员账号

- **用户名**：`admin`
- **密码**：`admin123`
- **角色**：超级管理员
- **权限**：所有权限

### 首次登录

1. 访问管理后台：http://localhost:5173
2. 使用默认账号登录
3. **立即修改密码**（安全要求）
4. 创建其他管理员账号

## 🛠️ 数据库管理工具

### 管理脚本

```bash
# 查看数据库状态
./scripts/manage-database.sh status

# 导出当前数据库
./scripts/manage-database.sh export

# 备份数据库
./scripts/manage-database.sh backup

# 重置数据库
./scripts/manage-database.sh reset
```

### 导出数据库

当你修改了数据库内容，需要更新初始化文件时：

```bash
# 1. 导出数据库
./scripts/export-database.sh

# 2. 修复数据库名称
./scripts/fix-database-name.sh

# 3. 或者使用一键命令
./scripts/manage-database.sh export
```

## 📊 数据管理

### 业务数据

项目包含以下业务数据：

1. **轮播图数据**：首页轮播图片
2. **服务配置**：公司服务介绍
3. **合作伙伴**：合作伙伴 Logo
4. **友情链接**：外部链接
5. **招聘信息**：职位招聘
6. **零件平台**：产品展示
7. **项目案例**：成功案例

### 权限数据

完整的 RBAC 权限系统：

1. **用户管理**：管理员账号管理
2. **角色管理**：角色定义和分配
3. **权限管理**：细粒度权限控制
4. **操作日志**：用户操作记录

## 🔧 故障排除

### 常见问题

1. **初始化失败**
   ```bash
   # 检查容器日志
   docker-compose logs mysql
   
   # 检查初始化文件
   ls -la server-go/scripts/init_database.sql
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose ps mysql
   
   # 测试连接
   docker-compose exec mysql mysql -u root -p
   ```

3. **数据丢失**
   ```bash
   # 恢复备份
   ./scripts/manage-database.sh backup
   
   # 重新初始化
   ./scripts/manage-database.sh reset
   ```

### 调试命令

```bash
# 进入数据库容器
docker-compose exec mysql bash

# 连接数据库
mysql -u root -p

# 查看数据库
SHOW DATABASES;
USE weishi_db;
SHOW TABLES;

# 检查管理员账号
SELECT * FROM admin_users;
```

## 🔒 安全建议

### 生产环境

1. **修改默认密码**
   - 立即修改管理员密码
   - 使用强密码策略

2. **数据库安全**
   - 修改数据库 root 密码
   - 限制数据库访问权限
   - 定期备份数据

3. **权限管理**
   - 按需分配权限
   - 定期审查用户权限
   - 启用操作日志

### 备份策略

```bash
# 定期备份（建议每日）
./scripts/manage-database.sh backup

# 备份文件位置
ls -la backups/

# 保留策略：保留最近30天的备份
find backups/ -name "*.sql" -mtime +30 -delete
```

## 📚 相关文档

- [部署指南](01-deployment-guide.md)
- [端口配置](06-port-configuration.md)
- [健康检查](03-health-monitoring.md)

---

*首次部署时，数据库会自动初始化。如需更新数据，请使用 `./scripts/manage-database.sh export` 重新导出。*
