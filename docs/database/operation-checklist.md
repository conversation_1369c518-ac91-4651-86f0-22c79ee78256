# ✅ 数据库表结构优化操作检查清单

## 🎯 优化前准备

### 环境检查
- [ ] 确认使用Docker MySQL容器
- [ ] 检查容器运行状态: `docker ps | grep mysql`
- [ ] 测试数据库连接: `make docker-shell DOCKER_MYSQL_CONTAINER=weishi_mysql_local`
- [ ] 确认数据库名称: `weizhi`
- [ ] 确认用户权限: `root`

### 代码分析
- [ ] 分析前端实际使用的字段
- [ ] 确认冗余字段列表
- [ ] 检查API接口依赖
- [ ] 评估删除字段的影响

### 备份准备
- [ ] 备份当前数据库: `make backup-db`
- [ ] 保存当前表结构: `mysqldump --no-data`
- [ ] 准备回滚脚本
- [ ] 测试回滚流程

## 🔧 优化执行

### Go模型修改
- [ ] 修改 `internal/model/content.go` 中的模型定义
- [ ] 删除冗余字段
- [ ] 保留必要字段
- [ ] 添加字段注释

### 迁移脚本准备
- [ ] 创建迁移脚本: `scripts/migrations/001_optimize_table_structure.sql`
- [ ] 编写DROP COLUMN语句
- [ ] 测试SQL语法
- [ ] 添加事务控制

### 执行迁移
- [ ] 停止应用服务: `make stop`
- [ ] 执行迁移: `make docker-migrate DOCKER_MYSQL_CONTAINER=weishi_mysql_local`
- [ ] 检查迁移结果
- [ ] 验证表结构变化

## ✅ 验证检查

### 表结构验证
- [ ] 检查 our_services 表: `DESCRIBE our_services;`
  - [ ] 确认只有5个字段: id, created_at, updated_at, image, order
- [ ] 检查 project_cases 表: `DESCRIBE project_cases;`
  - [ ] 确认只有5个字段: id, created_at, updated_at, url, deleted_at
- [ ] 检查 swipers 表: `DESCRIBE swipers;`
  - [ ] 确认删除了 status 字段
- [ ] 检查 part_platform 表: `DESCRIBE part_platform;`
  - [ ] 确认删除了 order 字段
- [ ] 检查 recruitments 表: `DESCRIBE recruitments;`
  - [ ] 确认删除了 is_active, order 字段

### 代码兼容性检查
- [ ] 修复Go代码编译错误
- [ ] 更新TypeScript类型定义
- [ ] 修复管理后台代码
- [ ] 更新前端组件
- [ ] 测试API接口

### 功能测试
- [ ] 启动服务器: `make start`
- [ ] 测试健康检查: `curl http://localhost:3000/api/health`
- [ ] 测试轮播图接口
- [ ] 测试服务列表接口
- [ ] 测试项目案例接口
- [ ] 测试零件平台接口
- [ ] 测试招聘信息接口

## 📊 性能验证

### 存储空间检查
- [ ] 查看表大小变化
```sql
SELECT 
    TABLE_NAME,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Size (MB)',
    TABLE_ROWS
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'weizhi' 
AND TABLE_NAME IN ('our_services', 'project_cases', 'swipers', 'part_platform', 'recruitments');
```

### 查询性能检查
- [ ] 测试查询速度
- [ ] 检查执行计划: `EXPLAIN SELECT * FROM table_name;`
- [ ] 验证索引使用情况

## 🔄 后续维护

### 文档更新
- [ ] 更新数据库表结构文档
- [ ] 更新API文档
- [ ] 更新开发者指南
- [ ] 记录变更日志

### 监控设置
- [ ] 设置性能监控
- [ ] 配置错误告警
- [ ] 定期备份计划
- [ ] 容量监控

## 🆘 应急处理

### 回滚准备
- [ ] 准备回滚脚本
- [ ] 测试回滚流程
- [ ] 确认数据恢复方案
- [ ] 通知相关人员

### 故障处理
- [ ] 准备故障排除手册
- [ ] 联系方式列表
- [ ] 紧急恢复流程
- [ ] 事后分析模板

## 📋 完成确认

### 技术确认
- [ ] 所有测试通过
- [ ] 性能指标达标
- [ ] 代码质量检查
- [ ] 安全性验证

### 业务确认
- [ ] 功能正常运行
- [ ] 用户体验良好
- [ ] 数据完整性
- [ ] 系统稳定性

### 文档确认
- [ ] 操作文档完整
- [ ] 变更记录准确
- [ ] 知识传递完成
- [ ] 培训材料更新

---

## 📝 签名确认

| 角色 | 姓名 | 签名 | 日期 |
|------|------|------|------|
| 开发者 | | | |
| 测试者 | | | |
| 运维者 | | | |
| 项目经理 | | | |

---

*检查清单 v1.0 | 2025-01-04*  
*项目: 蔚之领域智能科技*
