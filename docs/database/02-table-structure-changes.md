# 🔄 数据库表结构变更记录

本文档记录了项目数据库表结构的重要变更历史，确保开发团队了解数据库演进过程。

## 📋 变更概述

为了保持 server-go 项目与 server 项目（NestJS + Drizzle ORM）的数据库结构一致性，进行了全面的表结构对齐工作。

## 🎯 对齐目标

- **统一数据结构**: 确保两个后端项目使用相同的数据库结构
- **简化维护**: 减少因结构差异导致的维护复杂性
- **数据一致性**: 保证数据在不同系统间的一致性
- **迁移便利**: 便于在两个系统间进行数据迁移

## 🔧 主要变更

### 1. BaseModel 结构调整

#### 变更前
```go
type BaseModel struct {
    ID        uint           `gorm:"primarykey" json:"id"`
    CreatedAt time.Time      `json:"created_at"`
    UpdatedAt time.Time      `json:"updated_at"`
    DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}
```

#### 变更后
```go
type BaseModel struct {
    ID        uint      `gorm:"primarykey" json:"id"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}
```

**变更说明**:
- **移除软删除**: 删除了 `DeletedAt` 字段，不再使用GORM的软删除功能
- **简化结构**: 采用硬删除方式，简化数据管理
- **对齐目标**: 与NestJS项目的基础模型保持一致

### 2. Service 模型重构

#### 变更前
```go
type Service struct {
    BaseModel
    Name        string `json:"name" gorm:"size:100;not null"`
    Description string `json:"description" gorm:"type:text"`
    Image       string `json:"image" gorm:"size:255"`
    Order       int    `json:"order" gorm:"default:0"`
}
```

#### 变更后
```go
type Service struct {
    BaseModel
    Name            string `json:"name" gorm:"column:name;size:100;not null"`
    Type            string `json:"type" gorm:"column:type;size:50"`
    Description     string `json:"description" gorm:"column:description;type:text"`
    FullDescription string `json:"fullDescription" gorm:"column:fullDescription;type:text"`
    Image           string `json:"image" gorm:"column:image;size:500"`
    Features        string `json:"features" gorm:"column:features;type:text"`
    Equipments      string `json:"equipments" gorm:"column:equipments;type:text"`
    TestItems       string `json:"testItems" gorm:"column:testItems;type:text"`
    Order           int    `json:"order" gorm:"column:order;default:0"`
}
```

**变更说明**:
- **新增字段**: 添加了 `Type`, `FullDescription`, `Features`, `Equipments`, `TestItems` 字段
- **字段扩展**: 提供更详细的服务描述和功能说明
- **列映射**: 添加显式的数据库列映射

### 3. News 模型简化

#### 变更前
```go
type News struct {
    BaseModel
    Title       string    `json:"title" gorm:"size:255;not null"`
    Summary     string    `json:"summary" gorm:"size:500"`
    Content     string    `json:"content" gorm:"type:longtext;not null"`
    CoverImage  string    `json:"cover_image" gorm:"size:255"`
    Author      string    `json:"author" gorm:"size:100"`
    PublishTime time.Time `json:"publish_time"`
    IsPublished bool      `json:"is_published" gorm:"default:false"`
    ViewCount   int       `json:"view_count" gorm:"default:0"`
}
```

#### 变更后
```go
type News struct {
    BaseModel
    Title   string `json:"title" gorm:"column:title;size:255;not null"`
    Content string `json:"content" gorm:"column:content;type:longtext;not null"`
    Image   string `json:"image" gorm:"column:image;size:500"`
    Author  string `json:"author" gorm:"column:author;size:100"`
    Status  int    `json:"status" gorm:"column:status;default:1"`
}
```

**变更说明**:
- **字段简化**: 移除了 `Summary`, `CoverImage`, `PublishTime`, `IsPublished`, `ViewCount` 字段
- **状态管理**: 使用 `Status` 字段替代 `IsPublished` 布尔值
- **图片字段**: `CoverImage` 重命名为 `Image`

### 4. Recruitment 模型调整

#### 变更前
```go
type Recruitment struct {
    BaseModel
    Position    string `json:"position" gorm:"size:100;not null"`
    Department  string `json:"department" gorm:"size:100"`
    Description string `json:"description" gorm:"type:text"`
    Requirement string `json:"requirement" gorm:"type:text"`
    IsActive    bool   `json:"is_active" gorm:"default:true"`
}
```

#### 变更后
```go
type Recruitment struct {
    BaseModel
    Name    string `json:"name" gorm:"column:name;size:100;not null"`
    Content string `json:"content" gorm:"column:content;type:text"`
}
```

**变更说明**:
- **字段重命名**: `Position` → `Name`, `Description` → `Content`
- **字段移除**: 删除了 `Department`, `Requirement`, `IsActive` 字段
- **结构简化**: 采用更简洁的数据结构

### 5. ProjectCase 模型优化

#### 变更前
```go
type ProjectCase struct {
    BaseModel
    Title       string `json:"title" gorm:"size:255;not null"`
    Description string `json:"description" gorm:"type:text"`
    CoverImage  string `json:"cover_image" gorm:"size:255"`
    Tags        string `json:"tags" gorm:"size:500"`
    Order       int    `json:"order" gorm:"default:0"`
}
```

#### 变更后
```go
type ProjectCase struct {
    BaseModel
    Title       string `json:"title" gorm:"column:title;size:255"`
    Description string `json:"description" gorm:"column:description;type:text"`
    Image       string `json:"image" gorm:"column:image;size:500"`
    URL         string `json:"url" gorm:"column:url;size:500"`
    Sort        int    `json:"sort" gorm:"column:sort;default:0"`
}
```

**变更说明**:
- **字段调整**: `CoverImage` → `Image`, `Order` → `Sort`
- **新增字段**: 添加了 `URL` 字段用于外部链接
- **字段移除**: 删除了 `Tags` 字段
- **可选字段**: `Title` 和 `Description` 改为可选

### 6. PartPlatform 模型扩展

#### 变更前
```go
type PartPlatform struct {
    BaseModel
    Name        string `json:"name" gorm:"size:100;not null"`
    Description string `json:"description" gorm:"type:text"`
    Image       string `json:"image" gorm:"size:255"`
    URL         string `json:"url" gorm:"size:500"`
}
```

#### 变更后
```go
type PartPlatform struct {
    BaseModel
    Name         string `json:"name" gorm:"column:name;size:100;not null"`
    Description  string `json:"description" gorm:"column:description;type:text"`
    Image        string `json:"image" gorm:"column:image;size:500"`
    URL          string `json:"url" gorm:"column:url;size:500"`
    Parameters   string `json:"parameters" gorm:"column:parameters;type:text"`
    Applications string `json:"applications" gorm:"column:applications;type:text"`
}
```

**变更说明**:
- **新增字段**: 添加了 `Parameters` 和 `Applications` 字段
- **功能扩展**: 提供更详细的平台参数和应用场景描述

## 🔧 数据库字段映射

### 映射策略

为了解决SQL文件使用驼峰命名而GORM默认使用下划线命名的问题，添加了显式的列映射：

```go
// 示例：显式列映射
type Service struct {
    BaseModel
    FullDescription string `gorm:"column:fullDescription"`  // 映射到 fullDescription 列
    TestItems       string `gorm:"column:testItems"`        // 映射到 testItems 列
    CreatedAt       time.Time `gorm:"column:created_at"`    // 映射到 created_at 列
    UpdatedAt       time.Time `gorm:"column:updated_at"`    // 映射到 updated_at 列
}
```

### 命名规范

| Go字段名 | 数据库列名 | 说明 |
|----------|------------|------|
| FullDescription | fullDescription | 驼峰命名 |
| TestItems | testItems | 驼峰命名 |
| CreatedAt | created_at | 下划线命名 |
| UpdatedAt | updated_at | 下划线命名 |

## 📊 变更影响分析

### 数据迁移

| 表名 | 影响程度 | 迁移复杂度 | 备注 |
|------|----------|------------|------|
| services | 高 | 中等 | 需要数据转换 |
| news | 中等 | 低 | 字段映射 |
| recruitments | 中等 | 低 | 字段重命名 |
| project_cases | 低 | 低 | 字段调整 |
| part_platforms | 低 | 低 | 新增字段 |

### API兼容性

- **向后兼容**: 大部分API保持向后兼容
- **字段变更**: 部分字段名称变更需要前端适配
- **新增功能**: 新增字段提供更丰富的功能

## 🔄 迁移步骤

### 1. 数据库备份

```bash
# 备份现有数据库
mysqldump -u root -p weizhi > backup_before_migration.sql
```

### 2. 执行迁移

```bash
# 执行表结构迁移
cd server-go
make migrate-up

# 或手动执行
go run cmd/migrate/main.go
```

### 3. 数据验证

```bash
# 验证表结构
make verify-tables

# 验证数据完整性
make verify-data
```

## ✅ 验证清单

迁移完成后的验证项目：

- [ ] 所有表结构已更新
- [ ] 数据完整性检查通过
- [ ] API接口正常工作
- [ ] 前端页面正常显示
- [ ] 新增字段功能正常
- [ ] 性能测试通过
- [ ] 备份文件已创建

## 🔮 后续计划

### 短期计划
- **性能优化**: 为新增字段添加适当索引
- **数据清理**: 清理无用的历史数据
- **文档更新**: 更新相关API文档

### 长期计划
- **监控完善**: 添加表结构变更监控
- **自动化**: 建立自动化迁移流程
- **版本控制**: 完善数据库版本控制机制

## 📞 支持和反馈

如果在迁移过程中遇到问题：

1. **查看日志**: 检查应用和数据库日志
2. **回滚操作**: 使用备份文件进行回滚
3. **联系支持**: 联系开发团队获取帮助

---

*表结构变更已完成，系统运行稳定。如有问题请及时反馈。*
