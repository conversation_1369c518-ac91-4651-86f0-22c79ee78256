# 🗄️ 数据库概览

本文档介绍蔚之领域智能科技项目的数据库架构、设计原则和配置说明。

## 📊 数据库架构

### 技术选型

| 组件 | 版本 | 用途 | 特点 |
|------|------|------|------|
| MySQL | 8.0+ | 主数据库 | 高性能、事务支持、丰富生态 |
| GORM | v1.25.5 | ORM框架 | 自动迁移、关联查询、类型安全 |
| Redis | 7.0+ | 缓存数据库 | 高性能缓存、会话存储 |

### 数据库连接配置

**配置文件**: `server-go/config.yaml`

```yaml
database:
  host: "localhost"          # 数据库主机
  port: "3306"              # 数据库端口
  username: "root"          # 数据库用户名
  password: "Ydb3344%"      # 数据库密码
  database: "weizhi"        # 数据库名称
  charset: "utf8mb4"        # 字符集
  
  # 连接池配置
  max_idle_conns: 10        # 最大空闲连接数
  max_open_conns: 100       # 最大打开连接数
  conn_max_lifetime: 3600   # 连接最大生存时间(秒)
```

### 环境配置

#### 开发环境

```yaml
database:
  host: "localhost"
  port: "3306"
  username: "root"
  password: "Ydb3344%"
  database: "weizhi"
```

#### 生产环境

```yaml
database:
  host: "${DB_HOST}"
  port: "${DB_PORT}"
  username: "${DB_USER}"
  password: "${DB_PASSWORD}"
  database: "${DB_NAME}"
```

## 🏗️ 数据库设计原则

### 命名规范

#### 表命名

- **格式**: 小写字母 + 下划线
- **单数形式**: 使用单数名词
- **示例**: `user`, `news`, `service`, `project_case`

#### 字段命名

- **格式**: 小写字母 + 下划线
- **描述性**: 字段名要有明确含义
- **示例**: `user_id`, `created_at`, `updated_at`

#### 索引命名

- **主键**: `pk_表名`
- **外键**: `fk_表名_字段名`
- **唯一索引**: `uk_表名_字段名`
- **普通索引**: `idx_表名_字段名`

### 数据类型规范

| 数据类型 | 使用场景 | 示例 |
|----------|----------|------|
| INT | 整数、ID | `id`, `user_id`, `status` |
| VARCHAR | 短文本 | `title`, `name`, `email` |
| TEXT | 长文本 | `content`, `description` |
| DATETIME | 日期时间 | `created_at`, `updated_at` |
| DECIMAL | 精确数值 | `price`, `amount` |
| TINYINT | 布尔值 | `is_active`, `is_deleted` |

### 字段设计规范

#### 必备字段

每个业务表都应包含以下字段：

```sql
CREATE TABLE example (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at DATETIME NULL DEFAULT NULL,
    INDEX idx_deleted_at (deleted_at)
);
```

#### 字段约束

- **NOT NULL**: 必填字段使用 NOT NULL 约束
- **DEFAULT**: 为字段设置合理的默认值
- **COMMENT**: 为字段添加注释说明

## 📋 数据表结构

### 核心业务表

#### 1. 用户相关表

```mermaid
erDiagram
    users ||--o{ user_roles : has
    roles ||--o{ user_roles : belongs_to
    roles ||--o{ role_permissions : has
    permissions ||--o{ role_permissions : belongs_to
    
    users {
        bigint id PK
        varchar username
        varchar email
        varchar password_hash
        varchar real_name
        varchar phone
        tinyint status
        datetime created_at
        datetime updated_at
        datetime deleted_at
    }
    
    roles {
        bigint id PK
        varchar name
        varchar description
        tinyint status
        datetime created_at
        datetime updated_at
        datetime deleted_at
    }
    
    permissions {
        bigint id PK
        varchar name
        varchar code
        varchar description
        varchar resource
        varchar action
        datetime created_at
        datetime updated_at
    }
```

#### 2. 内容管理表

```mermaid
erDiagram
    news {
        bigint id PK
        varchar title
        text content
        varchar image
        varchar author
        tinyint status
        datetime published_at
        datetime created_at
        datetime updated_at
        datetime deleted_at
    }
    
    services {
        bigint id PK
        varchar name
        varchar type
        text description
        text full_description
        varchar image
        text features
        text equipments
        text test_items
        int order
        datetime created_at
        datetime updated_at
        datetime deleted_at
    }
    
    project_cases {
        bigint id PK
        varchar title
        text description
        varchar image
        varchar client
        varchar industry
        datetime project_date
        text technologies
        text results
        datetime created_at
        datetime updated_at
        datetime deleted_at
    }
```

#### 3. 系统配置表

```mermaid
erDiagram
    swipers {
        bigint id PK
        varchar title
        varchar image
        varchar link
        int order
        tinyint status
        datetime created_at
        datetime updated_at
        datetime deleted_at
    }
    
    partners {
        bigint id PK
        varchar name
        varchar logo
        varchar website
        text description
        int order
        datetime created_at
        datetime updated_at
        datetime deleted_at
    }
    
    friend_links {
        bigint id PK
        varchar name
        varchar url
        varchar description
        int order
        tinyint status
        datetime created_at
        datetime updated_at
        datetime deleted_at
    }
```

### 表关系说明

#### 用户权限关系

- **用户 ↔ 角色**: 多对多关系，通过 `user_roles` 中间表
- **角色 ↔ 权限**: 多对多关系，通过 `role_permissions` 中间表
- **用户权限**: 通过角色间接获得权限

#### 内容关系

- **新闻**: 独立表，支持分类和标签
- **服务**: 独立表，支持类型分类
- **项目案例**: 独立表，支持行业分类

## 🔧 数据库操作

### 初始化数据库

```bash
# 创建数据库
make create-database

# 初始化表结构
make init-tables

# 导入种子数据
make seed-data

# 一键完成所有操作
make setup-database
```

### 数据库迁移

```bash
# 查看迁移状态
make migration-status

# 执行迁移
make migrate-up

# 回滚迁移
make migrate-down

# 重置数据库
make reset-db
```

### 数据备份

```bash
# 备份数据库
make backup-db

# 恢复数据库
make restore-db

# 导出表结构
make export-schema
```

## 📊 性能优化

### 索引策略

#### 主键索引

```sql
-- 自动创建主键索引
ALTER TABLE users ADD PRIMARY KEY (id);
```

#### 唯一索引

```sql
-- 用户名唯一索引
CREATE UNIQUE INDEX uk_users_username ON users (username);

-- 邮箱唯一索引
CREATE UNIQUE INDEX uk_users_email ON users (email);
```

#### 复合索引

```sql
-- 状态和创建时间复合索引
CREATE INDEX idx_news_status_created ON news (status, created_at);

-- 软删除和状态复合索引
CREATE INDEX idx_users_deleted_status ON users (deleted_at, status);
```

### 查询优化

#### 分页查询

```sql
-- 使用 LIMIT 和 OFFSET
SELECT * FROM news 
WHERE status = 1 AND deleted_at IS NULL
ORDER BY created_at DESC
LIMIT 10 OFFSET 20;
```

#### 关联查询

```sql
-- 使用 JOIN 优化关联查询
SELECT u.*, r.name as role_name
FROM users u
LEFT JOIN user_roles ur ON u.id = ur.user_id
LEFT JOIN roles r ON ur.role_id = r.id
WHERE u.deleted_at IS NULL;
```

### 连接池配置

```go
// GORM 连接池配置
db.DB().SetMaxIdleConns(10)           // 最大空闲连接数
db.DB().SetMaxOpenConns(100)          // 最大打开连接数
db.DB().SetConnMaxLifetime(time.Hour) // 连接最大生存时间
```

## 🔒 数据安全

### 数据加密

- **密码**: 使用 bcrypt 加密存储
- **敏感信息**: 使用 AES 加密存储
- **传输加密**: 使用 HTTPS/TLS

### 访问控制

- **数据库用户**: 最小权限原则
- **网络访问**: 限制数据库访问IP
- **审计日志**: 记录所有数据库操作

### 备份策略

- **全量备份**: 每日凌晨执行
- **增量备份**: 每小时执行
- **异地备份**: 定期同步到远程存储

## 🔍 监控和维护

### 性能监控

```sql
-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';

-- 查看连接数
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Max_used_connections';

-- 查看表大小
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.TABLES 
WHERE table_schema = 'weizhi'
ORDER BY (data_length + index_length) DESC;
```

### 维护任务

```bash
# 优化表
OPTIMIZE TABLE news, users, services;

# 分析表
ANALYZE TABLE news, users, services;

# 检查表
CHECK TABLE news, users, services;

# 修复表
REPAIR TABLE news, users, services;
```

## 🐛 故障排除

### 常见问题

#### 连接失败

```bash
# 检查 MySQL 服务状态
systemctl status mysql

# 检查端口监听
netstat -tulpn | grep 3306

# 检查防火墙
ufw status
```

#### 权限问题

```sql
-- 检查用户权限
SHOW GRANTS FOR 'root'@'localhost';

-- 创建用户并授权
CREATE USER 'weishi'@'localhost' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON weizhi.* TO 'weishi'@'localhost';
FLUSH PRIVILEGES;
```

#### 性能问题

```sql
-- 查看正在执行的查询
SHOW PROCESSLIST;

-- 查看慢查询日志
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;

-- 查看表锁状态
SHOW OPEN TABLES WHERE In_use > 0;
```

---

*更多数据库相关信息请参考 [表结构设计](./02-table-structure.md) 和 [数据迁移](./03-migrations.md) 文档。*
