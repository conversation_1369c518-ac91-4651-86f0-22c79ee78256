# 🚀 数据库操作快速参考

## 📋 常用命令速查

### Docker MySQL 操作

```bash
# 查看MySQL容器
docker ps | grep mysql

# 连接到MySQL容器
docker exec -it weishi_mysql_local mysql -uroot -p'Ydb3344%' weizhi

# 执行SQL文件
docker exec -i weishi_mysql_local mysql -uroot -p'Ydb3344%' weizhi < script.sql

# 查看表结构
docker exec -i weishi_mysql_local mysql -uroot -p'Ydb3344%' weizhi -e "DESCRIBE table_name;"
```

### Makefile 命令

```bash
# 服务器管理
make start          # 启动服务器
make stop           # 停止服务器
make restart        # 重启服务器
make status         # 查看状态
make kill-port      # 杀掉3000端口
make force-stop     # 强制停止所有进程

# 数据库操作
make check-db       # 检查数据库连接
make backup-db      # 备份数据库
make auto-migrate   # GORM自动迁移

# Docker数据库操作
make docker-migrate DOCKER_MYSQL_CONTAINER=weishi_mysql_local  # 执行迁移
make docker-shell DOCKER_MYSQL_CONTAINER=weishi_mysql_local    # 连接容器
```

### SQL 查询速查

```sql
-- 查看所有表
SHOW TABLES;

-- 查看表结构
DESCRIBE table_name;
SHOW CREATE TABLE table_name;

-- 查看表大小
SELECT 
    TABLE_NAME,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Size (MB)',
    TABLE_ROWS
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'weizhi';

-- 查看字段信息
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = 'weizhi' AND TABLE_NAME = 'table_name';
```

## 🔧 表结构优化记录

### 已优化的表

| 表名 | 优化前字段数 | 优化后字段数 | 删除字段数 | 状态 |
|------|-------------|-------------|-----------|------|
| our_services | 12 | 5 | 7 | ✅ 完成 |
| project_cases | 11 | 5 | 6 | ✅ 完成 |
| swipers | 8 | 7 | 1 | ✅ 完成 |
| part_platform | 10 | 9 | 1 | ✅ 完成 |
| recruitments | 13 | 11 | 2 | ✅ 完成 |
| news | 14 | 14 | 0 | ⏸️ 跳过 |

### 删除的字段清单

**our_services**: name, type, description, fullDescription, features, equipments, testItems  
**project_cases**: title, description, sort, cover_image, tags, order  
**swipers**: status  
**part_platform**: order  
**recruitments**: is_active, order  

## ⚠️ 重要提醒

### GORM限制
- ✅ 可以添加字段和索引
- ❌ **不能删除字段**（需要手动SQL）
- ❌ 不能修改字段类型

### 操作安全
1. **生产环境必须先备份**
2. **测试环境先验证**
3. **保留回滚脚本**
4. **检查代码兼容性**

### Docker注意事项
- 确保容器正在运行
- 使用正确的容器名
- 密码包含特殊字符需要引号

## 🆘 故障排除

### 常见错误

```bash
# SQL语法错误
ERROR 1064 (42000): You have an error in your SQL syntax
# 解决：检查SQL语法，移除 IF EXISTS

# 连接失败
ERROR 2002 (HY000): Can't connect to MySQL server
# 解决：检查容器状态和连接参数

# 字段不存在
ERROR 1091 (42000): Can't DROP 'column_name'
# 解决：字段可能已删除，检查表结构

# 权限不足
ERROR 1142 (42000): ALTER command denied
# 解决：使用root用户或检查权限
```

### 调试步骤

1. **检查容器状态**: `docker ps | grep mysql`
2. **测试连接**: `make docker-shell DOCKER_MYSQL_CONTAINER=weishi_mysql_local`
3. **查看表结构**: `DESCRIBE table_name;`
4. **检查日志**: `docker logs weishi_mysql_local`

## 📞 联系信息

如有问题，请参考：
- [完整操作指南](./table-optimization-guide.md)
- [数据库表结构文档](./database-schema.md)
- GORM官方文档: https://gorm.io/docs/

---

*快速参考 v1.0 | 2025-01-04*
