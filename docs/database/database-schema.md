# 🗄️ 数据库表结构文档

本文档详细描述了蔚之领域智能科技项目（server-go）的完整数据库表结构。

## 📋 概述

- **数据库名称**: `weizhi`
- **字符集**: `utf8mb4`
- **排序规则**: `utf8mb4_unicode_ci`
- **数据库引擎**: InnoDB
- **总表数**: 18个表

## 🏗️ 表结构详情

### 1. 管理员用户表 (`admin_users`)

管理员用户基础信息表。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | bigint unsigned | PRIMARY KEY, AUTO_INCREMENT | - | 主键ID |
| created_at | datetime(3) | - | NULL | 创建时间 |
| updated_at | datetime(3) | - | NULL | 更新时间 |
| username | varchar(50) | NOT NULL, UNIQUE | - | 用户名 |
| email | varchar(255) | NOT NULL, UNIQUE | - | 邮箱地址 |
| password | varchar(255) | NOT NULL | - | 密码（加密） |
| real_name | varchar(100) | NOT NULL | - | 真实姓名 |
| avatar | varchar(500) | - | NULL | 头像URL |
| phone | varchar(20) | - | NULL | 手机号码 |
| status | enum('active','inactive','banned') | - | 'active' | 用户状态 |
| last_login_at | datetime(3) | - | NULL | 最后登录时间 |
| last_login_ip | varchar(45) | - | NULL | 最后登录IP |
| login_count | bigint | - | 0 | 登录次数 |
| remark | text | - | NULL | 备注 |
| created_by | bigint | - | NULL | 创建者ID |
| updated_by | bigint | - | NULL | 更新者ID |
| deleted_at | datetime(3) | - | NULL | 软删除时间 |

**索引**:
- `idx_admin_users_username` (UNIQUE)
- `idx_admin_users_email` (UNIQUE)
- `idx_admin_users_deleted_at`

### 2. 管理员角色表 (`admin_roles`)

角色管理表，定义不同的管理员角色。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | bigint unsigned | PRIMARY KEY, AUTO_INCREMENT | - | 主键ID |
| created_at | datetime(3) | - | NULL | 创建时间 |
| updated_at | datetime(3) | - | NULL | 更新时间 |
| name | varchar(50) | NOT NULL, UNIQUE | - | 角色名称 |
| code | varchar(50) | NOT NULL, UNIQUE | - | 角色代码 |
| description | text | - | NULL | 角色描述 |
| status | enum('active','inactive') | - | 'active' | 角色状态 |
| sort | bigint | - | 0 | 排序 |
| remark | text | - | NULL | 备注 |
| created_by | bigint | - | NULL | 创建者ID |
| updated_by | bigint | - | NULL | 更新者ID |

**索引**:
- `idx_admin_roles_name` (UNIQUE)
- `idx_admin_roles_code` (UNIQUE)

### 3. 管理员权限分组表 (`admin_permission_groups`)

权限分组表，用于组织权限的层级结构。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | bigint unsigned | PRIMARY KEY, AUTO_INCREMENT | - | 主键ID |
| created_at | datetime(3) | - | NULL | 创建时间 |
| updated_at | datetime(3) | - | NULL | 更新时间 |
| name | varchar(100) | NOT NULL | - | 分组名称 |
| code | varchar(100) | NOT NULL, UNIQUE | - | 分组代码 |
| description | text | - | NULL | 分组描述 |
| icon | varchar(100) | - | NULL | 图标 |
| sort | bigint | - | 0 | 排序 |
| status | enum('active','inactive') | - | 'active' | 状态 |

**索引**:
- `idx_admin_permission_groups_code` (UNIQUE)
- `idx_code`
- `idx_sort`

### 4. 管理员权限表 (`admin_permissions`)

权限定义表，包含菜单、按钮、API等权限。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | bigint unsigned | PRIMARY KEY, AUTO_INCREMENT | - | 主键ID |
| created_at | datetime(3) | - | NULL | 创建时间 |
| updated_at | datetime(3) | - | NULL | 更新时间 |
| name | varchar(100) | NOT NULL | - | 权限名称 |
| code | varchar(100) | NOT NULL, UNIQUE | - | 权限代码 |
| type | enum('menu','button','api') | NOT NULL | - | 权限类型 |
| parent_id | bigint unsigned | FOREIGN KEY | NULL | 父权限ID |
| group_id | bigint unsigned | FOREIGN KEY | NULL | 权限分组ID |
| path | varchar(255) | - | NULL | 路由路径 |
| component | varchar(255) | - | NULL | 组件路径 |
| icon | varchar(100) | - | NULL | 图标 |
| method | varchar(10) | - | NULL | HTTP方法 |
| description | text | - | NULL | 权限描述 |
| status | enum('active','inactive') | - | 'active' | 权限状态 |
| sort | bigint | - | 0 | 排序 |
| remark | text | - | NULL | 备注 |
| created_by | bigint | - | NULL | 创建者ID |
| updated_by | bigint | - | NULL | 更新者ID |

**索引**:
- `idx_admin_permissions_code` (UNIQUE)
- `idx_group_id`
- `fk_admin_permissions_children`

**外键约束**:
- `fk_admin_permissions_children`: parent_id → admin_permissions(id)
- `fk_admin_permissions_group`: group_id → admin_permission_groups(id)

### 5. 用户角色关联表 (`admin_user_roles`)

管理员用户与角色的多对多关联表。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | bigint unsigned | PRIMARY KEY, AUTO_INCREMENT | - | 主键ID |
| admin_user_id | bigint unsigned | NOT NULL | - | 管理员用户ID |
| role_id | bigint unsigned | NOT NULL | - | 角色ID |
| created_by | bigint | - | NULL | 创建者ID |
| created_at | datetime(3) | - | NULL | 创建时间 |
| updated_at | datetime(3) | - | NULL | 更新时间 |

### 6. 角色权限关联表 (`admin_role_permissions`)

角色与权限的多对多关联表。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | bigint unsigned | PRIMARY KEY, AUTO_INCREMENT | - | 主键ID |
| role_id | bigint unsigned | NOT NULL | - | 角色ID |
| permission_id | bigint unsigned | NOT NULL | - | 权限ID |
| created_by | bigint | - | NULL | 创建者ID |
| created_at | datetime(3) | - | NULL | 创建时间 |

### 7. 管理员操作日志表 (`admin_logs`)

记录管理员的所有操作日志。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | bigint unsigned | PRIMARY KEY, AUTO_INCREMENT | - | 主键ID |
| created_at | datetime(3) | - | NULL | 创建时间 |
| updated_at | datetime(3) | - | NULL | 更新时间 |
| admin_user_id | bigint unsigned | NOT NULL | - | 管理员用户ID |
| username | varchar(50) | - | NULL | 用户名 |
| action | varchar(100) | NOT NULL | - | 操作动作 |
| module | varchar(50) | NOT NULL | - | 操作模块 |
| method | varchar(10) | - | NULL | HTTP方法 |
| url | varchar(500) | - | NULL | 请求URL |
| ip | varchar(45) | NOT NULL | - | 操作IP |
| user_agent | text | - | NULL | 用户代理 |
| params | json | - | NULL | 请求参数 |
| result | text | - | NULL | 操作结果 |
| status | varchar(20) | - | 'success' | 操作状态 |
| error_msg | text | - | NULL | 错误信息 |
| duration | bigint | - | NULL | 执行时长(ms) |
| deleted_at | datetime(3) | - | NULL | 软删除时间 |
| content | text | - | NULL | 操作内容 |

**索引**:
- `idx_admin_logs_deleted_at`

### 8. 轮播图表 (`swipers`) - 🔧 已优化

首页轮播图管理表（优化后保留必要字段）。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | bigint unsigned | PRIMARY KEY, AUTO_INCREMENT | - | 主键ID |
| created_at | datetime(3) | - | NULL | 创建时间 |
| updated_at | datetime(3) | - | NULL | 更新时间 |
| url | varchar(255) | NOT NULL | - | 图片URL |
| title | varchar(255) | NOT NULL | - | 图片标题（用于alt属性，SEO优化） |
| order | bigint | - | 0 | 显示顺序，数字越小越靠前 |
| deleted_at | datetime(3) | - | NULL | 软删除时间 |

**索引**:
- `idx_swipers_deleted_at`

**优化说明**：
- ❌ **已删除冗余字段**：status（前端没有状态控制逻辑）
- ✅ **保留字段**：url（图片地址）, title（用于alt属性）, order（排序）
- 📝 **设计理念**：title字段用于图片alt属性，提升SEO和可访问性

### 9. 新闻表 (`news`)

新闻资讯管理表。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | bigint unsigned | PRIMARY KEY, AUTO_INCREMENT | - | 主键ID |
| created_at | datetime(3) | - | NULL | 创建时间 |
| updated_at | datetime(3) | - | NULL | 更新时间 |
| title | varchar(255) | NOT NULL | - | 新闻标题 |
| content | text | NOT NULL | - | 新闻内容 |
| image | varchar(255) | - | NULL | 新闻图片 |
| is_home_page | tinyint(1) | - | 0 | 是否首页显示 |
| summary | varchar(500) | - | NULL | 新闻摘要 |
| cover_image | varchar(255) | - | NULL | 封面图片 |
| author | varchar(100) | - | NULL | 作者 |
| publish_time | varchar(100) | - | NULL | 发布时间 |
| is_published | tinyint(1) | - | 0 | 是否已发布 |
| view_count | bigint | - | 0 | 浏览次数 |
| deleted_at | datetime(3) | - | NULL | 软删除时间 |

**索引**:
- `idx_news_deleted_at`

### 10. 服务表 (`our_services`) - 🔧 已优化

公司服务项目管理表（优化后保留必要字段）。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | bigint unsigned | PRIMARY KEY, AUTO_INCREMENT | - | 主键ID |
| created_at | datetime(3) | - | NULL | 创建时间 |
| updated_at | datetime(3) | - | NULL | 更新时间 |
| image | varchar(255) | NOT NULL | - | 服务背景图片 |
| order | int | - | 0 | 显示顺序，数字越小越靠前 |

**优化说明**：
- ❌ **已删除冗余字段**：name, type, description, fullDescription, features, equipments, testItems
- ✅ **保留字段**：image（背景图）, order（排序）
- 📝 **设计理念**：前端硬编码标题，数据库只管理图片和顺序

### 11. 项目案例表 (`project_cases`) - 🔧 已优化

项目案例展示表（优化后只保留实际使用的字段）。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | bigint unsigned | PRIMARY KEY, AUTO_INCREMENT | - | 主键ID |
| created_at | datetime(3) | - | NULL | 创建时间 |
| updated_at | datetime(3) | - | NULL | 更新时间 |
| url | varchar(255) | NOT NULL | - | 案例图片URL |
| deleted_at | datetime(3) | - | NULL | 软删除时间 |

**索引**:
- `idx_project_cases_deleted_at`

**优化说明**：
- ❌ **已删除冗余字段**：title, description, sort, cover_image, tags, order
- ✅ **保留字段**：url（前端实际使用显示案例图片）
- 📝 **原因**：前端只在轮播组件中显示案例图片，不显示标题、描述等文本信息

### 12. 合作伙伴表 (`partners`)

合作伙伴信息管理表。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | bigint unsigned | PRIMARY KEY, AUTO_INCREMENT | - | 主键ID |
| created_at | datetime(3) | - | NULL | 创建时间 |
| updated_at | datetime(3) | - | NULL | 更新时间 |
| name | varchar(255) | NOT NULL | - | 合作伙伴名称 |
| logo | varchar(255) | NOT NULL | - | 合作伙伴Logo |
| deleted_at | datetime(3) | - | NULL | 软删除时间 |

**索引**:
- `idx_partners_deleted_at`

### 13. 友情链接表 (`friend_links`)

友情链接管理表。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | bigint unsigned | PRIMARY KEY, AUTO_INCREMENT | - | 主键ID |
| created_at | datetime(3) | - | NULL | 创建时间 |
| updated_at | datetime(3) | - | NULL | 更新时间 |
| name | varchar(255) | NOT NULL | - | 链接名称 |
| url | varchar(255) | NOT NULL | - | 链接地址 |
| order | bigint | - | 0 | 排序 |
| deleted_at | datetime(3) | - | NULL | 软删除时间 |

**索引**:
- `idx_friend_links_deleted_at`

### 14. 招聘信息表 (`recruitments`) - 🔧 已优化

招聘信息管理表（优化后删除未使用字段）。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | bigint unsigned | PRIMARY KEY, AUTO_INCREMENT | - | 主键ID |
| created_at | datetime(3) | - | NULL | 创建时间 |
| updated_at | datetime(3) | - | NULL | 更新时间 |
| name | varchar(255) | NOT NULL | - | 姓名/职位名称 |
| location | varchar(255) | NOT NULL | - | 工作地点 |
| content | text | NOT NULL | - | 招聘内容 |
| position | varchar(255) | NOT NULL | - | 职位 |
| department | varchar(255) | NOT NULL | - | 部门 |
| description | text | NOT NULL | - | 职位描述 |
| requirement | text | - | NULL | 职位要求 |
| deleted_at | datetime(3) | - | NULL | 软删除时间 |

**索引**:
- `idx_recruitments_deleted_at`

**优化说明**：
- ❌ **已删除冗余字段**：is_active, order（前端没有状态过滤和排序功能）
- ✅ **保留字段**：name, location, content, position, department, description, requirement（前端实际使用）
- 📝 **设计理念**：前端按创建时间显示所有招聘信息，不需要状态控制和排序

### 15. 零件平台表 (`part_platform`) - 🔧 已优化

零件平台信息管理表（优化后删除未使用字段）。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | bigint unsigned | PRIMARY KEY, AUTO_INCREMENT | - | 主键ID |
| created_at | datetime(3) | - | NULL | 创建时间 |
| updated_at | datetime(3) | - | NULL | 更新时间 |
| name | varchar(255) | NOT NULL | - | 平台名称 |
| url | varchar(255) | NOT NULL | - | 平台图片URL |
| description | text | NOT NULL | - | 平台描述 |
| parameters | text | NOT NULL | - | 技术参数 |
| applications | text | NOT NULL | - | 应用场景 |
| deleted_at | datetime(3) | - | NULL | 软删除时间 |

**索引**:
- `idx_part_platform_deleted_at`

**优化说明**：
- ❌ **已删除冗余字段**：order（前端没有排序功能）
- ✅ **保留字段**：name, url, description, parameters, applications（前端实际使用）
- 📝 **设计理念**：保留所有在前端详情页和管理后台实际使用的字段

### 16. 文件上传表 (`file_uploads`)

文件上传记录管理表。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | bigint unsigned | PRIMARY KEY, AUTO_INCREMENT | - | 主键ID |
| original_name | varchar(255) | NOT NULL | - | 原始文件名 |
| file_name | varchar(255) | NOT NULL | - | 存储文件名 |
| file_path | varchar(500) | NOT NULL | - | 文件路径 |
| file_size | bigint | NOT NULL | - | 文件大小（字节） |
| mime_type | varchar(100) | NOT NULL | - | 文件类型 |
| bucket_name | varchar(100) | NOT NULL | - | 存储桶名称 |
| cos_url | varchar(500) | NOT NULL | - | COS访问URL |
| uploaded_by | bigint unsigned | NOT NULL | - | 上传用户ID |
| module | varchar(50) | NOT NULL | - | 所属模块 |
| status | varchar(20) | NOT NULL | 'active' | 文件状态 |
| created_at | timestamp | - | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | timestamp | - | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |
| deleted_at | timestamp | - | NULL | 软删除时间 |

**索引**:
- `idx_uploaded_by`
- `idx_module`
- `idx_status`
- `idx_created_at`

### 17. 缓存版本表 (`cache_versions`)

缓存版本管理表（用于缓存失效控制）。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | bigint unsigned | PRIMARY KEY, AUTO_INCREMENT | - | 主键ID |
| version | varchar(50) | NOT NULL, UNIQUE | - | 版本号 |
| cache_key | varchar(100) | NOT NULL, UNIQUE | - | 缓存键 |
| description | varchar(255) | - | '' | 版本描述 |
| created_by | varchar(50) | - | '' | 创建者 |
| created_at | datetime(3) | - | NULL | 创建时间 |
| updated_at | datetime(3) | - | NULL | 更新时间 |

**索引**:
- `idx_cache_versions_version` (UNIQUE)
- `cache_key` (UNIQUE)

### 18. 服务表 (`services`)

服务项目表（备用表，当前主要使用our_services）。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | bigint unsigned | PRIMARY KEY, AUTO_INCREMENT | - | 主键ID |
| created_at | datetime(3) | - | NULL | 创建时间 |
| updated_at | datetime(3) | - | NULL | 更新时间 |
| title | varchar(255) | NOT NULL | - | 服务标题 |
| description | text | NOT NULL | - | 服务描述 |
| icon | varchar(255) | - | NULL | 服务图标 |
| order | bigint | - | 0 | 排序 |
| deleted_at | datetime(3) | - | NULL | 软删除时间 |

**索引**:
- `idx_services_deleted_at`

## 🔗 表关系图

### 权限管理系统关系

```
admin_users (管理员用户)
    ↓ (多对多)
admin_user_roles (用户角色关联)
    ↓
admin_roles (角色)
    ↓ (多对多)
admin_role_permissions (角色权限关联)
    ↓
admin_permissions (权限)
    ↓ (分组)
admin_permission_groups (权限分组)
```

### 内容管理系统关系

```
file_uploads (文件上传) ← 关联 → 各内容表
    ↓
swipers (轮播图)
news (新闻)
our_services (服务)
project_cases (项目案例)
partners (合作伙伴)
friend_links (友情链接)
recruitments (招聘信息)
part_platform (零件平台)
```

## 📊 数据统计

### 表分类统计

| 分类 | 表数量 | 表名 | 优化状态 |
|------|--------|------|----------|
| 权限管理 | 6 | admin_users, admin_roles, admin_permissions, admin_permission_groups, admin_user_roles, admin_role_permissions | 🔒 无需优化 |
| 操作日志 | 1 | admin_logs | 🔒 无需优化 |
| 内容管理 | 8 | swipers✅, news⏸️, our_services✅, project_cases✅, partners✅, friend_links✅, recruitments✅, part_platform✅ | 🔧 已优化7/8 |
| 文件管理 | 1 | file_uploads | 🔒 无需优化 |
| 系统管理 | 2 | cache_versions, services | 🔒 无需优化 |
| **总计** | **18** | - | **7个表已优化** |

### 优化成果统计

| 表名 | 优化前字段数 | 优化后字段数 | 删除字段数 | 优化程度 |
|------|-------------|-------------|-----------|----------|
| **our_services** | 12 | 5 | 7 | 🚨 严重优化 |
| **project_cases** | 11 | 5 | 6 | 🚨 严重优化 |
| **swipers** | 8 | 7 | 1 | ⚠️ 轻微优化 |
| **part_platform** | 10 | 9 | 1 | ⚠️ 轻微优化 |
| **recruitments** | 13 | 11 | 2 | ⚠️ 轻微优化 |
| **news** | 14 | 14 | 0 | ⏸️ 暂未优化 |
| **总计删除字段** | - | - | **17** | **大幅精简** |

### 字段类型统计

| 数据类型 | 使用频率 | 说明 |
|----------|----------|------|
| bigint unsigned | 高 | 主键ID、外键、数值字段 |
| varchar | 高 | 字符串字段（名称、URL等） |
| text | 中 | 长文本内容 |
| datetime(3) | 高 | 时间戳字段 |
| enum | 中 | 状态、类型等枚举值 |
| tinyint(1) | 低 | 布尔值字段 |
| json | 低 | JSON数据存储 |
| timestamp | 低 | 文件上传表专用 |

## 🔧 设计特点

### 1. 统一的基础字段设计

所有业务表都包含标准的基础字段：
- `id`: 主键，bigint unsigned 自增
- `created_at`: 创建时间，datetime(3)
- `updated_at`: 更新时间，datetime(3)
- `deleted_at`: 软删除时间，datetime(3)（支持软删除的表）

### 2. 权限系统设计

采用经典的RBAC（基于角色的访问控制）模型：
- **用户（Users）**: 管理员用户基础信息
- **角色（Roles）**: 角色定义和管理
- **权限（Permissions）**: 细粒度权限控制
- **分组（Groups）**: 权限分组管理

### 3. 软删除支持

大部分业务表支持软删除机制：
- 使用 `deleted_at` 字段标记删除状态
- 保留历史数据，支持数据恢复
- 对应的索引：`idx_[table_name]_deleted_at`

### 4. 排序和状态管理

- **排序字段**: `order` 或 `sort` 字段，支持自定义排序
- **状态管理**: 使用 enum 类型定义状态值，如 'active', 'inactive'
- **审计字段**: `created_by`, `updated_by` 记录操作者

### 5. 文件管理集成

- 集成腾讯云COS对象存储
- 完整的文件上传记录和元数据管理
- 支持多模块文件分类管理

## 📝 使用说明

### 1. 数据库初始化

```bash
# 执行完整的数据库初始化
./scripts/setup_database.sh

# 或者分步执行
mysql -u root -p < scripts/create_database.sql
mysql -u root -p weizhi < scripts/init_database.sql
```

### 2. 模型定义位置

- **Go模型**: `server-go/internal/model/`
- **TypeScript类型**: `admin/src/types/api.ts`
- **数据库脚本**: `server-go/scripts/`

### 3. 权限配置

默认管理员账号：
- **用户名**: admin
- **密码**: admin123
- **角色**: 超级管理员（拥有所有权限）

---

*文档生成时间: 2025-01-04*
*数据库版本: MySQL 8.0*
*项目: 蔚之领域智能科技*
