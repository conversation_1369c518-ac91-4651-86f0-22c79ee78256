# 📡 API 概览

本文档介绍蔚之领域智能科技项目的 API 设计原则、通用规范和接口概览。

## 🎯 API 设计原则

### RESTful 设计

项目采用 RESTful API 设计风格，遵循以下原则：

- **资源导向**: 每个 URL 代表一种资源
- **HTTP 方法**: 使用标准 HTTP 方法表示操作
- **状态码**: 使用标准 HTTP 状态码表示结果
- **无状态**: 每个请求包含所有必要信息

### HTTP 方法使用

| 方法 | 用途 | 示例 |
|------|------|------|
| GET | 获取资源 | `GET /api/news` - 获取新闻列表 |
| POST | 创建资源 | `POST /api/news` - 创建新闻 |
| PUT | 更新资源 | `PUT /api/news/1` - 更新新闻 |
| DELETE | 删除资源 | `DELETE /api/news/1` - 删除新闻 |
| PATCH | 部分更新 | `PATCH /api/news/1` - 部分更新新闻 |

### 状态码规范

| 状态码 | 含义 | 使用场景 |
|--------|------|----------|
| 200 | 成功 | 请求成功处理 |
| 201 | 已创建 | 资源创建成功 |
| 204 | 无内容 | 删除成功 |
| 400 | 请求错误 | 参数错误、验证失败 |
| 401 | 未授权 | 未登录或 token 无效 |
| 403 | 禁止访问 | 权限不足 |
| 404 | 未找到 | 资源不存在 |
| 500 | 服务器错误 | 内部服务器错误 |

## 📋 统一响应格式

### 成功响应

```json
{
  "code": 0,
  "message": "success",
  "data": {
    // 具体数据内容
  }
}
```

### 错误响应

```json
{
  "code": 1001,
  "message": "参数验证失败",
  "data": null
}
```

### 分页响应

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "items": [
      // 数据列表
    ],
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "total": 100,
      "totalPages": 10
    }
  }
}
```

## 🔐 认证和授权

### JWT 认证

项目使用 JWT (JSON Web Token) 进行用户认证：

```http
Authorization: Bearer <jwt_token>
```

### Token 结构

```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "user_id": 1,
    "username": "admin",
    "role": "admin",
    "exp": **********
  }
}
```

### 权限控制

基于角色的访问控制 (RBAC)：

- **超级管理员**: 拥有所有权限
- **管理员**: 拥有内容管理权限
- **编辑**: 拥有内容编辑权限
- **查看者**: 只有查看权限

## 📊 API 分类

### 公开接口

无需认证即可访问的接口：

| 分类 | 路径前缀 | 说明 |
|------|----------|------|
| 内容展示 | `/api/public/` | 前台展示数据 |
| 健康检查 | `/api/health` | 服务健康状态 |
| 验证码 | `/api/captcha` | 验证码生成 |

### 认证接口

需要用户登录的接口：

| 分类 | 路径前缀 | 说明 |
|------|----------|------|
| 管理员认证 | `/api/admin/auth/` | 登录、注销 |
| 个人资料 | `/api/admin/profile/` | 个人信息管理 |

### 管理接口

需要管理员权限的接口：

| 分类 | 路径前缀 | 说明 |
|------|----------|------|
| 用户管理 | `/api/admin/users/` | 用户 CRUD |
| 角色管理 | `/api/admin/roles/` | 角色 CRUD |
| 权限管理 | `/api/admin/permissions/` | 权限 CRUD |
| 内容管理 | `/api/admin/content/` | 内容 CRUD |
| 系统管理 | `/api/admin/system/` | 系统配置 |

## 📝 请求规范

### 请求头

```http
Content-Type: application/json
Authorization: Bearer <jwt_token>
Accept: application/json
User-Agent: Weishi-Client/1.0
```

### 请求参数

#### 查询参数 (Query Parameters)

```http
GET /api/news?page=1&pageSize=10&keyword=技术&status=published
```

#### 路径参数 (Path Parameters)

```http
GET /api/news/{id}
PUT /api/news/{id}
DELETE /api/news/{id}
```

#### 请求体 (Request Body)

```json
{
  "title": "新闻标题",
  "content": "新闻内容",
  "status": "published",
  "tags": ["技术", "创新"]
}
```

### 分页参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| page | int | 1 | 页码 |
| pageSize | int | 10 | 每页数量 |
| sortBy | string | id | 排序字段 |
| sortOrder | string | desc | 排序方向 (asc/desc) |

### 搜索参数

| 参数 | 类型 | 说明 |
|------|------|------|
| keyword | string | 关键词搜索 |
| status | string | 状态筛选 |
| startDate | string | 开始日期 |
| endDate | string | 结束日期 |

## 🔍 错误处理

### 错误码定义

| 错误码 | 含义 | HTTP状态码 |
|--------|------|------------|
| 0 | 成功 | 200 |
| 1001 | 参数错误 | 400 |
| 1002 | 验证失败 | 400 |
| 2001 | 未授权 | 401 |
| 2002 | 权限不足 | 403 |
| 3001 | 资源不存在 | 404 |
| 5001 | 服务器错误 | 500 |
| 5002 | 数据库错误 | 500 |

### 错误响应示例

```json
{
  "code": 1001,
  "message": "参数验证失败: title 字段不能为空",
  "data": {
    "field": "title",
    "value": "",
    "constraint": "required"
  }
}
```

## 📈 性能优化

### 缓存策略

- **静态资源**: 浏览器缓存 1 年
- **API 响应**: Redis 缓存 5 分钟
- **数据库查询**: 查询结果缓存

### 限流策略

- **IP 限流**: 每分钟 100 次请求
- **用户限流**: 每分钟 60 次请求
- **接口限流**: 特定接口单独限制

### 压缩优化

- **Gzip 压缩**: 响应体自动压缩
- **图片优化**: 自动转换为 WebP 格式
- **JSON 优化**: 移除不必要的字段

## 📚 API 文档

### Swagger 文档

- **开发环境**: http://localhost:3001/swagger/index.html
- **生产环境**: https://api.your-domain.com/swagger/index.html

### 文档特性

- **交互式测试**: 直接在文档中测试 API
- **参数说明**: 详细的参数类型和约束
- **响应示例**: 真实的响应数据示例
- **错误码说明**: 完整的错误码列表

### 文档生成

```bash
# 生成 Swagger 文档
cd server-go
make swagger

# 或手动生成
swag init -g cmd/main.go
```

## 🔧 开发工具

### API 测试工具

- **Postman**: 图形化 API 测试
- **curl**: 命令行 API 测试
- **HTTPie**: 友好的命令行工具

### 示例请求

```bash
# 获取新闻列表
curl -X GET "http://localhost:3001/api/news" \
  -H "Accept: application/json"

# 创建新闻
curl -X POST "http://localhost:3001/api/admin/news" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "title": "新闻标题",
    "content": "新闻内容"
  }'
```

## 📋 接口清单

### 内容管理接口

| 功能 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 新闻列表 | GET | `/api/news` | 获取新闻列表 |
| 新闻详情 | GET | `/api/news/{id}` | 获取新闻详情 |
| 服务列表 | GET | `/api/services` | 获取服务列表 |
| 项目案例 | GET | `/api/projects` | 获取项目案例 |
| 招聘信息 | GET | `/api/jobs` | 获取招聘信息 |

### 管理后台接口

| 功能 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 管理员登录 | POST | `/api/admin/auth/login` | 管理员登录 |
| 获取资料 | GET | `/api/admin/profile` | 获取个人资料 |
| 用户管理 | GET | `/api/admin/users` | 用户列表 |
| 角色管理 | GET | `/api/admin/roles` | 角色列表 |
| 内容管理 | GET | `/api/admin/content` | 内容列表 |

### 系统接口

| 功能 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 健康检查 | GET | `/api/health` | 服务健康状态 |
| 验证码 | GET | `/api/captcha` | 生成验证码 |
| 文件上传 | POST | `/api/upload` | 文件上传 |

---

*详细的接口文档请参考各模块的具体文档。*
