# 📁 文件上传API文档

本文档介绍项目的文件上传功能，包括腾讯云COS集成、API接口和使用示例。

## 📋 概述

系统集成了腾讯云COS（Cloud Object Storage）服务，提供完整的文件上传、管理和删除功能，支持：

- **多种文件类型**: 图片、文档、视频等
- **文件大小限制**: 可配置的文件大小限制
- **安全上传**: 基于JWT的认证和授权
- **自动压缩**: 图片自动压缩和格式转换
- **CDN加速**: 通过腾讯云CDN提供快速访问

## 🔧 环境配置

### 1. 腾讯云COS配置

在 `config.yaml` 中配置COS相关参数：

```yaml
cos:
  secret_id: "your_cos_secret_id"      # 腾讯云SecretId
  secret_key: "your_cos_secret_key"    # 腾讯云SecretKey
  region: "ap-nanjing"                 # COS地域
  bucket: "weishi-files"               # 存储桶名称
  domain: "files.weishi.com"           # 自定义域名（可选）
```

### 2. 环境变量配置

```bash
# 腾讯云COS配置
WEISHI_COS_SECRET_ID=your_cos_secret_id
WEISHI_COS_SECRET_KEY=your_cos_secret_key
WEISHI_COS_REGION=ap-nanjing
WEISHI_COS_BUCKET=weishi-files
WEISHI_COS_DOMAIN=files.weishi.com
```

### 3. 文件类型配置

支持的文件类型和大小限制：

| 文件类型 | 扩展名 | 最大大小 | 用途 |
|----------|--------|----------|------|
| 图片 | jpg, jpeg, png, gif, webp | 10MB | 新闻图片、轮播图等 |
| 文档 | pdf, doc, docx, xls, xlsx | 50MB | 招聘附件、资料下载 |
| 视频 | mp4, avi, mov | 100MB | 宣传视频、案例展示 |

## 📡 API接口

### 1. 获取上传配置

**接口**: `GET /api/upload/config`

**说明**: 获取文件上传的配置信息（公开接口，无需认证）

**请求示例**:
```bash
curl -X GET "http://localhost:3001/api/upload/config"
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "maxSize": 52428800,
    "allowedTypes": [
      "image/jpeg", "image/png", "image/gif", "image/webp",
      "application/pdf", "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ],
    "uploadUrl": "/api/upload/file"
  }
}
```

### 2. 上传文件

**接口**: `POST /api/upload/file`

**说明**: 上传单个文件到腾讯云COS

**认证**: 需要JWT Token

**请求头**:
```http
Content-Type: multipart/form-data
Authorization: Bearer <jwt_token>
```

**请求参数**:
| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| file | File | ✅ | 要上传的文件 |
| category | String | ❌ | 文件分类（news, service, case等） |

**请求示例**:
```bash
curl -X POST "http://localhost:3001/api/upload/file" \
  -H "Authorization: Bearer <jwt_token>" \
  -F "file=@/path/to/image.jpg" \
  -F "category=news"
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "url": "https://files.weishi.com/news/2024/01/15/abc123.jpg",
    "filename": "abc123.jpg",
    "originalName": "image.jpg",
    "size": 1024000,
    "mimeType": "image/jpeg",
    "category": "news",
    "uploadedAt": "2024-01-15T10:30:00Z"
  }
}
```

### 3. 批量上传文件

**接口**: `POST /api/upload/batch`

**说明**: 批量上传多个文件

**认证**: 需要JWT Token

**请求参数**:
| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| files | File[] | ✅ | 要上传的文件数组 |
| category | String | ❌ | 文件分类 |

**请求示例**:
```bash
curl -X POST "http://localhost:3001/api/upload/batch" \
  -H "Authorization: Bearer <jwt_token>" \
  -F "files=@/path/to/image1.jpg" \
  -F "files=@/path/to/image2.jpg" \
  -F "category=gallery"
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": [
      {
        "url": "https://files.weishi.com/gallery/2024/01/15/abc123.jpg",
        "filename": "abc123.jpg",
        "originalName": "image1.jpg"
      },
      {
        "url": "https://files.weishi.com/gallery/2024/01/15/def456.jpg",
        "filename": "def456.jpg",
        "originalName": "image2.jpg"
      }
    ],
    "failed": [],
    "total": 2,
    "successCount": 2,
    "failedCount": 0
  }
}
```

### 4. 删除文件

**接口**: `DELETE /api/upload/file`

**说明**: 删除已上传的文件

**认证**: 需要JWT Token

**请求参数**:
```json
{
  "url": "https://files.weishi.com/news/2024/01/15/abc123.jpg"
}
```

**请求示例**:
```bash
curl -X DELETE "http://localhost:3001/api/upload/file" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://files.weishi.com/news/2024/01/15/abc123.jpg"}'
```

**响应示例**:
```json
{
  "code": 0,
  "message": "文件删除成功",
  "data": null
}
```

## 🎨 前端集成

### Vue组件示例

```vue
<template>
  <div class="file-upload">
    <el-upload
      ref="uploadRef"
      :action="uploadUrl"
      :headers="uploadHeaders"
      :before-upload="beforeUpload"
      :on-success="onSuccess"
      :on-error="onError"
      :show-file-list="false"
      accept="image/*"
    >
      <el-button type="primary">选择文件</el-button>
    </el-upload>
    
    <div v-if="uploading" class="upload-progress">
      <el-progress :percentage="uploadProgress" />
    </div>
    
    <div v-if="uploadedFile" class="upload-result">
      <img :src="uploadedFile.url" alt="上传的图片" />
      <p>{{ uploadedFile.originalName }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()
const uploadRef = ref()
const uploading = ref(false)
const uploadProgress = ref(0)
const uploadedFile = ref(null)

const uploadUrl = '/api/upload/file'
const uploadHeaders = {
  Authorization: `Bearer ${authStore.token}`
}

const beforeUpload = (file: File) => {
  // 文件类型检查
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  
  // 文件大小检查
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('图片大小不能超过 10MB!')
    return false
  }
  
  uploading.value = true
  return true
}

const onSuccess = (response: any) => {
  uploading.value = false
  uploadProgress.value = 0
  
  if (response.code === 0) {
    uploadedFile.value = response.data
    ElMessage.success('上传成功!')
  } else {
    ElMessage.error(response.message || '上传失败!')
  }
}

const onError = (error: any) => {
  uploading.value = false
  uploadProgress.value = 0
  ElMessage.error('上传失败!')
  console.error('Upload error:', error)
}
</script>
```

### React组件示例

```tsx
import React, { useState } from 'react'
import { Upload, Button, Progress, message } from 'antd'
import { UploadOutlined } from '@ant-design/icons'

const FileUpload: React.FC = () => {
  const [uploading, setUploading] = useState(false)
  const [uploadedFile, setUploadedFile] = useState(null)

  const uploadProps = {
    name: 'file',
    action: '/api/upload/file',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`
    },
    beforeUpload: (file: File) => {
      const isImage = file.type.startsWith('image/')
      if (!isImage) {
        message.error('只能上传图片文件!')
        return false
      }
      
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        message.error('图片大小不能超过 10MB!')
        return false
      }
      
      setUploading(true)
      return true
    },
    onChange: (info: any) => {
      if (info.file.status === 'done') {
        setUploading(false)
        if (info.file.response.code === 0) {
          setUploadedFile(info.file.response.data)
          message.success('上传成功!')
        } else {
          message.error(info.file.response.message || '上传失败!')
        }
      } else if (info.file.status === 'error') {
        setUploading(false)
        message.error('上传失败!')
      }
    }
  }

  return (
    <div className="file-upload">
      <Upload {...uploadProps} showUploadList={false}>
        <Button icon={<UploadOutlined />} loading={uploading}>
          选择文件
        </Button>
      </Upload>
      
      {uploadedFile && (
        <div className="upload-result">
          <img src={uploadedFile.url} alt="上传的图片" />
          <p>{uploadedFile.originalName}</p>
        </div>
      )}
    </div>
  )
}

export default FileUpload
```

## 🔒 安全配置

### 1. 文件类型验证

```go
// 允许的文件类型
var allowedMimeTypes = map[string]bool{
    "image/jpeg":      true,
    "image/png":       true,
    "image/gif":       true,
    "image/webp":      true,
    "application/pdf": true,
    // 添加更多类型...
}

// 文件类型检查
func validateFileType(fileHeader *multipart.FileHeader) error {
    file, err := fileHeader.Open()
    if err != nil {
        return err
    }
    defer file.Close()
    
    // 读取文件头部信息
    buffer := make([]byte, 512)
    _, err = file.Read(buffer)
    if err != nil {
        return err
    }
    
    // 检测MIME类型
    mimeType := http.DetectContentType(buffer)
    if !allowedMimeTypes[mimeType] {
        return errors.New("不支持的文件类型")
    }
    
    return nil
}
```

### 2. 文件大小限制

```go
const (
    MaxImageSize    = 10 * 1024 * 1024  // 10MB
    MaxDocumentSize = 50 * 1024 * 1024  // 50MB
    MaxVideoSize    = 100 * 1024 * 1024 // 100MB
)

func validateFileSize(fileHeader *multipart.FileHeader, fileType string) error {
    var maxSize int64
    
    switch {
    case strings.HasPrefix(fileType, "image/"):
        maxSize = MaxImageSize
    case strings.HasPrefix(fileType, "application/"):
        maxSize = MaxDocumentSize
    case strings.HasPrefix(fileType, "video/"):
        maxSize = MaxVideoSize
    default:
        return errors.New("不支持的文件类型")
    }
    
    if fileHeader.Size > maxSize {
        return fmt.Errorf("文件大小超过限制: %d bytes", maxSize)
    }
    
    return nil
}
```

## 🐛 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 4001 | 文件类型不支持 | 检查文件扩展名和MIME类型 |
| 4002 | 文件大小超过限制 | 压缩文件或选择更小的文件 |
| 4003 | 上传失败 | 检查网络连接和COS配置 |
| 4004 | 认证失败 | 检查JWT Token是否有效 |
| 5001 | COS配置错误 | 检查COS密钥和存储桶配置 |

### 错误响应示例

```json
{
  "code": 4001,
  "message": "不支持的文件类型",
  "data": {
    "allowedTypes": ["image/jpeg", "image/png", "image/gif"],
    "receivedType": "application/exe"
  }
}
```

## 📊 使用统计

### 文件上传统计

可以通过以下API获取文件上传统计信息：

**接口**: `GET /api/upload/stats`

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "totalFiles": 1250,
    "totalSize": "2.5GB",
    "todayUploads": 15,
    "categoryStats": {
      "news": 450,
      "services": 200,
      "cases": 300,
      "others": 300
    }
  }
}
```

---

*文件上传功能已完整集成，支持多种文件类型和安全验证。如有问题请参考错误处理部分或联系技术支持。*
