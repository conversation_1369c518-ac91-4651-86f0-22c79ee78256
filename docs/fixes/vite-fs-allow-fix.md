# Vite文件系统访问权限修复

## 问题描述

在运行 `pnpm run dev` 启动admin开发服务器时，出现以下错误：

```
The request url "/Users/<USER>/Documents/code/work/weishi_com/Nuxt3Web/node_modules/.pnpm/element-plus@2.4.4_vue@3.5.14_typescript@5.8.3_/node_modules/element-plus/theme-chalk/el-dialog.css" is outside of Vite serving allow list.
```

## 问题原因

- **项目结构**：admin目录是项目根目录下的子目录
- **依赖位置**：Element Plus等依赖安装在项目根目录的node_modules中
- **Vite限制**：默认情况下，Vite只允许访问当前工作目录及其子目录的文件
- **访问冲突**：admin目录中的Vite需要访问上级目录的node_modules文件

## 解决方案

在 `admin/vite.config.ts` 中添加 `server.fs.allow` 配置：

```typescript
server: {
  port: 5173,
  host: true,
  fs: {
    // 允许访问项目根目录的node_modules
    allow: ['..']
  },
  proxy: {
    '/api': {
      target: 'http://localhost:3001',
      changeOrigin: true,
    },
  },
}
```

## 配置说明

- **`fs.allow: ['..']`**：允许Vite访问上级目录（项目根目录）
- **安全性**：只允许访问上一级目录，不会过度开放文件系统访问权限
- **兼容性**：适用于monorepo或子目录项目结构

## 验证修复

修复后重新启动开发服务器：

```bash
cd admin
pnpm run dev
```

应该不再出现文件访问权限错误，Element Plus的CSS文件能够正常加载。

## 相关文档

- [Vite Server Options - fs.allow](https://vite.dev/config/server-options.html#server-fs-allow)
- [Vite Security - File System Access](https://vite.dev/guide/migration.html#automatic-https-certificate-generation)

## 注意事项

1. **开发环境专用**：此配置仅影响开发服务器，不影响生产构建
2. **安全考虑**：在生产环境中，静态文件由Caddy等Web服务器提供，不存在此问题
3. **替代方案**：如果不想修改fs.allow，可以考虑将admin目录移到项目根目录，但会影响项目结构
