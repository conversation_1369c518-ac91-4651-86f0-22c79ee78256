# 百度地图CSP错误故障排除

## 当前问题

仍然出现CSP错误：
```
Refused to load the script 'https://api.map.baidu.com/api?v=3.0&ak=F9b5H7wTqvcNyA9bF1fZeOwwxt6CRBjy&callback=_initBaiduMap' because it violates the following Content Security Policy directive: "script-src 'self' 'unsafe-inline' 'unsafe-eval'".
```

## 可能原因

1. **配置未重新部署**：Caddy配置修改后需要重新部署才能生效
2. **浏览器缓存**：浏览器可能缓存了旧的CSP策略
3. **配置冲突**：可能存在多个CSP配置源
4. **服务未重启**：Caddy服务可能需要重启

## 解决步骤

### 步骤1：重新部署服务

使用Gitea Actions重新部署：

1. 进入Gitea项目的Actions页面
2. 选择"构建并部署"工作流
3. 点击"Run workflow"
4. 配置参数：
   ```
   操作类型: build-and-deploy
   构建服务: core
   版本号: v0.1.1
   首次部署: false  (重要！保留数据)
   ```
5. 点击"Run workflow"开始部署

### 步骤2：手动重启Caddy服务

如果重新部署后仍有问题，可以手动重启Caddy：

```bash
# 连接到服务器
ssh user@your-server

# 进入部署目录
cd /opt/weishi

# 重启Caddy服务
docker compose --env-file production.env -f docker-compose.prod.yml restart caddy

# 检查Caddy状态
docker compose --env-file production.env -f docker-compose.prod.yml ps caddy

# 查看Caddy日志
docker compose --env-file production.env -f docker-compose.prod.yml logs caddy
```

### 步骤3：验证CSP配置

检查实际生效的CSP配置：

```bash
# 检查HTTP响应头
curl -I https://your-domain.com

# 或者使用详细模式
curl -v https://your-domain.com 2>&1 | grep -i "content-security-policy"
```

### 步骤4：清除浏览器缓存

1. **Chrome/Edge**：
   - 按 F12 打开开发者工具
   - 右键点击刷新按钮
   - 选择"清空缓存并硬性重新加载"

2. **Firefox**：
   - 按 Ctrl+Shift+R 强制刷新
   - 或者清除浏览器缓存

3. **Safari**：
   - 按 Cmd+Option+R 强制刷新

### 步骤5：检查配置冲突

确认没有其他CSP配置源：

1. **检查Nuxt配置**（已确认正确）：
   ```typescript
   // web/nuxt.config.ts
   head: {
     meta: [
       {
         'http-equiv': 'Content-Security-Policy',
         content: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://api.map.baidu.com https://*.bdstatic.com https://*.bdimg.com; ..."
       }
     ]
   }
   ```

2. **检查Caddy配置**（已确认正确）：
   ```caddyfile
   header {
     Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://api.map.baidu.com https://*.bdstatic.com https://*.bdimg.com; ..."
   }
   ```

## 调试方法

### 1. 检查实际CSP策略

在浏览器开发者工具中：

1. 打开Network标签页
2. 刷新页面
3. 查看主页面请求的响应头
4. 确认`Content-Security-Policy`头部内容

### 2. 测试CSP策略

使用在线CSP测试工具：
- https://csp-evaluator.withgoogle.com/
- 输入当前的CSP策略进行验证

### 3. 临时禁用CSP测试

**仅用于调试，不要在生产环境长期使用**：

临时注释掉Caddy中的CSP配置：
```caddyfile
header {
    X-Frame-Options "SAMEORIGIN"
    X-XSS-Protection "1; mode=block"
    X-Content-Type-Options "nosniff"
    Referrer-Policy "no-referrer-when-downgrade"
    Strict-Transport-Security "max-age=31536000; includeSubDomains"
    # Content-Security-Policy "..."  # 临时注释
    -Server
}
```

## 预期结果

修复成功后应该看到：

1. **浏览器控制台**：没有CSP相关错误
2. **网络请求**：百度地图API请求成功
3. **地图显示**：联系我们页面的地图正常显示
4. **响应头**：包含正确的CSP策略

## 紧急处理

如果问题紧急需要立即解决：

### 方案1：临时移除CSP
```bash
# 备份当前配置
cp caddy/Caddyfile caddy/Caddyfile.backup

# 临时注释CSP配置
sed -i 's/Content-Security-Policy/#Content-Security-Policy/' caddy/Caddyfile

# 重启Caddy
docker compose restart caddy
```

### 方案2：使用更宽松的CSP
临时使用更宽松的script-src配置：
```
script-src 'self' 'unsafe-inline' 'unsafe-eval' https: http:;
```

## 后续监控

1. **定期检查**：确保CSP策略持续生效
2. **日志监控**：关注CSP违规日志
3. **功能测试**：定期测试百度地图功能

## 联系支持

如果以上步骤都无法解决问题，请提供：

1. 当前的CSP响应头内容
2. 浏览器控制台的完整错误信息
3. Caddy服务的日志
4. 部署时间和版本信息
