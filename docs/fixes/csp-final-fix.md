# 百度地图CSP错误最终修复

## 🎯 问题根源已找到！

**真正的问题**：生产环境使用的是 `deployment/caddy/Caddyfile.prod` 文件，而不是 `caddy/Caddyfile`。

### 📁 文件结构说明

```
项目根目录/
├── caddy/Caddyfile                    # 开发环境配置（已修改但未生效）
└── deployment/
    ├── Dockerfile.caddy-admin         # 生产环境Dockerfile
    └── caddy/
        └── Caddyfile.prod            # 生产环境配置（已修复）
```

### 🔧 已完成的修复

1. ✅ **修改了正确的配置文件**：`deployment/caddy/Caddyfile.prod`
2. ✅ **添加了百度地图域名**到CSP策略：
   ```
   script-src 'self' 'unsafe-inline' 'unsafe-eval' https://api.map.baidu.com https://*.bdstatic.com https://*.bdimg.com
   ```

### 🚀 立即部署修复

**现在需要重新构建并部署 `caddy-admin` 服务**：

#### 方法1：使用Gitea Actions（推荐）

1. 进入Gitea项目的Actions页面
2. 选择"构建并部署"工作流
3. 点击"Run workflow"
4. 配置参数：
   ```
   操作类型: build-and-deploy
   构建服务: caddy-admin  ← 重要！只构建caddy-admin
   版本号: v0.1.2
   部署环境: production
   首次部署: false
   ```
5. 点击"Run workflow"

#### 方法2：手动构建（如果Actions不可用）

```bash
# 1. 连接到服务器
ssh user@your-server

# 2. 进入项目目录
cd /path/to/project

# 3. 拉取最新代码
git pull origin main

# 4. 构建新的caddy-admin镜像
docker build -f deployment/Dockerfile.caddy-admin -t weizhi_admin_caddy:latest .

# 5. 停止并重启caddy服务
cd /opt/weishi
docker compose --env-file production.env -f docker-compose.prod.yml stop caddy
docker compose --env-file production.env -f docker-compose.prod.yml up -d caddy

# 6. 检查服务状态
docker compose --env-file production.env -f docker-compose.prod.yml ps caddy
```

## 🔍 验证修复

### 1. 检查CSP头部

```bash
curl -I https://your-domain.com | grep -i content-security-policy
```

应该看到包含百度地图域名的CSP：
```
Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://api.map.baidu.com https://*.bdstatic.com https://*.bdimg.com; ...
```

### 2. 测试百度地图

1. **清除浏览器缓存**：
   - 按 Ctrl+Shift+R (Windows) 或 Cmd+Shift+R (Mac)
   - 或者按F12 → 右键刷新按钮 → "清空缓存并硬性重新加载"

2. **访问联系我们页面**：
   - 访问 `https://your-domain.com/contact`
   - 地图应该正常显示

3. **检查控制台**：
   - 按F12打开开发者工具
   - Console标签应该没有CSP错误

## 📋 修改详情

### 修改的文件
- `deployment/caddy/Caddyfile.prod` (第40行)

### 修改前
```caddyfile
Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; media-src 'self'; object-src 'none'; frame-src 'none';"
```

### 修改后
```caddyfile
Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://api.map.baidu.com https://*.bdstatic.com https://*.bdimg.com; style-src 'self' 'unsafe-inline' https://api.map.baidu.com; img-src 'self' data: https: https://*.bdstatic.com https://*.bdimg.com https://api.map.baidu.com; font-src 'self' data: https://*.bdstatic.com; connect-src 'self' https: https://api.map.baidu.com; media-src 'self'; object-src 'none'; frame-src 'none';"
```

## ⚡ 为什么之前的修复没有生效

1. **错误的配置文件**：修改了 `caddy/Caddyfile`，但生产环境使用的是 `deployment/caddy/Caddyfile.prod`
2. **Docker镜像缓存**：即使修改了配置，旧的Docker镜像仍在使用
3. **需要重新构建**：Caddy配置文件被打包到Docker镜像中，需要重新构建镜像

## 🎯 预期结果

修复成功后：
- ✅ 联系我们页面的百度地图正常显示
- ✅ 浏览器控制台没有CSP错误
- ✅ 百度地图API请求成功加载
- ✅ 地图交互功能正常

## 🚨 如果仍有问题

### 检查镜像是否更新
```bash
# 检查镜像构建时间
docker images | grep weizhi_admin_caddy

# 检查容器使用的镜像
docker inspect weizhi-caddy-prod | grep Image
```

### 强制重新构建
```bash
# 删除旧镜像强制重新构建
docker rmi weizhi_admin_caddy:latest
docker build --no-cache -f deployment/Dockerfile.caddy-admin -t weizhi_admin_caddy:latest .
```

### 检查配置是否生效
```bash
# 进入容器检查配置文件
docker exec weizhi-caddy-prod cat /etc/caddy/Caddyfile | grep -A5 -B5 "Content-Security-Policy"
```

## 📝 总结

这次修复的关键是找到了正确的配置文件。生产环境的Caddy配置文件是 `deployment/caddy/Caddyfile.prod`，而不是项目根目录的 `caddy/Caddyfile`。

现在请重新构建并部署 `caddy-admin` 服务，百度地图的CSP错误应该就能解决了！
