# 百度地图CSP错误快速修复

## 🚨 紧急修复步骤

### 问题描述
百度地图仍然报CSP错误，可能是因为：
1. 配置修改后未重新部署
2. Nuxt和Caddy的CSP配置冲突

### 已完成的修复
1. ✅ 修改了Caddy配置，添加百度地图域名到CSP
2. ✅ 移除了Nuxt配置中的CSP设置，避免冲突

### 立即执行的步骤

#### 步骤1：重新部署应用

**使用Gitea Actions部署**：

1. 访问Gitea项目页面
2. 点击"Actions"标签
3. 选择"构建并部署"工作流
4. 点击"Run workflow"
5. 配置参数：
   ```
   操作类型: build-and-deploy
   构建服务: core
   版本号: v0.1.1
   部署环境: production
   首次部署（启用数据导入）: false  ← 重要！保留数据
   强制部署（跳过健康检查）: false
   ```
6. 点击"Run workflow"

#### 步骤2：等待部署完成

- 部署通常需要5-10分钟
- 在Actions页面监控部署进度
- 确保所有步骤都成功完成

#### 步骤3：验证修复

1. **清除浏览器缓存**：
   - Chrome: Ctrl+Shift+R (Windows) 或 Cmd+Shift+R (Mac)
   - 或者按F12 → 右键刷新按钮 → "清空缓存并硬性重新加载"

2. **访问联系我们页面**：
   - 访问 `https://your-domain.com/contact`
   - 检查地图是否正常显示

3. **检查控制台**：
   - 按F12打开开发者工具
   - 查看Console标签，确认没有CSP错误

## 🔧 手动修复（如果自动部署失败）

### 连接服务器手动重启

```bash
# 1. 连接到服务器
ssh user@your-server

# 2. 进入部署目录
cd /opt/weishi

# 3. 拉取最新代码（如果需要）
git pull origin main

# 4. 重启服务
docker compose --env-file production.env -f docker-compose.prod.yml down
docker compose --env-file production.env -f docker-compose.prod.yml up -d

# 5. 检查服务状态
docker compose --env-file production.env -f docker-compose.prod.yml ps
```

### 验证Caddy配置

```bash
# 检查Caddy配置是否正确
docker compose --env-file production.env -f docker-compose.prod.yml exec caddy caddy validate --config /etc/caddy/Caddyfile

# 查看Caddy日志
docker compose --env-file production.env -f docker-compose.prod.yml logs caddy

# 测试CSP头部
curl -I https://your-domain.com | grep -i content-security-policy
```

## 🔍 验证修复成功

### 1. 检查HTTP响应头

```bash
curl -I https://your-domain.com
```

应该看到包含百度地图域名的CSP头部：
```
Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://api.map.baidu.com https://*.bdstatic.com https://*.bdimg.com; ...
```

### 2. 浏览器测试

1. 访问联系我们页面
2. 地图应该正常显示
3. 控制台没有CSP错误

### 3. 网络请求检查

在开发者工具的Network标签中：
- 百度地图API请求应该成功（状态码200）
- 没有被CSP阻止的请求

## 📋 修改总结

### 配置变更

1. **Caddy配置** (`caddy/Caddyfile`)：
   ```caddyfile
   Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://api.map.baidu.com https://*.bdstatic.com https://*.bdimg.com; style-src 'self' 'unsafe-inline' https://api.map.baidu.com; img-src 'self' data: https: https://*.bdstatic.com https://*.bdimg.com https://api.map.baidu.com; font-src 'self' data: https://*.bdstatic.com; connect-src 'self' https: https://api.map.baidu.com; media-src 'self'; object-src 'none';"
   ```

2. **Nuxt配置** (`web/nuxt.config.ts`)：
   - 移除了CSP meta标签配置
   - 避免与Caddy配置冲突

### 为什么这样修改

1. **统一管理**：在Caddy层面统一管理CSP策略
2. **避免冲突**：防止多个CSP配置源产生冲突
3. **更好控制**：反向代理层面的控制更可靠

## 🚨 如果仍然有问题

### 临时解决方案

如果修复后仍有问题，可以临时使用更宽松的CSP：

1. 编辑 `caddy/Caddyfile`
2. 将script-src改为：
   ```
   script-src 'self' 'unsafe-inline' 'unsafe-eval' https: http:;
   ```
3. 重启Caddy服务

### 联系技术支持

提供以下信息：
1. 当前的HTTP响应头
2. 浏览器控制台错误截图
3. 部署日志
4. 服务器时间和时区

## ✅ 预期结果

修复成功后：
- ✅ 联系我们页面地图正常显示
- ✅ 浏览器控制台无CSP错误
- ✅ 百度地图API请求成功
- ✅ 网站其他功能正常
