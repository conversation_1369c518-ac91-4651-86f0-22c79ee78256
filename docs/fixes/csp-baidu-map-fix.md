# 百度地图CSP错误修复

## 问题描述

发布上线后出现以下错误：
```
Refused to load the script 'https://api.map.baidu.com/api?v=3.0&ak=F9b5H7wTqvcNyA9bF1fZeOwwxt6CRBjy&callback=_initBaiduMap' because it violates the following Content Security Policy directive: "script-src 'self' 'unsafe-inline' 'unsafe-eval'". Note that 'script-src-elem' was not explicitly set, so 'script-src' is used as a fallback.
```

## 问题原因

生产环境中Caddy服务器的CSP配置没有包含百度地图API的域名，导致浏览器阻止了百度地图脚本的加载。

## 解决方案

### 1. 修改Caddy配置

已修改 `caddy/Caddyfile` 文件，在主站点和管理后台的安全头设置中添加了完整的CSP配置：

```caddyfile
Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://api.map.baidu.com https://*.bdstatic.com https://*.bdimg.com; style-src 'self' 'unsafe-inline' https://api.map.baidu.com; img-src 'self' data: https: https://*.bdstatic.com https://*.bdimg.com https://api.map.baidu.com; font-src 'self' data: https://*.bdstatic.com; connect-src 'self' https: https://api.map.baidu.com; media-src 'self'; object-src 'none';"
```

### 2. CSP配置说明

新的CSP配置包含以下百度地图相关域名：
- `https://api.map.baidu.com` - 百度地图API主域名
- `https://*.bdstatic.com` - 百度静态资源域名
- `https://*.bdimg.com` - 百度图片资源域名

### 3. 部署步骤

1. **重新构建并部署**：
   ```bash
   # 停止服务
   docker-compose down
   
   # 重新启动服务
   docker-compose up -d
   ```

2. **验证配置**：
   ```bash
   # 检查Caddy配置是否正确加载
   docker-compose logs caddy
   
   # 测试网站访问
   curl -I https://your-domain.com
   ```

3. **测试百度地图功能**：
   - 访问联系我们页面：`https://your-domain.com/contact`
   - 检查浏览器控制台是否还有CSP错误
   - 确认地图正常加载和显示

## 验证方法

### 1. 浏览器开发者工具

1. 打开网站的联系我们页面
2. 按F12打开开发者工具
3. 查看Console标签页，确认没有CSP相关错误
4. 查看Network标签页，确认百度地图API请求成功

### 2. CSP头部检查

使用curl命令检查响应头：
```bash
curl -I https://your-domain.com
```

应该能看到包含百度地图域名的CSP头部。

### 3. 在线CSP检查工具

可以使用在线工具检查CSP配置：
- https://csp-evaluator.withgoogle.com/
- https://cspvalidator.org/

## 注意事项

1. **配置一致性**：确保Caddy的CSP配置与 `web/nuxt.config.ts` 中的CSP配置保持一致
2. **安全考虑**：只添加必要的域名，避免过度宽松的CSP配置
3. **测试环境**：在测试环境中先验证配置的正确性
4. **监控**：部署后持续监控是否有新的CSP错误

## 相关文件

- `caddy/Caddyfile` - Caddy服务器配置文件
- `web/nuxt.config.ts` - Nuxt应用CSP配置
- `web/pages/contact.vue` - 使用百度地图的页面
