# MySQL数据保护修复

## 问题描述

在CI/CD工作流中，每次部署都会执行 `docker compose down --remove-orphans --volumes`，这会删除所有Docker volumes，包括MySQL数据库的数据卷，导致数据丢失。

## 问题影响

- 每次部署后数据库数据被清空
- 用户数据、配置数据、业务数据全部丢失
- 需要重新初始化数据库和重新录入数据

## 解决方案

### 修改内容

修改了 `.gitea/workflows/build-and-deploy.yml` 文件中的数据清理逻辑：

**修改前**：
```bash
# 3. 使用 docker compose 彻底清理
docker compose --env-file production.env -f docker-compose.prod.yml down --remove-orphans --volumes 2>/dev/null || true
```

**修改后**：
```bash
# 3. 使用 docker compose 清理
if [ '${{ needs.parse-params.outputs.first_deployment }}' = 'true' ]; then
  echo '🗑️ 首次部署：清理所有数据（包括数据库）'
  docker compose --env-file production.env -f docker-compose.prod.yml down --remove-orphans --volumes 2>/dev/null || true
else
  echo '🔄 非首次部署：保留数据库数据'
  docker compose --env-file production.env -f docker-compose.prod.yml down --remove-orphans 2>/dev/null || true
fi
```

### 逻辑说明

1. **首次部署** (`first_deployment = true`)：
   - 清理所有数据卷（包括MySQL数据）
   - 确保干净的初始环境
   - 执行完整的数据库初始化

2. **非首次部署** (`first_deployment = false`)：
   - 只清理容器和网络，保留数据卷
   - 保护MySQL数据不被删除
   - 保持业务数据的连续性

## 使用方法

### 首次部署

在Gitea Actions中手动触发工作流时：

1. 设置 `首次部署（启用数据导入）` = `true`
2. 这将清理所有数据并重新初始化

### 后续部署

在Gitea Actions中手动触发工作流时：

1. 设置 `首次部署（启用数据导入）` = `false`（默认值）
2. 这将保留现有的MySQL数据

## 验证方法

### 1. 检查工作流日志

在部署过程中查看日志输出：

- 首次部署会显示：`🗑️ 首次部署：清理所有数据（包括数据库）`
- 非首次部署会显示：`🔄 非首次部署：保留数据库数据`

### 2. 检查数据库数据

部署完成后连接数据库验证数据是否保留：

```bash
# 连接到MySQL容器
docker exec -it weizhi-mysql-prod mysql -u root -p

# 检查数据库和表
SHOW DATABASES;
USE weizhi;
SHOW TABLES;

# 检查关键数据表
SELECT COUNT(*) FROM admin_users;
SELECT COUNT(*) FROM swipers;
SELECT COUNT(*) FROM services;
```

### 3. 检查Docker卷

```bash
# 查看MySQL数据卷
docker volume ls | grep mysql

# 检查卷的创建时间（非首次部署时不应该是最新时间）
docker volume inspect <volume_name>
```

## 注意事项

### 1. 首次部署标识

- 只有在真正的首次部署或需要重置所有数据时才设置为 `true`
- 日常更新部署应该保持为 `false`

### 2. 数据备份

在执行首次部署（清理数据）之前，建议先备份重要数据：

```bash
# 备份数据库
docker exec weizhi-mysql-prod mysqldump -u root -p"$MYSQL_ROOT_PASSWORD" weizhi > backup.sql

# 或使用项目提供的备份脚本
./deployment/scripts/backup.sh
```

### 3. 回滚策略

如果需要回滚到之前的数据状态：

1. 停止当前服务
2. 恢复数据库备份
3. 重新启动服务

## 相关文件

- `.gitea/workflows/build-and-deploy.yml` - CI/CD工作流配置
- `deployment/docker-compose.prod.yml` - 生产环境Docker配置
- `deployment/mysql/init/01-init_database.sql` - 数据库初始化脚本
- `docs/deployment/07-database-initialization.md` - 数据库初始化文档

## 测试建议

1. **测试首次部署**：
   - 在测试环境中设置 `first_deployment = true`
   - 验证数据库被完全重置
   - 验证初始数据正确导入

2. **测试非首次部署**：
   - 先添加一些测试数据
   - 设置 `first_deployment = false` 进行部署
   - 验证测试数据仍然存在

3. **测试数据迁移**：
   - 验证应用更新后数据库结构正确迁移
   - 验证现有数据不受影响
