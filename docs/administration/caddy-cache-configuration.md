# 🚀 Caddy 缓存配置优化指南

本文档详细说明了蔚之领域项目中 Caddy 的缓存配置策略，旨在平衡性能和更新及时性。

## 📋 缓存策略概览

### 缓存分层策略

我们采用**分层缓存策略**，根据文件类型和更新频率设置不同的缓存时间：

| 文件类型 | 缓存时间 | 策略 | 原因 |
|---------|---------|------|------|
| HTML 文件 | 不缓存 | `no-cache` | 确保内容更新及时 |
| 带版本号的静态资源 | 1年 | `max-age=31536000, immutable` | 文件名包含哈希，永不变更 |
| 普通静态资源 | 1天 | `max-age=86400` | 平衡性能和更新 |
| 图标文件 | 1小时 | `max-age=3600` | 较少变更但需要及时更新 |
| API 响应 | 不缓存 | `no-cache` | 动态内容，不应缓存 |

## 🔧 详细配置说明

### 1. 管理后台缓存配置

#### HTML 文件 - 不缓存
```caddyfile
@html {
    path /admin/index.html /admin/ /admin
}
header @html Cache-Control "no-cache, no-store, must-revalidate"
header @html Pragma "no-cache"
header @html Expires "0"
```

**作用**：
- 确保 `index.html` 总是获取最新版本
- 避免因缓存导致的资源文件 404 问题
- 支持 SPA 应用的即时更新

#### 带版本号的静态资源 - 长期缓存
```caddyfile
@versioned_assets {
    path_regexp versioned ^/admin/assets/.*-[a-zA-Z0-9_-]+\.(js|css|woff2?|ttf|eot|svg|png|jpg|jpeg|gif|webp|ico)$
}
header @versioned_assets Cache-Control "public, max-age=31536000, immutable"
```

**作用**：
- 匹配 Vite 构建的带哈希值的文件（如 `index-Buzjvb7G.js`）
- 1年长期缓存，因为文件名变更时内容必然变更
- `immutable` 指令告诉浏览器文件永不变更

#### 其他静态资源 - 中期缓存
```caddyfile
@other_assets {
    path /admin/assets/*
    not path_regexp ^/admin/assets/.*-[a-zA-Z0-9_-]+\.(js|css|...)$
}
header @other_assets Cache-Control "public, max-age=86400"
```

**作用**：
- 处理不带版本号的静态资源
- 1天缓存时间，平衡性能和更新及时性

### 2. 前端网站缓存配置

#### 静态资源缓存
```caddyfile
@static_files {
    path /_nuxt/* /favicon.ico *.css *.js *.png *.jpg *.jpeg *.gif *.svg *.webp *.woff *.woff2 *.ttf *.eot
}
header @static_files Cache-Control "public, max-age=31536000, immutable"
```

**作用**：
- Nuxt3 构建的静态资源通常包含哈希值
- 长期缓存提高加载性能

#### HTML 页面不缓存
```caddyfile
@html_pages {
    path / /about /services /cases /news /contact
    header Content-Type text/html*
}
header @html_pages Cache-Control "no-cache, no-store, must-revalidate"
```

**作用**：
- 确保页面内容及时更新
- 支持 SEO 和动态内容

## ⏰ 缓存时间详解

### 时间单位说明
```
3600 = 1小时
86400 = 1天 (24 * 60 * 60)
604800 = 1周 (7 * 24 * 60 * 60)
2592000 = 1个月 (30 * 24 * 60 * 60)
31536000 = 1年 (365 * 24 * 60 * 60)
```

### 推荐的缓存时间

#### 🚀 高性能场景（适合稳定的生产环境）
```caddyfile
# 带版本号的资源
max-age=31536000, immutable  # 1年

# 普通静态资源
max-age=604800              # 1周

# 图标和字体
max-age=86400               # 1天

# HTML 文件
no-cache                    # 不缓存
```

#### ⚡ 平衡场景（推荐，当前使用）
```caddyfile
# 带版本号的资源
max-age=31536000, immutable  # 1年

# 普通静态资源
max-age=86400               # 1天

# 图标和字体
max-age=3600                # 1小时

# HTML 文件
no-cache                    # 不缓存
```

#### 🔄 开发场景（频繁更新）
```caddyfile
# 带版本号的资源
max-age=86400               # 1天

# 普通静态资源
max-age=3600                # 1小时

# 图标和字体
max-age=300                 # 5分钟

# HTML 文件
no-cache                    # 不缓存
```

## 🛡️ 安全头配置

### 内容类型保护
```caddyfile
header X-Content-Type-Options "nosniff"
```
- 防止浏览器 MIME 类型嗅探攻击

### 框架保护
```caddyfile
header X-Frame-Options "SAMEORIGIN"
```
- 防止点击劫持攻击

### XSS 保护
```caddyfile
header X-XSS-Protection "1; mode=block"
```
- 启用浏览器 XSS 过滤器

## 🔧 环境特定配置

### 开发环境配置
```caddyfile
# 开发环境 - 禁用所有缓存
@dev_env {
    header_regexp Host ^localhost
}
header @dev_env Cache-Control "no-cache, no-store, must-revalidate"
```

### 生产环境配置
```caddyfile
# 生产环境 - 启用压缩
encode gzip

# 启用 HTTP/2 推送（可选）
@push_assets {
    path /admin/index.html
}
push @push_assets /admin/assets/vendor-*.js
```

## 📊 缓存效果监控

### 1. 浏览器开发者工具检查
- 打开 Network 标签
- 查看 `Cache-Control` 响应头
- 检查 `Status` 列是否显示 `(from cache)`

### 2. 缓存命中率检查
```bash
# 检查 Caddy 访问日志
docker-compose logs caddy | grep "GET /admin/assets"
```

### 3. 性能测试
```bash
# 使用 curl 测试缓存头
curl -I https://your-domain.com/admin/assets/index-xxx.js
```

## 🚨 常见问题和解决方案

### 问题1：更新后用户看不到新版本
**原因**：HTML 文件被缓存
**解决**：确保 HTML 文件设置为 `no-cache`

### 问题2：静态资源 404 错误
**原因**：文件名变更但浏览器缓存了旧的 HTML
**解决**：
1. HTML 文件不缓存
2. 使用带版本号的资源文件名

### 问题3：加载速度慢
**原因**：缓存时间过短
**解决**：适当延长静态资源缓存时间

### 问题4：开发时缓存干扰调试
**解决**：
```caddyfile
# 开发环境禁用缓存
{$ENVIRONMENT:production} != "development" {
    # 生产环境缓存配置
}
```

## 🔄 缓存清理策略

### 1. 服务端清理
```bash
# 重启 Caddy 服务
docker-compose restart caddy

# 清理 Docker 缓存
docker system prune -f
```

### 2. 客户端清理
- 强制刷新：`Ctrl+Shift+R` (Windows) / `Cmd+Shift+R` (Mac)
- 清空缓存：浏览器设置 -> 清除浏览数据
- 无痕模式：测试时使用无痕模式

### 3. CDN 清理（如果使用）
```bash
# 示例：清理 CloudFlare 缓存
curl -X POST "https://api.cloudflare.com/client/v4/zones/{zone_id}/purge_cache" \
  -H "Authorization: Bearer {api_token}" \
  -H "Content-Type: application/json" \
  --data '{"purge_everything":true}'
```

## 📈 性能优化建议

### 1. 启用压缩
```caddyfile
encode gzip zstd
```

### 2. 使用 HTTP/2
```caddyfile
# Caddy 默认启用 HTTP/2
protocols h1 h2 h2c
```

### 3. 预加载关键资源
```caddyfile
@critical_css {
    path /admin/index.html
}
header @critical_css Link "</admin/assets/index-*.css>; rel=preload; as=style"
```

### 4. 设置合理的 TTL
- 根据更新频率调整缓存时间
- 监控缓存命中率
- 定期评估和调整策略

## 🎯 最佳实践总结

1. **HTML 文件永不缓存**：确保应用更新及时
2. **带版本号的资源长期缓存**：利用文件名哈希特性
3. **分层缓存策略**：根据文件类型设置不同缓存时间
4. **安全头配置**：增强应用安全性
5. **监控和调整**：定期检查缓存效果并优化
6. **环境区分**：开发和生产环境使用不同策略
7. **清理机制**：提供多种缓存清理方案

通过这套缓存配置，可以有效避免缓存导致的更新问题，同时最大化性能收益。
