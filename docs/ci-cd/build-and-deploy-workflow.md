# 构建并部署工作流使用指南

## 🎯 概述

`build-and-deploy.yml` 是一个完整的 Gitea Actions 工作流，可以在一个流程中完成构建和部署，无需分别触发两个工作流。

## 🚀 功能特性

### ✅ 一键完成
- **构建** → **打包** → **部署** 全流程自动化
- 无需手动触发多个工作流
- 自动版本管理和制品传递

### ✅ 灵活配置
- 选择构建服务（all, web, admin, server-go）
- 选择推送目标（外部仓库、Gitea 包仓库）
- 支持强制部署和首次部署
- 支持跳过健康检查

### ✅ 并行构建
- 3个服务同时构建，提高效率
- 智能服务选择，只构建需要的服务
- 构建失败不影响其他服务

### ✅ 安全部署
- 自动生成生产环境配置
- SSH 密钥认证
- 健康检查和状态验证
- 配置备份和回滚支持

### ✅ 版本控制
- 自动生成唯一版本号
- 支持指定版本部署
- 完整的版本追踪和管理
- 支持版本回滚

## 🏷️ 版本号管理

### 版本号格式
```
YYYYMMDD-HHMMSS-{git_hash}
例如: 20241201-143022-a1b2c3d4
```

### 版本号生成规则
- **构建并部署模式**: 自动生成基于当前时间和 Git 提交哈希的版本号
- **仅部署模式**: 使用手动指定的版本号

### 版本号获取方法

#### 1. **从构建日志获取**
在构建完成后的日志中查找：
```
✅ 参数解析完成
版本号: 20241201-143022-a1b2c3d4
```

#### 2. **从部署总结获取**
在工作流完成后的总结中查看：
```markdown
### 📋 部署信息
- **版本号**: 20241201-143022-a1b2c3d4
```

#### 3. **从制品名称获取**
下载的制品文件名包含版本号：
```
weizhi-deploy-20241201-143022-a1b2c3d4.tar.gz
```

#### 4. **从服务器查看**
登录服务器查看 VERSION 文件：
```bash
cd /path/to/deploy
cat VERSION
```

### 版本号使用场景
- **回滚**: 使用之前的稳定版本号
- **重新部署**: 使用相同版本号重新部署
- **测试**: 使用特定的测试版本号

## 📋 使用方法

### 1. **触发工作流**

在 Gitea 界面中：
```
Actions → 构建并部署 → Run workflow
```

### 2. **操作模式**

#### **🚀 构建并部署模式** (build-and-deploy)
- 构建最新代码并生成新镜像
- 自动生成版本号：`20241201-143022-a1b2c3d4`
- 推送镜像到指定仓库
- 部署新构建的镜像

#### **📦 仅部署模式** (deploy-only)
- 使用已存在的镜像进行部署
- **必须指定版本号**：如 `20241201-143022-a1b2c3d4`
- 跳过构建阶段，直接部署
- 适用于回滚或部署之前构建的版本

### 3. **配置参数**

#### 基本参数
```yaml
操作类型: build-and-deploy        # 构建并部署 / 仅部署
构建服务: all                    # 构建所有服务 (仅构建模式有效)
版本号: 20241201-143022-a1b2c3d4 # 指定版本 (仅部署模式必填)
部署环境: production             # 部署到生产环境
```

#### 镜像推送参数 (仅构建模式有效)
```yaml
推送到外部镜像仓库: ✅            # 推送到阿里云等外部仓库
推送到 Gitea 包仓库: ✅          # 推送到 Gitea 内置仓库
```

#### 高级参数
```yaml
强制部署: ❌                     # 正常部署，包含健康检查
首次部署: ❌                     # 非首次部署，不导入数据
```

### 4. **参数说明**

#### **构建服务选项**
- `all` - 构建所有服务（web + caddy-admin + server-go）
- `web` - 只构建前端服务
- `caddy-admin` - 只构建管理后台（包含 Caddy 反向代理）
- `server-go` - 只构建后端服务
- `web,caddy-admin` - 构建前端和管理后台
- `web,server-go` - 构建前端和后端
- `caddy-admin,server-go` - 构建管理后台和后端

#### **推送选项**
- **推送到外部镜像仓库**: 推送到阿里云容器镜像服务等
- **推送到 Gitea 包仓库**: 推送到 Gitea 内置的包仓库

#### **部署选项**
- **强制部署**: 跳过健康检查，强制更新服务
- **首次部署**: 启用数据导入，用于全新环境部署

## 🔄 工作流程

### 完整流程图
```mermaid
graph TD
    A[手动触发] --> B[解析参数]
    B --> C[并行构建服务]
    C --> D[生成部署制品]
    D --> E[下载制品]
    E --> F[生成环境配置]
    F --> G[上传到服务器]
    G --> H[解压制品]
    H --> I[拉取镜像]
    I --> J[启动服务]
    J --> K[健康检查]
    K --> L[数据导入?]
    L --> M[部署完成]
```

### 阶段详解

#### **1. 解析参数阶段**
- 生成唯一版本号：`20241201-143022-a1b2c3d4`
- 解析构建服务列表
- 设置部署参数

#### **2. 并行构建阶段**
- 同时构建选中的服务
- 推送镜像到指定仓库
- 生成构建元数据

#### **3. 制品打包阶段**
- 创建部署配置包
- 包含 Docker Compose 文件
- 包含版本信息和快速部署脚本

#### **4. 部署阶段**
- 下载构建制品
- 生成生产环境配置
- 上传到服务器并解压
- 拉取镜像并启动服务
- 执行健康检查
- 可选的数据导入

## 📊 输出和监控

### 构建总结
工作流完成后会生成详细的部署总结：

```markdown
## 🎉 部署完成总结

### 📋 部署信息
- **版本号**: 20241201-143022-a1b2c3d4
- **部署环境**: production
- **部署服务**: all
- **部署时间**: 2024-12-01 14:30:22
- **强制部署**: false
- **首次部署**: false

### 🔗 访问地址
- **前端**: https://viclink.cn
- **管理后台**: https://admin.viclink.cn

### ✅ 部署成功
所有服务已成功部署并运行！
```

### 制品输出
- **部署制品**: `weizhi-deploy-{version}.tar.gz`
- **构建元数据**: 每个服务的构建信息
- **版本信息**: 完整的版本和构建详情

## 🎯 使用场景

### 场景1：日常功能发布 (构建并部署)
```yaml
操作类型: build-and-deploy
构建服务: all
部署环境: production
推送到外部镜像仓库: ✅
推送到 Gitea 包仓库: ✅
强制部署: ❌
首次部署: ❌
```

### 场景2：管理后台更新 (构建并部署)
```yaml
操作类型: build-and-deploy
构建服务: caddy-admin
部署环境: production
推送到外部镜像仓库: ✅
推送到 Gitea 包仓库: ❌
强制部署: ✅
首次部署: ❌
```

### 场景3：全新环境部署 (构建并部署)
```yaml
操作类型: build-and-deploy
构建服务: all
部署环境: production
推送到外部镜像仓库: ✅
推送到 Gitea 包仓库: ✅
强制部署: ❌
首次部署: ✅
```

### 场景4：版本回滚 (仅部署)
```yaml
操作类型: deploy-only
版本号: 20241130-120000-x1y2z3a4  # 之前的稳定版本
部署环境: production
强制部署: ✅                      # 快速回滚
首次部署: ❌
```

### 场景5：重新部署指定版本 (仅部署)
```yaml
操作类型: deploy-only
版本号: 20241201-143022-a1b2c3d4  # 指定版本
部署环境: production
强制部署: ❌                      # 正常部署流程
首次部署: ❌
```

### 场景6：测试特定版本 (仅部署)
```yaml
操作类型: deploy-only
版本号: 20241201-100000-test123   # 测试版本
部署环境: production
强制部署: ✅                      # 跳过健康检查
首次部署: ❌
```

## ⚠️ 注意事项

### 1. **环境变量配置**
确保在 Gitea 中配置了所有必要的 Variables 和 Secrets：
- 镜像仓库配置
- 数据库密码
- 应用密钥
- 服务器连接信息

### 2. **SSH 密钥配置**
确保 `DEPLOY_SSH_KEY` 包含正确的私钥，对应的公钥已添加到服务器。

### 3. **首次部署**
只在全新环境或需要重新初始化数据时启用"首次部署"选项。

### 4. **强制部署**
只在紧急情况或确认安全时使用"强制部署"选项。

## 🛠️ 故障排除

### 常见问题

#### 1. 构建失败
- 检查代码是否有语法错误
- 验证 Docker 文件是否正确
- 确认依赖包是否可用

#### 2. 推送失败
- 检查镜像仓库认证信息
- 验证网络连接
- 确认仓库权限

#### 3. 部署失败
- 检查服务器连接
- 验证 SSH 密钥
- 确认服务器资源充足

#### 4. 健康检查失败
- 检查服务启动日志
- 验证端口配置
- 确认依赖服务状态

---

通过这个一体化工作流，你可以实现真正的一键部署，从代码构建到服务上线，全程自动化！
