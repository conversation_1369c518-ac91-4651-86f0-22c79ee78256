# Gitea Actions CI/CD 配置完成

我已经为你的项目配置了完整的 Gitea Actions CI/CD 流水线。以下是配置的详细说明：

## 📁 创建的文件

### 工作流文件 (.gitea/workflows/)
1. **ci-cd.yml** - 主要的 CI/CD 流水线
   - 代码质量检查和测试
   - 自动构建 Docker 镜像
   - 推送到镜像仓库
   - 创建部署包

2. **release.yml** - 发布流程
   - 构建生产版本镜像
   - 创建完整发布包
   - 上传到 Gitea Packages

3. **build-push.yml** - 快速构建推送
   - 智能检测文件变化
   - 只构建有变化的服务
   - 支持手动触发

### 配置和脚本文件
4. **.gitea/README.md** - 详细的使用说明
5. **.gitea/scripts/check-config.sh** - 配置检查脚本
6. **.gitea/scripts/local-build.sh** - 本地构建测试脚本
7. **.gitea/scripts/setup.sh** - 快速设置向导

## 🚀 工作流触发条件

### 自动触发
- **推送到 main/master 分支** → 触发完整 CI/CD 流水线
- **推送到 develop 分支** → 触发构建和测试
- **创建 Pull Request** → 触发代码检查
- **发布 Release** → 触发发布流程

### 手动触发
- **Build and Push** - 可选择构建特定服务
- **Release Package** - 创建发布版本

## 🐳 Docker 镜像

每次构建会生成以下镜像：

| 服务 | 镜像名称 | 描述 |
|------|----------|------|
| web | `{REGISTRY_URL}/{NAMESPACE}/weizhi_web` | Nuxt3 前端应用 |
| caddy-admin | `{REGISTRY_URL}/{NAMESPACE}/weizhi_admin_caddy` | Caddy + 管理后台静态资源 |
| server-go | `{REGISTRY_URL}/{NAMESPACE}/weizhi_server` | Go 后端服务 |

## ⚙️ 必需的配置

### 1. 在 Gitea 中配置 Secrets

进入仓库设置 → Secrets，添加以下配置：

```
REGISTRY_USERNAME=your_registry_username
REGISTRY_PASSWORD=your_registry_password
REGISTRY_URL=registry.cn-hangzhou.aliyuncs.com  # 可选
NAMESPACE=vest  # 可选
```

### 2. 阿里云容器镜像服务设置

1. 创建命名空间：`vest`
2. 创建镜像仓库：
   - `weizhi_web`
   - `weizhi_admin` 
   - `weizhi_server`

## 🛠️ 使用方法

### 快速开始
```bash
# 1. 运行设置向导
./.gitea/scripts/setup.sh

# 2. 检查配置
./.gitea/scripts/check-config.sh

# 3. 本地构建测试
./.gitea/scripts/local-build.sh
```

### 自动构建
```bash
# 推送代码触发自动构建
git add .
git commit -m "feat: 添加新功能"
git push origin main
```

### 创建发布版本
```bash
# 创建标签触发发布
git tag v1.0.0
git push origin v1.0.0
```

### 手动构建特定服务
在 Gitea Actions 页面手动触发 "Build and Push"：
- 选择服务：web, admin, server-go 或 all
- 选择是否推送到镜像仓库

## 📦 构建产物

### 1. Docker 镜像
- 自动推送到配置的镜像仓库
- 包含版本标签和 latest 标签

### 2. 部署包
包含完整的部署文件：
- Docker Compose 配置
- 部署脚本
- 配置文件
- 版本信息

## 🔧 高级功能

### 智能构建
- 自动检测文件变化
- 只构建有变化的服务
- 并行构建支持

### 缓存优化
- Docker 层缓存
- 依赖缓存
- 构建时间优化

### 多环境支持
- 开发环境 (develop 分支)
- 生产环境 (main 分支)
- 测试环境部署

## 📊 监控和调试

### 查看构建状态
1. 进入 Gitea 仓库
2. 点击 "Actions" 标签
3. 查看工作流运行状态

### 常见问题排查
```bash
# 检查配置
./.gitea/scripts/check-config.sh

# 本地测试构建
./.gitea/scripts/local-build.sh --dry-run

# 查看构建日志
# 在 Gitea Actions 页面点击具体的工作流运行
```

## 🔒 安全最佳实践

1. **Secrets 管理**
   - 不要在代码中硬编码敏感信息
   - 使用 Gitea Secrets 存储凭证
   - 定期轮换密码

2. **镜像安全**
   - 使用最小化基础镜像
   - 定期更新依赖
   - 扫描安全漏洞

3. **访问控制**
   - 限制镜像仓库访问权限
   - 使用专用的 CI/CD 账号
   - 启用双因素认证

## 📈 性能优化

1. **构建优化**
   - 使用多阶段构建
   - 优化 Docker 层缓存
   - 并行构建服务

2. **镜像优化**
   - 最小化镜像大小
   - 使用 .dockerignore
   - 压缩静态资源

## 🎯 下一步建议

1. **立即执行**：
   ```bash
   # 运行设置向导
   ./.gitea/scripts/setup.sh
   
   # 配置 Gitea Secrets
   # 参考 .gitea/SECRETS_SETUP.md
   
   # 测试首次构建
   git push origin main
   ```

2. **后续优化**：
   - 添加自动化测试
   - 配置代码质量检查
   - 设置部署通知
   - 添加性能监控

3. **团队协作**：
   - 制定分支策略
   - 设置代码审查流程
   - 配置部署权限

## 📚 相关文档

- [.gitea/README.md](.gitea/README.md) - 详细使用说明
- [.gitea/SECRETS_SETUP.md](.gitea/SECRETS_SETUP.md) - Secrets 配置指南
- [deployment/README.md](deployment/README.md) - 部署说明

## 🆘 获取帮助

如果遇到问题：

1. 查看工作流日志
2. 运行配置检查脚本
3. 参考故障排除文档
4. 检查 Gitea Actions 文档

---

**配置完成！** 🎉

现在你可以享受自动化的 CI/CD 流水线了。每次推送代码都会自动构建、测试和部署你的应用。
