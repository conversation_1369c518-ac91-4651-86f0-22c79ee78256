# Gitea 软件包仓库配置指南

本文档介绍如何配置 Gitea 软件包仓库来存储 Docker 镜像，以及如何使用构建推送工作流。

## 📋 前置要求

1. **Gitea 实例**：确保你的 Gitea 实例支持软件包功能
2. **Docker Registry**：Gitea 内置的 Docker Registry 功能
3. **访问权限**：确保有推送软件包的权限

## 🔧 配置步骤

### 1. 启用 Gitea 软件包功能

在 Gitea 的 `app.ini` 配置文件中确保启用了软件包功能：

```ini
[packages]
ENABLED = true
CHUNKED_UPLOAD_PATH = tmp/package-upload
```

### 2. 配置仓库变量 (Variables)

在你的 Gitea 仓库设置中，添加以下变量：

- **PACKAGE_REGISTRY**: 你的 Gitea 软件包仓库地址
  - 格式：`your-gitea-domain.com` 或 `gitea.example.com`
  - 示例：`**********:6001` (如果使用 IP 和端口)

### 3. 配置仓库密钥 (Secrets)

在你的 Gitea 仓库设置中，添加以下密钥：

- **PACKAGE_TOKEN**: 用于推送软件包的访问令牌
  - 生成方法：用户设置 → 应用 → 生成新令牌
  - 权限：需要 `write:packages` 权限

### 4. 更新 Docker Compose 配置

修改你的 `deployment/docker-compose.prod.yml` 文件，使用 Gitea 软件包仓库的镜像：

```yaml
version: '3.8'

services:
  web:
    image: ${PACKAGE_REGISTRY}/${NAMESPACE}/weizhi-web:${VERSION:-latest}
    # ... 其他配置

  admin:
    image: ${PACKAGE_REGISTRY}/${NAMESPACE}/weizhi-admin:${VERSION:-latest}
    # ... 其他配置

  server:
    image: ${PACKAGE_REGISTRY}/${NAMESPACE}/weizhi-server:${VERSION:-latest}
    # ... 其他配置
```

## 🚀 使用方法

### 自动触发构建

工作流会在以下情况自动触发：

1. **发布 Release**

### 手动触发构建

1. 进入仓库的 Actions 页面
2. 选择 "构建并推送" 工作流
3. 点击 "Run workflow"
4. 选择要构建的服务和推送选项

### 拉取镜像

构建完成后，你可以使用以下命令拉取镜像：

```bash
# 拉取最新版本
docker pull your-gitea-domain.com/your-org/weizhi-web:latest
docker pull your-gitea-domain.com/your-org/weizhi-admin:latest
docker pull your-gitea-domain.com/your-org/weizhi-server:latest

# 拉取指定版本
docker pull your-gitea-domain.com/your-org/weizhi-web:v1.0.0
```

## 📦 镜像命名规则

- **Web 服务**: `weizhi-web`
- **Admin 服务**: `weizhi-admin`
- **Server 服务**: `weizhi-server`

## 🏷️ 标签规则

每个镜像都会推送两个标签：

1. **版本标签**: 基于构建时间和提交哈希，如 `20240131-143022-abc12345`
2. **latest 标签**: 始终指向最新构建的镜像

## 🔍 查看软件包

在 Gitea 中查看推送的软件包：

1. 进入你的仓库
2. 点击 "Packages" 标签
3. 查看 Docker 镜像列表

## 🛠️ 故障排除

### 常见问题

1. **推送失败 - 认证错误**
   - 检查 `PACKAGE_TOKEN` 是否正确设置
   - 确认令牌有 `write:packages` 权限

2. **推送失败 - 仓库地址错误**
   - 检查 `PACKAGE_REGISTRY` 变量是否正确
   - 确认地址格式（不要包含 `http://` 或 `https://`）

3. **构建失败 - Dockerfile 找不到**
   - 确认 `deployment/Dockerfile.web`、`deployment/Dockerfile.admin`、`deployment/Dockerfile.server-go` 文件存在

### 调试步骤

1. 查看 Actions 日志中的详细错误信息
2. 检查仓库变量和密钥配置
3. 验证 Gitea 软件包功能是否启用

## 📝 配置示例

### 环境变量示例

```bash
# .env 文件
PACKAGE_REGISTRY=gitea.example.com
NAMESPACE=your-org
VERSION=latest
```

### Docker Compose 示例

```yaml
version: '3.8'

services:
  web:
    image: ${PACKAGE_REGISTRY:-gitea.example.com}/${NAMESPACE:-your-org}/weizhi-web:${VERSION:-latest}
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production

  admin:
    image: ${PACKAGE_REGISTRY:-gitea.example.com}/${NAMESPACE:-your-org}/weizhi-admin:${VERSION:-latest}
    ports:
      - "4000:4000"
    environment:
      - NODE_ENV=production

  server:
    image: ${PACKAGE_REGISTRY:-gitea.example.com}/${NAMESPACE:-your-org}/weizhi-server:${VERSION:-latest}
    ports:
      - "8080:8080"
    environment:
      - GO_ENV=production
```

## 📋 下一步操作

1. **配置 Gitea 变量和密钥**：
   - 在仓库设置中添加 `PACKAGE_REGISTRY` 变量
   - 在仓库设置中添加 `PACKAGE_TOKEN` 密钥

2. **更新 Docker Compose 文件**：
   - 修改镜像地址指向 Gitea 软件包仓库
   - 参考上面的配置示例

3. **测试工作流**：
   - 手动触发一次构建验证配置正确
   - 检查 Gitea 软件包页面确认镜像推送成功

## 🔄 与现有工作流的关系

新的 `build-and-push-packages.yml` 工作流与现有工作流的关系：

- **独立运行**: 不会影响现有的构建流程
- **并行推送**: 可以同时推送到外部镜像仓库和 Gitea 软件包仓库
- **灵活配置**: 可以选择只推送到 Gitea 或同时推送到多个仓库

## 📚 相关文档

- [Gitea 软件包文档](https://docs.gitea.io/en-us/packages/overview/)
- [Docker Registry API](https://docs.docker.com/registry/spec/api/)
- [GitHub Actions Docker 构建](https://docs.github.com/en/actions/publishing-packages/publishing-docker-images)
