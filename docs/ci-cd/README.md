# 🔧 CI/CD 配置与管理

本项目采用现代化的CI/CD流程，支持自动化构建、测试和部署。

## 📋 目录

- [Gitea Actions配置](./gitea-actions-setup.md) - 完整的Gitea Actions CI/CD配置指南
- [Gitea Packages设置](./gitea-packages-setup.md) - Gitea包管理配置
- [自动部署配置](./gitea-auto-deploy-setup.md) - 自动化部署设置
- [构建和部署工作流](./build-and-deploy-workflow.md) - CI/CD工作流详解
- [并行构建优化](./parallel-build-optimization.md) - 构建性能优化指南

## 🚀 快速开始

### 1. 配置Gitea Actions

在Gitea仓库中配置以下Secrets：

```bash
REGISTRY_USERNAME=your_registry_username
REGISTRY_PASSWORD=your_registry_password
REGISTRY_URL=registry.cn-hangzhou.aliyuncs.com
NAMESPACE=vest
```

### 2. 自动触发构建

- **推送到main/master分支** → 触发完整CI/CD流水线
- **推送到develop分支** → 触发构建和测试
- **创建Pull Request** → 触发代码检查
- **发布Release** → 触发发布流程

### 3. 构建产物

每次构建会生成以下Docker镜像：

- `weizhi_web` - Nuxt3前端应用
- `weizhi_admin` - Vue3管理后台
- `weizhi_server` - Go后端服务

## 📊 监控和调试

查看构建状态：
1. 进入Gitea仓库
2. 点击"Actions"标签
3. 查看工作流运行状态

常见问题排查：
```bash
# 检查配置
./.gitea/scripts/check-config.sh

# 本地测试构建
./.gitea/scripts/local-build.sh
```

---

*最后更新: 2025-08-01*