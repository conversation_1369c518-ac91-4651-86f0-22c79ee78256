# Gitea Actions 自动化部署配置指南

本文档详细说明如何配置 Gitea Actions 进行自动化部署。

## 🚀 部署工作流概览

### 1. 自动化部署 (`deploy.yml`)
**功能特性**：
- ✅ 手动触发部署 - 完全可控的部署流程
- ✅ 自动部署 - 构建成功后自动部署到测试环境
- 🎯 选择性部署 - 支持部署指定服务
- 🔄 滚动更新 - 零停机部署
- 🏥 健康检查 - 自动验证部署结果
- 📦 备份机制 - 部署前自动备份

### 2. 服务回滚 (`rollback.yml`)
**功能特性**：
- 🔄 版本回滚 - 回滚到上一个版本
- 📋 指定备份回滚 - 回滚到特定备份点
- 🚨 紧急停止 - 快速停止服务
- ✅ 安全确认 - 防止误操作

## ⚙️ 配置要求

### 生产环境配置 (Variables)

在 Gitea 仓库设置 → Variables 中配置：

```bash
# 生产环境服务器配置
PROD_DEPLOY_HOST=your.production.server.com    # 生产服务器地址
PROD_DEPLOY_USER=deploy                         # 部署用户
PROD_DEPLOY_PATH=/opt/weishi                    # 部署路径

# 测试环境服务器配置
STAGING_DEPLOY_HOST=your.staging.server.com    # 测试服务器地址
STAGING_DEPLOY_USER=deploy                      # 部署用户
STAGING_DEPLOY_PATH=/opt/weishi-staging         # 部署路径

# 镜像仓库配置
REGISTRY_URL=registry.cn-hangzhou.aliyuncs.com # 镜像仓库地址
NAMESPACE=weishi                                # 命名空间
```

### 生产环境密钥 (Secrets)

在 Gitea 仓库设置 → Secrets 中配置：

```bash
# SSH 连接密钥
PROD_SSH_PRIVATE_KEY=-----BEGIN OPENSSH PRIVATE KEY-----
# 生产服务器 SSH 私钥内容
-----END OPENSSH PRIVATE KEY-----

STAGING_SSH_PRIVATE_KEY=-----BEGIN OPENSSH PRIVATE KEY-----
# 测试服务器 SSH 私钥内容
-----END OPENSSH PRIVATE KEY-----

# 镜像仓库认证
REGISTRY_USERNAME=your_registry_username        # 镜像仓库用户名
REGISTRY_PASSWORD=your_registry_password        # 镜像仓库密码
```

## 🛠️ 服务器环境准备

### 1. 创建部署用户

```bash
# 在服务器上创建部署用户
sudo useradd -m -s /bin/bash deploy
sudo usermod -aG docker deploy

# 创建部署目录
sudo mkdir -p /opt/weishi
sudo chown deploy:deploy /opt/weishi

# 切换到部署用户
sudo su - deploy
```

### 2. 配置 SSH 密钥认证

```bash
# 在本地生成 SSH 密钥对
ssh-keygen -t rsa -b 4096 -C "deploy@weishi" -f ~/.ssh/weishi_deploy

# 将公钥添加到服务器
ssh-copy-id -i ~/.ssh/weishi_deploy.pub <EMAIL>

# 或手动添加
mkdir -p ~/.ssh
echo "your_public_key_content" >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys
chmod 700 ~/.ssh
```

### 3. 安装必要软件

```bash
# 安装 Docker 和 Docker Compose
curl -fsSL https://get.docker.com | sh
sudo usermod -aG docker deploy

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker --version
docker-compose --version
```

### 4. 准备部署目录结构

```bash
cd /opt/weishi

# 创建必要目录
mkdir -p {logs/{mysql,server,caddy},uploads,backups}

# 设置权限
chmod 755 logs uploads backups
```

### 5. 配置生产环境变量

```bash
# 复制环境配置模板
cp deployment/production.env.example production.env

# 编辑生产环境配置
nano production.env
```

**重要配置项**：
```bash
# 域名配置
DOMAIN=your-domain.com
ADMIN_DOMAIN=admin.your-domain.com

# 数据库密码（必须修改）
MYSQL_ROOT_PASSWORD=your_very_secure_root_password
MYSQL_PASSWORD=your_very_secure_db_password

# JWT 密钥（必须修改）
JWT_SECRET=your-super-complex-jwt-secret-key

# 管理员账号（必须修改）
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_very_secure_admin_password

# SSL 证书邮箱
SSL_EMAIL=<EMAIL>
```

## 🚀 部署流程

### 1. 手动部署

1. **进入 Gitea Actions 页面**
2. **选择 "自动化部署" 工作流**
3. **点击 "Run workflow"**
4. **配置部署选项**：
   - 环境：production/staging
   - 服务：all/web/admin/server-go
   - 版本：latest 或指定版本
   - 强制部署：是否跳过健康检查

### 2. 自动部署

- **触发条件**：构建工作流成功完成
- **目标环境**：自动部署到测试环境
- **部署范围**：所有服务

### 3. 部署监控

部署过程中可以实时查看：
- 📊 部署进度
- 🔍 详细日志
- ✅ 健康检查结果
- 📦 备份信息

## 🔄 回滚操作

### 1. 版本回滚

```bash
# 通过 Gitea Actions 界面
1. 选择 "服务回滚" 工作流
2. 选择回滚类型：previous_version
3. 确认回滚操作
```

### 2. 指定备份回滚

```bash
# 查看可用备份
ssh <EMAIL> "ls -la /opt/weishi/backups/"

# 通过 Gitea Actions 回滚
1. 选择 "服务回滚" 工作流
2. 选择回滚类型：specific_backup
3. 输入备份ID：20241201_143022
4. 确认回滚操作
```

### 3. 紧急停止

```bash
# 紧急情况下快速停止服务
1. 选择 "服务回滚" 工作流
2. 选择回滚类型：emergency_stop
3. 选择要停止的服务
4. 确认操作
```

## 📋 部署检查清单

### 部署前检查
- [ ] 服务器环境已准备
- [ ] SSH 密钥已配置
- [ ] 环境变量已设置
- [ ] 域名 DNS 已解析
- [ ] 防火墙规则已配置
- [ ] SSL 证书配置正确

### 部署后验证
- [ ] 所有服务正常运行
- [ ] 健康检查通过
- [ ] 前端页面可访问
- [ ] API 接口正常
- [ ] 管理后台可登录
- [ ] 数据库连接正常

## 🔧 故障排除

### 常见问题

1. **SSH 连接失败**
   ```bash
   # 检查 SSH 密钥格式
   # 确保私钥以 -----BEGIN OPENSSH PRIVATE KEY----- 开头
   # 检查服务器 SSH 配置
   ```

2. **Docker 权限问题**
   ```bash
   # 确保部署用户在 docker 组中
   sudo usermod -aG docker deploy
   # 重新登录生效
   ```

3. **健康检查失败**
   ```bash
   # 检查服务日志
   docker compose -f docker-compose.prod.yml logs
   # 检查端口占用
   netstat -tlnp | grep :3000
   ```

4. **镜像拉取失败**
   ```bash
   # 检查镜像仓库认证
   docker login registry.cn-hangzhou.aliyuncs.com
   # 手动拉取测试
   docker pull registry.cn-hangzhou.aliyuncs.com/weishi/weizhi-web:latest
   ```

### 日志查看

```bash
# 查看部署日志
ssh <EMAIL> "cd /opt/weishi && docker compose -f docker-compose.prod.yml logs -f"

# 查看特定服务日志
docker compose -f docker-compose.prod.yml logs -f web
docker compose -f docker-compose.prod.yml logs -f server
docker compose -f docker-compose.prod.yml logs -f caddy
```

## 🔒 安全建议

1. **定期更新密钥**：每 3-6 个月更新 SSH 密钥和访问令牌
2. **限制网络访问**：配置防火墙，只开放必要端口
3. **监控部署活动**：定期检查部署日志和访问记录
4. **备份策略**：确保定期备份数据库和重要文件
5. **权限最小化**：部署用户只授予必要的权限

## 📞 支持联系

如遇到部署问题，请：
1. 查看 Gitea Actions 日志
2. 检查服务器日志
3. 参考故障排除指南
4. 联系技术支持团队
