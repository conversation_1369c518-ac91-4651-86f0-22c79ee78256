# 并行构建优化指南

## 🎯 问题描述

虽然配置了矩阵策略，但观察到服务是按顺序构建（admin → server-go → web），而不是真正的并行构建。

## 🔍 可能的原因

### 1. **Gitea Runner 资源限制**
- Runner 实例数量不足
- 单个 Runner 的并发任务限制
- 内存或 CPU 资源不足

### 2. **Gitea Actions 配置限制**
- 组织级别的并发限制
- 仓库级别的并发限制
- 工作流级别的并发限制

### 3. **Docker 资源竞争**
- Docker 守护进程资源限制
- 镜像构建时的磁盘 I/O 竞争
- 网络带宽限制

## ✅ 优化方案

### 1. **工作流配置优化**

#### 当前优化配置
```yaml
strategy:
  matrix:
    service: [web, admin, server-go]
  fail-fast: false        # 一个服务失败不停止其他服务构建
  max-parallel: 3         # 最多同时运行3个构建任务
```

#### 并行构建监控
```yaml
- name: 并行构建状态
  run: |
    echo "🚀 开始并行构建服务: ${{ matrix.service }}"
    echo "构建开始时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "Runner ID: ${{ github.run_id }}-${{ strategy.job-index }}"
    echo "当前任务索引: ${{ strategy.job-index }}"
    echo "总任务数: ${{ strategy.job-total }}"
```

### 2. **Gitea Runner 配置检查**

#### 检查 Runner 状态
```bash
# 在 Gitea 管理界面检查
Settings → Actions → Runners

# 确认以下信息：
- Runner 数量: 建议至少3个
- Runner 状态: 所有 Runner 都在线
- 并发限制: 每个 Runner 支持多个并发任务
```

#### Runner 配置示例
```yaml
# .gitea/runners/config.yml
runner:
  capacity: 3           # 每个 Runner 最多同时运行3个任务
  timeout: 3600         # 任务超时时间
  
labels:
  - "ubuntu-latest:docker://node:18"
  - "ubuntu-latest:docker://golang:1.21"
```

### 3. **Docker 构建优化**

#### 使用 Docker Buildx 缓存
```yaml
- name: 设置Docker Buildx
  uses: docker/setup-buildx-action@v3
  with:
    driver-opts: |
      network=host
    config-inline: |
      [worker.oci]
        max-parallelism = 4
```

#### 构建缓存策略
```yaml
- name: 构建并推送镜像
  uses: docker/build-push-action@v5
  with:
    cache-from: type=gha,scope=${{ matrix.service }}
    cache-to: type=gha,mode=max,scope=${{ matrix.service }}
```

### 4. **资源使用优化**

#### 分离构建步骤
```yaml
# 前端服务使用 Node.js 环境
- name: 设置Node.js (前端服务)
  if: matrix.service == 'web' || matrix.service == 'admin'
  uses: actions/setup-node@v4
  with:
    node-version: 18
    cache: 'pnpm'

# 后端服务使用 Go 环境  
- name: 设置Go (后端服务)
  if: matrix.service == 'server-go'
  uses: actions/setup-go@v4
  with:
    go-version: 1.21
    cache: true
```

## 🔧 验证并行构建

### 1. **时间戳监控**
在构建日志中查看时间戳：
```bash
🚀 开始并行构建服务: web
构建开始时间: 2024-12-01 14:30:00

🚀 开始并行构建服务: admin  
构建开始时间: 2024-12-01 14:30:01

🚀 开始并行构建服务: server-go
构建开始时间: 2024-12-01 14:30:02
```

### 2. **任务索引检查**
```bash
当前任务索引: 0  # web
当前任务索引: 1  # admin
当前任务索引: 2  # server-go
总任务数: 3
```

### 3. **构建时间对比**
- **串行构建**: 总时间 = 各服务时间之和
- **并行构建**: 总时间 ≈ 最慢服务的时间

## 🎯 Gitea Runner 配置建议

### 1. **增加 Runner 数量**
```bash
# 建议配置
- 至少3个 Runner 实例
- 每个 Runner 支持并发任务
- 分布在不同的机器上（如果可能）
```

### 2. **Runner 资源配置**
```yaml
# 推荐配置
CPU: 4核心以上
内存: 8GB以上
磁盘: SSD，至少50GB可用空间
网络: 稳定的网络连接
```

### 3. **Runner 标签配置**
```yaml
labels:
  - "ubuntu-latest"
  - "docker"
  - "node"
  - "golang"
```

## 🚀 进一步优化

### 1. **条件构建**
只构建有变更的服务：
```yaml
- name: 检测服务变更
  id: changes
  run: |
    # 检测文件变更
    if git diff --name-only HEAD~1 | grep -q "^web/"; then
      echo "web_changed=true" >> $GITHUB_OUTPUT
    fi
    
    if git diff --name-only HEAD~1 | grep -q "^admin/"; then
      echo "admin_changed=true" >> $GITHUB_OUTPUT
    fi
    
    if git diff --name-only HEAD~1 | grep -q "^server-go/"; then
      echo "server_changed=true" >> $GITHUB_OUTPUT
    fi

# 条件构建
- name: 构建服务
  if: steps.changes.outputs[format('{0}_changed', matrix.service)] == 'true'
```

### 2. **构建优先级**
```yaml
strategy:
  matrix:
    include:
      - service: server-go
        priority: high
      - service: web  
        priority: medium
      - service: admin
        priority: low
```

### 3. **分阶段构建**
```yaml
# 第一阶段：后端服务
build-backend:
  strategy:
    matrix:
      service: [server-go]

# 第二阶段：前端服务（依赖后端）
build-frontend:
  needs: build-backend
  strategy:
    matrix:
      service: [web, admin]
```

## 📊 监控和调试

### 1. **构建时间分析**
```bash
# 在构建总结中显示
echo "## 📊 构建时间分析" >> $GITHUB_STEP_SUMMARY
echo "- Web 服务: 5分钟" >> $GITHUB_STEP_SUMMARY  
echo "- Admin 服务: 4分钟" >> $GITHUB_STEP_SUMMARY
echo "- Server-Go 服务: 6分钟" >> $GITHUB_STEP_SUMMARY
echo "- 总时间: 6分钟（并行）" >> $GITHUB_STEP_SUMMARY
```

### 2. **资源使用监控**
```bash
# 在构建过程中监控资源
- name: 监控资源使用
  run: |
    echo "CPU 使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)"
    echo "内存使用: $(free -h | awk '/^Mem:/ {print $3 "/" $2}')"
    echo "磁盘使用: $(df -h / | awk 'NR==2 {print $3 "/" $2}')"
```

---

通过这些优化措施，应该能够实现真正的并行构建，将构建时间从15-20分钟缩短到5-7分钟。
