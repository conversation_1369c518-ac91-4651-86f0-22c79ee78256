# 菜单项清理方案

## 现状分析

### 当前的平台配置相关路由：

1. **平台列表** (`/platform/list`) ✅ 保留
   - 主入口页面，需要保留

2. **扩展数据配置** (`/platform/extensions/:id`) ❌ 删除
   - 功能已整合到一体化配置页面
   - 不再需要独立页面

3. **表格列配置** (`/platform/table-columns/:tableId`) ❌ 删除
   - 功能已整合到一体化配置页面的"列配置"标签
   - 不再需要独立页面

4. **表格数据管理** (`/platform/table-data/:tableId`) ❌ 删除
   - 功能已整合到一体化配置页面的"数据管理"标签
   - 不再需要独立页面

5. **平台配置** (`/platform/config/:id`) ✅ 保留
   - 新的一体化配置页面，是主要功能入口

## 清理计划

### 第一步：更新路由配置
- 删除不需要的路由定义
- 保留必要的路由
- 更新路由跳转逻辑

### 第二步：更新页面跳转
- 将原来跳转到分散页面的链接改为跳转到一体化配置页面
- 更新面包屑导航
- 更新菜单项

### 第三步：清理文件
- 删除不再使用的页面文件
- 清理相关的组件和样式文件

### 第四步：更新文档
- 更新用户手册
- 更新开发文档

## 向后兼容性考虑

### 旧链接重定向
为了避免用户收藏的旧链接失效，可以考虑添加重定向：

```javascript
// 重定向旧的扩展数据配置页面到一体化配置页面
{
  path: 'extensions/:id',
  redirect: to => `/platform/config/${to.params.id}`
}

// 重定向旧的表格列配置页面到一体化配置页面
{
  path: 'table-columns/:tableId',
  redirect: to => {
    // 需要根据tableId找到对应的平台ID
    // 这里可能需要API调用或者其他方式获取平台ID
    return `/platform/config/[platformId]`
  }
}

// 重定向旧的表格数据管理页面到一体化配置页面
{
  path: 'table-data/:tableId',
  redirect: to => {
    // 需要根据tableId找到对应的平台ID
    return `/platform/config/[platformId]`
  }
}
```

### 渐进式迁移
1. **第一阶段**：保留旧页面，但添加提示引导用户使用新页面
2. **第二阶段**：将旧页面设为只读，引导用户到新页面进行编辑
3. **第三阶段**：完全删除旧页面，添加重定向

## 实施建议

### 推荐方案：直接清理
由于一体化配置页面功能完整，用户体验更好，建议直接清理旧的菜单项：

1. **立即删除**不需要的路由和页面文件
2. **添加重定向**处理旧链接
3. **更新所有跳转链接**到新的一体化页面
4. **通知用户**新功能的使用方法

### 优势：
- 简化系统架构
- 减少维护成本
- 提升用户体验
- 避免功能重复

### 风险控制：
- 保留数据库结构不变
- API接口保持兼容
- 添加详细的操作日志
- 提供回滚方案

## 文件清理列表

### 需要删除的文件：
1. `admin/src/views/content/part-platforms/extensions.vue`
2. `admin/src/views/content/part-platforms/table-columns.vue`
3. `admin/src/views/content/part-platforms/table-data.vue`

### 需要保留的文件：
1. `admin/src/views/content/part-platforms/index.vue` (平台列表)
2. `admin/src/views/content/part-platforms/integrated-config.vue` (一体化配置)
3. `admin/src/components/platform/TableColumnsConfig.vue` (列配置组件)
4. `admin/src/components/platform/TableDataManagement.vue` (数据管理组件)
5. `admin/src/components/platform/ColumnTemplateSelector.vue` (模板选择器)

### 需要更新的文件：
1. `admin/src/router/index.ts` (路由配置)
2. `admin/src/views/content/part-platforms/index.vue` (更新跳转链接)
