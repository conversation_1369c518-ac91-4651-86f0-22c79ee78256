# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 📋 Project Overview

This is an enterprise-level full-stack monorepo solution for 江苏蔚之领域智能科技有限公司, featuring a modern tech stack with corporate website, admin dashboard, and backend services.

## 🏗️ Architecture

### Project Structure
- **web/** - Nuxt3 corporate website (port 4000)
- **admin/** - Vue3 admin dashboard (port 3001) 
- **server-go/** - Go backend API (port 3000)
- **server/** - ⚠️ DEPRECATED (Node.js backend - replaced by Go)
- **docs/** - Project documentation
- **scripts/** - Build and deployment scripts

### Architecture Pattern
**Layered Architecture (Go Backend):**
- **Handler Layer** (`internal/handler/`) - HTTP request handling
- **Service Layer** (`internal/service/`) - Business logic
- **Repository Layer** (`internal/repository/`) - Data access
- **Model Layer** (`internal/model/`) - Data structures

**Frontend State Management:**
- **<PERSON>nia** stores for both admin and web frontends
- **Composables** for shared logic in web/ (useWebData.ts)
- **API modules** in admin/src/api/ for backend communication

## 🛠️ Technology Stack

- **Frontend**: Vue 3 + TypeScript + Nuxt 3 + Element Plus + UnoCSS
- **Backend**: Go 1.23 + Gin + GORM + JWT + Logrus + Swagger
- **Database**: MySQL 8.0 (Docker)
- **Package Manager**: pnpm (monorepo workspace)
- **Build**: Vite (admin), Nuxt (web), Air (Go hot reload)
- **Cloud**: Tencent COS file storage

## 📊 Database Schema

**Core Tables (15 total):**
- **Auth**: admin_users, roles, permissions, admin_user_roles, role_permissions
- **Content**: news, our_services, project_cases, recruitments
- **Config**: swipers, friend_links, partners, part_platform
- **System**: admin_logs, file_uploads

## 🚀 Common Development Commands

### Environment Setup
```bash
# Install dependencies
pnpm install

# Database setup (Go backend)
cd server-go
make create-database    # Create MySQL database
make init-admin        # Initialize admin user (admin/admin123)
make seed-data         # Load business data
```

### Development Servers
```bash
# Start all services
pnpm dev:all           # Uses ./scripts/start-all.sh

# Start individual services
pnpm dev:web           # Web frontend (port 4000)
pnpm dev:admin         # Admin dashboard (port 3001)
cd server-go && make dev  # Go backend with hot reload (port 3000)
```

### Build Commands
```bash
# Build all projects
pnpm build

# Build individual projects
pnpm build:web
pnpm build:admin
cd server-go && make build
```

### Go Backend Commands (Makefile)
```bash
# Development
make dev               # Hot reload with Air
make build             # Build binary
make run               # Build and run

# Database Management
make create-database   # Create database
make init-admin        # Initialize admin data
make seed-data         # Import business data
make reset-db          # Reset all data

# API Testing
make test-health       # Test health endpoint
make test-api          # Test basic APIs

# Service Management
make start             # Background start
make stop              # Stop service
make status            # Check status
make logs              # View logs
```

### Docker Commands
```bash
# Full deployment
docker-compose up -d --build

# Check status
docker-compose ps

# View logs
docker-compose logs -f
```

## 📊 Database Configuration

**Default Credentials:**
- **Database**: weizhi
- **User**: root
- **Password**: Ydb3344%
- **Port**: 3306 (Docker)

**Admin Login:**
- **Username**: admin
- **Password**: admin123 (or 123456 for simple setup)

## 📡 API Endpoints

- **Health Check**: http://localhost:3000/api/health
- **API Documentation**: http://localhost:3000/swagger/index.html
- **Admin API**: http://localhost:3000/api/admin/*
- **Public API**: http://localhost:3000/api/*

## 📁 Key File Locations

**Configuration Files:**
- `server-go/config.yaml` - Go backend configuration
- `server-go/Makefile` - Build and database commands
- `docker-compose.yml` - Container orchestration
- `pnpm-workspace.yaml` - Monorepo workspace

**Database Scripts:**
- `server-go/scripts/create_database.sql` - Database creation
- `server-go/scripts/init_admin.sql` - Admin user setup
- `server-go/scripts/seed_data.sql` - Business data

**Frontend APIs:**
- `admin/src/api/` - Admin API modules
- `web/utils/api.ts` - Web API utilities
- `web/composables/useWebData.ts` - Web data composable

## ⚠️ Important Notes

**From .cursor/rules/common.mdc:**
- **Package Manager**: Use pnpm only
- **Backend**: `server-go/` is active, `server/` is DEPRECATED
- **Database**: MySQL runs in Docker
- **Service Management**: Ask user before starting/restarting services
- **Backend Fields**: Don't modify database fields without user approval
- **Current Services**: admin (port 3001), server-go (port 3000), web (port 4000)

**Development Workflow:**
1. User should handle service start/restart operations
2. Use Serena tools when available
3. Sequential_Thinking for analysis
4. Playwright for browser automation

## 📚 Documentation

Comprehensive documentation available in `/docs/`:
- Project overview and architecture
- API documentation and testing
- Database design and migrations
- Deployment guides
- Development workflows