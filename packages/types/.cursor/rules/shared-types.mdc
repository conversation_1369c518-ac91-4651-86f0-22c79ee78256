---
description: 
globs: 
alwaysApply: false
---
# 共享类型包开发规范

## 包结构
```
packages/types/
├── src/
│   ├── api/          # API 相关类型
│   ├── entities/     # 实体类型定义
│   ├── common/       # 通用类型
│   ├── enums/        # 枚举类型（使用 const 对象）
│   └── index.ts      # 统一导出
├── package.json
└── tsconfig.json
```

## 类型定义规范

### 实体类型
```typescript
// entities/user.types.ts
export interface UserBase {
  id: string
  email: string
  name: string
  createdAt: string
  updatedAt: string
}

export interface UserProfile extends UserBase {
  avatar?: string
  bio?: string
  department?: string
  role: UserRole
}

export interface UserCredentials {
  email: string
  password: string
}

export interface UserPayload {
  id: string
  email: string
  role: UserRole
}
```

### API 类型定义
```typescript
// api/common.types.ts
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
  timestamp: string
}

export interface ApiError {
  success: false
  error: {
    code: string
    message: string
    details?: Record<string, any>
  }
  timestamp: string
}

export interface PaginationMeta {
  page: number
  limit: number
  total: number
  totalPages: number
}

export interface PaginatedResponse<T> {
  items: T[]
  pagination: PaginationMeta
}

export interface PaginationQuery {
  page?: number
  limit?: number
}
```

```typescript
// api/news.types.ts
export interface NewsBase {
  title: string
  content: string
  category: string
  isPublished: boolean
}

export interface NewsEntity extends NewsBase {
  id: string
  authorId: string
  createdAt: string
  updatedAt: string
}

export interface NewsResponse extends NewsEntity {
  author?: {
    id: string
    name: string
  }
}

export interface CreateNewsRequest extends NewsBase {}

export interface UpdateNewsRequest extends Partial<NewsBase> {}

export interface NewsQuery extends PaginationQuery {
  search?: string
  category?: string
  authorId?: string
}

export type NewsListResponse = PaginatedResponse<NewsResponse>
```

### 常量和枚举替代
```typescript
// enums/user-roles.ts
export const UserRole = {
  ADMIN: 'admin',
  EDITOR: 'editor',
  USER: 'user',
  GUEST: 'guest'
} as const

export type UserRole = typeof UserRole[keyof typeof UserRole]

// 提供类型守卫函数
export const isUserRole = (value: string): value is UserRole => {
  return Object.values(UserRole).includes(value as UserRole)
}

// 提供角色权限映射
export const UserRolePermissions = {
  [UserRole.ADMIN]: ['read', 'write', 'delete', 'manage'],
  [UserRole.EDITOR]: ['read', 'write'],
  [UserRole.USER]: ['read'],
  [UserRole.GUEST]: []
} as const
```

```typescript
// enums/news-categories.ts
export const NewsCategory = {
  COMPANY: 'company',
  PRODUCT: 'product',
  INDUSTRY: 'industry',
  ANNOUNCEMENT: 'announcement'
} as const

export type NewsCategory = typeof NewsCategory[keyof typeof NewsCategory]

export const NewsCategoryLabels = {
  [NewsCategory.COMPANY]: '公司新闻',
  [NewsCategory.PRODUCT]: '产品动态',
  [NewsCategory.INDUSTRY]: '行业资讯',
  [NewsCategory.ANNOUNCEMENT]: '公告通知'
} as const
```

### 通用工具类型
```typescript
// common/utility.types.ts
// 时间戳类型
export type Timestamp = string // ISO 8601 格式

// ID 类型
export type EntityId = string // UUID 格式

// 可选字段工具类型
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

// 时间戳字段
export interface Timestamped {
  createdAt: Timestamp
  updatedAt: Timestamp
}

// 软删除字段
export interface SoftDeletable {
  deletedAt?: Timestamp
}

// 创建和更新类型生成器
export type CreateInput<T> = Omit<T, 'id' | 'createdAt' | 'updatedAt'>
export type UpdateInput<T> = Partial<Omit<T, 'id' | 'createdAt' | 'updatedAt'>>

// 查询过滤器类型
export interface BaseFilter {
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 文件上传类型
export interface FileUpload {
  filename: string
  mimeType: string
  size: number
  url: string
}
```

### 表单和验证类型
```typescript
// common/form.types.ts
export interface FormField<T = any> {
  value: T
  error?: string
  touched: boolean
  dirty: boolean
}

export interface FormState<T extends Record<string, any>> {
  fields: {
    [K in keyof T]: FormField<T[K]>
  }
  isValid: boolean
  isSubmitting: boolean
  isDirty: boolean
}

// 验证规则类型
export interface ValidationRule<T = any> {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: T) => string | undefined
}

export type ValidationRules<T extends Record<string, any>> = {
  [K in keyof T]?: ValidationRule<T[K]>
}
```

## 导出规范

### 模块化导出
```typescript
// src/index.ts
// API 类型
export * from './api/common.types'
export * from './api/auth.types'
export * from './api/news.types'
export * from './api/user.types'

// 实体类型
export * from './entities/user.types'
export * from './entities/news.types'

// 枚举和常量
export * from './enums/user-roles'
export * from './enums/news-categories'
export * from './enums/api-status'

// 通用类型
export * from './common/utility.types'
export * from './common/form.types'
```

### 分类导出
```typescript
// src/api/index.ts
export * from './common.types'
export * from './auth.types'
export * from './news.types'
export * from './user.types'

// src/entities/index.ts
export * from './user.types'
export * from './news.types'

// src/enums/index.ts
export * from './user-roles'
export * from './news-categories'
```

## 类型安全最佳实践

### 字面量类型使用
```typescript
// ✅ 推荐：使用字面量类型
export interface ApiEndpoint {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  path: string
  authenticated: boolean
}

// ✅ 推荐：使用 const 断言
export const ApiMethods = ['GET', 'POST', 'PUT', 'DELETE'] as const
export type ApiMethod = typeof ApiMethods[number]
```

### 条件类型和映射类型
```typescript
// 根据条件生成不同的类型
export type ApiPayload<T extends ApiMethod> = T extends 'GET'
  ? never
  : T extends 'POST' | 'PUT'
  ? Record<string, any>
  : never

// 映射类型生成 API 客户端接口
export type ApiClient = {
  [K in keyof ApiEndpoints]: (
    payload: ApiPayload<ApiEndpoints[K]['method']>
  ) => Promise<ApiResponse>
}
```

### 类型守卫和工具函数
```typescript
// common/guards.ts
export const isApiError = (response: any): response is ApiError => {
  return typeof response === 'object' && response.success === false
}

export const isValidEntityId = (id: string): id is EntityId => {
  // UUID v4 正则验证
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(id)
}

export const hasTimestamps = (obj: any): obj is Timestamped => {
  return typeof obj === 'object' && 
         typeof obj.createdAt === 'string' && 
         typeof obj.updatedAt === 'string'
}
```

## 文档和注释规范

### TSDoc 注释
```typescript
/**
 * 用户信息接口
 * @interface UserInfo
 */
export interface UserInfo {
  /** 用户唯一标识符，UUID v4 格式 */
  id: EntityId
  
  /** 用户邮箱地址，必须唯一 */
  email: string
  
  /** 用户显示名称 */
  name: string
  
  /** 
   * 用户角色
   * @see {@link UserRole} 可用角色列表
   */
  role: UserRole
  
  /** 
   * 创建时间戳
   * @format ISO 8601
   * @example "2024-01-01T00:00:00.000Z"
   */
  createdAt: Timestamp
}

/**
 * 创建新用户的请求数据
 * @example
 * ```typescript
 * const newUser: CreateUserRequest = {
 *   email: "<EMAIL>",
 *   name: "张三",
 *   password: "securePassword123"
 * }
 * ```
 */
export interface CreateUserRequest {
  email: string
  name: string
  password: string
}
```

## 版本管理和兼容性

### 版本化类型
```typescript
// 当需要 API 版本升级时
export namespace v1 {
  export interface UserResponse {
    id: string
    name: string
    email: string
  }
}

export namespace v2 {
  export interface UserResponse extends v1.UserResponse {
    profile: UserProfile
    permissions: string[]
  }
}

// 当前版本别名
export type UserResponse = v2.UserResponse
```

### 向后兼容
```typescript
// 使用可选字段保持向后兼容
export interface NewsResponse {
  id: string
  title: string
  content: string
  
  // 新增字段使用可选
  tags?: string[]
  viewCount?: number
  
  // 废弃字段保持但标记
  /** @deprecated 使用 authorId 替代 */
  author?: string
  authorId: string
}
```
