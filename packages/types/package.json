{"name": "@weizhi/types", "version": "1.0.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc -w", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .ts", "prepare": "pnpm run build"}, "devDependencies": {"typescript": "^5.0.0", "rimraf": "^5.0.0", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0"}}