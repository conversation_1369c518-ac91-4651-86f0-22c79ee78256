export interface DatabaseConfig {
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  ssl?: boolean;
}

export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db?: number;
}

export interface JwtConfig {
  secret: string;
  expiresIn: string | number;
  refreshSecret: string;
  refreshExpiresIn: string | number;
}

export interface StorageConfig {
  type: 'local' | 'oss' | 's3';
  local?: {
    path: string;
    baseUrl: string;
  };
  oss?: {
    accessKeyId: string;
    accessKeySecret: string;
    bucket: string;
    region: string;
    endpoint: string;
  };
  s3?: {
    accessKeyId: string;
    secretAccessKey: string;
    bucket: string;
    region: string;
  };
}

export interface AppConfig {
  port: number;
  env: 'development' | 'production' | 'test';
  database: DatabaseConfig;
  redis?: RedisConfig;
  jwt: JwtConfig;
  storage: StorageConfig;
  cors?: {
    origin: string | string[];
    credentials: boolean;
  };
} 