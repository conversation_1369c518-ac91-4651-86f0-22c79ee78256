export interface User {
  id: number;
  username: string;
  email: string;
  password?: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserProfile extends Omit<User, 'password'> {
  nickname?: string;
  bio?: string;
  phone?: string;
}

export interface UserLoginDto {
  username: string;
  password: string;
}

export interface UserRegisterDto extends UserLoginDto {
  email: string;
  confirmPassword: string;
} 