export interface FileMetadata {
  filename: string;
  mimetype: string;
  size: number;
  path: string;
  url: string;
}

export interface UploadResponse {
  file: FileMetadata;
  message: string;
}

export interface MultipleUploadResponse {
  files: FileMetadata[];
  message: string;
}

export interface FileUploadOptions {
  maxSize?: number; // 最大文件大小（字节）
  allowedTypes?: string[]; // 允许的文件类型
  storageType?: 'local' | 'oss' | 's3'; // 存储类型
  path?: string; // 存储路径
} 