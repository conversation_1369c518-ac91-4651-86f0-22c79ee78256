export interface News {
  id: number;
  title: string;
  description: string;
  content: string;
  image: string;
  url: string;
  isHomePage: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface NewsQueryParams {
  page?: number;
  pageSize?: number;
  isHomePage?: boolean;
}

export interface NewsCreateDto {
  title: string;
  description?: string;
  content: string;
  image?: string;
  url?: string;
  isHomePage?: boolean;
  order?: number;
}

export interface NewsUpdateDto extends Partial<NewsCreateDto> {
  id: number;
} 