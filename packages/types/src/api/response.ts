import { ErrorCode } from '../enums';
import { ApiResponse } from '../models';

export interface SuccessResponse<T = void> extends ApiResponse<T> {
  code: ErrorCode.SUCCESS;
}

export interface ErrorResponse extends ApiResponse {
  code: Exclude<ErrorCode, ErrorCode.SUCCESS>;
  errors?: Record<string, string[]>;
}

export interface TokenResponse extends SuccessResponse<{
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}> {} 