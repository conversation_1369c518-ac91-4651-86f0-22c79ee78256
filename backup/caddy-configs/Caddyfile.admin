# Vue管理后台Caddy配置文件
# 用于提供静态文件服务和SPA路由支持

:8080 {
    # 设置根目录
    root * /srv/admin
    
    # 启用文件服务器
    file_server
    
    # SPA路由支持 - 所有路由都返回index.html
    try_files {path} /index.html
    
    # 安全头配置
    header {
        # 防止MIME类型嗅探
        X-Content-Type-Options nosniff
        # 防止页面被嵌入iframe
        X-Frame-Options DENY
        # XSS保护
        X-XSS-Protection "1; mode=block"
        # 引用策略
        Referrer-Policy strict-origin-when-cross-origin
        # 隐藏服务器信息
        -Server
        # 内容安全策略
        Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; media-src 'self'; object-src 'none'; frame-src 'none';"
    }
    
    # 静态资源缓存配置
    @static {
        path *.css *.js *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2 *.ttf *.eot *.map
    }
    header @static {
        Cache-Control "public, max-age=31536000, immutable"
        Vary "Accept-Encoding"
    }
    
    # HTML文件缓存配置
    @html {
        path *.html
    }
    header @html {
        Cache-Control "no-cache, no-store, must-revalidate"
        Pragma "no-cache"
        Expires "0"
    }
    
    # API代理（如果需要）
    handle /api/* {
        reverse_proxy {$API_BACKEND:http://localhost:3001}
    }
    
    # 健康检查端点
    handle /health {
        respond "Admin Panel OK" 200
    }
    
    # 启用压缩
    encode {
        gzip 6
        zstd
        minimum_length 1024
        match {
            header Content-Type text/*
            header Content-Type application/json*
            header Content-Type application/javascript*
            header Content-Type application/xml*
            header Content-Type image/svg+xml*
        }
    }
    
    # 访问日志
    log {
        output file /logs/admin_access.log {
            roll_size 10mb
            roll_keep 5
            roll_keep_for 168h
        }
        format json {
            time_format "2006-01-02T15:04:05.000Z07:00"
            message_key "message"
            level_key "level"
            time_key "timestamp"
        }
    }
    
    # 错误页面
    handle_errors {
        @404 {
            expression {http.error.status_code} == 404
        }
        rewrite @404 /index.html
        file_server
    }
}
