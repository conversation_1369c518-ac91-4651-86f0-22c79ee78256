# 本地 macOS 模拟线上部署（使用 Docker，全栈容器化）
# - 仅开放 Caddy 的 80 端口，通过 hosts 使用域名访问
# - 其它服务走容器内网络，不对外暴露
# - 采用与生产一致的四个服务：mysql、server、web、caddy（集成 admin 静态）

services:
  mysql:
    image: docker.cnb.cool/yuandongbin/docker-sync/mysql:8.0
    container_name: weizhi-mysql-local
    restart: unless-stopped
    env_file:
      - ./production.env
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-Ydb3344%}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-weizhi}
      MYSQL_USER: ${MYSQL_USER:-weizhi}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-Ydb3344%}
      TZ: Asia/Shanghai
    ports:
      # 避免与本机 3306 冲突，本地映射到 3307
      - "3307:3306"
    volumes:
      - mysql_local_data:/var/lib/mysql
      # 初始化 SQL（可选，如不需要可注释）
      - ./mysql/init:/docker-entrypoint-initdb.d:ro
    networks:
      - weizhi-local-network
    healthcheck:
      test: ["CMD", "sh", "-c", "mysqladmin ping -h localhost -u root -p'${MYSQL_ROOT_PASSWORD:-Ydb3344%}' --port=3306 && mysql -h localhost -u root -p'${MYSQL_ROOT_PASSWORD:-Ydb3344%}' --port=3306 -e 'SELECT 1'"]
      timeout: 30s
      retries: 25
      interval: 20s
      start_period: 60s

  # Go 后端 API 服务
  server:
    build:
      context: ..
      dockerfile: deployment/Dockerfile.server-go
    container_name: weizhi-server-local
    restart: unless-stopped
    env_file:
      - ./production.env
    environment:
      APP_MODE: production
      APP_PORT: "3001"
      TZ: Asia/Shanghai
      DB_HOST: mysql
      DB_PORT: "3306"
      DB_USERNAME: ${MYSQL_USER:-weizhi}
      DB_PASSWORD: ${MYSQL_PASSWORD:-Ydb3344%}
      DB_DATABASE: ${MYSQL_DATABASE:-weizhi}
      JWT_SECRET: ${JWT_SECRET:-dev_secret}
      JWT_EXPIRE_TIME: ${JWT_EXPIRE_TIME:-28800}
      # 迁移可选开关（默认关闭）
      AUTO_MIGRATE: ${AUTO_MIGRATE:-false}
      # 文件上传配置（按需）
      MAX_FILE_SIZE: ${MAX_FILE_SIZE:-52428800}
      ALLOWED_FILE_TYPES: ${ALLOWED_FILE_TYPES:-image/jpeg,image/png,image/gif,image/webp,application/pdf,text/plain}
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - weizhi-local-network
    volumes:
      - ./logs/server:/app/logs
      - ./uploads:/app/uploads
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 15s
      timeout: 10s
      retries: 10
      start_period: 60s

  # Nuxt3 前端网站
  web:
    build:
      context: ..
      dockerfile: deployment/Dockerfile.web
    env_file:
      - ./production.env
    container_name: weizhi-web-local
    restart: unless-stopped
    environment:
      NODE_ENV: production
      NUXT_HOST: 0.0.0.0
      NUXT_PORT: 3000
      TZ: Asia/Shanghai
      # 通过 Caddy 反代统一为同域 /api
      API_BASE_URL: http://server:3001
      NUXT_PUBLIC_API_BASE: http://weizhi.local
      NODE_OPTIONS: "--max-old-space-size=1024"
    depends_on:
      server:
        condition: service_healthy
    networks:
      - weizhi-local-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Caddy 反向代理 + 管理后台静态
  caddy:
    build:
      context: ..
      dockerfile: deployment/Dockerfile.caddy-admin
    env_file:
      - ./production.env
    container_name: weizhi-caddy-local
    restart: unless-stopped
    ports:
      - "80:80"
      # 如需本地 https，可自行映射 443，并调整 Caddy 配置为内部证书
      # - "443:443"
    environment:
      TZ: Asia/Shanghai
    volumes:
      # 使用本地 Caddyfile.local（HTTP，无证书）
      - ./caddy/Caddyfile.local:/etc/caddy/Caddyfile:ro
      - caddy_data_local:/data
      - caddy_config_local:/config
      - ./logs/caddy:/var/log/caddy
    depends_on:
      - web
      - server
    networks:
      - weizhi-local-network
    healthcheck:
      test: ["CMD", "sh", "-lc", "curl -fsS -H 'Host: weizhi.local' http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  mysql_local_data:
    driver: local
  caddy_data_local:
    driver: local
  caddy_config_local:
    driver: local

networks:
  weizhi-local-network:
    driver: bridge

