# MySQL 8.0 生产环境配置
# 针对阿里云服务器优化

[mysqld]
# ================================
# 基本配置
# ================================
port = 3306
bind-address = 0.0.0.0
socket = /var/run/mysqld/mysqld.sock
pid-file = /var/run/mysqld/mysqld.pid
datadir = /var/lib/mysql
tmpdir = /tmp

# ================================
# 字符集和排序规则
# ================================
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# ================================
# 连接配置
# ================================
max_connections = 200
max_connect_errors = 10000
max_allowed_packet = 64M
interactive_timeout = 28800
wait_timeout = 28800
connect_timeout = 10

# ================================
# 缓存配置
# ================================
# InnoDB缓冲池大小（建议设置为可用内存的70-80%）
innodb_buffer_pool_size = 512M
innodb_buffer_pool_instances = 4

# 查询缓存（MySQL 8.0已移除，但保留配置以防回退）
# query_cache_size = 64M
# query_cache_type = 1
# query_cache_limit = 2M

# 表缓存
table_open_cache = 2000
table_definition_cache = 1400

# 线程缓存
thread_cache_size = 50

# ================================
# InnoDB配置
# ================================
# 存储引擎
default_storage_engine = InnoDB

# 日志文件配置
innodb_log_file_size = 256M
innodb_log_files_in_group = 2
innodb_log_buffer_size = 16M

# 刷新配置
innodb_flush_log_at_trx_commit = 1
innodb_flush_method = O_DIRECT

# 文件格式
innodb_file_format = Barracuda
innodb_file_per_table = 1

# 锁等待超时
innodb_lock_wait_timeout = 50

# IO配置
innodb_io_capacity = 200
innodb_io_capacity_max = 2000
innodb_read_io_threads = 4
innodb_write_io_threads = 4

# ================================
# 日志配置
# ================================
# 错误日志
log-error = /var/log/mysql/error.log

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
log_queries_not_using_indexes = 1
log_slow_admin_statements = 1

# 二进制日志（用于复制和备份）
log-bin = /var/log/mysql/mysql-bin
binlog_format = ROW
binlog_expire_logs_seconds = 604800
max_binlog_size = 100M

# 通用查询日志（生产环境通常关闭）
general_log = 0
general_log_file = /var/log/mysql/general.log

# ================================
# 安全配置
# ================================
# SQL模式
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

# 禁用符号链接
symbolic-links = 0

# 跳过域名解析
skip-name-resolve = 1

# 禁用LOAD DATA LOCAL INFILE
local-infile = 0

# ================================
# 性能优化
# ================================
# 排序缓冲区
sort_buffer_size = 2M
read_buffer_size = 128K
read_rnd_buffer_size = 256K
join_buffer_size = 128K

# 临时表
tmp_table_size = 64M
max_heap_table_size = 64M

# MyISAM配置（如果使用）
key_buffer_size = 32M
myisam_sort_buffer_size = 8M

# ================================
# 时区配置
# ================================
default-time-zone = '+08:00'

# ================================
# 复制配置（如果需要主从复制）
# ================================
# server-id = 1
# gtid_mode = ON
# enforce_gtid_consistency = ON
# log_slave_updates = ON

# ================================
# 监控配置
# ================================
# 性能模式
performance_schema = ON
performance_schema_max_table_instances = 12500
performance_schema_max_table_handles = 4000

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
port = 3306
socket = /var/run/mysqld/mysqld.sock

[mysqldump]
quick
quote-names
max_allowed_packet = 64M
default-character-set = utf8mb4

[mysqladmin]
default-character-set = utf8mb4
