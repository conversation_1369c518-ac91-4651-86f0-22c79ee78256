services:
  # MySQL数据库
  mysql:
    image: docker.cnb.cool/yuandongbin/docker-sync/mysql:8.0
    container_name: weizhi-mysql-prod
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-weizhi}
      MYSQL_USER: ${MYSQL_USER:-weizhi}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
      TZ: Asia/Shanghai
    ports:
      - "${MYSQL_PORT:-3306}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d:ro
      - ./mysql/init:/docker-entrypoint-initdb.d:ro
      - ./logs/mysql:/var/log/mysql
    networks:
      - weizhi-network
    healthcheck:
      test: ["<PERSON><PERSON>", "sh", "-c", "mysqladmin ping -h localhost -u root -p'${MYSQL_ROOT_PASSWORD}' --port=3306 && mysql -h localhost -u root -p'${MYSQL_ROOT_PASSWORD}' --port=3306 -e 'SELECT 1'"]
      timeout: 30s
      retries: 25
      interval: 20s
      start_period: 240s

  # 后端API服务 (Go)
  server:
    image: ${REGISTRY_URL}/${NAMESPACE}/weizhi_server:${VERSION:-latest}
    container_name: weizhi-server-prod
    restart: unless-stopped
    dns:
      - *******
      - *******
    environment:
      # Go 应用配置
      APP_MODE: production
      APP_PORT: "3001"
      TZ: Asia/Shanghai

      # 数据库配置
      DB_HOST: mysql
      DB_PORT: "3306"
      DB_USERNAME: ${MYSQL_USER:-weizhi}
      DB_PASSWORD: ${MYSQL_PASSWORD}
      DB_DATABASE: ${MYSQL_DATABASE:-weizhi}
      
      # JWT 配置
      JWT_SECRET: ${JWT_SECRET}
      JWT_EXPIRE_TIME: ${JWT_EXPIRE_TIME:-28800}
      
      # COS/OSS 配置
      COS_SECRET_ID: ${COS_SECRET_ID:-}
      COS_SECRET_KEY: ${COS_SECRET_KEY:-}
      COS_REGION: ${COS_REGION:-ap-nanjing}
      COS_BUCKET: ${COS_BUCKET:-}
      
      # 阿里云OSS配置（可选，替代COS）
      OSS_ACCESS_KEY_ID: ${OSS_ACCESS_KEY_ID:-}
      OSS_ACCESS_KEY_SECRET: ${OSS_ACCESS_KEY_SECRET:-}
      OSS_ENDPOINT: ${OSS_ENDPOINT:-}
      OSS_BUCKET: ${OSS_BUCKET:-}
      
      # 管理员配置
      ADMIN_USERNAME: ${ADMIN_USERNAME:-admin}
      ADMIN_PASSWORD: ${ADMIN_PASSWORD}
      ADMIN_INIT_PASSWORD: ${ADMIN_INIT_PASSWORD:-true}

      # 数据导入配置
      IMPORT_FULL_DATA: ${IMPORT_FULL_DATA:-false}

      # 数据库迁移配置
      AUTO_MIGRATE: ${AUTO_MIGRATE:-false}

      # 文件上传配置
      MAX_FILE_SIZE: ${MAX_FILE_SIZE:-52428800}
      ALLOWED_FILE_TYPES: ${ALLOWED_FILE_TYPES:-image/jpeg,image/png,image/gif,image/webp,application/pdf,text/plain}
    ports:
      - "${SERVER_PORT:-3001}:3001"
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - weizhi-network
    volumes:
      - ./logs/server:/app/logs
      - ./uploads:/app/uploads
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 15s
      timeout: 10s
      retries: 10
      start_period: 120s

  # 前端网站
  web:
    image: ${REGISTRY_URL}/${NAMESPACE}/weizhi_web:${VERSION:-latest}
    container_name: weizhi-web-prod
    restart: unless-stopped
    environment:
      NODE_ENV: production
      NUXT_HOST: 0.0.0.0
      NUXT_PORT: 3000
      TZ: Asia/Shanghai
      API_BASE_URL: http://server:3001
      NUXT_PUBLIC_API_BASE: https://${DOMAIN}
      NODE_OPTIONS: "--max-old-space-size=1024"
    ports:
      - "${WEB_PORT:-3000}:3000"
    depends_on:
      server:
        condition: service_healthy
    networks:
      - weizhi-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 90s

  # Caddy反向代理和SSL终端 + 管理后台
  caddy:
    image: ${REGISTRY_URL}/${NAMESPACE}/weizhi_admin_caddy:${VERSION:-latest}
    container_name: weizhi-caddy-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    environment:
      - DOMAIN=${DOMAIN}
      - ADMIN_DOMAIN=${ADMIN_DOMAIN}
      - SSL_EMAIL=${SSL_EMAIL:-<EMAIL>}
      - TZ=Asia/Shanghai
    volumes:
      - ./caddy/Caddyfile.prod:/etc/caddy/Caddyfile:ro
      - caddy_data:/data
      - caddy_config:/config
      - ./logs/caddy:/var/log/caddy
    depends_on:
      - web
      - server
    networks:
      - weizhi-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s



volumes:
  mysql_data:
    driver: local
  mysql_logs:
    driver: local
  server_logs:
    driver: local
  server_uploads:
    driver: local
  caddy_data:
    driver: local
  caddy_config:
    driver: local
  caddy_logs:
    driver: local

networks:
  weizhi-network:
    driver: bridge
