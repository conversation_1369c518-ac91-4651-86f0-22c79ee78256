# 🔄 数据库升级指南

本文档整合了蔚之领域项目数据库升级的完整操作指南，包括容器环境和直接部署两种方式。

## 📋 升级概述

蔚之领域项目使用 MySQL 8.0 数据库，支持在线升级和版本管理。数据库升级包括：

- **结构变更**：表结构优化、字段调整
- **数据迁移**：数据格式转换、数据清理
- **版本管理**：升级版本控制、回滚机制

## 🚀 快速参考

### 升级前检查
```bash
# 1. 备份数据库
./migrate-docker.sh backup

# 2. 检查数据库连接
./test-mysql-connection.sh

# 3. 查看可用迁移
./migrate-docker.sh list
```

### 执行升级
```bash
# 一键完整升级（推荐）
./migrate-docker.sh full-upgrade

# 或分步执行
./migrate-docker.sh up 001    # 优化表结构
./migrate-docker.sh up 002    # 删除services表
```

### 升级后验证
```bash
# 1. 测试数据库连接
./test-mysql-connection.sh

# 2. 检查服务状态
./check-services.sh

# 3. 查看应用日志
docker-compose logs -f server
```

### 应急回滚
```bash
# 回滚版本002
./migrate-docker.sh down 002

# 回滚版本001
./migrate-docker.sh down 001

# 恢复备份
./migrate-docker.sh restore backup_YYYYMMDD_HHMMSS.sql
```

## 📊 迁移版本说明

| 版本 | 描述 | 风险 | 预计时间 |
|------|------|------|----------|
| 001 | 优化表结构（删除冗余字段） | 中等 | 5-10分钟 |
| 002 | 删除services表 | 低 | 2-5分钟 |

## 🏗️ 升级架构

### 迁移系统

项目使用完整的迁移系统进行数据库升级：

```
deployment/
├── migrations/                  # 迁移文件目录
│   ├── 001_optimize_table_structure.sql
│   ├── 001_optimize_table_structure_rollback.sql
│   ├── 002_drop_services_table.sql
│   └── 002_drop_services_table_rollback.sql
├── migrate-docker.sh           # 容器环境专用升级脚本
├── migrate.sh                  # 通用升级脚本
└── check-services.sh           # 服务健康检查
```

### 环境选择

根据您的部署环境选择相应的升级方式：

- **容器环境**：使用 `migrate-docker.sh`（推荐）
- **直接部署**：使用 `migrate.sh`

## 🚀 升级流程

### 容器环境升级（推荐）

```bash
# 1. 一键完整升级（推荐）
./migrate-docker.sh full-upgrade

# 2. 手动分步升级
./migrate-docker.sh backup                    # 备份数据库
./migrate-docker.sh up 001                    # 升级版本001
./migrate-docker.sh up 002                    # 升级版本002
./check-services.sh                           # 验证服务状态
```

### 直接部署升级

```bash
# 1. 升级前准备
./migrate.sh backup                          # 备份数据库
./test-mysql-connection.sh                    # 测试连接

# 2. 执行升级
./migrate.sh up 001                           # 升级版本001
./migrate.sh up 002                           # 升级版本002

# 3. 升级后验证
./test-mysql-connection.sh                    # 验证连接
./check-services.sh                           # 验证服务状态
```

## 📊 具体升级内容

### 版本001：优化表结构

#### 变更内容
- **移除软删除**：删除所有表的 `deleted_at` 字段
- **简化结构**：采用硬删除方式
- **字段映射**：添加显式列映射

#### 影响的表
- `news` - 新闻表
- `our_services` - 服务表
- `project_cases` - 项目案例表
- `recruitments` - 招聘表
- `part_platform` - 平台表

#### 执行命令
```bash
# 执行升级
cd server-go && make migrate-up VERSION=001 ENV=dev

# 回滚（如需要）
cd server-go && make migrate-down VERSION=001 ENV=dev
```

### 版本002：删除services表

#### 变更内容
- **删除services表**：完全移除服务表
- **数据清理**：清理相关数据

#### 执行命令
```bash
# 执行升级
cd server-go && make migrate-up VERSION=002 ENV=dev

# 回滚（如需要）
cd server-go && make migrate-down VERSION=002 ENV=dev
```

## 🛡️ 安全措施

### 1. 自动备份

每次执行迁移前，系统会自动创建备份：

```bash
# 备份文件格式
backup_YYYYMMDD_HHMMSS.sql

# 查看备份文件
ls -la backup_*.sql
```

### 2. 回滚机制

每个迁移都有对应的回滚脚本：

```bash
# 回滚特定版本
cd server-go && make migrate-down VERSION=<version> ENV=prod

# 示例：回滚版本001
./server-go/scripts/migrate.sh down 001
```

### 3. 健康检查

升级过程中会进行多项检查：

- ✅ 数据库连接检查
- ✅ 备份文件创建
- ✅ 迁移文件存在性检查
- ✅ SQL语法验证

## 🔧 配置选项

### 容器环境配置

对于容器环境，使用 `migrate-docker.sh` 脚本：

```bash
# 容器配置（自动从 docker-compose.prod.yml 读取）
COMPOSE_FILE="docker-compose.prod.yml"
ENV_FILE="production.env"
MYSQL_CONTAINER="weishi-mysql-prod"
DB_NAME="weizhi"
DB_USER="root"
DB_PASS="Ydb3344%"
```

### 直接部署配置

对于直接部署，使用 `migrate.sh` 脚本：

```bash
# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="weizhi"
DB_USER="root"
DB_PASS="Ydb3344%"

# Docker配置（如使用Docker）
DOCKER_CONTAINER="weishi-mysql"
```

### 自定义配置

编辑 `server-go/scripts/migrate.sh` 修改配置：

```bash
# 修改数据库连接信息
DB_HOST="your-db-host"
DB_PORT="3306"
DB_USER="your-username"
DB_PASS="your-password"
```

## 📝 生产环境升级

### 1. 维护窗口

建议在低峰期进行升级：

- **时间窗口**：凌晨2:00-4:00
- **预计耗时**：5-15分钟
- **影响范围**：数据库只读

### 2. 升级步骤

```bash
# 1. 通知用户维护
echo "系统维护中，请稍后再试..."

# 2. 停止应用服务
docker-compose stop web admin server-go

# 3. 执行数据库升级
./server-go/scripts/migrate.sh up 001
./server-go/scripts/migrate.sh up 002

# 4. 验证升级结果
./deployment/test-mysql-connection.sh

# 5. 重启应用服务
docker-compose up -d

# 6. 检查服务状态
./deployment/check-services.sh
```

### 3. 应急预案

```bash
# 如果升级失败，立即回滚
./server-go/scripts/migrate.sh down 002
./server-go/scripts/migrate.sh down 001

# 恢复备份
mysql -h localhost -P 3306 -u root -p"Ydb3344%" weizhi < backup_YYYYMMDD_HHMMSS.sql
```

## 🔍 故障排除

### 常见问题

#### 1. 数据库连接失败

```bash
# 错误信息
❌ 无法连接到MySQL数据库

# 解决方案
# 检查数据库状态
docker-compose ps mysql

# 检查连接参数
./deployment/test-mysql-connection.sh
```

#### 2. 迁移文件不存在

```bash
# 错误信息
❌ 迁移文件不存在

# 解决方案
# 检查迁移文件
ls -la server-go/scripts/migrations/

# 重新生成迁移文件
cd server-go && make migrate-list ENV=dev
```

#### 3. 备份失败

```bash
# 错误信息
❌ 数据库备份失败

# 解决方案
# 检查磁盘空间
df -h

# 检查权限
chmod +x server-go/scripts/migrate.sh

# 手动备份
mysqldump -h localhost -P 3306 -u root -p"Ydb3344%" weizhi > manual_backup.sql
```

### 调试模式

```bash
# 启用详细输出
cd server-go && make migrate-up VERSION=001 ENV=dev

# 查看日志
docker-compose logs mysql
```

## 📊 升级检查清单

### 升级前检查
- [ ] 数据库备份完成
- [ ] 迁移文件存在
- [ ] 连接测试通过
- [ ] 磁盘空间充足
- [ ] 维护窗口已安排

### 升级中检查
- [ ] 备份文件创建成功
- [ ] 迁移执行无错误
- [ ] 数据完整性保持
- [ ] 应用状态正常

### 升级后检查
- [ ] 所有服务正常运行
- [ ] 数据访问正常
- [ ] 性能指标正常
- [ ] 日志无异常
- [ ] 回滚方案准备就绪

## 🔄 版本管理

### 迁移历史

```bash
# 查看迁移历史
./server-go/scripts/migrate.sh list

# 检查当前版本
mysql -h localhost -P 3306 -u root -p"Ydb3344%" weizhi -e "SELECT version FROM schema_migrations;"
```

### 版本标记

重要版本标记：
- `v1.0.0` - 初始版本
- `v1.1.0` - 表结构优化
- `v1.2.0` - services表移除

## 🐳 容器环境专题

### 容器环境特点

在容器环境中进行数据库升级有以下特点：

1. **服务隔离**：每个服务运行在独立的容器中
2. **网络管理**：容器间通过Docker网络通信
3. **数据持久化**：数据库数据存储在volume中
4. **配置管理**：通过环境变量和配置文件管理

### 容器环境最佳实践

```bash
# 1. 使用专用容器脚本
./migrate-docker.sh full-upgrade

# 2. 监控容器状态
docker-compose -f docker-compose.prod.yml --env-file production.env ps

# 3. 查看实时日志
docker-compose -f docker-compose.prod.yml --env-file production.env logs -f mysql

# 4. 检查资源使用
docker stats weishi-mysql-prod
```

### 容器环境故障排除

```bash
# 1. 容器网络问题
docker network ls
docker network inspect weishi_weishi-network

# 2. 容器权限问题
docker exec -it weishi-mysql-prod bash

# 3. 数据库连接问题
docker-compose -f docker-compose.prod.yml --env-file production.env exec mysql mysql -u root -p"Ydb3344%" -e "SELECT 1;"
```

## 🚨 紧急情况处理

### 升级失败应急处理

```bash
# 1. 立即停止升级操作
# 2. 执行回滚
./migrate-docker.sh down 002
./migrate-docker.sh down 001

# 3. 或从备份恢复
./migrate-docker.sh restore backup_YYYYMMDD_HHMMSS.sql

# 4. 联系技术支持
```

### 重要提醒

- **维护窗口**：建议在低峰期进行升级（凌晨2:00-4:00）
- **数据备份**：升级前必须备份，保留多个备份版本
- **测试验证**：先在测试环境验证，再在生产环境执行
- **监控告警**：升级过程中密切监控系统状态

---

*数据库升级是高风险操作，请严格按照文档执行，确保数据安全。*