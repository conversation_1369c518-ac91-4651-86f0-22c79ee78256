# Caddy + Admin 集成镜像
# 将Vue管理后台构建产物集成到Caddy镜像中

# 第一阶段：构建Vue管理后台
FROM docker.cnb.cool/yuandongbin/docker-sync/node:22.11.0-alpine AS admin-builder

# 设置 Alpine 镜像源为国内源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 配置 npm 镜像源
RUN npm config set registry https://registry.npmmirror.com

# 安装 pnpm
RUN npm install -g pnpm@latest

# 配置 pnpm 镜像源
RUN pnpm config set registry https://registry.npmmirror.com \
    && pnpm config set enable-pre-post-scripts true \
    && pnpm config set auto-install-peers true

# 安装构建依赖
RUN apk add --no-cache python3 make g++ curl

# 设置工作目录
WORKDIR /app

# 复制依赖配置文件（优化缓存）
COPY admin/package.json ./

# 安装依赖
RUN pnpm install --no-frozen-lockfile

# 复制源代码
COPY admin/ .

# 设置构建环境为生产环境
ENV NODE_ENV=production
# 强制设置API基础URL为相对路径，避免任何字符串拼接错误
ENV VITE_API_BASE_URL="/api"
ENV VITE_APP_TITLE="蔚之领域智能科技 - 管理系统"
ENV VITE_APP_VERSION=1.0.0
ENV VITE_APP_ENV=production
ENV VITE_DEBUG=false
# 管理后台使用独立域名，资源文件使用根路径
ENV VITE_BASE=/

# 显示环境变量（调试用）
RUN echo "Environment variables:" && env | grep VITE

# 构建应用（使用生产环境配置）
RUN pnpm build

# 第二阶段：Caddy + 静态文件
FROM docker.cnb.cool/yuandongbin/docker-sync/caddy:2-alpine

# 配置 Alpine 仓库镜像（解决网络问题）
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 设置时区
ENV TZ=Asia/Shanghai
# 安装时区与健康检查所需工具
RUN apk add --no-cache tzdata curl wget

# 创建必要的目录
RUN mkdir -p /opt/weishi/admin \
    && mkdir -p /opt/weishi/static \
    && mkdir -p /var/log/caddy

# 复制Vue管理后台构建产物
COPY --from=admin-builder /app/dist /opt/weishi/admin

# 复制Caddy配置文件
COPY deployment/caddy/Caddyfile.prod /etc/caddy/Caddyfile

# 创建静态文件目录
RUN mkdir -p /opt/weishi/static

# 可选静态文件：如需自定义静态资源，请将文件放到 /opt/weishi/static（无需构建时拷贝）

# 创建健康检查脚本
RUN echo '#!/bin/sh' > /health-check.sh \
    && echo 'curl -f http://localhost:80/health || exit 1' >> /health-check.sh \
    && chmod +x /health-check.sh

# 设置工作目录
WORKDIR /opt/weishi

# 暴露端口
EXPOSE 80 443

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD /health-check.sh

# 启动Caddy
CMD ["caddy", "run", "--config", "/etc/caddy/Caddyfile", "--adapter", "caddyfile"]
