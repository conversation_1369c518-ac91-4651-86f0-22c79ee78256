# 蔚之领域智能科技 - 阿里云生产环境配置
# 复制此文件为 production.env 并填写实际配置值

# ================================
# 镜像仓库配置
# ================================
REGISTRY_URL=registry.cn-hangzhou.aliyuncs.com
NAMESPACE=weishi
VERSION=latest

# ================================
# 域名配置
# ================================
# 🌐 您的域名（必须配置）
DOMAIN=your-domain.com
# 前端API访问地址
NUXT_PUBLIC_API_BASE=https://your-domain.com

# ================================
# 数据库配置
# ================================
# 🔐 数据库密码（必须修改）
MYSQL_ROOT_PASSWORD=your_very_secure_root_password_2024
MYSQL_PASSWORD=your_very_secure_db_password_2024
MYSQL_DATABASE=weizhi
MYSQL_USER=weishi
MYSQL_PORT=3306

# ================================
# JWT安全配置
# ================================
# 🔐 JWT密钥（必须修改为复杂密钥）
JWT_SECRET=your-super-complex-jwt-secret-key-for-production-environment-2024
JWT_EXPIRE_TIME=28800

# ================================
# 管理员账号配置
# ================================
# 🔐 管理员账号（必须修改）
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_very_secure_admin_password_2024
ADMIN_INIT_PASSWORD=true

# ================================
# 服务端口配置
# ================================
SERVER_PORT=3001
WEB_PORT=3000
ADMIN_PORT=8080

# ================================
# 腾讯云COS配置（文件存储）
# ================================
# 🔐 腾讯云COS配置（可选）
COS_SECRET_ID=your_cos_secret_id
COS_SECRET_KEY=your_cos_secret_key
COS_REGION=ap-nanjing
COS_BUCKET=your_cos_bucket_name

# ================================
# 阿里云OSS配置（文件存储）
# ================================
# 🔐 阿里云OSS配置（可选，替代COS）
OSS_ACCESS_KEY_ID=your_oss_access_key_id
OSS_ACCESS_KEY_SECRET=your_oss_access_key_secret
OSS_ENDPOINT=oss-cn-hangzhou.aliyuncs.com
OSS_BUCKET=your_oss_bucket_name

# ================================
# 文件上传配置
# ================================
# 最大文件大小（50MB）
MAX_FILE_SIZE=52428800
# 允许的文件类型
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp,application/pdf,text/plain

# ================================
# 阿里云服务器配置
# ================================
# 服务器信息
SERVER_IP=your_server_ip
SERVER_USER=root
SERVER_SSH_PORT=22

# 部署目录
DEPLOY_DIR=/opt/weishi
BACKUP_DIR=/opt/weishi/backups

# ================================
# 监控和日志配置
# ================================
# 启用监控
ENABLE_MONITORING=true
# 日志级别
LOG_LEVEL=info
# 日志保留天数
LOG_RETENTION_DAYS=30

# ================================
# 安全配置
# ================================
# 启用防火墙
ENABLE_FIREWALL=true
# 允许的IP地址（逗号分隔，留空表示允许所有）
ALLOWED_IPS=

# SSL证书配置
SSL_EMAIL=<EMAIL>
ENABLE_AUTO_SSL=true

# ================================
# 备份配置
# ================================
# 启用自动备份
ENABLE_BACKUP=true
# 备份保留天数
BACKUP_RETENTION_DAYS=30
# 备份时间（cron格式）
BACKUP_SCHEDULE="0 2 * * *"

# ================================
# 性能配置
# ================================
# 数据库连接池
DB_MAX_CONNECTIONS=100
DB_MAX_IDLE_CONNECTIONS=10

# 缓存配置
ENABLE_REDIS=false
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# ================================
# 开发和调试配置
# ================================
# 环境标识
NODE_ENV=production
APP_ENV=production

# 调试模式（生产环境建议关闭）
DEBUG=false
VERBOSE_LOGGING=false

# ================================
# 第三方服务配置
# ================================
# 邮件服务配置（可选）
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=<EMAIL>

# 短信服务配置（可选）
SMS_ACCESS_KEY_ID=
SMS_ACCESS_KEY_SECRET=
SMS_SIGN_NAME=
SMS_TEMPLATE_CODE=

# ================================
# 安全提醒
# ================================
# 🔒 重要安全提醒：
# 1. 请务必修改所有默认密码和密钥
# 2. 使用强密码（至少16位，包含大小写字母、数字、特殊字符）
# 3. 定期轮换密钥和密码
# 4. 不要将此文件提交到版本控制系统
# 5. 限制服务器SSH访问，使用密钥认证
# 6. 配置防火墙，只开放必要端口
# 7. 启用SSL证书，确保HTTPS访问
# 8. 定期备份数据库和重要文件
# 9. 监控服务器资源使用情况
# 10. 及时更新系统和软件包

# ================================
# 数据导入配置
# ================================
# 🗄️ 数据导入选项（首次部署时使用）
IMPORT_FULL_DATA=true

# 数据导入等待配置
DATA_IMPORT_MAX_WAIT=300
DATA_IMPORT_CHECK_INTERVAL=10

# ================================
# 部署检查清单
# ================================
# □ 修改所有密码和密钥
# □ 配置域名DNS解析
# □ 配置SSL证书
# □ 设置防火墙规则
# □ 配置文件存储（COS/OSS）
# □ 测试所有服务功能
# □ 设置监控和告警
# □ 配置自动备份
# □ 性能测试和优化
# □ 安全扫描和加固
