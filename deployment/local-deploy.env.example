# 本地部署环境变量配置示例
# 复制此文件为 local-deploy.env 并根据实际情况修改

# 镜像仓库配置
REGISTRY_URL=your-registry.com
NAMESPACE=your-namespace
VERSION=v0.2.0

# 域名配置（本地部署使用）
DOMAIN=weizhi.local
ADMIN_DOMAIN=admin.weizhi.local

# 数据库配置
MYSQL_ROOT_PASSWORD=Ydb3344%
MYSQL_PASSWORD=Ydb3344%
MYSQL_DATABASE=weizhi
MYSQL_USER=weizhi

# JWT 配置
JWT_SECRET=your-jwt-secret-key-here
JWT_EXPIRE_TIME=28800

# 管理员配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-admin-password
ADMIN_INIT_PASSWORD=true

# 数据库迁移和数据导入
AUTO_MIGRATE=true
IMPORT_FULL_DATA=false

# 文件上传配置
MAX_FILE_SIZE=52428800
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp,application/pdf,text/plain

# 腾讯云COS配置（可选）
COS_SECRET_ID=
COS_SECRET_KEY=
COS_REGION=ap-nanjing
COS_BUCKET=
COS_DOMAIN=

# 阿里云OSS配置（可选，替代COS）
OSS_ACCESS_KEY_ID=
OSS_ACCESS_KEY_SECRET=
OSS_ENDPOINT=
OSS_BUCKET=
