# 阿里云优化的Nuxt3前端服务多阶段构建Dockerfile
# 构建阶段：编译Nuxt3应用
# 运行阶段：精简的Node.js运行时环境

# ================================
# 构建阶段
# ================================
FROM docker.cnb.cool/yuandongbin/docker-sync/node:22.11.0-alpine AS builder

# 设置阿里云Alpine镜像源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 设置npm和pnpm阿里云镜像源
RUN npm config set registry https://registry.npmmirror.com && \
    npm install -g pnpm@latest && \
    pnpm config set registry https://registry.npmmirror.com && \
    pnpm config set enable-pre-post-scripts true && \
    pnpm config set auto-install-peers true

# 安装构建依赖
RUN apk add --no-cache python3 make g++ curl git

# 设置工作目录
WORKDIR /app

# 复制工作区配置文件
COPY web/package.json ./
COPY web/pnpm-lock.yam[l] ./
COPY pnpm-workspace.yam[l] ./

# 安装依赖（不使用 frozen-lockfile，因为工作区结构可能不完整）
RUN pnpm install --no-frozen-lockfile

# 复制源代码
COPY web/ .

# 构建应用
RUN pnpm build

# ================================
# 生产运行阶段
# ================================
FROM docker.cnb.cool/yuandongbin/docker-sync/node:22.11.0-alpine AS runner

# 设置阿里云Alpine镜像源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 设置npm国内镜像源并安装pnpm
RUN npm config set registry https://registry.npmmirror.com \
    && npm install -g pnpm@latest \
    && apk add --no-cache curl dumb-init

# 创建非root用户
RUN addgroup -g 1001 -S nodejs \
    && adduser -S nuxtjs -u 1001

# 设置时区
ENV TZ=Asia/Shanghai

# 设置工作目录
WORKDIR /app

# 复制构建产物
COPY --from=builder --chown=nuxtjs:nodejs /app/.output ./.output
COPY --from=builder --chown=nuxtjs:nodejs /app/package.json ./
COPY --from=builder --chown=nuxtjs:nodejs /app/nuxt.config.ts ./

# 切换到非root用户
USER nuxtjs

# 设置环境变量
ENV NODE_ENV=production
ENV NUXT_HOST=0.0.0.0
ENV NUXT_PORT=3000
ENV NODE_OPTIONS="--max-old-space-size=1024"

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

# 启动应用
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", ".output/server/index.mjs"]
