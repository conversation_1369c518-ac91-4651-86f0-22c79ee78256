#!/bin/bash

# 微智领域服务健康检查脚本
# 用于检查所有服务是否正常启动和运行

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose是否可用
check_docker() {
    log_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装或不在PATH中"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装或不在PATH中"
        exit 1
    fi
    
    log_success "Docker环境检查通过"
}

# 检查容器状态
check_containers() {
    log_info "检查容器状态..."
    
    local containers=("weishi-mysql-prod" "weishi-server-prod" "weishi-web-prod" "weishi-caddy-prod")
    local all_running=true
    
    for container in "${containers[@]}"; do
        if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "$container"; then
            local status=$(docker ps --format "table {{.Names}}\t{{.Status}}" | grep "$container" | awk '{print $2}')
            if [[ $status == "Up" ]]; then
                log_success "容器 $container 正在运行"
            else
                log_warning "容器 $container 状态异常: $status"
                all_running=false
            fi
        else
            log_error "容器 $container 未找到或未运行"
            all_running=false
        fi
    done
    
    if [ "$all_running" = true ]; then
        log_success "所有容器状态正常"
    else
        log_warning "部分容器状态异常"
    fi
}

# 检查容器健康状态
check_health() {
    log_info "检查容器健康状态..."
    
    local containers=("weishi-mysql-prod" "weishi-server-prod" "weishi-web-prod" "weishi-caddy-prod")
    
    for container in "${containers[@]}"; do
        if docker ps --format "table {{.Names}}" | grep -q "$container"; then
            local health=$(docker inspect --format='{{.State.Health.Status}}' "$container" 2>/dev/null || echo "no-health-check")
            
            case $health in
                "healthy")
                    log_success "容器 $container 健康检查通过"
                    ;;
                "unhealthy")
                    log_error "容器 $container 健康检查失败"
                    ;;
                "starting")
                    log_warning "容器 $container 健康检查启动中..."
                    ;;
                "no-health-check")
                    log_info "容器 $container 未配置健康检查"
                    ;;
                *)
                    log_warning "容器 $container 健康状态未知: $health"
                    ;;
            esac
        fi
    done
}

# 检查端口连通性
check_ports() {
    log_info "检查服务端口连通性..."

    local ports=("3307:MySQL数据库" "3002:Go后端服务" "3003:Nuxt3前端" "80:Caddy HTTP" "443:Caddy HTTPS")

    for port_info in "${ports[@]}"; do
        local port=$(echo $port_info | cut -d: -f1)
        local service=$(echo $port_info | cut -d: -f2)

        # 检查端口是否在监听
        if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
            log_success "端口 $port ($service) 正在监听"

            # 对HTTP端口进行连通性测试
            if [[ "$port" == "80" ]] || [[ "$port" == "3002" ]] || [[ "$port" == "3003" ]]; then
                if timeout 5 bash -c "echo >/dev/tcp/localhost/$port" 2>/dev/null; then
                    log_success "端口 $port 连通性测试通过"
                else
                    log_warning "端口 $port 监听但连通性测试失败"
                fi
            fi
        else
            log_warning "端口 $port ($service) 未监听"

            # 检查是否有进程占用该端口
            local process=$(lsof -ti:$port 2>/dev/null)
            if [[ -n "$process" ]]; then
                log_info "端口 $port 被进程 $process 占用"
            fi
        fi
    done
}

# 检查HTTP服务响应
check_http_services() {
    log_info "检查HTTP服务响应..."

    # 等待服务启动
    log_info "等待服务完全启动..."
    sleep 5

    # 检查Caddy健康检查端点
    local health_response=$(curl -s http://localhost/health 2>/dev/null)
    if [[ "$health_response" == "OK" ]]; then
        log_success "Caddy健康检查端点响应正常"
    else
        log_warning "Caddy健康检查端点无响应或响应异常: '$health_response'"
    fi

    # 检查前端服务
    local frontend_response=$(curl -s -I http://localhost 2>/dev/null | head -n 1)
    if [[ "$frontend_response" =~ "200 OK" ]]; then
        # 进一步检查是否返回HTML内容
        local content_type=$(curl -s -I http://localhost 2>/dev/null | grep -i "content-type" | head -n 1)
        if [[ "$content_type" =~ "text/html" ]]; then
            log_success "前端服务响应正常"
        else
            log_warning "前端服务响应异常，Content-Type: $content_type"
        fi
    else
        log_warning "前端服务无响应或状态异常: $frontend_response"
    fi

    # 检查管理后台（独立域名）
    local admin_domain=${ADMIN_DOMAIN:-admin.localhost}
    local admin_response=$(curl -s -I "http://$admin_domain/" 2>/dev/null | head -n 1)
    if [[ "$admin_response" =~ "200 OK" ]]; then
        # 进一步检查是否返回HTML内容
        local admin_content_type=$(curl -s -I "http://$admin_domain/" 2>/dev/null | grep -i "content-type" | head -n 1)
        if [[ "$admin_content_type" =~ "text/html" ]]; then
            log_success "管理后台响应正常 ($admin_domain)"
        else
            log_warning "管理后台响应异常，Content-Type: $admin_content_type"
        fi
    else
        log_warning "管理后台无响应 ($admin_domain): $admin_response"
        # 如果独立域名失败，尝试localhost（向后兼容）
        local fallback_response=$(curl -s -I http://localhost/admin/ 2>/dev/null | head -n 1)
        if [[ "$fallback_response" =~ "200 OK" ]]; then
            log_warning "管理后台在主域名下可访问（可能配置了旧的路径模式）"
        fi
    fi

    # 检查API服务
    local api_response=$(curl -s http://localhost:3002/health 2>/dev/null)
    if [[ -n "$api_response" ]] && [[ "$api_response" != "404"* ]] && [[ "$api_response" != "Connection refused"* ]]; then
        log_success "API服务响应正常: $api_response"
    else
        log_warning "API服务无响应或响应异常: '$api_response'"
        # 尝试检查API服务是否至少在监听
        local api_status=$(curl -s -I http://localhost:3002/ 2>/dev/null | head -n 1)
        if [[ "$api_status" =~ "200 OK" ]] || [[ "$api_status" =~ "404" ]]; then
            log_warning "API服务在运行但健康检查端点可能未配置"
        fi
    fi
}

# 检查日志中的错误
check_logs() {
    log_info "检查服务日志中的错误..."
    
    local containers=("weishi-mysql-prod" "weishi-server-prod" "weishi-web-prod" "weishi-caddy-prod")
    
    for container in "${containers[@]}"; do
        if docker ps --format "table {{.Names}}" | grep -q "$container"; then
            local error_count=$(docker logs "$container" --tail 50 2>&1 | grep -i "error\|fatal\|panic" | wc -l)
            
            if [ "$error_count" -eq 0 ]; then
                log_success "容器 $container 日志无错误"
            else
                log_warning "容器 $container 日志中发现 $error_count 个错误"
                log_info "最近的错误日志:"
                docker logs "$container" --tail 10 2>&1 | grep -i "error\|fatal\|panic" | head -3
            fi
        fi
    done
}

# 详细的服务可用性测试
test_service_availability() {
    log_info "执行详细的服务可用性测试..."

    # 测试前端服务
    log_info "测试前端服务..."
    local frontend_content=$(curl -s --max-time 10 http://localhost 2>/dev/null | head -c 200)
    if [[ "$frontend_content" =~ "<!DOCTYPE html" ]] || [[ "$frontend_content" =~ "<html" ]]; then
        log_success "前端服务返回有效的HTML内容"
    else
        log_warning "前端服务未返回有效的HTML内容"
        log_info "响应内容预览: ${frontend_content:0:100}..."
    fi

    # 测试管理后台
    log_info "测试管理后台..."
    local admin_domain=${ADMIN_DOMAIN:-admin.localhost}
    local admin_content=$(curl -s --max-time 10 "http://$admin_domain/" 2>/dev/null | head -c 200)
    if [[ "$admin_content" =~ "<!DOCTYPE html" ]] || [[ "$admin_content" =~ "<html" ]]; then
        log_success "管理后台返回有效的HTML内容"
    else
        log_warning "管理后台未返回有效的HTML内容"
        log_info "响应内容预览: ${admin_content:0:100}..."
    fi

    # 测试API服务
    log_info "测试API服务..."
    local api_test=$(curl -s --max-time 10 http://localhost:3002/api/health 2>/dev/null)
    if [[ -n "$api_test" ]] && [[ "$api_test" != *"404"* ]] && [[ "$api_test" != *"Connection refused"* ]]; then
        log_success "API服务健康检查响应: $api_test"
    else
        # 尝试访问API根路径
        local api_root=$(curl -s --max-time 10 http://localhost:3002/ 2>/dev/null)
        if [[ -n "$api_root" ]]; then
            log_warning "API服务在运行但健康检查端点可能未配置"
            log_info "API根路径响应: ${api_root:0:100}..."
        else
            log_warning "API服务无响应"
        fi
    fi

    # 测试数据库连接（通过API）
    log_info "测试数据库连接..."
    local db_test=$(curl -s --max-time 10 http://localhost:3002/api/ping 2>/dev/null)
    if [[ -n "$db_test" ]] && [[ "$db_test" != *"404"* ]]; then
        log_success "数据库连接测试响应: $db_test"
    else
        log_warning "数据库连接测试无响应（可能端点未配置）"
    fi
}

# 显示服务摘要
show_summary() {
    log_info "=== 服务状态摘要 ==="
    
    echo ""
    echo "🐳 容器状态:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep weishi || echo "未找到相关容器"
    
    echo ""
    echo "📊 资源使用:"
    docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}" | grep weishi || echo "未找到相关容器"
    
    echo ""
    echo "🌐 访问地址:"
    local admin_domain=${ADMIN_DOMAIN:-admin.localhost}
    echo "  前端服务: http://localhost"
    echo "  管理后台: http://$admin_domain"
    echo "  API文档: http://localhost:3002/docs (如果启用)"
    echo "  健康检查: http://localhost/health"
    echo "  管理后台健康检查: http://$admin_domain/health"
    
    echo ""
    echo "📝 日志查看命令:"
    echo "  所有服务: docker-compose -f docker-compose.prod.yml --env-file docker.env logs -f"
    echo "  单个服务: docker logs -f weishi-[service]-prod"
}

# 主函数
main() {
    echo ""
    log_info "=== 微智领域服务健康检查 ==="
    echo ""
    
    check_docker
    echo ""
    
    check_containers
    echo ""
    
    check_health
    echo ""
    
    check_ports
    echo ""
    
    check_http_services
    echo ""

    test_service_availability
    echo ""

    check_logs
    echo ""

    show_summary
    
    echo ""
    log_info "健康检查完成！"
    echo ""
}

# 运行主函数
main "$@"
