#!/usr/bin/env bash
# Caddy 调试脚本 - 帮助诊断 Caddy 启动问题

set -euo pipefail

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境变量
check_env_vars() {
    log_info "检查关键环境变量..."
    
    if [ -f "production.env" ]; then
        source production.env
        log_success "已加载 production.env"
    else
        log_error "未找到 production.env 文件"
        return 1
    fi
    
    echo ""
    echo "关键环境变量检查："
    
    if [ -n "${DOMAIN:-}" ]; then
        log_success "DOMAIN: $DOMAIN"
    else
        log_error "DOMAIN 未设置"
    fi
    
    if [ -n "${ADMIN_DOMAIN:-}" ]; then
        log_success "ADMIN_DOMAIN: $ADMIN_DOMAIN"
    else
        log_error "ADMIN_DOMAIN 未设置"
    fi
    
    if [ -n "${REGISTRY_URL:-}" ]; then
        log_success "REGISTRY_URL: $REGISTRY_URL"
    else
        log_error "REGISTRY_URL 未设置"
    fi
    
    if [ -n "${VERSION:-}" ]; then
        log_success "VERSION: $VERSION"
    else
        log_warning "VERSION 未设置，将使用 latest"
    fi
}

# 检查 Caddy 配置文件
check_caddy_config() {
    log_info "检查 Caddy 配置文件..."
    
    if [ -f "caddy/Caddyfile.prod" ]; then
        log_success "找到 Caddyfile.prod"
    else
        log_error "未找到 caddy/Caddyfile.prod"
        return 1
    fi
    
    # 使用调试配置测试 Caddy 语法
    log_info "测试 Caddy 配置语法..."
    if docker run --rm -v "$(pwd)/caddy/Caddyfile.debug:/etc/caddy/Caddyfile:ro" \
        docker.cnb.cool/yuandongbin/docker-sync/caddy:2-alpine \
        caddy validate --config /etc/caddy/Caddyfile; then
        log_success "Caddy 配置语法正确"
    else
        log_error "Caddy 配置语法错误"
        return 1
    fi
}

# 检查镜像
check_images() {
    log_info "检查 Caddy 镜像..."
    
    source production.env
    CADDY_IMAGE="${REGISTRY_URL}/${NAMESPACE}/weizhi_admin_caddy:${VERSION:-latest}"
    
    log_info "尝试拉取镜像: $CADDY_IMAGE"
    if docker pull "$CADDY_IMAGE"; then
        log_success "镜像拉取成功"
    else
        log_error "镜像拉取失败，请检查镜像仓库配置和网络连接"
        return 1
    fi
}

# 使用调试配置启动 Caddy
start_debug_caddy() {
    log_info "使用调试配置启动 Caddy..."
    
    source production.env
    
    # 停止现有的 Caddy 容器
    docker stop weizhi-caddy-prod 2>/dev/null || true
    docker rm weizhi-caddy-prod 2>/dev/null || true
    
    # 启动调试 Caddy
    docker run -d \
        --name weizhi-caddy-debug \
        --network weizhi-network \
        -p 8080:8080 \
        -p 2019:2019 \
        -e DOMAIN="${DOMAIN:-localhost}" \
        -e ADMIN_DOMAIN="${ADMIN_DOMAIN:-admin.localhost}" \
        -v "$(pwd)/caddy/Caddyfile.debug:/etc/caddy/Caddyfile:ro" \
        -v "$(pwd)/logs/caddy:/var/log/caddy" \
        "${REGISTRY_URL}/${NAMESPACE}/weizhi_admin_caddy:${VERSION:-latest}"
    
    log_success "调试 Caddy 已启动"
    
    # 等待启动
    sleep 5
    
    # 测试健康检查
    log_info "测试健康检查端点..."
    if curl -f http://localhost:8080/health; then
        log_success "健康检查通过"
    else
        log_error "健康检查失败"
        log_info "查看 Caddy 日志："
        docker logs weizhi-caddy-debug
        return 1
    fi
}

# 清理调试容器
cleanup_debug() {
    log_info "清理调试容器..."
    docker stop weizhi-caddy-debug 2>/dev/null || true
    docker rm weizhi-caddy-debug 2>/dev/null || true
    log_success "清理完成"
}

# 主函数
main() {
    log_info "开始 Caddy 调试..."
    
    cd "$(dirname "$0")"
    
    if ! check_env_vars; then
        log_error "环境变量检查失败"
        exit 1
    fi
    
    if ! check_caddy_config; then
        log_error "Caddy 配置检查失败"
        exit 1
    fi
    
    if ! check_images; then
        log_error "镜像检查失败"
        exit 1
    fi
    
    if ! start_debug_caddy; then
        log_error "调试启动失败"
        cleanup_debug
        exit 1
    fi
    
    echo ""
    log_success "Caddy 调试完成！"
    echo ""
    echo "测试地址："
    echo "  健康检查: http://localhost:8080/health"
    echo "  状态检查: http://localhost:8080/status"
    echo "  Caddy 管理: http://localhost:2019"
    echo ""
    echo "如果调试成功，可以使用正常配置重新部署："
    echo "  docker compose -f docker-compose.prod.yml --env-file production.env up -d caddy"
    echo ""
    echo "清理调试容器："
    echo "  ./debug-caddy.sh cleanup"
}

# 处理命令行参数
case "${1:-}" in
    "cleanup")
        cleanup_debug
        ;;
    *)
        main "$@"
        ;;
esac
