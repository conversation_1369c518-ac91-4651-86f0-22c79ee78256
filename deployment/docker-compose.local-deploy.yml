# 本地部署版 Docker Compose
# 用于本地环境模拟生产部署，使用制品镜像但不启用 HTTPS
# 使用方法：docker compose -f deployment/docker-compose.local-deploy.yml --env-file deployment/production.env up -d

services:
  # MySQL数据库
  mysql:
    image: docker.cnb.cool/yuandongbin/docker-sync/mysql:8.0
    container_name: weizhi-mysql-local-deploy
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-Ydb3344%}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-weizhi}
      MYSQL_USER: ${MYSQL_USER:-weizhi}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-Ydb3344%}
      TZ: Asia/Shanghai
    ports:
      # 避免与本机 3306 冲突，映射到 3307
      - "3307:3306"
    volumes:
      - mysql_local_deploy_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d:ro
      - ./logs/mysql:/var/log/mysql
    networks:
      - weizhi-local-deploy-network
    healthcheck:
      test: ["CMD", "sh", "-c", "mysqladmin ping -h localhost -u root -p'${MYSQL_ROOT_PASSWORD}' --port=3306 && mysql -h localhost -u root -p'${MYSQL_ROOT_PASSWORD}' --port=3306 -e 'SELECT 1'"]
      timeout: 30s
      retries: 25
      interval: 20s
      start_period: 240s

  # 后端API服务 (Go) - 使用制品镜像
  server:
    image: ${REGISTRY_URL}/${NAMESPACE}/weizhi_server:${VERSION:-latest}
    container_name: weizhi-server-local-deploy
    restart: unless-stopped
    environment:
      # Go 应用配置
      APP_MODE: production
      APP_PORT: "3001"
      TZ: Asia/Shanghai

      # 数据库配置
      DB_HOST: mysql
      DB_PORT: "3306"
      DB_USERNAME: ${MYSQL_USER:-weizhi}
      DB_PASSWORD: ${MYSQL_PASSWORD:-Ydb3344%}
      DB_DATABASE: ${MYSQL_DATABASE:-weizhi}
      
      # JWT 配置
      JWT_SECRET: ${JWT_SECRET}
      JWT_EXPIRE_TIME: ${JWT_EXPIRE_TIME:-28800}
      
      # 管理员配置
      ADMIN_USERNAME: ${ADMIN_USERNAME:-admin}
      ADMIN_PASSWORD: ${ADMIN_PASSWORD}
      ADMIN_INIT_PASSWORD: ${ADMIN_INIT_PASSWORD:-true}

      # 数据导入配置
      IMPORT_FULL_DATA: ${IMPORT_FULL_DATA:-false}

      # 数据库迁移配置
      AUTO_MIGRATE: ${AUTO_MIGRATE:-false}

      # 文件上传配置
      MAX_FILE_SIZE: ${MAX_FILE_SIZE:-52428800}
      ALLOWED_FILE_TYPES: ${ALLOWED_FILE_TYPES:-image/jpeg,image/png,image/gif,image/webp,application/pdf,text/plain}
    ports:
      - "3001:3001"
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - weizhi-local-deploy-network
    volumes:
      - ./logs/server:/app/logs
      - ./uploads:/app/uploads
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 15s
      timeout: 10s
      retries: 10
      start_period: 120s

  # 前端网站 - 使用制品镜像
  web:
    image: ${REGISTRY_URL}/${NAMESPACE}/weizhi_web:${VERSION:-latest}
    container_name: weizhi-web-local-deploy
    restart: unless-stopped
    environment:
      NODE_ENV: production
      NUXT_HOST: 0.0.0.0
      NUXT_PORT: 3000
      TZ: Asia/Shanghai
      API_BASE_URL: http://server:3001
      NUXT_PUBLIC_API_BASE: http://${DOMAIN:-weizhi.local}
      NODE_OPTIONS: "--max-old-space-size=1024"
    ports:
      - "3000:3000"
    depends_on:
      server:
        condition: service_healthy
    networks:
      - weizhi-local-deploy-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 90s

  # Caddy反向代理 + 管理后台 - 使用制品镜像
  caddy:
    image: ${REGISTRY_URL}/${NAMESPACE}/weizhi_admin_caddy:${VERSION:-latest}
    container_name: weizhi-caddy-local-deploy
    restart: unless-stopped
    ports:
      - "80:80"
      - "2019:2019"  # Caddy 管理 API
    environment:
      - DOMAIN=${DOMAIN:-weizhi.local}
      - ADMIN_DOMAIN=${ADMIN_DOMAIN:-admin.weizhi.local}
      - TZ=Asia/Shanghai
    volumes:
      - ./caddy/Caddyfile.local-deploy:/etc/caddy/Caddyfile:ro
      - ./logs/caddy:/var/log/caddy
    depends_on:
      - web
      - server
    networks:
      - weizhi-local-deploy-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  mysql_local_deploy_data:
    driver: local

networks:
  weizhi-local-deploy-network:
    driver: bridge
