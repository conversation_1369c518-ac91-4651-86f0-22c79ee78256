# 调试用简化 Caddyfile
# 用于排查 Caddy 启动问题

# 全局配置
{
    # 禁用自动 HTTPS（调试用）
    auto_https off
    
    # 管理接口
    admin localhost:2019
    
    # 调试日志
    log {
        level DEBUG
        output stdout
    }
}

# 简单的健康检查端点
:8080 {
    handle /health {
        respond "Caddy Debug OK" 200
    }
    
    handle /status {
        respond "Status: Running in Debug Mode" 200
    }
    
    handle {
        respond "Caddy Debug Server" 200
    }
}

# 如果环境变量存在，则配置主域名
{$DOMAIN:localhost} {
    handle /health {
        respond "Main Domain OK" 200
    }
    
    handle /api/* {
        reverse_proxy server:3001 {
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
        }
    }
    
    handle {
        reverse_proxy web:3000 {
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
        }
    }
}

# 如果管理域名环境变量存在，则配置管理后台
{$ADMIN_DOMAIN:admin.localhost} {
    handle /health {
        respond "Admin Domain OK" 200
    }
    
    handle /api/* {
        reverse_proxy server:3001 {
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
        }
    }
    
    handle {
        root * /opt/weishi/admin
        try_files {path} /index.html
        file_server
    }
}
