{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "src/**/*.tsx", "src/**/*.jsx", "auto-imports.d.ts", "components.d.ts"], "exclude": ["src/**/__tests__/*", "dist", "node_modules"], "compilerOptions": {"composite": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["element-plus/global", "vite/client"], "target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "jsxImportSource": "vue", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true}}