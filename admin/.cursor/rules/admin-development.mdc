---
description:
globs:
alwaysApply: false
---
---
description: 蔚之领域管理后台核心开发规范，包含技术栈要求、TSX/JSX支持、文件结构和常用模式
globs: ["admin/src/**/*"]
alwaysApply: true
---

# 蔚之领域管理后台开发规范

## 技术栈要求

### CSS 框架
- **必须使用 UnoCSS**，禁止使用传统 CSS 或其他框架
- 优先使用 UnoCSS 原子类：`w-full`、`h-screen`、`flex`、`items-center` 等
- 使用预定义的快捷类：`btn`、`btn-primary`、`card`、`form-input`
- 响应式设计使用：`sm:`、`md:`、`lg:`、`xl:` 前缀

### Vue 3 最佳实践
- **必须使用 Composition API** 和 `<script setup>` 语法
- 使用 `ref()` 和 `reactive()` 进行状态管理
- 组件props定义使用 `defineProps<Props>()`
- 事件定义使用 `defineEmits<Emits>()`

### TSX/JSX 支持
- **支持 TSX/JSX 语法**，适用于复杂逻辑渲染场景
- TSX 文件使用 `.tsx` 扩展名，JSX 文件使用 `.jsx` 扩展名
- **选择原则**：
  - 简单页面和组件：优先使用 Vue SFC (`.vue`)
  - 复杂条件渲染、动态组件：使用 TSX/JSX
  - 需要更强类型检查的组件：使用 TSX
  - 函数式组件：推荐使用 TSX

### Element Plus 组件
- 优先使用 Element Plus 组件：`el-button`、`el-form`、`el-table`、`el-card`
- 表单验证使用 Element Plus 的 rules 系统
- 消息提示使用 `ElMessage`、确认框使用 `ElMessageBox`

## 文件结构规范

### 页面组件结构
```vue
<template>
  <!-- 使用 UnoCSS 类 -->
  <div class="page-container">
    <div class="content-wrapper">
      <!-- 页面内容 -->
    </div>
  </div>
</template>

<script setup lang="ts">
// 1. 导入
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'

// 2. 类型定义
interface Props {
  // props定义
}

// 3. 响应式数据
const loading = ref(false)
const formData = reactive({})

// 4. 方法定义
const handleSubmit = () => {}

// 5. 生命周期
onMounted(() => {})
</script>
```

### 目录命名
- 页面文件：`src/views/模块名/index.vue`
- 组件文件：`src/components/ComponentName.vue`（使用 PascalCase）
- 工具函数：`src/utils/functionName.ts`（使用 camelCase）

## 常用模式

### 表格页面模式
```vue
<template>
  <div class="p-6">
    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold">模块管理</h2>
      <el-button type="primary" @click="handleAdd">
        <i class="i-mdi-plus mr-2"></i>
        新建
      </el-button>
    </div>
    
    <!-- 搜索区域 -->
    <el-card class="mb-6">
      <el-form :model="searchForm" inline>
        <!-- 搜索表单 -->
      </el-form>
    </el-card>
    
    <!-- 表格区域 -->
    <el-card>
      <el-table :data="tableData" v-loading="loading">
        <!-- 表格列 -->
      </el-table>
      
      <!-- 分页 -->
      <div class="flex justify-end mt-4">
        <el-pagination />
      </div>
    </el-card>
  </div>
</template>
```

### 表单页面模式
```vue
<template>
  <div class="p-6">
    <el-card>
      <template #header>
        <h3 class="text-lg font-medium">{{ isEdit ? '编辑' : '新建' }}模块</h3>
      </template>
      
      <el-form 
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <!-- 表单项 -->
        
        <div class="flex justify-end mt-6">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            确定
          </el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>
```

### TSX 组件模式
```tsx
// 函数式组件示例
import { defineComponent } from 'vue'
import type { PropType } from 'vue'

interface TableColumn {
  key: string
  label: string
  width?: number
}

export default defineComponent({
  name: 'DynamicTable',
  props: {
    columns: {
      type: Array as PropType<TableColumn[]>,
      required: true
    },
    data: {
      type: Array as PropType<Record<string, any>[]>,
      required: true
    }
  },
  setup(props) {
    const renderColumns = () => {
      return props.columns.map(column => (
        <el-table-column
          key={column.key}
          prop={column.key}
          label={column.label}
          width={column.width}
        />
      ))
    }

    return () => (
      <div class="p-6">
        <el-table data={props.data}>
          {renderColumns()}
        </el-table>
      </div>
    )
  }
})
```

## 图标使用
- 使用 UnoCSS 图标：`i-mdi-icon-name`、`i-carbon-icon-name`、`i-tabler-icon-name`
- 常用图标：
  - 编辑：`i-mdi-pencil`
  - 删除：`i-mdi-delete`
  - 查看：`i-mdi-eye`
  - 搜索：`i-mdi-magnify`
  - 新增：`i-mdi-plus`

## API 调用
- 使用 axios 进行 API 调用
- 统一错误处理和加载状态管理
- API 基础路径：`/api`

## 状态管理
- 简单状态使用 `ref()` 和 `reactive()`
- 复杂状态使用 Pinia stores
- Store 文件位置：`src/stores/moduleName.ts`

## 路由配置
- 路由文件：`src/router/index.ts`
- 使用懒加载：`() => import('@/views/module/index.vue')`
- 路由元信息包含：`title`、`icon`、`hideInMenu`

## 性能优化
- 大列表使用虚拟滚动
- 图片懒加载
- 组件懒加载
- 适当使用 `v-memo` 和 `v-once`
