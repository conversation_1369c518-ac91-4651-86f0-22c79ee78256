---
description:
globs:
alwaysApply: false
---
---
description: Vue 3 组件开发模式，包含基础组件模板、TSX/JSX组件模式和开发原则
globs: ["admin/src/components/**/*", "admin/src/views/**/*"]
alwaysApply: false
---

# Vue 3 组件开发模式

## 基础组件模板

### 表格组件模板
```vue
<template>
  <div class="table-container">
    <!-- 工具栏 -->
    <div class="flex justify-between items-center mb-4">
      <div class="flex items-center gap-4">
        <slot name="toolbar-left">
          <h3 class="text-lg font-medium">{{ title }}</h3>
        </slot>
      </div>
      <div class="flex items-center gap-2">
        <slot name="toolbar-right">
          <el-button type="primary" @click="$emit('add')">
            <i class="i-mdi-plus mr-2"></i>
            新建
          </el-button>
        </slot>
      </div>
    </div>
    
    <!-- 搜索区域 -->
    <el-card v-if="showSearch" class="mb-4">
      <slot name="search">
        <div class="text-gray-500 text-center py-4">
          请在此插槽中添加搜索表单
        </div>
      </slot>
    </el-card>
    
    <!-- 表格 -->
    <el-table 
      :data="data" 
      v-loading="loading"
      class="w-full"
      @selection-change="$emit('selection-change', $event)"
    >
      <slot />
    </el-table>
    
    <!-- 分页 -->
    <div v-if="showPagination" class="flex justify-end mt-4">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="pageSizes"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="$emit('page-change', $event)"
        @size-change="$emit('size-change', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title?: string
  data: any[]
  loading?: boolean
  showSearch?: boolean
  showPagination?: boolean
  total?: number
  currentPage?: number
  pageSize?: number
  pageSizes?: number[]
}

interface Emits {
  add: []
  'selection-change': [selection: any[]]
  'page-change': [page: number]
  'size-change': [size: number]
}

withDefaults(defineProps<Props>(), {
  title: '',
  loading: false,
  showSearch: true,
  showPagination: true,
  total: 0,
  currentPage: 1,
  pageSize: 10,
  pageSizes: () => [10, 20, 50, 100]
})

defineEmits<Emits>()
</script>
```

### 表单组件模板
```vue
<template>
  <el-form
    ref="formRef"
    :model="modelValue"
    :rules="rules"
    :label-width="labelWidth"
    class="form-component"
  >
    <slot />
    
    <div v-if="showActions" class="flex justify-end gap-2 mt-6">
      <el-button @click="$emit('cancel')">
        {{ cancelText }}
      </el-button>
      <el-button 
        type="primary" 
        :loading="loading"
        @click="handleSubmit"
      >
        {{ submitText }}
      </el-button>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  modelValue: Record<string, any>
  rules?: FormRules
  labelWidth?: string
  loading?: boolean
  showActions?: boolean
  submitText?: string
  cancelText?: string
}

interface Emits {
  'update:modelValue': [value: Record<string, any>]
  submit: [formData: Record<string, any>]
  cancel: []
}

const props = withDefaults(defineProps<Props>(), {
  labelWidth: '120px',
  loading: false,
  showActions: true,
  submitText: '确定',
  cancelText: '取消'
})

const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    emit('submit', props.modelValue)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 暴露表单实例方法
defineExpose({
  validate: () => formRef.value?.validate(),
  resetFields: () => formRef.value?.resetFields(),
  clearValidate: () => formRef.value?.clearValidate()
})
</script>
```

### 搜索组件模板
```vue
<template>
  <div class="search-component">
    <el-form :model="searchForm" inline>
      <slot :form="searchForm" />
      
      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          <i class="i-mdi-magnify mr-2"></i>
          搜索
        </el-button>
        <el-button @click="handleReset">
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
interface Props {
  modelValue: Record<string, any>
}

interface Emits {
  'update:modelValue': [value: Record<string, any>]
  search: [params: Record<string, any>]
  reset: []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const searchForm = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const handleSearch = () => {
  emit('search', searchForm.value)
}

const handleReset = () => {
  const resetForm = Object.keys(searchForm.value).reduce((acc, key) => {
    acc[key] = ''
    return acc
  }, {} as Record<string, any>)
  
  emit('update:modelValue', resetForm)
  emit('reset')
}
</script>
```

## 页面组件模式

### CRUD页面模板
```vue
<template>
  <div class="p-6">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold">{{ pageTitle }}</h2>
    </div>
    
    <!-- 搜索区域 -->
    <SearchComponent 
      v-model="searchParams" 
      @search="handleSearch"
      @reset="handleSearchReset"
    >
      <template #default="{ form }">
        <!-- 搜索表单项 -->
      </template>
    </SearchComponent>
    
    <!-- 表格区域 -->
    <TableComponent
      :title="tableTitle"
      :data="tableData"
      :loading="loading"
      :total="total"
      :current-page="pagination.page"
      :page-size="pagination.size"
      @add="handleAdd"
      @page-change="handlePageChange"
      @size-change="handleSizeChange"
    >
      <!-- 表格列定义 -->
      <el-table-column prop="name" label="名称" />
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button 
            size="small" 
            type="danger" 
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </TableComponent>
    
    <!-- 弹窗表单 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="dialogTitle"
      width="600px"
    >
      <FormComponent
        v-model="formData"
        :rules="formRules"
        :loading="submitLoading"
        @submit="handleSubmit"
        @cancel="dialogVisible = false"
      >
        <!-- 表单项 -->
      </FormComponent>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式状态
const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const tableData = ref([])
const formData = ref({})
const searchParams = ref({})

const pagination = reactive({
  page: 1,
  size: 10
})

// 计算属性
const pageTitle = '模块管理'
const tableTitle = '模块列表'
const total = ref(0)

const isEdit = computed(() => !!formData.value.id)
const dialogTitle = computed(() => isEdit.value ? '编辑模块' : '新建模块')

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' }
  ]
}

// 方法
const fetchData = async () => {
  loading.value = true
  try {
    // API调用
    // const response = await api.getList({
    //   ...searchParams.value,
    //   page: pagination.page,
    //   size: pagination.size
    // })
    // tableData.value = response.data
    // total.value = response.total
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleSearchReset = () => {
  pagination.page = 1
  fetchData()
}

const handleAdd = () => {
  formData.value = {}
  dialogVisible.value = true
}

const handleEdit = (row: any) => {
  formData.value = { ...row }
  dialogVisible.value = true
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除"${row.name}"吗？`, '确认删除', {
      type: 'warning'
    })
    
    // await api.delete(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = async (data: any) => {
  submitLoading.value = true
  try {
    if (isEdit.value) {
      // await api.update(data.id, data)
      ElMessage.success('更新成功')
    } else {
      // await api.create(data)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    fetchData()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    submitLoading.value = false
  }
}

const handlePageChange = (page: number) => {
  pagination.page = page
  fetchData()
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchData()
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>
```

## TSX/JSX 组件模式

### 函数式组件 (推荐用于简单逻辑)
```tsx
import { defineComponent } from 'vue'

interface Props {
  title: string
  count: number
}

export default defineComponent<Props>((props) => {
  return () => (
    <div class="p-4 border rounded">
      <h3 class="text-lg font-bold">{props.title}</h3>
      <p class="text-gray-600">计数: {props.count}</p>
    </div>
  )
})
```

### 复杂条件渲染组件
```tsx
import { defineComponent, ref } from 'vue'
import type { PropType } from 'vue'

type Status = 'loading' | 'success' | 'error' | 'empty'

export default defineComponent({
  name: 'StatusRenderer',
  props: {
    status: {
      type: String as PropType<Status>,
      required: true
    },
    data: {
      type: Array as PropType<any[]>,
      default: () => []
    }
  },
  setup(props, { slots }) {
    const renderContent = () => {
      switch (props.status) {
        case 'loading':
          return (
            <div class="flex justify-center items-center h-32">
              <el-icon class="is-loading">
                <i class="i-mdi-loading"></i>
              </el-icon>
              <span class="ml-2">加载中...</span>
            </div>
          )
        
        case 'error':
          return (
            <div class="text-center py-8">
              <i class="i-mdi-alert-circle text-4xl text-red-500"></i>
              <p class="mt-2 text-gray-600">加载失败，请重试</p>
            </div>
          )
        
        case 'empty':
          return (
            <div class="text-center py-8">
              <i class="i-mdi-inbox text-4xl text-gray-400"></i>
              <p class="mt-2 text-gray-600">暂无数据</p>
            </div>
          )
        
        case 'success':
          return slots.default?.({ data: props.data }) || (
            <div>数据加载成功</div>
          )
        
        default:
          return null
      }
    }

    return () => (
      <div class="status-renderer">
        {renderContent()}
      </div>
    )
  }
})
```

### 动态表单组件 (TSX 适用场景)
```tsx
import { defineComponent, ref, reactive } from 'vue'
import type { PropType } from 'vue'

interface FormField {
  key: string
  label: string
  type: 'input' | 'select' | 'date' | 'textarea'
  options?: Array<{ label: string; value: any }>
  required?: boolean
  placeholder?: string
}

export default defineComponent({
  name: 'DynamicForm',
  props: {
    fields: {
      type: Array as PropType<FormField[]>,
      required: true
    },
    modelValue: {
      type: Object as PropType<Record<string, any>>,
      required: true
    }
  },
  emits: ['update:modelValue', 'submit'],
  setup(props, { emit }) {
    const formRef = ref()
    
    const updateValue = (key: string, value: any) => {
      emit('update:modelValue', {
        ...props.modelValue,
        [key]: value
      })
    }

    const renderField = (field: FormField) => {
      const commonProps = {
        modelValue: props.modelValue[field.key],
        'onUpdate:modelValue': (value: any) => updateValue(field.key, value),
        placeholder: field.placeholder
      }

      switch (field.type) {
        case 'input':
          return <el-input {...commonProps} />
        
        case 'textarea':
          return <el-input type="textarea" {...commonProps} />
        
        case 'select':
          return (
            <el-select {...commonProps}>
              {field.options?.map(option => (
                <el-option
                  key={option.value}
                  label={option.label}
                  value={option.value}
                />
              ))}
            </el-select>
          )
        
        case 'date':
          return <el-date-picker {...commonProps} />
        
        default:
          return <el-input {...commonProps} />
      }
    }

    const handleSubmit = () => {
      formRef.value?.validate((valid: boolean) => {
        if (valid) {
          emit('submit', props.modelValue)
        }
      })
    }

    return () => (
      <el-form ref={formRef} model={props.modelValue} label-width="120px">
        {props.fields.map(field => (
          <el-form-item
            key={field.key}
            label={field.label}
            prop={field.key}
            rules={field.required ? [{ required: true, message: `请输入${field.label}` }] : undefined}
          >
            {renderField(field)}
          </el-form-item>
        ))}
        
        <el-form-item>
          <el-button type="primary" onClick={handleSubmit}>
            提交
          </el-button>
        </el-form-item>
      </el-form>
    )
  }
})
```

### TSX vs SFC 选择指南

**使用 TSX 的场景：**
- 复杂的条件渲染逻辑
- 动态组件生成
- 需要更强的类型检查
- 函数式组件
- 大量的计算属性和方法

**使用 SFC (.vue) 的场景：**
- 简单的页面和组件
- 需要样式隔离 (scoped CSS)
- 模板逻辑相对简单
- 团队更熟悉模板语法

## 组件开发原则

1. **单一职责**: 每个组件只负责一个功能
2. **可复用性**: 通过props和slots提供灵活性
3. **类型安全**: 使用TypeScript定义明确的接口
4. **事件通信**: 使用emit传递事件，避免直接修改props
5. **插槽设计**: 提供命名插槽增强组件的可定制性
6. **暴露方法**: 使用defineExpose暴露必要的方法给父组件
7. **TSX/JSX 优先**: 复杂逻辑组件优先考虑 TSX/JSX 实现
