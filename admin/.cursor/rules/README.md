# Admin 后台管理系统 Cursor Rules

## 📚 规则概览

本项目包含以下 Cursor Rules，用于规范和指导 admin 后台管理系统的开发：

### 1. 🎯 `admin-development.mdc` - 核心开发规范
- **作用范围**: `src/**/*`
- **自动应用**: ✅ 总是激活
- **内容**: 
  - 技术栈要求（UnoCSS、Vue 3、Element Plus）
  - **TSX/JSX 支持**（复杂逻辑渲染场景）
  - 文件结构规范
  - 常用页面模式（表格页面、表单页面、TSX组件）
  - 图标使用指南
  - 性能优化建议

### 2. 🎨 `unocss-patterns.mdc` - UnoCSS 使用指南
- **作用范围**: `src/**/*.vue`, `src/**/*.ts`
- **激活方式**: 手动激活 `@unocss-patterns`
- **内容**:
  - 布局类（Flexbox、Grid）
  - 间距和尺寸系统
  - 颜色和主题
  - 响应式设计
  - 组件样式模式

### 3. 🧩 `component-patterns.mdc` - 组件开发模式
- **作用范围**: `src/components/**/*`, `src/views/**/*`
- **激活方式**: 手动激活 `@component-patterns`
- **内容**:
  - 基础组件模板（表格、表单、搜索）
  - **TSX/JSX 组件模式**（函数式组件、复杂条件渲染、动态表单）
  - CRUD页面模板
  - 组件开发原则
  - TypeScript 接口定义
  - **TSX vs SFC 选择指南**

### 4. 🔌 `api-and-state.mdc` - API 和状态管理
- **作用范围**: `src/api/**/*`, `src/stores/**/*`, `src/utils/**/*`
- **激活方式**: 手动激活 `@api-and-state`
- **内容**:
  - Axios 配置和拦截器
  - API 模块化设计
  - Pinia Store 模板
  - 组合式函数模式
  - 错误处理机制

## 🚀 如何使用

### 自动激活
- `admin-development.mdc` 会在编辑 `src/` 目录下的任何文件时自动激活

### 手动激活
在对话中提及规则名称来激活特定规则：
```
请参考 @unocss-patterns 来设计这个组件的样式
使用 @component-patterns 中的表格组件模板
按照 @api-and-state 的规范创建用户管理API
```

### 在 VS Code 中使用
1. 使用 `Cmd + Shift + P` 打开命令面板
2. 输入 "Cursor: New Rule" 查看或编辑规则
3. 在 Cursor 设置中管理规则状态

## 📋 开发工作流

### 创建新页面
1. 📖 参考 `admin-development.mdc` 中的页面组件结构
2. 🎨 使用 `unocss-patterns.mdc` 中的样式类
3. 🧩 基于 `component-patterns.mdc` 的 CRUD 模板

### 开发组件
1. 🧩 参考 `component-patterns.mdc` 中的组件模板
2. 🎨 使用 UnoCSS 原子类进行样式设计
3. 📝 遵循 TypeScript 接口定义规范

### API 集成
1. 🔌 按照 `api-and-state.mdc` 配置 Axios
2. 📦 使用模块化 API 设计
3. 🗃️ 创建对应的 Pinia Store

## 🛠 技术栈要求

根据规则配置，项目必须使用：

- ✅ **Vue 3** + Composition API
- ✅ **TypeScript** 类型安全
- ✅ **TSX/JSX** 支持（复杂逻辑场景）
- ✅ **UnoCSS** CSS 原子类
- ✅ **Element Plus** UI 组件库
- ✅ **Pinia** 状态管理
- ✅ **Vue Router** 路由管理

## 📝 代码风格

### 必须遵循
- 使用 `<script setup>` 语法（SFC）或 `defineComponent`（TSX）
- Props 和 Emits 使用 TypeScript 接口
- 组件名使用 PascalCase
- 文件名使用 kebab-case（.vue）或 PascalCase（.tsx/.jsx）
- 只使用 UnoCSS 类，禁用传统 CSS
- **TSX/JSX 文件使用 `.tsx` 或 `.jsx` 扩展名**

### 推荐模式
- 使用组合式函数封装逻辑
- API 调用统一错误处理
- 响应式设计优先
- 组件插槽设计增强灵活性
- **复杂逻辑组件优先考虑 TSX/JSX 实现**
- **简单页面和组件使用 Vue SFC**

## 🔄 规则更新

当需要更新开发规范时：
1. 编辑对应的 `.mdc` 文件
2. 规则会自动应用到新的开发对话中
3. 团队成员共享统一的开发标准

---

💡 **提示**: 这些规则帮助确保代码质量和开发效率，建议在开发过程中主动参考相关规则。 