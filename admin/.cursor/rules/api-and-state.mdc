---
description: API 和状态管理规范，包含 Axios 配置、API 模块化设计、Pinia Store 模板和组合式函数模式
globs: ["admin/src/api/**/*", "admin/src/stores/**/*", "admin/src/utils/**/*", "admin/src/composables/**/*"]
alwaysApply: false
---
---
description: API 和状态管理规范，包含 Axios 配置、API 模块化设计、Pinia Store 模板和组合式函数模式
globs: ["admin/src/api/**/*", "admin/src/stores/**/*", "admin/src/utils/**/*", "admin/src/composables/**/*"]
alwaysApply: false
---

# API调用和状态管理规范

## API调用规范

### Axios配置
```typescript
// src/utils/request.ts
import axios from 'axios'
import { ElMessage } from 'element-plus'

const request = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('admin_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    const { code, data, message } = response.data
    
    if (code === 200) {
      return data
    } else {
      ElMessage.error(message || '请求失败')
      return Promise.reject(new Error(message))
    }
  },
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('admin_token')
      window.location.href = '/login'
    } else {
      ElMessage.error(error.message || '网络错误')
    }
    return Promise.reject(error)
  }
)

export default request
```

### API模块化设计
```typescript
// src/api/types.ts
export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

export interface PaginationParams {
  page: number
  size: number
}

export interface PaginationResult<T> {
  list: T[]
  total: number
  page: number
  size: number
}

// src/api/user.ts
import request from '@/utils/request'
import type { User, CreateUserData, UpdateUserData } from '@/types/user'

export const userApi = {
  // 获取用户列表
  getList: (params: PaginationParams & { name?: string }) => {
    return request.get<PaginationResult<User>>('/users', { params })
  },

  // 获取用户详情
  getById: (id: number) => {
    return request.get<User>(`/users/${id}`)
  },

  // 创建用户
  create: (data: CreateUserData) => {
    return request.post<User>('/users', data)
  },

  // 更新用户
  update: (id: number, data: UpdateUserData) => {
    return request.put<User>(`/users/${id}`, data)
  },

  // 删除用户
  delete: (id: number) => {
    return request.delete(`/users/${id}`)
  }
}
```

### API组合式函数
```typescript
// src/composables/useApi.ts
export function useApi<T>(apiFunction: (...args: any[]) => Promise<T>) {
  const loading = ref(false)
  const error = ref<string | null>(null)
  const data = ref<T | null>(null)

  const execute = async (...args: any[]) => {
    loading.value = true
    error.value = null
    
    try {
      const result = await apiFunction(...args)
      data.value = result
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : '请求失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    loading: readonly(loading),
    error: readonly(error),
    data: readonly(data),
    execute
  }
}

// 使用示例
const { loading, error, data, execute } = useApi(userApi.getList)
```

## Pinia状态管理

### Store模板
```typescript
// src/stores/user.ts
import { defineStore } from 'pinia'
import { userApi } from '@/api/user'
import type { User } from '@/types/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const users = ref<User[]>([])
  const currentUser = ref<User | null>(null)
  const loading = ref(false)
  const total = ref(0)

  // Getters
  const activeUsers = computed(() => 
    users.value.filter(user => user.status === 'active')
  )

  const getUserById = computed(() => 
    (id: number) => users.value.find(user => user.id === id)
  )

  // Actions
  const fetchUsers = async (params?: any) => {
    loading.value = true
    try {
      const result = await userApi.getList(params)
      users.value = result.list
      total.value = result.total
    } catch (error) {
      console.error('获取用户列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const createUser = async (userData: CreateUserData) => {
    const user = await userApi.create(userData)
    users.value.push(user)
    return user
  }

  const updateUser = async (id: number, userData: UpdateUserData) => {
    const updatedUser = await userApi.update(id, userData)
    const index = users.value.findIndex(user => user.id === id)
    if (index !== -1) {
      users.value[index] = updatedUser
    }
    return updatedUser
  }

  const deleteUser = async (id: number) => {
    await userApi.delete(id)
    const index = users.value.findIndex(user => user.id === id)
    if (index !== -1) {
      users.value.splice(index, 1)
    }
  }

  const setCurrentUser = (user: User | null) => {
    currentUser.value = user
  }

  // 重置状态
  const $reset = () => {
    users.value = []
    currentUser.value = null
    loading.value = false
    total.value = 0
  }

  return {
    // 状态
    users: readonly(users),
    currentUser: readonly(currentUser),
    loading: readonly(loading),
    total: readonly(total),
    
    // Getters
    activeUsers,
    getUserById,
    
    // Actions
    fetchUsers,
    createUser,
    updateUser,
    deleteUser,
    setCurrentUser,
    $reset
  }
})
```

### 认证Store
```typescript
// src/stores/auth.ts
import { defineStore } from 'pinia'
import { authApi } from '@/api/auth'
import type { LoginData, UserInfo } from '@/types/auth'

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string | null>(localStorage.getItem('admin_token'))
  const userInfo = ref<UserInfo | null>(null)
  const permissions = ref<string[]>([])

  const isLoggedIn = computed(() => !!token.value)
  
  const hasPermission = computed(() => 
    (permission: string) => permissions.value.includes(permission)
  )

  const login = async (loginData: LoginData) => {
    const response = await authApi.login(loginData)
    token.value = response.token
    userInfo.value = response.userInfo
    permissions.value = response.permissions
    
    localStorage.setItem('admin_token', response.token)
  }

  const logout = () => {
    token.value = null
    userInfo.value = null
    permissions.value = []
    localStorage.removeItem('admin_token')
  }

  const getUserInfo = async () => {
    if (!token.value) return
    
    try {
      const response = await authApi.getUserInfo()
      userInfo.value = response.userInfo
      permissions.value = response.permissions
    } catch (error) {
      logout()
      throw error
    }
  }

  return {
    token: readonly(token),
    userInfo: readonly(userInfo),
    permissions: readonly(permissions),
    isLoggedIn,
    hasPermission,
    login,
    logout,
    getUserInfo
  }
})
```

## 组合式函数模式

### 表格数据管理
```typescript
// src/composables/useTable.ts
export function useTable<T>(
  apiFunction: (params: any) => Promise<PaginationResult<T>>,
  initialParams: Record<string, any> = {}
) {
  const loading = ref(false)
  const data = ref<T[]>([])
  const total = ref(0)
  const searchParams = ref({ ...initialParams })
  
  const pagination = reactive({
    page: 1,
    size: 10
  })

  const fetchData = async (resetPage = false) => {
    if (resetPage) {
      pagination.page = 1
    }
    
    loading.value = true
    try {
      const params = {
        ...searchParams.value,
        page: pagination.page,
        size: pagination.size
      }
      
      const result = await apiFunction(params)
      data.value = result.list
      total.value = result.total
    } catch (error) {
      console.error('获取数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  const handleSearch = (params: Record<string, any>) => {
    searchParams.value = { ...params }
    fetchData(true)
  }

  const handlePageChange = (page: number) => {
    pagination.page = page
    fetchData()
  }

  const handleSizeChange = (size: number) => {
    pagination.size = size
    fetchData(true)
  }

  const refresh = () => {
    fetchData()
  }

  return {
    loading: readonly(loading),
    data: readonly(data),
    total: readonly(total),
    pagination: readonly(pagination),
    searchParams,
    fetchData,
    handleSearch,
    handlePageChange,
    handleSizeChange,
    refresh
  }
}
```

### 表单管理
```typescript
// src/composables/useForm.ts
export function useForm<T extends Record<string, any>>(
  initialData: T,
  submitFunction: (data: T) => Promise<any>
) {
  const formData = ref<T>({ ...initialData })
  const loading = ref(false)
  const errors = ref<Record<string, string>>({})

  const resetForm = () => {
    formData.value = { ...initialData }
    errors.value = {}
  }

  const setData = (data: Partial<T>) => {
    formData.value = { ...formData.value, ...data }
  }

  const submit = async () => {
    loading.value = true
    errors.value = {}
    
    try {
      const result = await submitFunction(formData.value)
      return result
    } catch (error) {
      if (error.response?.data?.errors) {
        errors.value = error.response.data.errors
      }
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    formData,
    loading: readonly(loading),
    errors: readonly(errors),
    resetForm,
    setData,
    submit
  }
}
```

## 错误处理

### 全局错误处理
```typescript
// src/utils/errorHandler.ts
import { ElMessage } from 'element-plus'

export class ApiError extends Error {
  constructor(
    public code: number,
    public message: string,
    public data?: any
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

export const handleError = (error: unknown) => {
  if (error instanceof ApiError) {
    ElMessage.error(error.message)
  } else if (error instanceof Error) {
    ElMessage.error(error.message)
  } else {
    ElMessage.error('未知错误')
  }
  
  console.error('Error:', error)
}
```

## 类型定义

### API响应类型
```typescript
// src/types/api.ts
export interface BaseEntity {
  id: number
  createdAt: string
  updatedAt: string
}

export interface ApiListResponse<T> {
  list: T[]
  total: number
  page: number
  size: number
}

export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}
```
