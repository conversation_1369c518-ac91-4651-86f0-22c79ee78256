---
description:
globs:
alwaysApply: false
---
---
description: UnoCSS 使用指南，包含布局类、间距系统、颜色主题和响应式设计模式
globs: ["admin/src/**/*.vue", "admin/src/**/*.tsx", "admin/src/**/*.jsx"]
alwaysApply: false
---

# UnoCSS 使用模式指南

## 布局类

### Flexbox 布局
```html
<!-- 水平居中 -->
<div class="flex justify-center items-center">

<!-- 两端对齐 -->
<div class="flex justify-between items-center">

<!-- 垂直布局 -->
<div class="flex flex-col gap-4">

<!-- 响应式flex -->
<div class="flex flex-col md:flex-row">
```

### Grid 布局
```html
<!-- 基础网格 -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">

<!-- 自适应网格 -->
<div class="grid grid-cols-auto-fit-200 gap-4">
```

## 间距系统
```html
<!-- 内边距 -->
<div class="p-4">            <!-- 16px 全方向 -->
<div class="px-6 py-4">      <!-- 水平24px，垂直16px -->
<div class="pt-8 pb-4">      <!-- 顶部32px，底部16px -->

<!-- 外边距 -->
<div class="m-4">            <!-- 16px 全方向 -->
<div class="mb-6">           <!-- 底部24px -->
<div class="mt-auto">        <!-- 顶部自动 -->

<!-- 间隙 -->
<div class="flex gap-4">     <!-- flex间隙16px -->
<div class="grid gap-6">     <!-- grid间隙24px -->
```

## 尺寸系统
```html
<!-- 宽度 -->
<div class="w-full">         <!-- 100% -->
<div class="w-screen">       <!-- 100vw -->
<div class="w-64">           <!-- 256px -->
<div class="w-1/2">          <!-- 50% -->

<!-- 高度 -->
<div class="h-screen">       <!-- 100vh -->
<div class="h-64">           <!-- 256px -->
<div class="min-h-screen">   <!-- 最小100vh -->

<!-- 最大宽度 -->
<div class="max-w-7xl mx-auto"> <!-- 居中容器 -->
```

## 颜色系统
```html
<!-- 背景色 -->
<div class="bg-white">       <!-- 白色 -->
<div class="bg-gray-50">     <!-- 浅灰 -->
<div class="bg-blue-500">    <!-- 蓝色 -->
<div class="bg-primary-600"> <!-- 主题色 -->

<!-- 文字色 -->
<div class="text-gray-900">  <!-- 深灰文字 -->
<div class="text-blue-600">  <!-- 蓝色文字 -->
<div class="text-white">     <!-- 白色文字 -->

<!-- 边框色 -->
<div class="border border-gray-200"> <!-- 浅灰边框 -->
```

## 圆角和阴影
```html
<!-- 圆角 -->
<div class="rounded">        <!-- 4px -->
<div class="rounded-lg">     <!-- 8px -->
<div class="rounded-full">   <!-- 圆形 -->

<!-- 阴影 -->
<div class="shadow">         <!-- 基础阴影 -->
<div class="shadow-lg">      <!-- 大阴影 -->
<div class="shadow-none">    <!-- 无阴影 -->
```

## 交互状态
```html
<!-- 悬停状态 -->
<button class="hover:bg-blue-600 hover:text-white">

<!-- 焦点状态 -->
<input class="focus:ring-2 focus:ring-blue-500 focus:border-blue-500">

<!-- 禁用状态 -->
<button class="disabled:opacity-50 disabled:cursor-not-allowed">

<!-- 活跃状态 -->
<button class="active:bg-blue-700">
```

## 响应式断点
```html
<!-- 移动优先设计 -->
<div class="text-sm md:text-base lg:text-lg">

<!-- 隐藏/显示 -->
<div class="hidden md:block">     <!-- 移动端隐藏，桌面端显示 -->
<div class="block md:hidden">     <!-- 移动端显示，桌面端隐藏 -->

<!-- 网格响应式 -->
<div class="grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
```

## 表单样式
```html
<!-- 输入框 -->
<input class="form-input" />

<!-- 自定义输入框 -->
<input class="w-full px-3 py-2 border border-gray-300 rounded-md 
              focus:outline-none focus:ring-2 focus:ring-blue-500" />

<!-- 标签 -->
<label class="form-label">标签文字</label>
```

## 按钮样式
```html
<!-- 使用快捷类 -->
<button class="btn">基础按钮</button>
<button class="btn-primary">主要按钮</button>
<button class="btn-secondary">次要按钮</button>

<!-- 自定义按钮 -->
<button class="px-4 py-2 bg-blue-600 text-white rounded-md 
               hover:bg-blue-700 transition-colors">
  自定义按钮
</button>
```

## 卡片样式
```html
<!-- 使用快捷类 -->
<div class="card">
  <div class="card-header">
    <h3>标题</h3>
  </div>
  <div class="card-body">
    内容
  </div>
</div>

<!-- 自定义卡片 -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
  <div class="px-6 py-4 border-b border-gray-200">
    标题区域
  </div>
  <div class="px-6 py-4">
    内容区域
  </div>
</div>
```

## 动画和过渡
```html
<!-- 过渡效果 -->
<div class="transition-all duration-300 ease-in-out">

<!-- 变换 -->
<div class="transform hover:scale-105">

<!-- 透明度 -->
<div class="opacity-0 hover:opacity-100 transition-opacity">
```

## 图标使用
```html
<!-- Material Design Icons -->
<i class="i-mdi-home text-lg"></i>
<i class="i-mdi-user text-blue-600"></i>

<!-- Carbon Icons -->
<i class="i-carbon-settings text-gray-500"></i>

<!-- Tabler Icons -->
<i class="i-tabler-search text-sm"></i>
```

## 实用工具类
```html
<!-- 文字截断 -->
<div class="truncate">长文本会被截断...</div>

<!-- 滚动 -->
<div class="overflow-auto">
<div class="overflow-hidden">

<!-- 定位 -->
<div class="relative">
<div class="absolute top-0 right-0">

<!-- 层级 -->
<div class="z-10">
<div class="z-50">
