# 蔚之领域管理后台

江苏蔚之领域智能科技有限公司企业官网管理后台系统。

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **UI组件库**: Element Plus
- **CSS框架**: UnoCSS
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **构建工具**: Vite

## 项目结构

```
admin/
├── src/
│   ├── api/                 # API接口模块
│   │   ├── auth.ts         # 认证相关API
│   │   ├── admin-users.ts  # 管理员用户API
│   │   ├── roles.ts        # 角色管理API
│   │   ├── permissions.ts  # 权限管理API
│   │   └── admin-logs.ts   # 操作日志API
│   ├── components/         # 公共组件
│   ├── composables/        # 组合式函数
│   │   └── useAdminApi.ts  # Admin API组合函数
│   ├── layout/             # 布局组件
│   │   ├── index.vue       # 主布局
│   │   └── components/     # 布局子组件
│   ├── router/             # 路由配置
│   ├── stores/             # Pinia状态管理
│   │   ├── auth.ts         # 认证状态
│   │   ├── app.ts          # 应用状态
│   │   └── index.ts        # 统一导出
│   ├── types/              # TypeScript类型定义
│   │   └── api.ts          # API相关类型
│   ├── utils/              # 工具函数
│   │   ├── request.ts      # Axios封装
│   │   └── errorHandler.ts # 错误处理
│   ├── views/              # 页面组件
│   │   ├── login/          # 登录页面
│   │   ├── dashboard/      # 仪表盘
│   │   ├── admin/          # 管理员管理
│   │   ├── content/        # 内容管理
│   │   ├── users/          # 用户管理
│   │   └── settings/       # 系统设置
│   └── style/              # 样式文件
├── public/                 # 静态资源
│   ├── logo.svg           # 主Logo
│   ├── logo-mini.svg      # 迷你Logo
│   └── favicon.ico        # 网站图标
└── package.json
```

## 功能特性

### 🔐 认证系统
- 用户登录/登出
- 自动token刷新
- 权限验证
- 路由守卫
- 记住我功能
- 验证码支持（失败次数过多时）

### 👥 管理员管理
- 管理员用户CRUD
- 角色分配
- 权限管理
- 密码重置
- 状态管理

### 📊 系统功能
- 仪表盘数据展示
- 操作日志记录
- 系统设置
- 内容管理
- 用户管理

## 开发指南

### 安装依赖

```bash
pnpm install
```

### 启动开发服务器

```bash
pnpm dev
```

### 构建生产版本

```bash
pnpm build
```

### 预览生产版本

```bash
pnpm preview
```

## 登录系统使用说明

### 1. 登录流程

1. 用户访问需要认证的页面时，会自动重定向到登录页面
2. 输入用户名和密码进行登录
3. 登录成功后自动跳转到目标页面或首页
4. 系统会自动保存认证状态和用户信息

### 2. 开发环境快速登录

在开发环境下，登录页面提供快速登录按钮：

- **超级管理员**: `admin` / `admin123`
- **普通管理员**: `manager` / `manager123`
- **测试API**: 检查后端服务连接状态

### 3. 权限控制

系统支持基于角色的权限控制：

```typescript
// 检查单个权限
authStore.hasPermission('admin:user:create')

// 检查多个权限（任一满足）
authStore.hasAnyPermission(['admin:user:create', 'admin:user:edit'])

// 检查多个权限（全部满足）
authStore.hasAllPermissions(['admin:user:create', 'admin:user:edit'])
```

### 4. 路由权限

在路由配置中设置权限要求：

```typescript
{
  path: '/admin/users',
  component: AdminUsers,
  meta: {
    title: '管理员用户',
    requiresAuth: true,
    permissions: ['admin:user:list']
  }
}
```

### 5. API调用

使用封装的API模块进行数据请求：

```typescript
import { authApi } from '@/api/auth'

// 登录
const response = await authApi.login({ username, password })

// 获取用户信息
const userInfo = await authApi.getUserInfo()

// 修改密码
await authApi.changePassword({ oldPassword, newPassword })
```

### 6. 状态管理

使用Pinia进行状态管理：

```typescript
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// 检查登录状态
if (authStore.isLoggedIn) {
  // 已登录
}

// 获取用户信息
const userInfo = authStore.userInfo

// 登出
await authStore.logout()
```

## 错误处理

系统提供完善的错误处理机制：

- **网络错误**: 自动重试和提示
- **认证错误**: 自动跳转登录页
- **权限错误**: 友好的权限提示
- **表单验证**: 实时验证和错误提示

## 开发规范

### 代码风格
- 使用TypeScript进行类型检查
- 遵循Vue 3 Composition API规范
- 使用UnoCSS进行样式开发
- 组件命名使用PascalCase

### API规范
- 统一的错误处理
- 类型安全的接口定义
- 模块化的API组织
- 自动的认证token处理

### 组件开发
- 使用`<script setup>`语法
- 合理使用组合式函数
- 提供完整的TypeScript类型
- 遵循单一职责原则

## 环境配置

### 环境变量文件

项目提供了完整的环境变量配置：

```bash
env.example          # 配置示例文件
.env.development     # 开发环境配置（已创建）
.env.production      # 生产环境配置（已创建）
```

### 开发环境配置

开发环境配置文件 `.env.development` 主要配置项：

```bash
# API 配置 - 指向本地后端服务
VITE_API_BASE_URL=http://localhost:3000/api

# 应用信息
VITE_APP_TITLE=蔚之领域智能科技 - 管理系统
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=development

# 开发模式
VITE_DEBUG=true

# 服务器配置
VITE_DEV_SERVER_PORT=3001
VITE_DEV_SERVER_HOST=localhost

# 后端服务配置
VITE_BACKEND_URL=http://localhost:3000
```

### 生产环境配置

生产环境需要修改 `.env.production` 中的相关配置：

```bash
# 生产环境 API 地址
VITE_API_BASE_URL=https://your-api-domain.com/api
VITE_APP_ENV=production
VITE_DEBUG=false
VITE_IMAGE_BASE_URL=https://your-cdn-domain.com
```

### 跨域处理

开发环境通过 Vite 代理自动解决跨域问题：

```typescript
// vite.config.ts
server: {
  port: 3001,
  proxy: {
    '/api': {
      target: 'http://localhost:3000',  // 后端服务地址
      changeOrigin: true,               // 修改请求头中的 Origin
    },
  },
}
```

这样配置后：
- 前端运行在 `http://localhost:3001`
- 后端运行在 `http://localhost:3000`
- API 请求 `/api/*` 会自动代理到后端服务

## 部署说明

### 构建配置

项目使用Vite进行构建，支持：
- 代码分割
- 资源优化
- 环境变量注入
- TypeScript编译

## 常见问题

### Q: 登录后页面空白？
A: 检查后端服务是否正常运行，确认API接口可访问。

### Q: 权限验证不生效？
A: 确认用户角色和权限配置正确，检查路由meta配置。

### Q: 开发环境API连接失败？
A: 检查后端服务端口，确认代理配置正确。

## 相关文档

- [项目文档中心](../docs/readme.md) - 完整的项目文档
- [开发环境配置](../docs/development/01-environment-setup.md) - 开发环境搭建
- [代码规范](../docs/development/02-coding-standards.md) - 代码规范和最佳实践
- [TSX/JSX指南](../docs/development/06-tsx-jsx-guide.md) - TSX/JSX开发指南

## 联系方式

如有问题请联系开发团队或查看项目文档。