{"name": "weizhi-admin", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit -p tsconfig.json --composite false"}, "dependencies": {"@element-plus/icons-vue": "^2.3.0", "axios": "^1.6.0", "echarts": "^5.4.0", "element-plus": "^2.4.0", "pinia": "^2.1.0", "vue": "^3.4.0", "vue-echarts": "^6.6.0", "vue-router": "^4.2.0"}, "devDependencies": {"@iconify-json/carbon": "^1.2.8", "@iconify-json/mdi": "^1.2.3", "@iconify-json/tabler": "^1.2.18", "@types/node": "^20.0.0", "@unocss/preset-attributify": "66.1.0-beta.7", "@unocss/preset-icons": "66.1.0-beta.7", "@unocss/preset-uno": "66.1.0-beta.3", "@vitejs/plugin-vue": "^4.5.0", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "eslint": "^8.0.0", "eslint-plugin-vue": "^9.0.0", "prettier": "^3.0.0", "typescript": "^5.0.0", "unocss": "66.1.0-beta.3", "unplugin-auto-import": "^0.16.0", "unplugin-vue-components": "^0.25.0", "vite": "^5.0.0", "vue-tsc": "^1.8.0"}, "packageManager": "pnpm@10.6.1+sha512.40ee09af407fa9fbb5fbfb8e1cb40fbb74c0af0c3e10e9224d7b53c7658528615b2c92450e74cfad91e3a2dcafe3ce4050d80bda71d757756d2ce2b66213e9a3"}