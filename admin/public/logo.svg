<svg width="160" height="40" viewBox="0 0 160 40" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="160" height="40" rx="6" fill="url(#logoGradient)"/>
  
  <!-- 图标部分 -->
  <g transform="translate(8, 8)">
    <!-- 外圆 -->
    <circle cx="12" cy="12" r="10" fill="none" stroke="white" stroke-width="1.5"/>
    <!-- 内部科技元素 -->
    <path d="M8 12 L12 8 L16 12 L12 16 Z" fill="white" opacity="0.9"/>
    <circle cx="12" cy="12" r="2" fill="white"/>
    <!-- 科技线条 -->
    <path d="M4 12 L8 12 M16 12 L20 12 M12 4 L12 8 M12 16 L12 20" stroke="white" stroke-width="1" opacity="0.7"/>
  </g>
  
  <!-- 文字部分 -->
  <text x="36" y="26" font-family="'Microsoft YaHei', sans-serif" font-size="14" font-weight="600" fill="white">
    蔚之领域
  </text>
</svg> 