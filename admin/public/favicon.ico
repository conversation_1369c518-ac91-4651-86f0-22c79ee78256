<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="faviconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="32" height="32" rx="6" fill="url(#faviconGradient)"/>
  
  <!-- 图标元素 -->
  <g transform="translate(16, 16)">
    <!-- 外圆 -->
    <circle cx="0" cy="0" r="8" fill="none" stroke="white" stroke-width="1.2"/>
    <!-- 内部科技元素 -->
    <path d="M-4 0 L0 -4 L4 0 L0 4 Z" fill="white" opacity="0.9"/>
    <circle cx="0" cy="0" r="1.5" fill="white"/>
    <!-- 科技线条 -->
    <path d="M-8 0 L-4 0 M4 0 L8 0 M0 -8 L0 -4 M0 4 L0 8" stroke="white" stroke-width="0.8" opacity="0.7"/>
  </g>
</svg> 