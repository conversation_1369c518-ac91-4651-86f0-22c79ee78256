import router from '@/router'
import type { AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import axios from 'axios'
import { ElMessage } from 'element-plus'

// API响应接口
export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
  success: boolean
}

// 分页参数接口
export interface PaginationParams {
  page: number
  size: number
}

// 分页结果接口
export interface PaginationResult<T> {
  list: T[]
  total: number
  page: number
  size: number
}

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  },
  // 全局参数序列化器
  paramsSerializer: (params) => {
    const searchParams = new URLSearchParams()
    Object.keys(params || {}).forEach(key => {
      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
        searchParams.append(key, params[key])
      }
    })
    return searchParams.toString()
  }
})

// 创建一个处理认证错误的函数
const handleAuthError = async () => {
  console.log('🚨 [Request] 处理认证错误')

  // 动态导入 useAuthStore 避免循环依赖
  const { useAuthStore } = await import('@/stores/auth')
  const authStore = useAuthStore()

  // 清除所有认证状态
  console.log('🧹 [Request] 清除认证状态')
  authStore.clearAuthState()

  // 显示错误消息
  ElMessage({
    message: '登录已过期，请重新登录',
    type: 'error',
    duration: 3000,
    showClose: true
  })

  // 使用 Vue Router 进行跳转
  const currentRoute = router.currentRoute.value
  console.log('🔄 [Request] 当前路由:', currentRoute.path)
  if (currentRoute.path !== '/login') {
    console.log('➡️ [Request] 跳转到登录页面')
    router.push({
      path: '/login',
      query: { redirect: currentRoute.fullPath }
    })
  } else {
    console.log('⚠️ [Request] 已在登录页面，不重复跳转')
  }
}

// 请求拦截器
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 添加认证token
    const token = localStorage.getItem('admin_token')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加时间戳防止浏览器缓存
    if (config.method === 'get') {
      // 处理嵌套的params对象
      let params = config.params || {}
      
      // 如果params中有params属性，说明参数被嵌套了，需要展开
      if (params.params && typeof params.params === 'object') {
        params = params.params
      }
      
      config.params = {
        ...params,
        _t: Date.now()
      }
    }
    
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response

    // 检查业务状态码
    if (data.success || data.code === 200) {
      return data.data
    } else {
      // 检查是否是认证错误（业务状态码401）
      if (data.code === 401) {
        console.log('🔐 [Request] 收到业务状态码401，处理认证错误')
        handleAuthError()
        return Promise.reject(new Error(data.message || 'token无效或已过期'))
      }

      // 其他业务错误
      const errorMessage = data.message || '请求失败'
      ElMessage({
        message: errorMessage,
        type: 'error',
        duration: 6000, // 增加错误提示显示时间到6秒
        showClose: true
      })
      return Promise.reject(new Error(errorMessage))
    }
  },
  (error) => {
    // HTTP错误处理
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 认证错误，统一处理
          console.log('🔐 [Request] 收到401响应，处理认证错误')
          handleAuthError()
          break
        case 403:
          ElMessage({
            message: '没有权限访问该资源',
            type: 'error',
            duration: 6000,
            showClose: true
          })
          break
        case 404:
          ElMessage({
            message: '请求的资源不存在',
            type: 'error',
            duration: 6000,
            showClose: true
          })
          break
        case 500:
          ElMessage({
            message: '服务器内部错误',
            type: 'error',
            duration: 8000,
            showClose: true
          })
          break
        default:
          ElMessage({
            message: data?.message || `请求失败 (${status})`,
            type: 'error',
            duration: 6000,
            showClose: true
          })
      }
    } else if (error.request) {
      ElMessage({
        message: '网络连接失败，请检查网络',
        type: 'error',
        duration: 8000,
        showClose: true
      })
    } else {
      ElMessage({
        message: error.message || '请求配置错误',
        type: 'error',
        duration: 6000,
        showClose: true
      })
    }
    
    console.error('响应拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 请求方法封装
export const http = {
  get: <T = any>(url: string, params?: any): Promise<T> => {
    return request.get(url, { params })
  },
  
  post: <T = any>(url: string, data?: any): Promise<T> => {
    return request.post(url, data)
  },
  
  put: <T = any>(url: string, data?: any): Promise<T> => {
    return request.put(url, data)
  },
  
  delete: <T = any>(url: string): Promise<T> => {
    return request.delete(url)
  },
  
  patch: <T = any>(url: string, data?: any): Promise<T> => {
    return request.patch(url, data)
  }
}

export default request 