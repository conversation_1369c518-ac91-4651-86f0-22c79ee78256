import { ElMessage, ElNotification } from 'element-plus'

// 自定义API错误类
export class ApiError extends Error {
  constructor(
    public code: number,
    public message: string,
    public data?: any
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

// 网络错误类
export class NetworkError extends Error {
  constructor(message: string = '网络连接失败') {
    super(message)
    this.name = 'NetworkError'
  }
}

// 权限错误类
export class PermissionError extends Error {
  constructor(message: string = '没有权限访问') {
    super(message)
    this.name = 'PermissionError'
  }
}

// 错误处理函数
export const handleError = (error: unknown, showNotification = false) => {
  let message = '未知错误'
  
  if (error instanceof ApiError) {
    message = error.message
  } else if (error instanceof NetworkError) {
    message = error.message
  } else if (error instanceof PermissionError) {
    message = error.message
  } else if (error instanceof Error) {
    message = error.message
  } else if (typeof error === 'string') {
    message = error
  }
  
  // 显示错误消息
  if (showNotification) {
    ElNotification.error({
      title: '错误',
      message,
      duration: 5000
    })
  } else {
    ElMessage.error(message)
  }
  
  // 记录错误日志
  console.error('Error:', error)
}

// 成功消息处理
export const handleSuccess = (message: string, showNotification = false) => {
  if (showNotification) {
    ElNotification.success({
      title: '成功',
      message,
      duration: 3000
    })
  } else {
    ElMessage.success(message)
  }
}

// 警告消息处理
export const handleWarning = (message: string, showNotification = false) => {
  if (showNotification) {
    ElNotification.warning({
      title: '警告',
      message,
      duration: 4000
    })
  } else {
    ElMessage.warning(message)
  }
}

// 信息消息处理
export const handleInfo = (message: string, showNotification = false) => {
  if (showNotification) {
    ElNotification.info({
      title: '提示',
      message,
      duration: 3000
    })
  } else {
    ElMessage.info(message)
  }
} 