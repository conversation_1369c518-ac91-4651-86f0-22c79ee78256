/**
 * 日期格式化工具函数
 */

export type DateInput = string | number | Date | null | undefined

/**
 * 格式化日期时间
 * @param date 日期输入
 * @param format 格式类型
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: DateInput, format: 'datetime' | 'date' | 'time' = 'datetime'): string => {
  if (!date) return '-'
  
  const dateObj = new Date(date)
  if (isNaN(dateObj.getTime())) return '-'
  
  const options: Intl.DateTimeFormatOptions = {
    timeZone: 'Asia/Shanghai'
  }
  
  switch (format) {
    case 'datetime':
      Object.assign(options, {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
      break
    case 'date':
      Object.assign(options, {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
      break
    case 'time':
      Object.assign(options, {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
      break
  }
  
  return dateObj.toLocaleString('zh-CN', options)
}

/**
 * 格式化相对时间（如：刚刚、5分钟前、1小时前等）
 * @param date 日期输入
 * @returns 相对时间字符串
 */
export const formatRelativeTime = (date: DateInput): string => {
  if (!date) return '-'
  
  const dateObj = new Date(date)
  if (isNaN(dateObj.getTime())) return '-'
  
  const now = new Date()
  const diff = now.getTime() - dateObj.getTime()
  
  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚'
  }
  
  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    const minutes = Math.floor(diff / (60 * 1000))
    return `${minutes}分钟前`
  }
  
  // 小于24小时
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000))
    return `${hours}小时前`
  }
  
  // 小于7天
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const days = Math.floor(diff / (24 * 60 * 60 * 1000))
    return `${days}天前`
  }
  
  // 超过7天显示具体日期
  return formatDate(date, 'date')
}

/**
 * 检查日期是否有效
 * @param date 日期输入
 * @returns 是否为有效日期
 */
export const isValidDate = (date: DateInput): boolean => {
  if (!date) return false
  const dateObj = new Date(date)
  return !isNaN(dateObj.getTime())
}

/**
 * 获取日期范围文本
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 日期范围文本
 */
export const formatDateRange = (startDate: DateInput, endDate: DateInput): string => {
  const start = formatDate(startDate, 'date')
  const end = formatDate(endDate, 'date')
  
  if (start === '-' || end === '-') return '-'
  if (start === end) return start
  
  return `${start} 至 ${end}`
}

/**
 * 格式化文件大小显示的时间戳
 * @param date 日期输入
 * @returns 简化的时间显示
 */
export const formatSimpleDate = (date: DateInput): string => {
  if (!date) return '-'
  
  const dateObj = new Date(date)
  if (isNaN(dateObj.getTime())) return '-'
  
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const dateDay = new Date(dateObj.getFullYear(), dateObj.getMonth(), dateObj.getDate())
  
  // 今天显示时间
  if (dateDay.getTime() === today.getTime()) {
    return formatDate(date, 'time')
  }
  
  // 今年显示月-日
  if (dateObj.getFullYear() === now.getFullYear()) {
    return dateObj.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit'
    })
  }
  
  // 其他年份显示年-月-日
  return formatDate(date, 'date')
} 