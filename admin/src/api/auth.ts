import type { AdminUser, LoginData, LoginResponse } from '@/types/api';
import { http } from '@/utils/request';

export const authApi = {
  // 登录
  login: (data: LoginData): Promise<LoginResponse> => {
    return http.post('/admin/auth/login', data)
  },

  // 获取验证码
  getCaptcha: (): Promise<{ captchaId: string; captchaUrl: string }> => {
    return http.get('/admin/auth/captcha').then((response: any) => {
      // 适配Go后端返回格式: { code: 200, data: { id: "...", image: "4 - 2 = ?" }, msg: "success" }
      if (response && response.id && response.image) {
        return {
          captchaId: response.id,
          captchaUrl: `data:text/plain;charset=utf-8,${encodeURIComponent(response.image)}`
        }
      }
      
      // 如果数据格式不对，抛出错误
      throw new Error('验证码数据格式错误')
    })
  },

  // 获取用户信息 - 后端返回用户信息和权限
  getUserInfo: (): Promise<{ userInfo: AdminUser; permissions: string[] }> => {
    return http.get('/admin/auth/profile')
  },

  // 退出登录
  logout: (): Promise<void> => {
    return http.post('/admin/auth/logout')
  },

  // 更新个人信息
  updateProfile: (data: Partial<AdminUser>): Promise<AdminUser> => {
    return http.put('/admin/profile', data)
  },

  // 修改密码
  changePassword: (data: { oldPassword: string; newPassword: string }): Promise<void> => {
    return http.post('/admin/profile/change-password', data)
  }
} 