import type {
    CreateFriendLinkData,
    CreateNewsData,
    CreatePartnerData,
    CreatePartPlatformData,
    CreatePartPlatformExtensionData,
    CreatePartPlatformTableColumnData,
    CreatePartPlatformTableData,
    CreatePartPlatformTableRowData,
    CreateProjectCaseData,
    CreateRecruitmentData,
    CreateSwiperData,
    FriendLink,
    News,
    PaginatedResponse,
    PaginationParams,
    Partner,
    PartPlatform,
    PartPlatformExtension,
    PartPlatformTable,
    PartPlatformTableColumn,
    PartPlatformTableRow,
    ProjectCase,
    Recruitment,
    Swiper,
    UpdateFriendLinkData,
    UpdateNewsData,
    UpdatePartnerData,
    UpdatePartPlatformData,
    UpdatePartPlatformExtensionData,
    UpdatePartPlatformTableColumnData,
    UpdatePartPlatformTableData,
    UpdatePartPlatformTableRowData,
    UpdateProjectCaseData,
    UpdateRecruitmentData,
    UpdateSwiperData
} from '@/types/api'
import { http } from '@/utils/request'

// 轮播图 API
export const swiperApi = {
  getList: (params?: PaginationParams & { keyword?: string }): Promise<PaginatedResponse<Swiper>> => {
    return http.get('/swipers', params)
  },
  
  getById: (id: number): Promise<Swiper> => {
    return http.get(`/swipers/${id}`)
  },
  
  create: (data: CreateSwiperData): Promise<Swiper> => {
    return http.post('/swipers', data)
  },
  
  update: (id: number, data: UpdateSwiperData): Promise<Swiper> => {
    return http.put(`/swipers/${id}`, data)
  },
  
  delete: (id: number): Promise<void> => {
    return http.delete(`/swipers/${id}`)
  },

  updateOrder: (id: number, order: number): Promise<Swiper> => {
    return http.patch(`/swipers/${id}/order`, { order })
  }
}

// 新闻 API
export const newsApi = {
  getList: (params?: PaginationParams & { 
    keyword?: string
    isHomePage?: boolean 
  }): Promise<PaginatedResponse<News>> => {
    return http.get('/news', params)
  },
  
  getById: (id: number): Promise<News> => {
    return http.get(`/news/${id}`)
  },
  
  create: (data: CreateNewsData): Promise<News> => {
    return http.post('/news', data)
  },
  
  update: (id: number, data: UpdateNewsData): Promise<News> => {
    return http.put(`/news/${id}`, data)
  },
  
  delete: (id: number): Promise<void> => {
    return http.delete(`/news/${id}`)
  },

  setHomePage: (id: number, isHomePage: boolean): Promise<News> => {
    return http.patch(`/news/${id}/home-page`, { isHomePage })
  }
}



// 项目案例 API
export const projectCaseApi = {
  getList: (params?: PaginationParams & { keyword?: string }): Promise<PaginatedResponse<ProjectCase>> => {
    return http.get('/project-cases', params)
  },
  
  getById: (id: number): Promise<ProjectCase> => {
    return http.get(`/project-cases/${id}`)
  },
  
  create: (data: CreateProjectCaseData): Promise<ProjectCase> => {
    return http.post('/project-cases', data)
  },
  
  update: (id: number, data: UpdateProjectCaseData): Promise<ProjectCase> => {
    return http.put(`/project-cases/${id}`, data)
  },
  
  delete: (id: number): Promise<void> => {
    return http.delete(`/project-cases/${id}`)
  },

  updateSort: (id: number, sort: number): Promise<ProjectCase> => {
    return http.patch(`/project-cases/${id}/sort`, { sort })
  }
}

// 合作伙伴 API
export const partnerApi = {
  getList: (params?: PaginationParams & { keyword?: string }): Promise<PaginatedResponse<Partner>> => {
    return http.get('/partners', params)
  },
  
  getById: (id: number): Promise<Partner> => {
    return http.get(`/partners/${id}`)
  },
  
  create: (data: CreatePartnerData): Promise<Partner> => {
    return http.post('/partners', data)
  },
  
  update: (id: number, data: UpdatePartnerData): Promise<Partner> => {
    return http.put(`/partners/${id}`, data)
  },
  
  delete: (id: number): Promise<void> => {
    return http.delete(`/partners/${id}`)
  }
}

// 友情链接 API
export const friendLinkApi = {
  getList: (params?: PaginationParams & { keyword?: string }): Promise<PaginatedResponse<FriendLink>> => {
    return http.get('/friendlinks', params)
  },
  
  getById: (id: number): Promise<FriendLink> => {
    return http.get(`/friendlinks/${id}`)
  },
  
  create: (data: CreateFriendLinkData): Promise<FriendLink> => {
    return http.post('/friendlinks', data)
  },
  
  update: (id: number, data: UpdateFriendLinkData): Promise<FriendLink> => {
    return http.put(`/friendlinks/${id}`, data)
  },
  
  delete: (id: number): Promise<void> => {
    return http.delete(`/friendlinks/${id}`)
  },

  updateOrder: (id: number, order: number): Promise<FriendLink> => {
    return http.patch(`/friendlinks/${id}/order`, { order })
  }
}

// 招聘信息 API
export const recruitmentApi = {
  getList: (params?: PaginationParams & { 
    keyword?: string
    location?: string 
  }): Promise<PaginatedResponse<Recruitment>> => {
    return http.get('/recruitments', params)
  },
  
  getById: (id: number): Promise<Recruitment> => {
    return http.get(`/recruitments/${id}`)
  },
  
  create: (data: CreateRecruitmentData): Promise<Recruitment> => {
    return http.post('/recruitments', data)
  },
  
  update: (id: number, data: UpdateRecruitmentData): Promise<Recruitment> => {
    return http.put(`/recruitments/${id}`, data)
  },
  
  delete: (id: number): Promise<void> => {
    return http.delete(`/recruitments/${id}`)
  },

  updateOrder: (id: number, order: number): Promise<Recruitment> => {
    return http.patch(`/recruitments/${id}/order`, { order })
  }
}

// 平台 API
export const partPlatformApi = {
  getList: (params?: PaginationParams & { keyword?: string }): Promise<PaginatedResponse<PartPlatform>> => {
    return http.get('/part-platforms', params)
  },
  
  getById: (id: number): Promise<PartPlatform> => {
    return http.get(`/part-platforms/${id}`)
  },
  
  create: (data: CreatePartPlatformData): Promise<PartPlatform> => {
    return http.post('/part-platforms', data)
  },
  
  update: (id: number, data: UpdatePartPlatformData): Promise<PartPlatform> => {
    return http.put(`/part-platforms/${id}`, data)
  },
  
  delete: (id: number): Promise<void> => {
    return http.delete(`/part-platforms/${id}`)
  },

  updateOrder: (id: number, order: number): Promise<PartPlatform> => {
    return http.patch(`/part-platforms/${id}/order`, { order })
  }
}

// 平台扩展数据 API
export const partPlatformExtensionApi = {
  getByPlatformId: (platformId: number): Promise<PartPlatformExtension[]> => {
    return http.get(`/part-platform-extensions/platform/${platformId}`)
  },

  create: (data: CreatePartPlatformExtensionData): Promise<PartPlatformExtension> => {
    return http.post('/part-platform-extensions', data)
  },

  update: (id: number, data: UpdatePartPlatformExtensionData): Promise<PartPlatformExtension> => {
    return http.put(`/part-platform-extensions/${id}`, data)
  },

  delete: (id: number): Promise<void> => {
    return http.delete(`/part-platform-extensions/${id}`)
  }
}

// 平台表格 API
export const partPlatformTableApi = {
  create: (data: CreatePartPlatformTableData): Promise<PartPlatformTable> => {
    return http.post('/part-platform-tables', data)
  },

  update: (id: number, data: UpdatePartPlatformTableData): Promise<PartPlatformTable> => {
    return http.put(`/part-platform-tables/${id}`, data)
  },

  delete: (id: number): Promise<void> => {
    return http.delete(`/part-platform-tables/${id}`)
  }
}

// 平台表格列 API
export const partPlatformTableColumnApi = {
  getByTableId: (tableId: number): Promise<PartPlatformTableColumn[]> => {
    return http.get(`/part-platform-table-columns/table/${tableId}`)
  },

  create: (data: CreatePartPlatformTableColumnData): Promise<PartPlatformTableColumn> => {
    return http.post('/part-platform-table-columns', data)
  },

  update: (id: number, data: UpdatePartPlatformTableColumnData): Promise<PartPlatformTableColumn> => {
    return http.put(`/part-platform-table-columns/${id}`, data)
  },

  delete: (id: number): Promise<void> => {
    return http.delete(`/part-platform-table-columns/${id}`)
  }
}

// 平台表格行 API
export const partPlatformTableRowApi = {
  getByTableId: (tableId: number): Promise<PartPlatformTableRow[]> => {
    return http.get(`/part-platform-table-rows/table/${tableId}`)
  },

  create: (data: CreatePartPlatformTableRowData): Promise<PartPlatformTableRow> => {
    return http.post('/part-platform-table-rows', data)
  },

  update: (id: number, data: UpdatePartPlatformTableRowData): Promise<PartPlatformTableRow> => {
    return http.put(`/part-platform-table-rows/${id}`, data)
  },

  delete: (id: number): Promise<void> => {
    return http.delete(`/part-platform-table-rows/${id}`)
  }
}