import type {
    AdminLog,
    AdminLogStatistics,
    CreateAdminLogData,
    PaginatedResponse,
    QueryAdminLogParams
} from '@/types/api'
import { http } from '@/utils/request'

export const adminLogsApi = {
  // 获取操作日志列表
  getLogs: (params?: QueryAdminLogParams): Promise<PaginatedResponse<AdminLog>> => {
    return http.get('/admin/logs', { params })
  },

  // 获取日志详情
  getLog: (id: number): Promise<AdminLog> => {
    return http.get(`/admin/logs/${id}`)
  },

  // 创建操作日志
  createLog: (data: CreateAdminLogData): Promise<AdminLog> => {
    return http.post('/admin/logs', data)
  },

  // 删除日志
  deleteLog: (id: number): Promise<void> => {
    return http.delete(`/admin/logs/${id}`)
  },

  // 批量删除日志
  batchDeleteLogs: (ids: number[]): Promise<void> => {
    return http.post('/admin/logs/batch-delete', { ids })
  },

  // 清空日志
  clearLogs: (beforeDate?: string): Promise<void> => {
    const url = beforeDate ? `/admin/logs/clear?beforeDate=${beforeDate}` : '/admin/logs/clear'
    return http.delete(url)
  },

  // 获取操作统计
  getStatistics: (startDate?: string, endDate?: string): Promise<AdminLogStatistics> => {
    return http.get('/admin/logs/statistics', { 
      params: { startDate, endDate } 
    })
  },

  // 导出日志
  exportLogs: (params?: QueryAdminLogParams): Promise<Blob> => {
    return http.get('/admin/logs/export', { 
      params,
      responseType: 'blob'
    })
  },

  // 获取操作模块列表
  getModules: (): Promise<string[]> => {
    return http.get('/admin/logs/modules')
  },

  // 获取操作类型列表
  getActions: (module?: string): Promise<string[]> => {
    return http.get('/admin/logs/actions', { 
      params: module ? { module } : undefined 
    })
  },

  // 获取用户操作记录
  getUserLogs: (userId: number, params?: Omit<QueryAdminLogParams, 'userId'>): Promise<PaginatedResponse<AdminLog>> => {
    return http.get(`/admin/logs/user/${userId}`, { params })
  }
} 