# API 封装系统使用指南

## 概述

本项目采用模块化的API封装设计，提供了完整的请求拦截、错误处理、状态管理和组合式函数支持。

## 目录结构

```
src/
├── api/                    # API接口定义
│   ├── auth.ts            # 认证相关API
│   ├── user.ts            # 用户管理API
│   ├── article.ts         # 文章管理API
│   ├── dashboard.ts       # 仪表盘API
│   └── index.ts           # 统一导出
├── types/
│   └── api.ts             # API类型定义
├── utils/
│   ├── request.ts         # Axios封装
│   └── errorHandler.ts    # 错误处理工具
├── stores/
│   └── auth.ts            # 认证状态管理
└── composables/
    └── useApi.ts          # API组合式函数
```

## 核心特性

### 1. 请求封装 (`utils/request.ts`)

- **自动添加认证token**
- **统一错误处理**
- **请求/响应拦截器**
- **TypeScript类型支持**

```typescript
import { http } from '@/utils/request'

// 基础用法
const data = await http.get('/users')
const user = await http.post('/users', userData)
```

### 2. API模块化 (`api/`)

每个模块包含完整的CRUD操作：

```typescript
// api/user.ts
export const userApi = {
  getList: (params) => http.get('/users', params),
  getById: (id) => http.get(`/users/${id}`),
  create: (data) => http.post('/users', data),
  update: (id, data) => http.put(`/users/${id}`, data),
  delete: (id) => http.delete(`/users/${id}`)
}
```

### 3. 组合式函数 (`composables/useApi.ts`)

#### useApi - 基础API调用

```typescript
import { useApi } from '@/composables/useApi'
import { userApi } from '@/api/user'

const { loading, error, data, execute } = useApi(userApi.getList)

// 执行请求
await execute({ page: 1, size: 10 })
```

#### useTable - 表格数据管理

```typescript
const {
  loading,
  data,
  total,
  pagination,
  searchParams,
  handleSearch,
  handlePageChange,
  handleSizeChange,
  refresh
} = useTable(userApi.getList)
```

#### useForm - 表单管理

```typescript
const {
  formData,
  loading,
  errors,
  resetForm,
  setData,
  submit
} = useForm(initialData, userApi.create)
```

### 4. 状态管理 (`stores/auth.ts`)

```typescript
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// 登录
await authStore.login({ username, password })

// 检查权限
if (authStore.hasPermission('user:create')) {
  // 有权限
}

// 登出
await authStore.logout()
```

## 使用示例

### 1. 简单API调用

```vue
<script setup lang="ts">
import { ref } from 'vue'
import { userApi } from '@/api/user'

const users = ref([])
const loading = ref(false)

const fetchUsers = async () => {
  loading.value = true
  try {
    const result = await userApi.getList({ page: 1, size: 10 })
    users.value = result.list
  } catch (error) {
    console.error('获取用户失败:', error)
  } finally {
    loading.value = false
  }
}
</script>
```

### 2. 使用组合式函数

```vue
<script setup lang="ts">
import { useTable } from '@/composables/useApi'
import { userApi } from '@/api/user'

const {
  loading,
  data,
  total,
  pagination,
  searchParams,
  handleSearch,
  handlePageChange,
  refresh
} = useTable(userApi.getList)

// 搜索
const search = () => {
  handleSearch({ keyword: 'admin' })
}

// 刷新
const refreshData = () => {
  refresh()
}
</script>
```

### 3. 表单提交

```vue
<script setup lang="ts">
import { useForm } from '@/composables/useApi'
import { userApi } from '@/api/user'

const {
  formData,
  loading,
  errors,
  submit
} = useForm(
  { username: '', email: '', nickname: '' },
  userApi.create
)

const handleSubmit = async () => {
  try {
    await submit()
    ElMessage.success('创建成功')
  } catch (error) {
    ElMessage.error('创建失败')
  }
}
</script>
```

## 错误处理

### 1. 全局错误处理

请求拦截器会自动处理以下错误：

- **401**: 自动跳转登录页
- **403**: 显示权限错误
- **404**: 显示资源不存在
- **500**: 显示服务器错误

### 2. 自定义错误处理

```typescript
import { handleError } from '@/utils/errorHandler'

try {
  await userApi.create(userData)
} catch (error) {
  handleError(error, true) // 第二个参数控制是否显示通知
}
```

## 类型安全

所有API都有完整的TypeScript类型定义：

```typescript
// 类型定义
interface User {
  id: number
  username: string
  email: string
  // ...
}

// API调用自动推断类型
const user: User = await userApi.getById(1)
const users: PaginationResult<User> = await userApi.getList()
```

## 环境配置

在 `vite.config.ts` 中配置API代理：

```typescript
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true
      }
    }
  }
})
```

## 最佳实践

1. **模块化**: 按功能模块组织API接口
2. **类型安全**: 使用TypeScript定义所有接口类型
3. **错误处理**: 统一处理错误，提供友好的用户提示
4. **状态管理**: 使用Pinia管理全局状态
5. **组合式函数**: 复用常见的API调用模式
6. **拦截器**: 统一处理认证、错误等横切关注点

## 扩展指南

### 添加新的API模块

1. 在 `types/api.ts` 中定义类型
2. 在 `api/` 目录下创建新模块
3. 在 `api/index.ts` 中导出
4. 创建对应的Store（如需要）

### 自定义组合式函数

```typescript
// composables/useCustomApi.ts
export function useCustomApi() {
  // 自定义逻辑
  return {
    // 返回值
  }
}
```

# API 模块使用指南

## 概述

本项目采用模块化的API设计，每个功能模块都有对应的API文件和组合式函数，提供类型安全的接口调用。

## 目录结构

```
src/api/
├── auth.ts              # 认证相关API
├── admin-users.ts       # 管理员用户API
├── roles.ts             # 角色管理API
├── permissions.ts       # 权限管理API
├── admin-logs.ts        # 操作日志API
├── user.ts              # 前端用户API
├── article.ts           # 文章管理API
├── dashboard.ts         # 仪表盘API
└── index.ts             # 统一导出

src/composables/
├── useApi.ts            # 通用API组合式函数
├── useAdminApi.ts       # Admin专用API组合式函数
└── ...

src/types/
└── api.ts               # API类型定义
```

## Admin API 模块

### 1. 认证API (auth.ts)

```typescript
import { authApi } from '@/api'

// 管理员登录
const loginResponse = await authApi.login({
  username: 'admin',
  password: '123456'
})

// 获取当前用户信息
const { userInfo, permissions } = await authApi.getUserInfo()

// 修改密码
await authApi.changePassword({
  oldPassword: '123456',
  newPassword: 'newPassword'
})
```

### 2. 管理员用户API (admin-users.ts)

```typescript
import { adminUsersApi } from '@/api'

// 获取用户列表
const response = await adminUsersApi.getUsers({
  page: 1,
  size: 10,
  keyword: 'admin',
  status: 'active'
})

// 创建用户
await adminUsersApi.createUser({
  username: 'newuser',
  email: '<EMAIL>',
  password: '123456',
  realName: '新用户',
  roleIds: [1, 2]
})

// 重置密码
const result = await adminUsersApi.resetPassword(1)
console.log('新密码:', result.password)
```

### 3. 角色管理API (roles.ts)

```typescript
import { rolesApi } from '@/api'

// 获取角色列表
const roles = await rolesApi.getRoles({
  page: 1,
  size: 10,
  keyword: '管理员'
})

// 分配权限
await rolesApi.assignPermissions(1, {
  permissionIds: [1, 2, 3, 4]
})

// 复制角色
const newRole = await rolesApi.copyRole(1, '新角色名称')
```

### 4. 权限管理API (permissions.ts)

```typescript
import { permissionsApi } from '@/api'

// 获取权限树
const permissionTree = await permissionsApi.getPermissionTree()

// 创建权限
await permissionsApi.createPermission({
  name: '用户管理',
  code: 'user:manage',
  type: 'menu',
  path: '/users',
  icon: 'user'
})

// 移动权限位置
await permissionsApi.movePermission(1, 2, 0)
```

### 5. 操作日志API (admin-logs.ts)

```typescript
import { adminLogsApi } from '@/api'

// 获取日志列表
const logs = await adminLogsApi.getLogs({
  page: 1,
  size: 10,
  module: 'user',
  status: 'success'
})

// 获取统计数据
const statistics = await adminLogsApi.getStatistics('2024-01-01', '2024-12-31')

// 导出日志
const blob = await adminLogsApi.exportLogs({
  startDate: '2024-01-01',
  endDate: '2024-12-31'
})
```

## 组合式函数使用

### useAdminUsers - 管理员用户管理

```vue
<script setup lang="ts">
import { useAdminUsers } from '@/composables/useAdminApi'

const {
  loading,
  users,
  total,
  pagination,
  searchParams,
  fetchUsers,
  deleteUser,
  resetPassword,
  toggleUserStatus
} = useAdminUsers()

// 获取用户列表
onMounted(() => {
  fetchUsers()
})

// 搜索用户
const handleSearch = () => {
  searchParams.keyword = 'admin'
  searchParams.status = 'active'
  pagination.page = 1
  fetchUsers()
}

// 删除用户
const handleDelete = (id: number, username: string) => {
  deleteUser(id, username) // 自动处理确认对话框和消息提示
}
</script>
```

### useRoles - 角色管理

```vue
<script setup lang="ts">
import { useRoles } from '@/composables/useAdminApi'

const {
  loading,
  roles,
  total,
  pagination,
  searchParams,
  fetchRoles,
  fetchAllRoles,
  deleteRole,
  toggleRoleStatus
} = useRoles()

// 获取所有角色（用于下拉选择）
const allRoles = ref([])
onMounted(async () => {
  allRoles.value = await fetchAllRoles()
})
</script>
```

### usePermissions - 权限管理

```vue
<script setup lang="ts">
import { usePermissions } from '@/composables/useAdminApi'

const {
  loading,
  permissions,
  permissionTree,
  fetchPermissionTree,
  fetchAllPermissions,
  deletePermission,
  togglePermissionStatus
} = usePermissions()

// 获取权限树（用于树形展示）
onMounted(() => {
  fetchPermissionTree()
})
</script>
```

### useAdminLogs - 操作日志管理

```vue
<script setup lang="ts">
import { useAdminLogs } from '@/composables/useAdminApi'

const {
  loading,
  logs,
  total,
  statistics,
  pagination,
  searchParams,
  fetchLogs,
  fetchStatistics,
  clearLogs,
  exportLogs
} = useAdminLogs()

// 获取日志和统计
onMounted(async () => {
  await fetchLogs()
  await fetchStatistics()
})

// 导出日志
const handleExport = () => {
  exportLogs() // 自动下载Excel文件
}
</script>
```

## 类型定义

所有API都有完整的TypeScript类型定义，位于 `src/types/api.ts`：

```typescript
// 管理员用户类型
interface AdminUser extends BaseEntity {
  username: string
  email: string
  realName: string
  avatar?: string
  phone?: string
  status: 'active' | 'inactive' | 'banned'
  lastLoginAt?: string
  lastLoginIp?: string
  roles?: Role[]
}

// 角色类型
interface Role extends BaseEntity {
  name: string
  code: string
  description?: string
  status: 'active' | 'inactive'
  sort: number
  isSystem: boolean
  permissions?: Permission[]
}

// 权限类型
interface Permission extends BaseEntity {
  name: string
  code: string
  type: 'menu' | 'button' | 'api'
  parentId?: number
  path?: string
  component?: string
  icon?: string
  sort: number
  status: 'active' | 'inactive'
  children?: Permission[]
}
```

## 错误处理

所有API调用都有统一的错误处理机制：

1. **网络错误**: 自动重试和错误提示
2. **认证错误**: 自动跳转到登录页
3. **权限错误**: 显示权限不足提示
4. **业务错误**: 显示具体错误信息

```typescript
// 在组合式函数中，错误已经被处理
const { deleteUser } = useAdminUsers()

// 调用时无需手动处理错误
deleteUser(1, 'admin') // 自动显示确认对话框和结果提示
```

## 最佳实践

### 1. 使用组合式函数

优先使用组合式函数而不是直接调用API：

```typescript
// ✅ 推荐
const { fetchUsers, deleteUser } = useAdminUsers()

// ❌ 不推荐
import { adminUsersApi } from '@/api'
const users = await adminUsersApi.getUsers()
```

### 2. 类型安全

充分利用TypeScript类型检查：

```typescript
// ✅ 类型安全
const createData: CreateAdminUserData = {
  username: 'admin',
  email: '<EMAIL>',
  password: '123456',
  realName: '管理员'
}

// ❌ 缺少类型检查
const createData = {
  username: 'admin'
  // 缺少必需字段
}
```

### 3. 响应式数据

合理使用响应式数据：

```typescript
// ✅ 响应式搜索
watch(() => searchParams.keyword, () => {
  if (searchParams.keyword.length > 2) {
    fetchUsers()
  }
})

// ✅ 分页变化自动刷新
watch(() => pagination.page, fetchUsers)
```

### 4. 加载状态

利用内置的加载状态：

```vue
<template>
  <el-table :data="users" v-loading="loading">
    <!-- 表格内容 -->
  </el-table>
</template>

<script setup lang="ts">
const { loading, users } = useAdminUsers()
</script>
```

## 扩展指南

### 添加新的API模块

1. 创建API文件 `src/api/new-module.ts`
2. 定义类型 `src/types/api.ts`
3. 创建组合式函数 `src/composables/useNewModule.ts`
4. 在 `src/api/index.ts` 中导出

### 自定义组合式函数

```typescript
// src/composables/useCustomApi.ts
export function useCustomApi() {
  const loading = ref(false)
  const data = ref([])
  
  const fetchData = async () => {
    loading.value = true
    try {
      // API调用
    } catch (error) {
      ElMessage.error('获取数据失败')
    } finally {
      loading.value = false
    }
  }
  
  return {
    loading,
    data,
    fetchData
  }
}
``` 