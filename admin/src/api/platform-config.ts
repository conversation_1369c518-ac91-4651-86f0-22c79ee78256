import request from '@/utils/request'

// 平台配置相关接口
export const platformConfigApi = {
  // 获取平台配置数据
  getPlatformConfig: (id: number) => {
    return request.get(`/platform-config/${id}`)
  },

  // 扩展分类管理
  createExtension: (data: {
    part_platform_id: number
    title: string
    description?: string
    image_url?: string
    sort_order?: number
  }) => {
    return request.post('/platform-config/extensions', data)
  },

  updateExtension: (id: number, data: {
    title: string
    description?: string
    image_url?: string
    sort_order?: number
  }) => {
    return request.put(`/platform-config/extensions/${id}`, data)
  },

  deleteExtension: (id: number) => {
    return request.delete(`/platform-config/extensions/${id}`)
  },

  // 表格管理
  createTable: (data: {
    extension_id: number
    table_name: string
    combination_template_id?: number
    sort_order?: number
  }) => {
    return request.post('/platform-config/tables', data)
  },

  updateTable: (id: number, data: {
    table_name: string
    combination_template_id?: number
    sort_order?: number
  }) => {
    return request.put(`/platform-config/tables/${id}`, data)
  },

  deleteTable: (id: number) => {
    return request.delete(`/platform-config/tables/${id}`)
  },

  // 表格列管理
  getTableColumns: (tableId: number) => {
    return request.get(`/platform-config/tables/${tableId}/columns`)
  },

  // 获取表格的完整列配置（包含模板和覆盖）
  getTableColumnsWithTemplate: (tableId: number) => {
    return request.get(`/platform-config/tables/${tableId}/columns-with-template`)
  },

  createTableColumn: (data: {
    table_id: number
    column_name: string
    column_label: string
    column_type: string
    is_required?: boolean
    sort_order?: number
    remark?: string
  }) => {
    return request.post('/platform-config/table-columns', data)
  },

  updateTableColumn: (id: number, data: {
    column_name: string
    column_label: string
    column_type: string
    is_required?: boolean
    sort_order?: number
    remark?: string
  }) => {
    return request.put(`/platform-config/table-columns/${id}`, data)
  },

  deleteTableColumn: (id: number) => {
    return request.delete(`/platform-config/table-columns/${id}`)
  },

  // 表格数据管理
  getTableRows: (tableId: number) => {
    return request.get(`/platform-config/tables/${tableId}/rows`)
  },

  createTableRow: (data: {
    table_id: number
    data: Record<string, any>
    sort_order?: number
  }) => {
    return request.post('/platform-config/table-rows', data)
  },

  updateTableRow: (id: number, data: {
    data: Record<string, any>
    sort_order?: number
  }) => {
    return request.put(`/platform-config/table-rows/${id}`, data)
  },

  deleteTableRow: (id: number) => {
    return request.delete(`/platform-config/table-rows/${id}`)
  }
}

// 列模板相关接口
export const columnTemplateApi = {
  // 获取列模板列表
  getColumnTemplates: (params?: {
    category?: string
    common_only?: boolean
    search?: string
    exclude_columns?: string
  }) => {
    return request.get('/column-templates', { params })
  },

  // 获取列模板分类
  getColumnCategories: () => {
    return request.get('/column-templates/categories')
  },

  // 获取组合模板列表
  getCombinationTemplates: (params?: {
    search?: string
  }) => {
    return request.get('/combination-templates', { params })
  },

  // 创建列模板
  createColumnTemplate: (data: {
    name: string
    label: string
    type: string
    category: string
    description?: string
    is_required?: boolean
    is_common?: boolean
    default_value?: string
    options?: any
    validation_rules?: any
    sort_order?: number
  }) => {
    return request.post('/column-templates', data)
  },

  // 创建组合模板
  createCombinationTemplate: (data: {
    name: string
    description?: string
    category?: string
    scenarios?: string
    column_ids: number[]
  }) => {
    return request.post('/combination-templates', data)
  },

  // 删除列模板
  deleteColumnTemplate: (id: number) => {
    return request.delete(`/column-templates/${id}`)
  },

  // 删除组合模板
  deleteCombinationTemplate: (id: number) => {
    return request.delete(`/combination-templates/${id}`)
  },

  // 初始化模板数据
  initializeTemplateData: () => {
    return request.post('/column-templates/initialize')
  }
}
