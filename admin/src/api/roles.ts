import type {
  AssignPermissionsData,
  CreateRoleData,
  PaginatedResponse,
  Permission,
  QueryRoleParams,
  Role,
  UpdateRoleData
} from '@/types/api'
import { http } from '@/utils/request'

export const rolesApi = {
  // 获取角色列表
  getRoles: async (params?: QueryRoleParams): Promise<PaginatedResponse<Role>> => {
    // 转换前端参数为Go后端期望的参数格式
    const backendParams: any = {}
    if (params?.page) backendParams.page = params.page
    if (params?.size) backendParams.page_size = params.size  // Go后端使用page_size
    if (params?.keyword) backendParams.keyword = params.keyword  // Go后端使用keyword
    if (params?.status) backendParams.status = params.status
    
    const response: any = await http.get('/admin/roles', backendParams)
    
    // Go后端使用SuccessPage返回格式嵌套在data中: { code: 200, data: { list: [...], total: ..., page: ..., page_size: ... }, success: true }
    const pageData = response.data || response
    const items = pageData.list || []
    const total = pageData.total || 0
    const page = pageData.page || 1
    const page_size = pageData.page_size || 10
    
    // 转换为前端期望的格式
    return {
      list: items,
      total: total,
      page: page,
      page_size: page_size
    }
  },

  // 获取所有角色（不分页）
  getAllRoles: (): Promise<Role[]> => {
    return http.get('/admin/roles/all')
  },

  // 获取角色详情
  getRole: (id: number): Promise<Role> => {
    return http.get(`/admin/roles/${id}`)
  },

  // 创建角色
  createRole: (data: CreateRoleData): Promise<Role> => {
    return http.post('/admin/roles', data)
  },

  // 更新角色
  updateRole: (id: number, data: UpdateRoleData): Promise<Role> => {
    return http.put(`/admin/roles/${id}`, data)
  },

  // 删除角色
  deleteRole: (id: number): Promise<void> => {
    return http.delete(`/admin/roles/${id}`)
  },

  // 批量删除角色
  batchDeleteRoles: (ids: number[]): Promise<void> => {
    return http.post('/admin/roles/batch-delete', { ids })
  },

  // 获取角色权限
  getRolePermissions: (id: number): Promise<Permission[]> => {
    return http.get(`/admin/roles/${id}/permissions`)
  },

  // 分配权限给角色
  assignPermissions: (id: number, data: AssignPermissionsData): Promise<void> => {
    return http.post(`/admin/roles/${id}/permissions`, data)
  },

  // 切换角色状态
  toggleStatus: (id: number): Promise<Role> => {
    return http.put(`/admin/roles/${id}/toggle-status`)
  },

  // 复制角色
  copyRole: (id: number, name: string): Promise<Role> => {
    return http.post(`/admin/roles/${id}/copy`, { name })
  }
} 