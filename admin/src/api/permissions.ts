import type {
    CreatePermissionData,
    PaginatedResponse,
    Permission,
    QueryPermissionParams,
    UpdatePermissionData
} from '@/types/api'
import { http } from '@/utils/request'

export const permissionsApi = {
  // 获取权限列表
  getPermissions: async (params?: QueryPermissionParams): Promise<PaginatedResponse<Permission>> => {
    // 转换前端参数为Go后端期望的参数格式
    const backendParams: any = {}
    if (params?.page) backendParams.page = params.page
    if (params?.size) backendParams.page_size = params.size  // Go后端使用page_size
    if (params?.keyword) backendParams.keyword = params.keyword
    if (params?.type) backendParams.type = params.type
    if (params?.status) backendParams.status = params.status
    if (params?.parentId) backendParams.parent_id = params.parentId  // Go后端使用parent_id

    const response: any = await http.get('/admin/permissions', { params: backendParams })

    // 响应拦截器已经提取了data.data，所以response就是PageData对象
    return {
      list: response?.list || [],
      total: response?.total || 0,
      page: response?.page || 1,
      page_size: response?.page_size || 10
    }
  },

  // 获取权限树
  getPermissionTree: (): Promise<Permission[]> => {
    return http.get('/admin/permissions/tree')
  },

  // 获取所有权限（不分页）
  getAllPermissions: (): Promise<Permission[]> => {
    return http.get('/admin/permissions/all')
  },

  // 获取权限详情
  getPermission: (id: number): Promise<Permission> => {
    return http.get(`/admin/permissions/${id}`)
  },

  // 创建权限
  createPermission: (data: CreatePermissionData): Promise<Permission> => {
    return http.post('/admin/permissions', data)
  },

  // 更新权限
  updatePermission: (id: number, data: UpdatePermissionData): Promise<Permission> => {
    return http.put(`/admin/permissions/${id}`, data)
  },

  // 删除权限
  deletePermission: (id: number): Promise<void> => {
    return http.delete(`/admin/permissions/${id}`)
  },

  // 批量删除权限
  batchDeletePermissions: (ids: number[]): Promise<void> => {
    return http.post('/admin/permissions/batch-delete', { ids })
  },

  // 获取子权限
  getChildPermissions: (parentId: number): Promise<Permission[]> => {
    return http.get(`/admin/permissions/children/${parentId}`)
  },

  // 切换权限状态
  toggleStatus: (id: number): Promise<Permission> => {
    return http.put(`/admin/permissions/${id}/toggle-status`)
  },

  // 移动权限位置
  movePermission: (id: number, targetParentId: number, position: number): Promise<void> => {
    return http.put(`/admin/permissions/${id}/move`, { 
      targetParentId, 
      position 
    })
  },

  // 获取菜单权限（用于生成路由）
  getMenuPermissions: (): Promise<Permission[]> => {
    return http.get('/admin/permissions/menu')
  }
} 