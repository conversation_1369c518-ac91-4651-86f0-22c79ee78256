import { http } from '@/utils/request'

// 文件上传相关接口

/**
 * 上传配置信息
 */
export interface UploadConfig {
  maxSize: number
  allowedTypes: string[]
  modules: string[]
}

/**
 * 上传文件信息
 */
export interface UploadFileInfo {
  id: number
  originalName: string
  fileName: string
  fileSize: number
  mimeType: string
  cosUrl: string
  module: string
  uploadTime: string
  status?: string
}

/**
 * 文件列表响应
 */
export interface FileListResponse {
  files: UploadFileInfo[]
  total: number
}

/**
 * 获取上传配置
 */
export function getUploadConfig() {
  return http.get<UploadConfig>('/upload/config')
}

/**
 * 上传文件
 * @param file 要上传的文件
 * @param module 所属模块
 */
export function uploadFile(file: File, module?: string) {
  console.log('开始上传文件:', {
    fileName: file.name,
    fileSize: file.size,
    fileType: file.type,
    module: module
  })

  const formData = new FormData()
  formData.append('file', file)
  if (module) {
    formData.append('module', module)
  }

  // 使用axios直接发送请求，确保Content-Type正确
  return new Promise<UploadFileInfo>((resolve, reject) => {
    const token = localStorage.getItem('admin_token')
    const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api'
    
    console.log('上传请求配置:', {
      url: `${baseURL}/files/upload`,
      hasToken: !!token,
      formDataKeys: Array.from(formData.keys())
    })

    // 使用fetch发送请求，避免axios的默认配置干扰
    fetch(`${baseURL}/files/upload`, {
      method: 'POST',
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
        // 不设置Content-Type，让浏览器自动设置
      },
      body: formData
    })
    .then(async response => {
      console.log('上传响应状态:', response.status)
      
      if (!response.ok) {
        const errorText = await response.text()
        console.error('上传失败响应:', errorText)
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }
      
      const result = await response.json()
      console.log('上传成功响应:', result)
      
      // 根据后端响应格式处理
      if (result.success || result.code === 200) {
        resolve(result.data)
      } else {
        throw new Error(result.message || '上传失败')
      }
    })
    .catch(error => {
      console.error('上传请求错误:', error)
      reject(error)
    })
  })
}

/**
 * 删除文件
 * @param id 文件ID
 */
export function deleteFile(id: number) {
  return http.delete(`/files/${id}`)
}

/**
 * 获取文件信息
 * @param id 文件ID
 */
export function getFileInfo(id: number) {
  return http.get<UploadFileInfo>(`/files/${id}`)
}

/**
 * 获取我的文件列表
 */
export function getMyFiles() {
  return http.get<FileListResponse>('/files')
}

/**
 * 根据模块获取文件列表
 * @param module 模块名称
 */
export function getFilesByModule(module: string) {
  return http.get<FileListResponse>(`/files/module/${module}`)
}

/**
 * 文件大小格式化
 * @param size 文件大小（字节）
 */
export function formatFileSize(size: number): string {
  if (size < 1024) {
    return `${size} B`
  }
  if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(1)} KB`
  }
  if (size < 1024 * 1024 * 1024) {
    return `${(size / (1024 * 1024)).toFixed(1)} MB`
  }
  return `${(size / (1024 * 1024 * 1024)).toFixed(1)} GB`
}

/**
 * 获取文件类型图标
 * @param mimeType 文件MIME类型
 */
export function getFileTypeIcon(mimeType: string): string {
  if (mimeType.startsWith('image/')) {
    return 'Picture'
  }
  if (mimeType.includes('pdf')) {
    return 'Document'
  }
  if (mimeType.includes('word') || mimeType.includes('document')) {
    return 'Document'
  }
  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) {
    return 'Grid'
  }
  if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) {
    return 'Monitor'
  }
  if (mimeType.includes('zip') || mimeType.includes('rar')) {
    return 'Folder'
  }
  if (mimeType.includes('text')) {
    return 'Document'
  }
  return 'Document'
}

/**
 * 检查文件是否为图片
 * @param mimeType 文件MIME类型
 */
export function isImageFile(mimeType: string): boolean {
  return mimeType.startsWith('image/')
} 