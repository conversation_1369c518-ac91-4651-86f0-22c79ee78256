import type {
    AdminUser,
    CreateAdminUserData,
    PaginatedResponse,
    QueryAdminUserParams,
    Role,
    UpdateAdminUserData
} from '@/types/api'
import { http } from '@/utils/request'

export const adminUsersApi = {
  // 获取管理员用户列表
  getUsers: (params?: QueryAdminUserParams): Promise<PaginatedResponse<AdminUser>> => {
    return http.get('/admin/users', params)
  },

  // 获取管理员用户详情
  getUser: (id: number): Promise<AdminUser> => {
    return http.get(`/admin/users/${id}`)
  },

  // 创建管理员用户
  createUser: (data: CreateAdminUserData): Promise<AdminUser> => {
    return http.post('/admin/users', data)
  },

  // 更新管理员用户
  updateUser: (id: number, data: UpdateAdminUserData): Promise<AdminUser> => {
    return http.put(`/admin/users/${id}`, data)
  },

  // 删除管理员用户
  deleteUser: (id: number): Promise<void> => {
    return http.delete(`/admin/users/${id}`)
  },

  // 批量删除管理员用户
  batchDeleteUsers: (ids: number[]): Promise<void> => {
    return http.post('/admin/users/batch-delete', { ids })
  },

  // 重置用户密码
  resetPassword: (id: number, newPassword?: string): Promise<{ password: string }> => {
    return http.post(`/admin/users/${id}/reset-password`, { newPassword })
  },

  // 获取用户角色
  getUserRoles: (id: number): Promise<Role[]> => {
    return http.get(`/admin/users/${id}/roles`)
  },

  // 分配用户角色
  assignRoles: (id: number, roleIds: number[]): Promise<void> => {
    return http.post(`/admin/users/${id}/roles`, { roleIds })
  },

  // 切换用户状态
  toggleStatus: (id: number): Promise<AdminUser> => {
    return http.put(`/admin/users/${id}/toggle-status`)
  },

  // 更新最后登录时间
  updateLastLogin: (id: number): Promise<void> => {
    return http.patch(`/admin/users/${id}/last-login`)
  }
} 