import type { DashboardStats } from '@/types/api';
import { http } from '@/utils/request';

export const dashboardApi = {
  // 获取仪表盘统计数据
  getStats: (): Promise<DashboardStats> => {
    return http.get('/dashboard/stats')
  },

  // 获取访问量趋势数据
  getVisitTrend: (days: number = 7): Promise<Array<{ date: string; count: number }>> => {
    return http.get('/dashboard/visit-trend', { days })
  },

  // 获取文章发布趋势
  getArticleTrend: (days: number = 7): Promise<Array<{ date: string; count: number }>> => {
    return http.get('/dashboard/article-trend', { days })
  },

  // 获取用户注册趋势
  getUserTrend: (days: number = 7): Promise<Array<{ date: string; count: number }>> => {
    return http.get('/dashboard/user-trend', { days })
  },

  // 获取热门文章
  getPopularArticles: (limit: number = 10): Promise<Array<{
    id: number
    title: string
    viewCount: number
    publishedAt: string
  }>> => {
    return http.get('/dashboard/popular-articles', { limit })
  },

  // 获取最新用户
  getRecentUsers: (limit: number = 10): Promise<Array<{
    id: number
    username: string
    nickname: string
    createdAt: string
  }>> => {
    return http.get('/dashboard/recent-users', { limit })
  }
} 