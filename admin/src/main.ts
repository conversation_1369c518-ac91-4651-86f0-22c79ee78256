import { createPinia } from 'pinia'
import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { useAuthStore } from './stores/auth'

import 'element-plus/dist/index.css'
import 'uno.css'
import './style/index.css'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)

// 初始化认证状态并等待完成后再挂载应用
const initializeApp = async () => {
  console.log('🚀 [Main] 应用启动，初始化认证状态')
  const authStore = useAuthStore()

  try {
    await authStore.initAuth()
    console.log('✅ [Main] 认证状态初始化完成')
  } catch (error) {
    console.error('❌ [Main] 认证状态初始化失败:', error)
  }

  // 认证状态初始化完成后再挂载应用
  app.mount('#app')
  console.log('🎯 [Main] 应用挂载完成')
}

// 启动应用
initializeApp()