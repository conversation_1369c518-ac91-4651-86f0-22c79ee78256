<template>
  <div>
    <!-- 快速添加按钮 -->
    <el-button type="primary" @click="showTemplateSelector">
      <i class="i-mdi-lightning-bolt mr-1"></i>
      快速添加列
    </el-button>

    <!-- 模板选择对话框 -->
    <el-dialog
      v-model="templateDialogVisible"
      title="选择列模板"
      width="900px"
      @close="resetSelection"
    >
      <!-- 模板类型切换 -->
      <div class="mb-4">
        <el-radio-group v-model="templateType" class="mb-3">
          <el-radio-button label="combination">列组合模板</el-radio-button>
          <el-radio-button label="single">单列模板</el-radio-button>
        </el-radio-group>
      </div>

      <!-- 搜索区域 -->
      <div class="mb-4">
        <el-input
          v-model="searchKeyword"
          :placeholder="templateType === 'combination' ? '搜索组合模板...' : '搜索列模板...'"
          clearable
          class="w-64"
        >
          <template #prefix>
            <i class="i-mdi-magnify"></i>
          </template>
        </el-input>
      </div>

      <!-- 分类标签 -->
      <div v-if="templateType === 'single'" class="mb-4">
        <el-tag
          v-for="category in categories"
          :key="category"
          :type="selectedCategory === category ? 'primary' : ''"
          class="mr-2 mb-2 cursor-pointer"
          @click="selectCategory(category)"
        >
          {{ category }}
        </el-tag>
      </div>

      <!-- 组合模板列表 -->
      <div v-if="templateType === 'combination'" class="grid grid-cols-1 gap-4 max-h-96 overflow-auto">
        <div
          v-for="combination in filteredCombinations"
          :key="combination.id"
          class="border rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow"
          :class="{ 'border-blue-500 bg-blue-50': selectedCombination === combination.id }"
          @click="selectCombination(combination)"
        >
          <div class="flex justify-between items-start mb-3">
            <div>
              <h4 class="font-semibold text-lg">{{ combination.name }}</h4>
              <p class="text-gray-600 text-sm mt-1">{{ combination.description }}</p>
            </div>
            <el-tag size="small" type="success">
              {{ combination.columns.length }} 列
            </el-tag>
          </div>

          <!-- 包含的列预览 -->
          <div class="mb-3">
            <div class="text-sm text-gray-500 mb-2">包含列：</div>
            <div class="flex flex-wrap gap-1">
              <el-tag
                v-for="column in combination.columns.slice(0, 6)"
                :key="column.name"
                size="small"
                :type="getTypeColor(column.type)"
              >
                {{ column.label }}
              </el-tag>
              <el-tag v-if="combination.columns.length > 6" size="small" type="info">
                +{{ combination.columns.length - 6 }} 更多
              </el-tag>
            </div>
          </div>

          <!-- 适用场景 -->
          <div class="text-xs text-gray-500">
            适用场景: {{ combination.scenarios?.join('、') || '通用' }}
          </div>
        </div>
      </div>

      <!-- 单列模板列表 -->
      <div v-else class="grid grid-cols-2 gap-4 max-h-96 overflow-auto">
        <div
          v-for="template in filteredTemplates"
          :key="template.id"
          class="border rounded-lg p-3 cursor-pointer hover:shadow-md transition-shadow"
          :class="{ 'border-blue-500 bg-blue-50': selectedTemplates.includes(template.id) }"
          @click="toggleTemplate(template)"
        >
          <div class="flex items-center justify-between mb-2">
            <h4 class="font-medium">{{ template.label }}</h4>
            <el-tag size="small" :type="getTypeColor(template.type)">
              {{ getTypeLabel(template.type) }}
            </el-tag>
          </div>
          <p class="text-sm text-gray-600 mb-2">{{ template.description }}</p>
          <div class="text-xs text-gray-500">
            字段名: {{ template.name }}
            <span v-if="template.required" class="text-red-500 ml-2">*必填</span>
          </div>
        </div>
      </div>

      <!-- 已选择的模板 -->
      <div v-if="selectedTemplates.length > 0 || selectedCombination" class="mt-4 p-3 bg-gray-50 rounded">
        <!-- 组合模板选择 -->
        <div v-if="templateType === 'combination' && selectedCombination">
          <h5 class="font-medium mb-2">已选择组合模板：</h5>
          <div class="mb-2">
            <el-tag size="large" closable @close="selectedCombination = null">
              {{ getCombinationById(selectedCombination)?.name }}
              ({{ getCombinationById(selectedCombination)?.columns.length }} 列)
            </el-tag>
          </div>
          <div class="text-sm text-gray-600">
            将添加: {{ getCombinationById(selectedCombination)?.columns.map(c => c.label).join('、') }}
          </div>
        </div>

        <!-- 单列模板选择 -->
        <div v-else-if="templateType === 'single' && selectedTemplates.length > 0">
          <h5 class="font-medium mb-2">已选择 {{ selectedTemplates.length }} 个列模板：</h5>
          <div class="flex flex-wrap gap-2">
            <el-tag
              v-for="templateId in selectedTemplates"
              :key="templateId"
              closable
              @close="removeTemplate(templateId)"
            >
              {{ getTemplateById(templateId)?.label }}
            </el-tag>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-between">
          <div v-if="templateType === 'single'">
            <el-button @click="selectAllCommon">选择常用列</el-button>
            <el-button @click="selectAllCategory">选择当前分类</el-button>
          </div>
          <div v-else>
            <el-button @click="saveCurrentAsCombination" :disabled="!canSaveAsCombination">
              保存当前配置为组合
            </el-button>
          </div>
          <div>
            <el-button @click="templateDialogVisible = false">取消</el-button>
            <el-button
              type="primary"
              @click="applyTemplates"
              :disabled="!hasSelection"
            >
              {{ getApplyButtonText() }}
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { computed, ref } from 'vue'

// 列模板接口
interface ColumnTemplate {
  id: string
  name: string
  label: string
  type: string
  category: string
  description: string
  required: boolean
  defaultValue?: any
  options?: string[]
  validation?: any
  common?: boolean
}

// Props & Emits
interface Props {
  existingColumns?: string[]
}

const props = defineProps<Props>()
const emit = defineEmits<{
  addColumns: [columns: ColumnTemplate[]]
}>()

// 响应式数据
const templateDialogVisible = ref(false)
const templateType = ref<'combination' | 'single'>('combination')
const searchKeyword = ref('')
const selectedCategory = ref('全部')
const selectedTemplates = ref<string[]>([])
const selectedCombination = ref<string | null>(null)

// 组合模板接口
interface CombinationTemplate {
  id: string
  name: string
  description: string
  columns: ColumnTemplate[]
  scenarios?: string[]
  category?: string
}

// 模板数据
const combinationTemplates = ref<CombinationTemplate[]>([])
const columnTemplates = ref<ColumnTemplate[]>([])
const loading = ref(false)

// API方法
const loadColumnTemplates = async () => {
  try {
    loading.value = true
    const params: any = {}

    if (selectedCategory.value !== '全部') {
      params.category = selectedCategory.value
    }

    if (searchKeyword.value) {
      params.search = searchKeyword.value
    }

    if (props.existingColumns && props.existingColumns.length > 0) {
      params.exclude_columns = props.existingColumns
    }

    const response = await fetch('/api/column-templates?' + new URLSearchParams(params))
    const result = await response.json()

    if (result.code === 200) {
      columnTemplates.value = result.data
    }
  } catch (error) {
    console.error('加载列模板失败:', error)
    ElMessage.error('加载列模板失败')
  } finally {
    loading.value = false
  }
}

const loadCombinationTemplates = async () => {
  try {
    loading.value = true
    const params: any = {}

    if (searchKeyword.value) {
      params.search = searchKeyword.value
    }

    const response = await fetch('/api/combination-templates?' + new URLSearchParams(params))
    const result = await response.json()

    if (result.code === 200) {
      combinationTemplates.value = result.data
    }
  } catch (error) {
    console.error('加载组合模板失败:', error)
    ElMessage.error('加载组合模板失败')
  } finally {
    loading.value = false
  }
}

// 原来的硬编码数据作为备用（如果API不可用）
const fallbackCombinationTemplates = ref<CombinationTemplate[]>([
  {
    id: 'hydraulic_valve_standard',
    name: '液压阀门标准配置',
    description: '适用于各类液压阀门产品的标准列配置',
    scenarios: ['液压阀门', '控制阀', '换向阀'],
    columns: [
      { id: 'model', name: 'model', label: '型号', type: 'text', category: '基础信息', description: '产品型号', required: true, common: true },
      { id: 'name', name: 'name', label: '产品名称', type: 'text', category: '基础信息', description: '产品名称', required: true, common: true },
      { id: 'flow_rate', name: 'flow_rate', label: '流量(L/min)', type: 'number', category: '技术参数', description: '流量参数', required: false, common: true },
      { id: 'pressure', name: 'pressure', label: '压力(MPa)', type: 'number', category: '技术参数', description: '压力参数', required: false, common: true },
      { id: 'temperature', name: 'temperature', label: '工作温度(°C)', type: 'number', category: '技术参数', description: '工作温度', required: false },
      { id: 'material', name: 'material', label: '材质', type: 'text', category: '物理属性', description: '主要材质', required: false },
      { id: 'price', name: 'price', label: '价格(元)', type: 'number', category: '商务信息', description: '产品价格', required: false, common: true }
    ]
  },
  {
    id: 'motor_standard',
    name: '电机产品标准配置',
    description: '适用于各类电机产品的标准列配置',
    scenarios: ['电机', '马达', '驱动器'],
    columns: [
      { id: 'model', name: 'model', label: '型号', type: 'text', category: '基础信息', description: '产品型号', required: true, common: true },
      { id: 'name', name: 'name', label: '产品名称', type: 'text', category: '基础信息', description: '产品名称', required: true, common: true },
      { id: 'power', name: 'power', label: '功率(W)', type: 'number', category: '技术参数', description: '额定功率', required: false },
      { id: 'voltage', name: 'voltage', label: '电压(V)', type: 'number', category: '技术参数', description: '工作电压', required: false },
      { id: 'speed', name: 'speed', label: '转速(rpm)', type: 'number', category: '技术参数', description: '额定转速', required: false },
      { id: 'efficiency', name: 'efficiency', label: '效率(%)', type: 'number', category: '技术参数', description: '工作效率', required: false },
      { id: 'weight', name: 'weight', label: '重量(kg)', type: 'number', category: '物理属性', description: '产品重量', required: false },
      { id: 'price', name: 'price', label: '价格(元)', type: 'number', category: '商务信息', description: '产品价格', required: false, common: true }
    ]
  },
  {
    id: 'filter_standard',
    name: '过滤器标准配置',
    description: '适用于各类过滤器产品的标准列配置',
    scenarios: ['过滤器', '滤芯', '净化设备'],
    columns: [
      { id: 'model', name: 'model', label: '型号', type: 'text', category: '基础信息', description: '产品型号', required: true, common: true },
      { id: 'name', name: 'name', label: '产品名称', type: 'text', category: '基础信息', description: '产品名称', required: true, common: true },
      { id: 'filter_precision', name: 'filter_precision', label: '过滤精度(μm)', type: 'number', category: '技术参数', description: '过滤精度', required: false },
      { id: 'flow_capacity', name: 'flow_capacity', label: '流量容量(L/min)', type: 'number', category: '技术参数', description: '流量容量', required: false },
      { id: 'pressure_drop', name: 'pressure_drop', label: '压降(kPa)', type: 'number', category: '技术参数', description: '压力损失', required: false },
      { id: 'material', name: 'material', label: '滤材', type: 'text', category: '物理属性', description: '过滤材料', required: false },
      { id: 'life_span', name: 'life_span', label: '使用寿命(小时)', type: 'number', category: '技术参数', description: '使用寿命', required: false },
      { id: 'price', name: 'price', label: '价格(元)', type: 'number', category: '商务信息', description: '产品价格', required: false, common: true }
    ]
  },
  {
    id: 'basic_product',
    name: '基础产品配置',
    description: '适用于一般产品的基础列配置',
    scenarios: ['通用产品', '基础配置'],
    columns: [
      { id: 'model', name: 'model', label: '型号', type: 'text', category: '基础信息', description: '产品型号', required: true, common: true },
      { id: 'name', name: 'name', label: '产品名称', type: 'text', category: '基础信息', description: '产品名称', required: true, common: true },
      { id: 'brand', name: 'brand', label: '品牌', type: 'text', category: '基础信息', description: '产品品牌', required: false },
      { id: 'specification', name: 'specification', label: '规格', type: 'text', category: '基础信息', description: '产品规格', required: false, common: true },
      { id: 'price', name: 'price', label: '价格(元)', type: 'number', category: '商务信息', description: '产品价格', required: false, common: true },
      { id: 'supplier', name: 'supplier', label: '供应商', type: 'text', category: '商务信息', description: '产品供应商', required: false },
      { id: 'status', name: 'status', label: '状态', type: 'text', category: '状态信息', description: '产品状态', required: false }
    ]
  }
])

// 监听搜索和分类变化，重新加载数据
watch([searchKeyword, selectedCategory], () => {
  if (templateType.value === 'single') {
    loadColumnTemplates()
  }
}, { debounce: 300 })

watch(searchKeyword, () => {
  if (templateType.value === 'combination') {
    loadCombinationTemplates()
  }
}, { debounce: 300 })

// 监听模板类型切换
watch(templateType, (newType) => {
  if (newType === 'single') {
    loadColumnTemplates()
  } else {
    loadCombinationTemplates()
  }
})

// 原来的硬编码数据作为备用
const fallbackColumnTemplates = ref<ColumnTemplate[]>([
  // 基础信息类
  {
    id: 'model',
    name: 'model',
    label: '型号',
    type: 'text',
    category: '基础信息',
    description: '产品型号或编号',
    required: true,
    common: true
  },
  {
    id: 'name',
    name: 'name',
    label: '产品名称',
    type: 'text',
    category: '基础信息',
    description: '产品的完整名称',
    required: true,
    common: true
  },
  {
    id: 'brand',
    name: 'brand',
    label: '品牌',
    type: 'text',
    category: '基础信息',
    description: '产品品牌',
    required: false
  },
  {
    id: 'specification',
    name: 'specification',
    label: '规格',
    type: 'text',
    category: '基础信息',
    description: '产品规格描述',
    required: false,
    common: true
  },
  
  // 技术参数类
  {
    id: 'flow_rate',
    name: 'flow_rate',
    label: '流量(L/min)',
    type: 'number',
    category: '技术参数',
    description: '流体流量参数',
    required: false,
    common: true
  },
  {
    id: 'pressure',
    name: 'pressure',
    label: '压力(MPa)',
    type: 'number',
    category: '技术参数',
    description: '工作压力参数',
    required: false,
    common: true
  },
  {
    id: 'temperature',
    name: 'temperature',
    label: '工作温度(°C)',
    type: 'number',
    category: '技术参数',
    description: '工作温度范围',
    required: false
  },
  {
    id: 'voltage',
    name: 'voltage',
    label: '电压(V)',
    type: 'number',
    category: '技术参数',
    description: '工作电压',
    required: false
  },
  {
    id: 'power',
    name: 'power',
    label: '功率(W)',
    type: 'number',
    category: '技术参数',
    description: '额定功率',
    required: false
  },
  
  // 物理属性类
  {
    id: 'weight',
    name: 'weight',
    label: '重量(kg)',
    type: 'number',
    category: '物理属性',
    description: '产品重量',
    required: false
  },
  {
    id: 'dimensions',
    name: 'dimensions',
    label: '尺寸(mm)',
    type: 'text',
    category: '物理属性',
    description: '产品外形尺寸',
    required: false
  },
  {
    id: 'material',
    name: 'material',
    label: '材质',
    type: 'text',
    category: '物理属性',
    description: '主要材质',
    required: false
  },
  {
    id: 'color',
    name: 'color',
    label: '颜色',
    type: 'text',
    category: '物理属性',
    description: '产品颜色',
    required: false
  },
  
  // 商务信息类
  {
    id: 'price',
    name: 'price',
    label: '价格(元)',
    type: 'number',
    category: '商务信息',
    description: '产品价格',
    required: false,
    common: true
  },
  {
    id: 'supplier',
    name: 'supplier',
    label: '供应商',
    type: 'text',
    category: '商务信息',
    description: '产品供应商',
    required: false
  },
  {
    id: 'lead_time',
    name: 'lead_time',
    label: '交期(天)',
    type: 'number',
    category: '商务信息',
    description: '交货周期',
    required: false
  },
  {
    id: 'warranty',
    name: 'warranty',
    label: '保修期(月)',
    type: 'number',
    category: '商务信息',
    description: '保修期限',
    required: false
  },
  
  // 状态信息类
  {
    id: 'status',
    name: 'status',
    label: '状态',
    type: 'text',
    category: '状态信息',
    description: '产品状态',
    required: false,
    options: ['在售', '停产', '缺货', '预售']
  },
  {
    id: 'is_available',
    name: 'is_available',
    label: '是否可用',
    type: 'boolean',
    category: '状态信息',
    description: '产品是否可用',
    required: false,
    defaultValue: true
  },
  {
    id: 'created_date',
    name: 'created_date',
    label: '创建日期',
    type: 'date',
    category: '状态信息',
    description: '记录创建日期',
    required: false
  },
  {
    id: 'updated_date',
    name: 'updated_date',
    label: '更新日期',
    type: 'date',
    category: '状态信息',
    description: '记录更新日期',
    required: false
  },
  
  // 其他信息类
  {
    id: 'description',
    name: 'description',
    label: '描述',
    type: 'textarea',
    category: '其他信息',
    description: '产品详细描述',
    required: false
  },
  {
    id: 'notes',
    name: 'notes',
    label: '备注',
    type: 'textarea',
    category: '其他信息',
    description: '其他备注信息',
    required: false
  },
  {
    id: 'image_url',
    name: 'image_url',
    label: '产品图片',
    type: 'text',
    category: '其他信息',
    description: '产品图片链接',
    required: false
  }
])

// 计算属性
const categories = computed(() => {
  const cats = ['全部', ...new Set(columnTemplates.value.map(t => t.category))]
  return cats
})

const filteredTemplates = computed(() => {
  let templates = columnTemplates.value

  // 过滤已存在的列
  if (props.existingColumns) {
    templates = templates.filter(t => !props.existingColumns!.includes(t.name))
  }

  // 按分类过滤
  if (selectedCategory.value !== '全部') {
    templates = templates.filter(t => t.category === selectedCategory.value)
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    templates = templates.filter(t =>
      t.label.toLowerCase().includes(keyword) ||
      t.name.toLowerCase().includes(keyword) ||
      t.description.toLowerCase().includes(keyword)
    )
  }

  return templates
})

const filteredCombinations = computed(() => {
  let combinations = combinationTemplates.value

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    combinations = combinations.filter(c =>
      c.name.toLowerCase().includes(keyword) ||
      c.description.toLowerCase().includes(keyword) ||
      c.scenarios?.some(s => s.toLowerCase().includes(keyword))
    )
  }

  return combinations
})

const hasSelection = computed(() => {
  return templateType.value === 'combination'
    ? selectedCombination.value !== null
    : selectedTemplates.value.length > 0
})

const canSaveAsCombination = computed(() => {
  return props.existingColumns && props.existingColumns.length > 0
})

// 方法
const showTemplateSelector = () => {
  templateDialogVisible.value = true
  // 打开对话框时加载数据
  if (templateType.value === 'combination') {
    loadCombinationTemplates()
  } else {
    loadColumnTemplates()
  }
}

const selectCategory = (category: string) => {
  selectedCategory.value = category
}

const toggleTemplate = (template: ColumnTemplate) => {
  const index = selectedTemplates.value.indexOf(template.id)
  if (index > -1) {
    selectedTemplates.value.splice(index, 1)
  } else {
    selectedTemplates.value.push(template.id)
  }
}

const removeTemplate = (templateId: string) => {
  const index = selectedTemplates.value.indexOf(templateId)
  if (index > -1) {
    selectedTemplates.value.splice(index, 1)
  }
}

const getTemplateById = (id: string) => {
  return columnTemplates.value.find(t => t.id === id)
}

const getCombinationById = (id: string) => {
  return combinationTemplates.value.find(c => c.id === id)
}

const selectCombination = (combination: CombinationTemplate) => {
  selectedCombination.value = selectedCombination.value === combination.id ? null : combination.id
}

const getApplyButtonText = () => {
  if (templateType.value === 'combination') {
    const combination = getCombinationById(selectedCombination.value!)
    return combination ? `应用组合 (${combination.columns.length} 列)` : '选择组合模板'
  } else {
    return `添加选中的列 (${selectedTemplates.value.length})`
  }
}

const selectAllCommon = () => {
  const commonTemplates = filteredTemplates.value.filter(t => t.common)
  commonTemplates.forEach(t => {
    if (!selectedTemplates.value.includes(t.id)) {
      selectedTemplates.value.push(t.id)
    }
  })
}

const selectAllCategory = () => {
  filteredTemplates.value.forEach(t => {
    if (!selectedTemplates.value.includes(t.id)) {
      selectedTemplates.value.push(t.id)
    }
  })
}

const applyTemplates = () => {
  let selectedColumns: ColumnTemplate[] = []

  if (templateType.value === 'combination' && selectedCombination.value) {
    const combination = getCombinationById(selectedCombination.value)
    if (combination) {
      // 过滤掉已存在的列
      selectedColumns = combination.columns.filter(col =>
        !props.existingColumns?.includes(col.name)
      )
      ElMessage.success(`成功应用组合模板，添加 ${selectedColumns.length} 个列`)
    }
  } else {
    selectedColumns = selectedTemplates.value
      .map(id => getTemplateById(id))
      .filter(Boolean) as ColumnTemplate[]
    ElMessage.success(`成功添加 ${selectedColumns.length} 个列`)
  }

  emit('addColumns', selectedColumns)
  templateDialogVisible.value = false
  resetSelection()
}

const saveCurrentAsCombination = () => {
  // TODO: 实现保存当前配置为组合模板的功能
  ElMessage.info('保存为组合模板功能开发中...')
}

const resetSelection = () => {
  selectedTemplates.value = []
  selectedCombination.value = null
  searchKeyword.value = ''
  selectedCategory.value = '全部'
}

const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    text: '',
    number: 'success',
    date: 'warning',
    boolean: 'info',
    textarea: 'danger'
  }
  return colorMap[type] || ''
}

const getTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    text: '文本',
    number: '数字',
    date: '日期',
    boolean: '布尔值',
    textarea: '长文本'
  }
  return labelMap[type] || type
}
</script>

<style scoped>
.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

.max-h-96 {
  max-height: 24rem;
}

.overflow-auto {
  overflow: auto;
}
</style>
