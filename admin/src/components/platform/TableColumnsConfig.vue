<template>
  <div class="h-full flex flex-col">
    <!-- 操作区域 -->
    <div class="flex justify-between items-center mb-4">
      <div>
        <h4 class="font-medium">列配置</h4>
        <p class="text-sm text-gray-500">共 {{ columns.length }} 列</p>
      </div>
      <div class="flex gap-2">
        <ColumnTemplateSelector
          :existing-columns="existingColumnNames"
          @add-columns="handleAddFromTemplate"
        />
        <el-button type="primary" @click="addNewColumn">
          <i class="i-mdi-plus mr-1"></i>
          手动添加
        </el-button>
      </div>
    </div>

    <!-- 列配置列表 -->
    <div class="flex-1 overflow-auto">
      <div v-if="columns.length > 0" class="space-y-2">
        <div
          v-for="column in sortedColumns"
          :key="column.id"
          class="flex items-center justify-between p-3 bg-gray-50 rounded border hover:shadow-sm transition-shadow"
        >
          <div class="flex items-center space-x-4">
            <!-- 拖拽手柄 -->
            <div class="cursor-move text-gray-400">
              <i class="i-mdi-drag-vertical"></i>
            </div>
            
            <!-- 列信息 -->
            <div>
              <div class="flex items-center space-x-2">
                <span class="font-medium">{{ column.column_label }}</span>
                <el-tag size="small" :type="getColumnTypeColor(column.column_type)">
                  {{ getColumnTypeLabel(column.column_type) }}
                </el-tag>
              </div>
              <div class="text-sm text-gray-500">
                字段名: {{ column.column_name }}
                <span v-if="column.is_required" class="text-red-500 ml-2">*必填</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex items-center space-x-2">
            <el-button size="small" @click="editColumn(column)">
              <i class="i-mdi-pencil mr-1"></i>
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="deleteColumn(column)">
              <i class="i-mdi-delete mr-1"></i>
              删除
            </el-button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="text-center py-12 text-gray-500">
        <i class="i-mdi-table-column text-4xl mb-2"></i>
        <p>暂无列配置</p>
        <p class="text-sm">点击"添加列"开始配置表格结构</p>
      </div>
    </div>

    <!-- 列编辑对话框 -->
    <el-dialog
      v-model="columnDialogVisible"
      :title="editingColumn.id ? '编辑列' : '添加列'"
      width="600px"
      @close="resetColumnForm"
    >
      <el-form
        ref="columnFormRef"
        :model="editingColumn"
        :rules="columnFormRules"
        label-width="100px"
      >
        <el-form-item label="列标签" prop="column_label">
          <el-input
            v-model="editingColumn.column_label"
            placeholder="请输入列标签（显示名称）"
          />
        </el-form-item>
        
        <el-form-item label="字段名" prop="column_name">
          <el-input
            v-model="editingColumn.column_name"
            placeholder="请输入字段名（英文，用于数据存储）"
          />
        </el-form-item>
        
        <el-form-item label="数据类型" prop="column_type">
          <el-select v-model="editingColumn.column_type" placeholder="请选择数据类型">
            <el-option label="文本" value="text" />
            <el-option label="数字" value="number" />
            <el-option label="日期" value="date" />
            <el-option label="布尔值" value="boolean" />
            <el-option label="长文本" value="textarea" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="是否必填">
          <el-switch v-model="editingColumn.is_required" />
        </el-form-item>
        
        <el-form-item label="排序" prop="sort_order">
          <el-input-number
            v-model="editingColumn.sort_order"
            :min="0"
            :max="999"
            placeholder="排序"
          />
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="editingColumn.remark"
            type="textarea"
            :rows="3"
            placeholder="列的说明信息（可选）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button @click="columnDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveColumn" :loading="saveLoading">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus';
import { computed, onMounted, reactive, ref } from 'vue';
import ColumnTemplateSelector from './ColumnTemplateSelector.vue';

// Props
interface Props {
  tableId: number
}

const props = defineProps<Props>()

// 响应式数据
const columns = ref<any[]>([])
const columnDialogVisible = ref(false)
const saveLoading = ref(false)

// 表单相关
const columnFormRef = ref<FormInstance>()
const editingColumn = reactive({
  id: undefined as number | undefined,
  column_name: '',
  column_label: '',
  column_type: 'text',
  is_required: false,
  sort_order: 0,
  remark: ''
})

const columnFormRules: FormRules = {
  column_label: [{ required: true, message: '请输入列标签', trigger: 'blur' }],
  column_name: [
    { required: true, message: '请输入字段名', trigger: 'blur' },
    { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '字段名只能包含字母、数字和下划线，且不能以数字开头', trigger: 'blur' }
  ],
  column_type: [{ required: true, message: '请选择数据类型', trigger: 'change' }]
}

// 计算属性
const sortedColumns = computed(() => {
  return [...columns.value].sort((a, b) => a.sort_order - b.sort_order)
})

const existingColumnNames = computed(() => {
  return columns.value.map(col => col.column_name)
})

// 方法
const getColumnTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    text: '',
    number: 'success',
    date: 'warning',
    boolean: 'info',
    textarea: 'danger'
  }
  return colorMap[type] || ''
}

const getColumnTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    text: '文本',
    number: '数字',
    date: '日期',
    boolean: '布尔值',
    textarea: '长文本'
  }
  return labelMap[type] || type
}

const addNewColumn = () => {
  resetColumnForm()
  editingColumn.sort_order = columns.value.length
  columnDialogVisible.value = true
}

const handleAddFromTemplate = (templates: any[]) => {
  // 批量添加模板列
  templates.forEach((template, index) => {
    const newColumn = {
      id: Date.now() + index, // 临时ID
      table_id: props.tableId,
      column_name: template.name,
      column_label: template.label,
      column_type: template.type,
      is_required: template.required || false,
      sort_order: columns.value.length + index,
      remark: template.description || ''
    }
    columns.value.push(newColumn)
  })
}

const editColumn = (column: any) => {
  Object.assign(editingColumn, { ...column })
  columnDialogVisible.value = true
}

const deleteColumn = async (column: any) => {
  try {
    // 1. 检查列是否有数据
    const hasData = await checkColumnHasData(column)

    if (hasData) {
      // 2. 如果有数据，显示详细的确认对话框
      const action = await showDataImpactDialog(column)

      if (action === 'cancel') {
        return
      }

      // 3. 根据用户选择执行相应操作
      await executeColumnDeletion(column, action)
    } else {
      // 4. 如果没有数据，直接确认删除
      await ElMessageBox.confirm(`确定要删除列"${column.column_label}"吗？`, '确认删除', {
        type: 'warning'
      })

      await executeColumnDeletion(column, 'delete-all')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 检查列是否有数据
const checkColumnHasData = async (column: any): Promise<boolean> => {
  try {
    // TODO: 调用API检查列是否有数据
    // const response = await platformConfigApi.checkColumnData(props.tableId, column.id)
    // return response.data.hasData

    // 模拟检查：假设有数据
    return Math.random() > 0.5 // 50%概率有数据，用于演示
  } catch (error) {
    console.error('检查列数据失败:', error)
    return false
  }
}

// 显示数据影响对话框
const showDataImpactDialog = (column: any): Promise<string> => {
  return new Promise((resolve) => {
    ElMessageBox({
      title: '删除列配置',
      message: `
        <div style="padding: 10px 0;">
          <p style="margin-bottom: 15px; font-weight: 500;">
            列"<span style="color: #409eff;">${column.column_label}</span>"包含数据，请选择处理方式：
          </p>
          <div style="background: #f5f7fa; padding: 15px; border-radius: 6px; margin-bottom: 15px;">
            <div style="display: flex; align-items: center; margin-bottom: 10px;">
              <i class="el-icon-warning" style="color: #e6a23c; margin-right: 8px;"></i>
              <span style="font-weight: 500;">数据影响说明</span>
            </div>
            <ul style="margin: 0; padding-left: 20px; color: #606266;">
              <li style="margin-bottom: 5px;">删除列和数据：永久删除，无法恢复</li>
              <li style="margin-bottom: 5px;">仅删除列配置：数据保留，但界面中不显示</li>
              <li>取消操作：不进行任何更改</li>
            </ul>
          </div>
        </div>
      `,
      dangerouslyUseHTMLString: true,
      showCancelButton: true,
      showConfirmButton: false,
      customClass: 'column-delete-dialog',
      beforeClose: (action, instance, done) => {
        if (action === 'cancel') {
          resolve('cancel')
          done()
        }
      }
    }).then(() => {
      // 这里不会执行，因为没有确认按钮
    }).catch(() => {
      resolve('cancel')
    })

    // 添加自定义按钮
    setTimeout(() => {
      const dialog = document.querySelector('.column-delete-dialog')
      if (dialog) {
        const footer = dialog.querySelector('.el-message-box__btns')
        if (footer) {
          footer.innerHTML = `
            <button class="el-button el-button--danger" onclick="window.resolveColumnDelete('delete-all')">
              <i class="el-icon-delete"></i> 删除列和数据
            </button>
            <button class="el-button el-button--warning" onclick="window.resolveColumnDelete('delete-config-only')">
              <i class="el-icon-view"></i> 仅删除列配置
            </button>
            <button class="el-button" onclick="window.resolveColumnDelete('cancel')">
              取消
            </button>
          `
        }
      }
    }, 100)

    // 全局回调函数
    ;(window as any).resolveColumnDelete = (action: string) => {
      resolve(action)
      const dialog = document.querySelector('.column-delete-dialog')
      if (dialog) {
        const closeBtn = dialog.querySelector('.el-message-box__close') as HTMLElement
        if (closeBtn) {
          closeBtn.click()
        }
      }
    }
  })
}

// 执行列删除操作
const executeColumnDeletion = async (column: any, action: string) => {
  try {
    if (action === 'delete-all') {
      // 删除列和数据
      // TODO: 调用API删除列和数据
      // await platformConfigApi.deleteColumnWithData(props.tableId, column.id)

      const index = columns.value.findIndex(c => c.id === column.id)
      if (index > -1) {
        columns.value.splice(index, 1)
      }

      ElMessage.success('列和数据已删除')
    } else if (action === 'delete-config-only') {
      // 仅删除列配置，保留数据
      // TODO: 调用API仅删除列配置
      // await platformConfigApi.deleteColumnConfigOnly(props.tableId, column.id)

      const index = columns.value.findIndex(c => c.id === column.id)
      if (index > -1) {
        columns.value.splice(index, 1)
      }

      ElMessage.success('列配置已删除，数据已保留')
    }
  } catch (error) {
    ElMessage.error('删除操作失败')
    throw error
  }
}

const saveColumn = async () => {
  if (!columnFormRef.value) return

  try {
    await columnFormRef.value.validate()
    saveLoading.value = true

    const columnData = {
      table_id: props.tableId,
      column_name: editingColumn.column_name,
      column_label: editingColumn.column_label,
      column_type: editingColumn.column_type,
      is_required: editingColumn.is_required,
      sort_order: editingColumn.sort_order,
      remark: editingColumn.remark
    }

    if (editingColumn.id) {
      // 更新现有列
      // TODO: 调用更新API
      // await platformConfigApi.updateTableColumn(editingColumn.id, columnData)
      const index = columns.value.findIndex(c => c.id === editingColumn.id)
      if (index > -1) {
        columns.value[index] = { ...editingColumn }
      }
    } else {
      // 添加新列
      // TODO: 调用创建API
      // const response = await platformConfigApi.createTableColumn(columnData)
      const newColumn = {
        ...editingColumn,
        id: Date.now(), // 临时ID，实际应该使用API返回的ID
        table_id: props.tableId
      }
      columns.value.push(newColumn)
    }

    ElMessage.success(editingColumn.id ? '更新成功' : '添加成功')
    columnDialogVisible.value = false
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

const resetColumnForm = () => {
  Object.assign(editingColumn, {
    id: undefined,
    column_name: '',
    column_label: '',
    column_type: 'text',
    is_required: false,
    sort_order: 0,
    remark: ''
  })
  columnFormRef.value?.clearValidate()
}

const loadColumns = async () => {
  if (!props.tableId) return

  try {
    // TODO: 调用真实API
    // const response = await platformConfigApi.getTableColumns(props.tableId)
    // columns.value = response.data

    // 临时使用模拟数据
    columns.value = [
      {
        id: 1,
        table_id: props.tableId,
        column_name: 'model',
        column_label: '型号',
        column_type: 'text',
        is_required: true,
        sort_order: 0,
        remark: '产品型号'
      },
      {
        id: 2,
        table_id: props.tableId,
        column_name: 'flow_rate',
        column_label: '流量(L/min)',
        column_type: 'number',
        is_required: false,
        sort_order: 1,
        remark: '流量参数'
      },
      {
        id: 3,
        table_id: props.tableId,
        column_name: 'pressure',
        column_label: '压力(MPa)',
        column_type: 'number',
        is_required: false,
        sort_order: 2,
        remark: '压力参数'
      }
    ]
  } catch (error) {
    ElMessage.error('加载列配置失败')
    console.error('加载列配置失败:', error)
  }
}

onMounted(() => {
  loadColumns()
})
</script>

<style scoped>
.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.flex-1 {
  flex: 1;
}

.h-full {
  height: 100%;
}
</style>

<style>
/* 列删除对话框样式 */
.column-delete-dialog .el-message-box {
  width: 480px;
}

.column-delete-dialog .el-message-box__content {
  padding: 20px 20px 10px;
}

.column-delete-dialog .el-message-box__btns {
  padding: 15px 20px 20px;
  text-align: center;
}

.column-delete-dialog .el-message-box__btns .el-button {
  margin: 0 8px;
  min-width: 120px;
}

.column-delete-dialog .el-message-box__btns .el-button--danger {
  background: #f56565;
  border-color: #f56565;
}

.column-delete-dialog .el-message-box__btns .el-button--danger:hover {
  background: #e53e3e;
  border-color: #e53e3e;
}

.column-delete-dialog .el-message-box__btns .el-button--warning {
  background: #ed8936;
  border-color: #ed8936;
  color: white;
}

.column-delete-dialog .el-message-box__btns .el-button--warning:hover {
  background: #dd6b20;
  border-color: #dd6b20;
}
</style>
