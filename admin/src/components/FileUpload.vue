<template>
  <div class="file-upload">
    <!-- 上传区域 -->
    <el-upload
      ref="uploadRef"
      class="upload-container"
      :action="uploadAction"
      :headers="uploadHeaders"
      :data="uploadData"
      :multiple="multiple"
      :accept="acceptTypes"
      :before-upload="beforeUpload"
      :on-success="onSuccess"
      :on-error="onError"
      :on-progress="onProgress"
      :on-remove="onRemove"
      :file-list="fileList"
      :list-type="listType"
      :auto-upload="autoUpload"
      :show-file-list="showFileList"
      drag
    >
      <div class="upload-content">
        <el-icon class="upload-icon">
          <Plus />
        </el-icon>
        <div class="upload-text">
          <p>点击上传或拖拽文件到此区域</p>
          <p class="upload-tip">
            {{ acceptTip }}，单个文件不超过 {{ formatFileSize(maxSize) }}
          </p>
        </div>
      </div>
    </el-upload>

    <!-- 文件列表 -->
    <div v-if="!showFileList && fileList.length > 0" class="custom-file-list">
      <div
        v-for="file in fileList"
        :key="file.uid"
        class="file-item"
        :class="{ 'is-uploading': file.status === 'uploading' }"
      >
        <!-- 文件图标/预览 -->
        <div class="file-preview">
          <el-image
            v-if="isImageFile(file)"
            :src="getFileUrl(file)"
            class="file-image"
            fit="cover"
            :preview-src-list="[getFileUrl(file)]"
          />
          <el-icon v-else class="file-icon">
            <component :is="getFileIcon(file)" />
          </el-icon>
        </div>

        <!-- 文件信息 -->
        <div class="file-info">
          <div class="file-name" :title="file.name">{{ file.name }}</div>
          <div class="file-meta">
            <span class="file-size">{{ formatFileSize(file.size || 0) }}</span>
            <span v-if="file.status === 'success'" class="file-status success">
              上传成功
            </span>
            <span v-else-if="file.status === 'uploading'" class="file-status uploading">
              上传中...
            </span>
            <span v-else-if="file.status === 'ready'" class="file-status ready">
              等待上传
            </span>
          </div>
        </div>

        <!-- 进度条 -->
        <div v-if="file.status === 'uploading'" class="file-progress">
          <el-progress
            :percentage="file.percentage || 0"
            :stroke-width="4"
            :show-text="false"
          />
        </div>

        <!-- 操作按钮 -->
        <div class="file-actions">
          <el-button
            v-if="file.status === 'success'"
            type="primary"
            size="small"
            text
            @click="viewFile(file)"
          >
            查看
          </el-button>
          <el-button
            type="danger"
            size="small"
            text
            @click="removeFile(file)"
          >
            删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 手动上传按钮 -->
    <div v-if="!autoUpload && fileList.length > 0" class="upload-actions">
      <el-button type="primary" @click="submitUpload" :loading="uploading">
        开始上传
      </el-button>
      <el-button @click="clearFiles">清空列表</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { isImageFile as checkIsImageFile, formatFileSize, getFileTypeIcon, getUploadConfig } from '@/api/upload'
import { Document, Folder, Grid, Monitor, Picture, Plus } from '@element-plus/icons-vue'
import { ElMessage, type UploadFile, type UploadFiles, type UploadInstance } from 'element-plus'
import { computed, nextTick, ref } from 'vue'

// Props 定义
interface Props {
  module?: string // 上传模块
  multiple?: boolean // 是否支持多文件上传
  maxSize?: number // 最大文件大小（字节）
  accept?: string // 接受的文件类型
  listType?: 'text' | 'picture' | 'picture-card' // 文件列表类型
  autoUpload?: boolean // 是否自动上传
  showFileList?: boolean // 是否显示文件列表
  modelValue?: UploadFile[] // v-model 绑定的文件列表
}

const props = withDefaults(defineProps<Props>(), {
  module: 'general',
  multiple: false,
  maxSize: 50 * 1024 * 1024, // 50MB
  accept: '',
  listType: 'text',
  autoUpload: true,
  showFileList: false,
  modelValue: () => []
})

// Emits 定义
interface Emits {
  (e: 'update:modelValue', value: UploadFile[]): void
  (e: 'success', file: UploadFile, response: any): void
  (e: 'error', file: UploadFile, error: any): void
  (e: 'remove', file: UploadFile): void
  (e: 'progress', file: UploadFile, percentage: number): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const uploadRef = ref<UploadInstance>()
const uploading = ref(false)
const fileList = ref<UploadFiles>([...props.modelValue])
const uploadConfig = ref<any>({})

// 计算属性
const uploadAction = computed(() => {
  return `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api'}/files/upload`
})

const uploadHeaders = computed(() => {
  const token = localStorage.getItem('admin_token')
  return token ? { Authorization: `Bearer ${token}` } : {}
})

const uploadData = computed(() => {
  return { module: props.module }
})

const acceptTypes = computed(() => {
  if (props.accept) return props.accept
  return uploadConfig.value.allowedTypes?.join(',') || ''
})

const acceptTip = computed(() => {
  if (!uploadConfig.value.allowedTypes?.length) return '支持所有文件类型'
  
  const types = uploadConfig.value.allowedTypes.map((type: string) => {
    if (type.startsWith('image/')) return '图片'
    if (type.includes('pdf')) return 'PDF'
    if (type.includes('word') || type.includes('document')) return 'Word'
    if (type.includes('excel') || type.includes('spreadsheet')) return 'Excel'
    if (type.includes('powerpoint') || type.includes('presentation')) return 'PPT'
    if (type.includes('zip') || type.includes('rar')) return '压缩包'
    if (type.includes('text')) return '文本'
    return type
  })
  
  return `支持${[...new Set(types)].join('、')}文件`
})

// 初始化
const init = async () => {
  try {
    const config = await getUploadConfig()
    uploadConfig.value = config
  } catch (error) {
    console.error('获取上传配置失败:', error)
  }
}

// 上传前验证
const beforeUpload = (file: File) => {
  // 文件大小验证
  if (file.size > props.maxSize) {
    ElMessage.error(`文件大小不能超过 ${formatFileSize(props.maxSize)}`)
    return false
  }

  // 文件类型验证
  if (uploadConfig.value.allowedTypes?.length && !props.accept) {
    const isAllowed = uploadConfig.value.allowedTypes.some((type: string) => 
      file.type === type || file.type.startsWith(type.split('/')[0] + '/')
    )
    
    if (!isAllowed) {
      ElMessage.error('不支持的文件类型')
      return false
    }
  }

  return true
}

// 上传成功
const onSuccess = (response: any, file: UploadFile, fileList: UploadFiles) => {
  ;(file as any).response = response
  file.status = 'success'
  emit('success', file, response)
  updateModelValue()
}

// 上传失败
const onError = (error: any, file: UploadFile, fileList: UploadFiles) => {
  file.status = 'fail'
  emit('error', file, error)
  ElMessage.error(`文件上传失败：${file.name}`)
}

// 上传进度
const onProgress = (event: any, file: UploadFile, fileList: UploadFiles) => {
  file.percentage = event.percent
  emit('progress', file, event.percent)
}

// 移除文件
const onRemove = (file: UploadFile, fileList: UploadFiles) => {
  emit('remove', file)
  updateModelValue()
}

// 手动移除文件
const removeFile = (file: UploadFile) => {
  const index = fileList.value.findIndex(f => f.uid === file.uid)
  if (index > -1) {
    fileList.value.splice(index, 1)
    updateModelValue()
  }
}

// 查看文件
const viewFile = (file: UploadFile) => {
  const url = getFileUrl(file)
  if (url) {
    window.open(url, '_blank')
  }
}

// 获取文件URL
const getFileUrl = (file: UploadFile): string => {
  const response = (file as any).response
  if (response?.cosUrl) {
    return response.cosUrl
  }
  if (file.url) {
    return file.url
  }
  return ''
}

// 获取文件图标
const getFileIcon = (file: UploadFile) => {
  const mimeType = file.raw?.type || (file as any).type || ''
  const iconName = getFileTypeIcon(mimeType)
  
  switch (iconName) {
    case 'Picture': return Picture
    case 'Grid': return Grid
    case 'Monitor': return Monitor
    case 'Folder': return Folder
    default: return Document
  }
}

// 判断是否为图片文件
const isImageFile = (file: UploadFile): boolean => {
  const mimeType = file.raw?.type || (file as any).type || ''
  return checkIsImageFile(mimeType)
}

// 手动上传
const submitUpload = () => {
  uploading.value = true
  uploadRef.value?.submit()
  
  // 等待上传完成
  nextTick(() => {
    uploading.value = false
  })
}

// 清空文件列表
const clearFiles = () => {
  uploadRef.value?.clearFiles()
  fileList.value = []
  updateModelValue()
}

// 更新模型值
const updateModelValue = () => {
  emit('update:modelValue', [...fileList.value])
}

// 监听文件列表变化
const watchFileList = () => {
  fileList.value = [...props.modelValue]
}

// 初始化
init()

// 暴露方法给父组件
defineExpose({
  submit: submitUpload,
  clear: clearFiles,
  uploadRef
})
</script>

<style scoped lang="scss">
.file-upload {
  .upload-container {
    :deep(.el-upload) {
      width: 100%;
    }

    :deep(.el-upload-dragger) {
      width: 100%;
      height: 120px;
      border: 2px dashed var(--el-border-color);
      border-radius: 8px;
      background-color: var(--el-bg-color-page);
      transition: all 0.3s ease;

      &:hover {
        border-color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
      }
    }

    .upload-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      
      .upload-icon {
        font-size: 32px;
        color: var(--el-color-primary);
        margin-bottom: 12px;
      }

      .upload-text {
        text-align: center;
        
        p {
          margin: 0;
          color: var(--el-text-color-regular);
          
          &.upload-tip {
            font-size: 12px;
            color: var(--el-text-color-placeholder);
            margin-top: 4px;
          }
        }
      }
    }
  }

  .custom-file-list {
    margin-top: 16px;
    
    .file-item {
      display: flex;
      align-items: center;
      padding: 12px;
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      margin-bottom: 8px;
      background-color: var(--el-bg-color);
      transition: all 0.3s ease;

      &:hover {
        border-color: var(--el-color-primary-light-7);
        background-color: var(--el-color-primary-light-9);
      }

      &.is-uploading {
        border-color: var(--el-color-primary);
      }

      .file-preview {
        width: 40px;
        height: 40px;
        margin-right: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
        background-color: var(--el-bg-color-page);

        .file-image {
          width: 100%;
          height: 100%;
          border-radius: 6px;
        }

        .file-icon {
          font-size: 24px;
          color: var(--el-color-primary);
        }
      }

      .file-info {
        flex: 1;
        min-width: 0;

        .file-name {
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-primary);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-bottom: 4px;
        }

        .file-meta {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 12px;

          .file-size {
            color: var(--el-text-color-regular);
          }

          .file-status {
            &.success {
              color: var(--el-color-success);
            }
            &.uploading {
              color: var(--el-color-primary);
            }
            &.ready {
              color: var(--el-color-warning);
            }
          }
        }
      }

      .file-progress {
        width: 100px;
        margin: 0 12px;
      }

      .file-actions {
        display: flex;
        gap: 8px;
      }
    }
  }

  .upload-actions {
    margin-top: 16px;
    display: flex;
    gap: 12px;
  }
}
</style> 