import type { PropType } from 'vue'
import { defineComponent } from 'vue'

type Status = 'success' | 'warning' | 'error' | 'info'

export default defineComponent({
  name: 'StatusCard',
  props: {
    title: {
      type: String,
      required: true
    },
    value: {
      type: [String, Number],
      required: true
    },
    status: {
      type: String as PropType<Status>,
      default: 'info'
    },
    icon: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const getStatusClass = () => {
      const baseClass = 'p-6 rounded-lg border-l-4'
      switch (props.status) {
        case 'success':
          return `${baseClass} bg-green-50 border-green-500`
        case 'warning':
          return `${baseClass} bg-yellow-50 border-yellow-500`
        case 'error':
          return `${baseClass} bg-red-50 border-red-500`
        case 'info':
        default:
          return `${baseClass} bg-blue-50 border-blue-500`
      }
    }

    const getIconClass = () => {
      if (props.icon) return props.icon
      
      switch (props.status) {
        case 'success':
          return 'i-mdi-check-circle text-green-500'
        case 'warning':
          return 'i-mdi-alert text-yellow-500'
        case 'error':
          return 'i-mdi-alert-circle text-red-500'
        case 'info':
        default:
          return 'i-mdi-information text-blue-500'
      }
    }

    const getValueClass = () => {
      switch (props.status) {
        case 'success':
          return 'text-green-600'
        case 'warning':
          return 'text-yellow-600'
        case 'error':
          return 'text-red-600'
        case 'info':
        default:
          return 'text-blue-600'
      }
    }

    return () => (
      <div class={getStatusClass()}>
        <div class="flex items-center justify-between">
          <div class="flex-1">
            <h3 class="text-sm font-medium text-gray-600 mb-2">
              {props.title}
            </h3>
            <p class={`text-2xl font-bold ${getValueClass()}`}>
              {props.value}
            </p>
          </div>
          <div class="ml-4">
            <i class={`${getIconClass()} text-2xl`}></i>
          </div>
        </div>
      </div>
    )
  }
}) 