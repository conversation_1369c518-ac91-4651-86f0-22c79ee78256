import { defineComponent } from 'vue'

// 简单的函数式组件示例
export default defineComponent((props) => {
  return () => (
    <div class="p-4 bg-white rounded-lg shadow-sm border border-gray-200">
      <h3 class="text-lg font-semibold text-gray-800 mb-2">
        {props.title || '默认标题'}
      </h3>
      <p class="text-gray-600">
        {props.content || '这是一个使用 JSX 语法的简单卡片组件'}
      </p>
      {props.showButton && (
        <button 
          class="mt-3 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          onClick={props.onButtonClick}
        >
          {props.buttonText || '点击按钮'}
        </button>
      )}
    </div>
  )
}) 