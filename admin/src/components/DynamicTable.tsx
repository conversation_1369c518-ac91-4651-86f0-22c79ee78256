import type { PropType } from 'vue'
import { defineComponent, ref } from 'vue'

interface TableColumn {
  key: string
  label: string
  width?: number
  type?: 'text' | 'tag' | 'actions' | 'date'
  formatter?: (value: any, row: any) => string
  tagType?: (value: any) => 'success' | 'warning' | 'danger' | 'info'
}

interface TableAction {
  label: string
  type?: 'primary' | 'success' | 'warning' | 'danger'
  icon?: string
  onClick: (row: any) => void
  show?: (row: any) => boolean
}

export default defineComponent({
  name: 'DynamicTable',
  props: {
    columns: {
      type: Array as PropType<TableColumn[]>,
      required: true
    },
    data: {
      type: Array as PropType<Record<string, any>[]>,
      required: true
    },
    actions: {
      type: Array as PropType<TableAction[]>,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    showSelection: {
      type: Boolean,
      default: false
    }
  },
  emits: ['selection-change'],
  setup(props, { emit }) {
    const selectedRows = ref<any[]>([])

    const handleSelectionChange = (selection: any[]) => {
      selectedRows.value = selection
      emit('selection-change', selection)
    }

    const renderCellContent = (column: TableColumn, row: any) => {
      const value = row[column.key]
      
      switch (column.type) {
        case 'tag':
          const tagType = column.tagType ? column.tagType(value) : 'info'
          return (
            <el-tag type={tagType}>
              {column.formatter ? column.formatter(value, row) : value}
            </el-tag>
          )
        
        case 'date':
          const dateValue = column.formatter ? column.formatter(value, row) : value
          return <span class="text-gray-600">{dateValue}</span>
        
        case 'actions':
          return (
            <div class="flex gap-2">
              {props.actions
                .filter(action => !action.show || action.show(row))
                .map((action, index) => (
                  <el-button
                    key={index}
                    size="small"
                    type={action.type || 'primary'}
                    onClick={() => action.onClick(row)}
                  >
                    {action.icon && <i class={`${action.icon} mr-1`}></i>}
                    {action.label}
                  </el-button>
                ))
              }
            </div>
          )
        
        case 'text':
        default:
          return column.formatter ? column.formatter(value, row) : value
      }
    }

    const renderColumns = () => {
      const columns = []
      
      // 选择列
      if (props.showSelection) {
        columns.push(
          <el-table-column
            type="selection"
            width="55"
            align="center"
          />
        )
      }
      
      // 数据列
      props.columns.forEach(column => {
        columns.push(
          <el-table-column
            key={column.key}
            prop={column.key}
            label={column.label}
            width={column.width}
            align={column.type === 'actions' ? 'center' : 'left'}
            v-slots={{
              default: ({ row }: { row: any }) => renderCellContent(column, row)
            }}
          />
        )
      })
      
      return columns
    }

    const renderEmptyState = () => (
      <div class="text-center py-12">
        <i class="i-mdi-inbox text-6xl text-gray-300 mb-4"></i>
        <p class="text-gray-500 text-lg">暂无数据</p>
        <p class="text-gray-400 text-sm mt-2">请添加一些数据或调整筛选条件</p>
      </div>
    )

    const renderLoadingState = () => (
      <div class="text-center py-12">
        <el-icon class="is-loading text-4xl text-blue-500 mb-4">
          <i class="i-mdi-loading"></i>
        </el-icon>
        <p class="text-gray-500">数据加载中...</p>
      </div>
    )

    return () => (
      <div class="dynamic-table">
        {props.loading ? (
          renderLoadingState()
        ) : props.data.length === 0 ? (
          renderEmptyState()
        ) : (
          <el-table
            data={props.data}
            style="width: 100%"
            onSelection-change={handleSelectionChange}
            stripe
            border
          >
            {renderColumns()}
          </el-table>
        )}
      </div>
    )
  }
}) 