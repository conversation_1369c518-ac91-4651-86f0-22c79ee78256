import { readonly, ref } from 'vue'

/**
 * API调用组合式函数
 * @param apiFunction API函数
 * @returns 包含loading、error、data和execute方法的对象
 */
export function useApi<T>(apiFunction: (...args: any[]) => Promise<T>): any {
  const loading = ref(false)
  const error = ref<string | null>(null)
  const data = ref<T | null>(null)

  const execute = async (...args: any[]): Promise<T> => {
    loading.value = true
    error.value = null
    
    try {
      const result = await apiFunction(...args)
      data.value = result
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '请求失败'
      error.value = errorMessage
      throw err
    } finally {
      loading.value = false
    }
  }

  const reset = () => {
    loading.value = false
    error.value = null
    data.value = null
  }

  return {
    loading: readonly(loading),
    error: readonly(error),
    // @ts-ignore - Vue 3 readonly type inference issue
    data: readonly(data),
    execute,
    reset
  }
}

/**
 * 表格数据管理组合式函数
 * @param apiFunction 获取列表数据的API函数
 * @param initialParams 初始参数
 * @returns 表格数据管理相关的状态和方法
 */
export function useTable<T>(
  apiFunction: (params: any) => Promise<{ list: T[]; total: number; page: number; size: number }>,
  initialParams: Record<string, any> = {}
) {
  const loading = ref(false)
  const data = ref<T[]>([])
  const total = ref(0)
  const searchParams = ref({ ...initialParams })
  
  const pagination = ref({
    page: 1,
    size: 10
  })

  const fetchData = async (resetPage = false) => {
    if (resetPage) {
      pagination.value.page = 1
    }
    
    loading.value = true
    try {
      const params = {
        ...searchParams.value,
        page: pagination.value.page,
        size: pagination.value.size
      }
      
      const result = await apiFunction(params)
      data.value = result.list
      total.value = result.total
    } catch (error) {
      console.error('获取数据失败:', error)
      data.value = []
      total.value = 0
    } finally {
      loading.value = false
    }
  }

  const handleSearch = (params: Record<string, any>) => {
    searchParams.value = { ...params }
    fetchData(true)
  }

  const handlePageChange = (page: number) => {
    pagination.value.page = page
    fetchData()
  }

  const handleSizeChange = (size: number) => {
    pagination.value.size = size
    fetchData(true)
  }

  const refresh = () => {
    fetchData()
  }

  const reset = () => {
    searchParams.value = { ...initialParams }
    pagination.value = { page: 1, size: 10 }
    data.value = []
    total.value = 0
  }

  return {
    loading: readonly(loading),
    data: readonly(data),
    total: readonly(total),
    pagination: readonly(pagination),
    searchParams,
    fetchData,
    handleSearch,
    handlePageChange,
    handleSizeChange,
    refresh,
    reset
  }
}

/**
 * 表单管理组合式函数
 * @param initialData 初始表单数据
 * @param submitFunction 提交函数
 * @returns 表单管理相关的状态和方法
 */
export function useForm<T extends Record<string, any>>(
  initialData: T,
  submitFunction: (data: T) => Promise<any>
): any {
  const formData = ref<T>({ ...initialData })
  const loading = ref(false)
  const errors = ref<Record<string, string>>({})

  const resetForm = () => {
    formData.value = { ...initialData }
    errors.value = {}
  }

  const setData = (data: Partial<T>) => {
    formData.value = { ...formData.value, ...data }
  }

  const setErrors = (newErrors: Record<string, string>) => {
    errors.value = newErrors
  }

  const clearErrors = () => {
    errors.value = {}
  }

  const submit = async (): Promise<any> => {
    loading.value = true
    errors.value = {}
    
    try {
      const result = await submitFunction(formData.value)
      return result
    } catch (error: any) {
      if (error.response?.data?.errors) {
        errors.value = error.response.data.errors
      }
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    // @ts-ignore - Vue 3 ref type inference issue
    formData,
    loading: readonly(loading),
    errors: readonly(errors),
    resetForm,
    setData,
    setErrors,
    clearErrors,
    submit
  }
} 