import {
    adminLogs<PERSON>pi,
    adminUsersApi,
    permissionsApi,
    rolesApi,
    type AdminLog,
    type AdminUser,
    type Permission,
    type QueryAdminUserParams,
    type Role
} from '@/api'
import { ElMessage, ElMessageBox } from 'element-plus'
import { reactive, ref } from 'vue'

// 管理员用户管理
export function useAdminUsers() {
  const loading = ref(false)
  const users = ref<AdminUser[]>([])
  const total = ref(0)
  
  const pagination = reactive({
    page: 1,
    size: 10
  })

  const searchParams = reactive({
    keyword: '',
    status: undefined as 'active' | 'inactive' | 'banned' | undefined,
    roleId: undefined as number | undefined,
    startDate: '',
    endDate: ''
  })

  // 获取用户列表
  const fetchUsers = async () => {
    loading.value = true
    try {
      // 构建符合后端期望的参数格式
      const params: QueryAdminUserParams = {
        keyword: searchParams.keyword,
        status: searchParams.status,
        role_id: searchParams.roleId,
        start_date: searchParams.startDate,
        end_date: searchParams.endDate,
        page: pagination.page,
        size: pagination.size,
        page_size: pagination.size
      }
      
      const response = await adminUsersApi.getUsers(params)
      users.value = response.list || []
      total.value = response.total || 0
    } catch (error) {
      ElMessage.error('获取用户列表失败')
    } finally {
      loading.value = false
    }
  }

  // 删除用户
  const deleteUser = async (id: number, username: string) => {
    try {
      await ElMessageBox.confirm(`确定要删除用户"${username}"吗？`, '确认删除', {
        type: 'warning'
      })
      await adminUsersApi.deleteUser(id)
      ElMessage.success('删除成功')
      fetchUsers()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 重置密码
  const resetPassword = async (id: number, username: string) => {
    try {
      await ElMessageBox.confirm(`确定要重置用户"${username}"的密码吗？`, '确认重置', {
        type: 'warning'
      })
      const result = await adminUsersApi.resetPassword(id)
      ElMessage.success(`密码重置成功，新密码：${result.password}`)
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('重置密码失败')
      }
    }
  }

  // 切换用户状态
  const toggleUserStatus = async (id: number) => {
    try {
      await adminUsersApi.toggleStatus(id)
      ElMessage.success('状态切换成功')
      fetchUsers()
    } catch (error) {
      ElMessage.error('状态切换失败')
    }
  }

  return {
    loading,
    users,
    total,
    pagination,
    searchParams,
    fetchUsers,
    deleteUser,
    resetPassword,
    toggleUserStatus
  }
}

// 角色管理
export function useRoles() {
  const loading = ref(false)
  const roles = ref<Role[]>([])
  const total = ref(0)
  
  const pagination = reactive({
    page: 1,
    size: 10
  })

  const searchParams = reactive({
    keyword: '',
    status: undefined as 'active' | 'inactive' | undefined
  })

  // 获取角色列表
  const fetchRoles = async () => {
    loading.value = true
    try {
      const response = await rolesApi.getRoles({
        ...searchParams,
        ...pagination
      })
      roles.value = response.list || []
      total.value = response.total
    } catch (error) {
      ElMessage.error('获取角色列表失败')
    } finally {
      loading.value = false
    }
  }

  // 获取所有角色（不分页）
  const fetchAllRoles = async () => {
    try {
      const response = await rolesApi.getAllRoles()
      return response
    } catch (error) {
      ElMessage.error('获取角色列表失败')
      return []
    }
  }

  // 删除角色
  const deleteRole = async (id: number, name: string) => {
    try {
      await ElMessageBox.confirm(`确定要删除角色"${name}"吗？`, '确认删除', {
        type: 'warning'
      })
      await rolesApi.deleteRole(id)
      ElMessage.success('删除成功')
      fetchRoles()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 切换角色状态
  const toggleRoleStatus = async (id: number) => {
    try {
      await rolesApi.toggleStatus(id)
      ElMessage.success('状态切换成功')
      fetchRoles()
    } catch (error) {
      ElMessage.error('状态切换失败')
    }
  }

  return {
    loading,
    roles,
    total,
    pagination,
    searchParams,
    fetchRoles,
    fetchAllRoles,
    deleteRole,
    toggleRoleStatus
  }
}

// 权限管理
export function usePermissions() {
  const loading = ref(false)
  const permissions = ref<Permission[]>([])
  const permissionTree = ref<Permission[]>([])
  
  // 获取权限树
  const fetchPermissionTree = async () => {
    loading.value = true
    try {
      const response = await permissionsApi.getPermissionTree()
      permissionTree.value = response
      return response
    } catch (error) {
      ElMessage.error('获取权限树失败')
      return []
    } finally {
      loading.value = false
    }
  }

  // 获取所有权限
  const fetchAllPermissions = async () => {
    try {
      const response = await permissionsApi.getAllPermissions()
      permissions.value = response
      return response
    } catch (error) {
      ElMessage.error('获取权限列表失败')
      return []
    }
  }

  // 删除权限
  const deletePermission = async (id: number, name: string) => {
    try {
      await ElMessageBox.confirm(`确定要删除权限"${name}"吗？`, '确认删除', {
        type: 'warning'
      })
      await permissionsApi.deletePermission(id)
      ElMessage.success('删除成功')
      fetchPermissionTree()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 切换权限状态
  const togglePermissionStatus = async (id: number) => {
    try {
      await permissionsApi.toggleStatus(id)
      ElMessage.success('状态切换成功')
      fetchPermissionTree()
    } catch (error) {
      ElMessage.error('状态切换失败')
    }
  }

  return {
    loading,
    permissions,
    permissionTree,
    fetchPermissionTree,
    fetchAllPermissions,
    deletePermission,
    togglePermissionStatus
  }
}

// 操作日志管理
export function useAdminLogs() {
  const loading = ref(false)
  const logs = ref<AdminLog[]>([])
  const total = ref(0)
  const statistics = ref<any>({})
  
  const pagination = reactive({
    page: 1,
    size: 10
  })

  const searchParams = reactive({
    keyword: '',
    userId: undefined as number | undefined,
    module: '',
    action: '',
    status: undefined as 'success' | 'error' | undefined,
    startDate: '',
    endDate: ''
  })

  // 获取日志列表
  const fetchLogs = async () => {
    loading.value = true
    try {
      const response = await adminLogsApi.getLogs({
        ...searchParams,
        ...pagination
      })
      logs.value = response.list || []
      total.value = response.total
    } catch (error) {
      ElMessage.error('获取日志列表失败')
    } finally {
      loading.value = false
    }
  }

  // 获取统计数据
  const fetchStatistics = async (startDate?: string, endDate?: string) => {
    try {
      const response = await adminLogsApi.getStatistics(startDate, endDate)
      statistics.value = response
      return response
    } catch (error) {
      ElMessage.error('获取统计数据失败')
      return null
    }
  }

  // 清空日志
  const clearLogs = async (beforeDate?: string) => {
    try {
      const message = beforeDate 
        ? `确定要清空${beforeDate}之前的所有日志吗？` 
        : '确定要清空所有日志吗？'
      
      await ElMessageBox.confirm(message, '确认清空', {
        type: 'warning'
      })
      
      await adminLogsApi.clearLogs(beforeDate)
      ElMessage.success('清空成功')
      fetchLogs()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('清空失败')
      }
    }
  }

  // 导出日志
  const exportLogs = async () => {
    try {
      const blob = await adminLogsApi.exportLogs({
        ...searchParams,
        ...pagination
      })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `admin-logs-${new Date().toISOString().split('T')[0]}.xlsx`
      link.click()
      window.URL.revokeObjectURL(url)
      ElMessage.success('导出成功')
    } catch (error) {
      ElMessage.error('导出失败')
    }
  }

  return {
    loading,
    logs,
    total,
    statistics,
    pagination,
    searchParams,
    fetchLogs,
    fetchStatistics,
    clearLogs,
    exportLogs
  }
} 