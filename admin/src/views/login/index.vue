<template>
  <div class="login-container">
    <!-- 初始化加载状态 -->
    <div v-if="initializing" class="login-box">
      <div class="login-header">
        <div class="logo-section">
          <img src="/logo.svg" alt="蔚之领域" class="logo" />
        </div>
        <h2>蔚之领域管理后台</h2>
        <p>正在初始化...</p>
      </div>
      <div class="flex justify-center items-center py-8">
        <el-icon class="is-loading text-2xl">
          <i class="i-mdi-loading"></i>
        </el-icon>
        <span class="ml-2">正在验证登录状态...</span>
      </div>
    </div>
    
    <!-- 登录表单 -->
    <div v-else class="login-box">
      <div class="login-header">
        <div class="logo-section">
          <img src="/logo.svg" alt="蔚之领域" class="logo" />
        </div>
        <h2>蔚之领域管理后台</h2>
        <p>请使用管理员账号登录</p>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        size="large"
        @keyup.enter="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            prefix-icon="User"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="Lock"
            show-password
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item v-if="showCaptcha" prop="captcha">
          <div class="captcha-container">
            <el-input 
              v-model="loginForm.captcha"
              placeholder="请输入验证码"
              class="captcha-input"
            />
            <div class="captcha-display" @click="refreshCaptcha">
              <!-- 显示文本格式的数学题验证码 -->
              <div v-if="captchaText" class="captcha-text">
                {{ captchaText }}
              </div>
              <div v-else class="captcha-loading">
                加载中...
              </div>
              <div class="refresh-tip">点击刷新</div>
            </div>
          </div>
        </el-form-item>
        
        <el-form-item>
          <div class="login-options">
            <el-checkbox v-model="rememberMe">记住我</el-checkbox>
            <el-link type="primary" :underline="false" @click="handleForgotPassword">
              忘记密码？
            </el-link>
          </div>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            class="login-button"
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
        
        <!-- 错误提示区域 -->
        <div v-if="errorTips.length > 0" class="error-tips">
          <el-alert
            v-for="(tip, index) in errorTips"
            :key="index"
            :title="tip.title"
            :description="tip.description"
            :type="tip.type"
            :closable="true"
            @close="removeErrorTip(index)"
            class="error-tip-item"
            show-icon
          />
        </div>
        
        <!-- 网络状态提示 -->
        <div v-if="networkError" class="network-error-tip">
          <el-alert
            title="网络连接异常"
            description="当前网络连接不稳定，请检查网络设置或稍后重试。如果问题持续存在，请联系系统管理员。"
            type="warning"
            :closable="true"
            @close="networkError = false"
            show-icon
          />
        </div>
      </el-form>
      
      <!-- 开发环境快速登录 -->
      <div v-if="isDev" class="dev-login">
        <el-divider>开发环境快速登录</el-divider>
        <div class="quick-login-buttons">
          <el-button size="small" @click="quickLogin('admin')">
            超级管理员
          </el-button>
          <el-button size="small" type="info" @click="testApi">
            测试API
          </el-button>
        </div>
      </div>
    </div> <!-- 结束登录表单的 login-box -->
    
    <!-- 版权信息 -->
    <div class="copyright">
      <p>&copy; 2024 江苏蔚之领域智能科技有限公司. All rights reserved.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { authApi } from '@/api/auth'
import { useAuthStore } from '@/stores/auth'
import type { LoginData } from '@/types/api'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const loginFormRef = ref<FormInstance>()
const loading = ref(false)
const initializing = ref(true) // 添加初始化状态
const showCaptcha = ref(false)
const captchaUrl = ref('')
const captchaId = ref('')
const captchaText = ref('')
const rememberMe = ref(false)
const networkError = ref(false)

// 错误提示
const errorTips = ref<Array<{
  title: string
  description: string
  type: 'error' | 'warning' | 'info'
}>>([])

// 开发环境判断
const isDev = computed(() => import.meta.env.DEV)

const loginForm = reactive<LoginData>({
  username: '',
  password: '',
  captcha: '',
  captchaId: ''
})

const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  captcha: [
    { 
      required: true, 
      message: '请输入验证码', 
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (showCaptcha.value && !value) {
          callback(new Error('请输入验证码'))
        } else {
          callback()
        }
      }
    }
  ]
}

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    await loginFormRef.value.validate()
    loading.value = true
    
    // 调用登录API
    await authStore.login(loginForm)
    
    // 清除所有错误提示
    clearAllErrorTips()
    
    ElMessage.success('登录成功')
    
    // 记住我功能
    if (rememberMe.value) {
      localStorage.setItem('remember_username', loginForm.username)
    } else {
      localStorage.removeItem('remember_username')
    }
    
    // 获取用户信息
    await authStore.getUserInfo()
    
    // 跳转到目标页面或首页
    const redirect = route.query.redirect as string
    router.push(redirect || '/')
    
  } catch (error: any) {
    console.error('登录失败:', error)
    
    // 清空之前的错误提示
    errorTips.value = []
    
    // 处理不同类型的错误
    if (error?.response?.status === 400) {
      // 处理验证码错误或其他400错误
      const errorMessage = error?.response?.data?.message || '请求参数错误'
      if (errorMessage.includes('验证码')) {
        ElMessage({
          message: '验证码错误，请重新输入',
          type: 'error',
          duration: 8000,
          showClose: true
        })
        addErrorTip(
          '验证码错误',
          '请检查验证码是否输入正确，验证码区分大小写。如果看不清楚，请点击验证码图片刷新。',
          'error'
        )
        // 自动刷新验证码
        if (showCaptcha.value) {
          refreshCaptcha()
        }
        // 只清空验证码，保留用户名和密码
        loginForm.captcha = ''
      } else {
        ElMessage({
          message: errorMessage,
          type: 'error',
          duration: 8000,
          showClose: true
        })
        addErrorTip('请求错误', errorMessage, 'error')
        // 清空密码
        loginForm.password = ''
      }
    } else if (error?.response?.status === 401) {
      ElMessage({
        message: '用户名或密码错误',
        type: 'error',
        duration: 8000,
        showClose: true
      })
      addErrorTip(
        '登录失败',
        '用户名或密码错误，请检查输入是否正确。如果忘记密码，请联系系统管理员。',
        'error'
      )
      // 清空密码
      loginForm.password = ''
    } else if (error?.response?.status === 429) {
      ElMessage({
        message: '登录尝试次数过多，请稍后再试',
        type: 'warning',
        duration: 10000,
        showClose: true
      })
      addErrorTip(
        '登录限制',
        '登录尝试次数过多，为了账户安全，请稍后再试。现在需要输入验证码进行验证。',
        'warning'
      )
      showCaptcha.value = true
      refreshCaptcha()
      // 清空密码
      loginForm.password = ''
    } else if (error?.response?.data?.message) {
      ElMessage({
        message: error.response.data.message,
        type: 'error',
        duration: 8000,
        showClose: true
      })
      addErrorTip('登录错误', error.response.data.message, 'error')
      // 清空密码
      loginForm.password = ''
    } else {
      ElMessage({
        message: '登录失败，请检查网络连接',
        type: 'error',
        duration: 10000,
        showClose: true
      })
      addErrorTip(
        '网络错误',
        '无法连接到服务器，请检查网络连接是否正常，或稍后重试。',
        'error'
      )
      // 显示网络错误提示
      networkError.value = true
      // 清空密码
      loginForm.password = ''
    }
    
  } finally {
    loading.value = false
  }
}

// 刷新验证码
const refreshCaptcha = async () => {
  try {
    const { captchaId: newCaptchaId, captchaUrl: newCaptchaUrl } = await authApi.getCaptcha()
    captchaId.value = newCaptchaId
    captchaUrl.value = newCaptchaUrl
    
    // 从data URL中提取验证码文本
    if (newCaptchaUrl.startsWith('data:text/plain')) {
      const encodedText = newCaptchaUrl.split(',')[1]
      captchaText.value = decodeURIComponent(encodedText)
    } else {
      captchaText.value = '验证码加载失败'
    }
    
    loginForm.captchaId = newCaptchaId
    loginForm.captcha = '' // 清空验证码输入
  } catch (error: any) {
    console.error('获取验证码失败:', error)
    const errorMessage = error?.message || '获取验证码失败，请重试'
    ElMessage({
      message: errorMessage,
      type: 'error',
      duration: 8000,
      showClose: true
    })
    
    // 如果是网络错误，可以提供重试选项
    if (errorMessage.includes('网络')) {
      ElMessage({
        message: '网络连接失败，点击验证码图片可重试',
        type: 'warning',
        duration: 10000,
        showClose: true
      })
    }
  }
}

// 忘记密码
const handleForgotPassword = () => {
  ElMessage.info('请联系系统管理员重置密码')
}

// 移除错误提示
const removeErrorTip = (index: number) => {
  errorTips.value.splice(index, 1)
}

// 清除所有错误提示
const clearAllErrorTips = () => {
  errorTips.value = []
  networkError.value = false
}

// 添加错误提示
const addErrorTip = (title: string, description: string, type: 'error' | 'warning' | 'info' = 'error') => {
  errorTips.value.push({ title, description, type })
}

// 快速登录（开发环境）
const quickLogin = (type: 'admin') => {
  const accounts = {
    admin: { username: 'admin', password: 'admin123' }
  }
  
  const account = accounts[type]
  loginForm.username = account.username
  loginForm.password = account.password
  
  handleLogin()
}

// 测试API连接
const testApi = async () => {
  try {
    ElMessage.info('正在测试API连接...')
    // 简单的API连接测试
    const response = await fetch('/api/admin/auth/profile', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    if (response.status === 401) {
      ElMessage.success('API服务正常运行（需要认证）')
    } else if (response.ok) {
      ElMessage.success('API服务连接正常')
    } else {
      ElMessage.warning(`API响应状态: ${response.status}`)
    }
  } catch (error: any) {
    console.error('API测试失败:', error)
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      ElMessage.error('无法连接到后端服务，请检查服务是否启动')
    } else {
      ElMessage.error('API测试失败')
    }
  }
}

// 初始化认证状态
const initializeAuth = async () => {
  console.log('🚀 [Login] 初始化认证状态')
  try {
    const savedToken = localStorage.getItem('admin_token')
    console.log('🔑 [Login] 检查本地token:', !!savedToken)

    if (savedToken) {
      try {
        console.log('🔍 [Login] 验证token有效性')
        // 验证 token 是否有效
        await authStore.getUserInfo()

        // 如果验证成功且已经登录，跳转
        if (authStore.isLoggedIn) {
          const redirect = route.query.redirect as string
          console.log('✅ [Login] Token有效，跳转到:', redirect || '/')
          router.push(redirect || '/')
          return
        }
      } catch (error: any) {
        console.error('❌ [Login] Token验证失败:', error)
        // Token 无效，清除状态，显示友好提示
        authStore.clearAuthState()

        if (error?.message?.includes('过期')) {
          console.log('⏰ [Login] Token过期，显示提示')
          addErrorTip(
            '登录已过期',
            '您的登录状态已过期，请重新登录。',
            'warning'
          )
        }
      }
    } else {
      console.log('📝 [Login] 无本地token，显示登录表单')
    }
    
    // 恢复记住的用户名
    const rememberedUsername = localStorage.getItem('remember_username')
    if (rememberedUsername) {
      loginForm.username = rememberedUsername
      rememberMe.value = true
    }
    
    // 检查是否需要显示验证码
    const loginAttempts = localStorage.getItem('login_attempts')
    if (loginAttempts && parseInt(loginAttempts) >= 3) {
      showCaptcha.value = true
      refreshCaptcha()
    }
  } finally {
    // 无论如何都要结束初始化状态
    initializing.value = false
  }
}

// 初始化
onMounted(() => {
  initializeAuth()
})
</script>

<style scoped>
.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

.login-box {
  width: 420px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo-section {
  margin-bottom: 16px;
}

.logo {
  height: 48px;
  width: auto;
}

.login-header h2 {
  color: #333;
  margin-bottom: 8px;
  font-weight: 600;
  font-size: 24px;
}

.login-header p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.login-form {
  width: 100%;
}

.captcha-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.captcha-input {
  flex: 1;
}

.captcha-display {
  width: 120px;
  height: 40px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.2s;
}

.captcha-display:hover {
  border-color: #409eff;
}

.captcha-display .captcha-text {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  background: #f8f9fa;
  user-select: none;
}

.captcha-display .captcha-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  color: #999;
  background: #f8f9fa;
}

.refresh-tip {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 10px;
  text-align: center;
  padding: 2px;
  opacity: 0;
  transition: opacity 0.2s;
}

.captcha-display:hover .refresh-tip {
  opacity: 1;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.login-button {
  width: 100%;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(64, 158, 255, 0.3);
}

.login-button:active {
  transform: translateY(0);
}

.dev-login {
  margin-top: 24px;
  text-align: center;
}

.quick-login-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.copyright {
  position: absolute;
  bottom: 20px;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

.copyright p {
  margin: 0;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  height: 44px;
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  transition: all 0.3s;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

:deep(.el-input.is-focus .el-input__wrapper) {
  box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item:last-child) {
  margin-bottom: 0;
}

:deep(.el-checkbox__label) {
  font-size: 14px;
  color: #666;
}

:deep(.el-link) {
  font-size: 14px;
}

:deep(.el-divider__text) {
  font-size: 12px;
  color: #999;
}

/* 错误提示区域样式 */
.error-tips {
  margin-top: 20px;
}

.error-tip-item {
  margin-bottom: 12px;
  animation: fadeInUp 0.3s ease-out;
}

.network-error-tip {
  margin-top: 16px;
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-box {
    width: 90%;
    padding: 24px;
    margin: 20px;
  }
  
  .login-header h2 {
    font-size: 20px;
  }
  
  .captcha-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .captcha-display {
    align-self: center;
  }
  
  .quick-login-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .error-tips {
    margin-top: 12px;
  }
  
  :deep(.el-alert__title) {
    font-size: 13px;
  }
  
  :deep(.el-alert__description) {
    font-size: 12px;
  }
}
</style> 