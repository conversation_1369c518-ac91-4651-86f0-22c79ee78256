<template>
  <div class="auth-debug-container">
    <el-card header="认证状态调试面板">
      <div class="debug-section">
        <h3>当前认证状态</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="登录状态">
            <el-tag :type="authStore.isLoggedIn ? 'success' : 'danger'">
              {{ authStore.isLoggedIn ? '已登录' : '未登录' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="Token">
            <el-tag :type="authStore.token ? 'success' : 'info'">
              {{ authStore.token ? '存在' : '不存在' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="用户信息">
            <el-tag :type="authStore.userInfo ? 'success' : 'info'">
              {{ authStore.userInfo ? authStore.userInfo.username : '无' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="权限数量">
            <el-tag type="info">{{ authStore.permissions.length }}</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="debug-section">
        <h3>本地存储状态</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="admin_token">
            <el-tag :type="getLocalStorageItem('admin_token') ? 'success' : 'info'">
              {{ getLocalStorageItem('admin_token') ? '存在' : '不存在' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="admin_user_info">
            <el-tag :type="getLocalStorageItem('admin_user_info') ? 'success' : 'info'">
              {{ getLocalStorageItem('admin_user_info') ? '存在' : '不存在' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="admin_permissions">
            <el-tag :type="getLocalStorageItem('admin_permissions') ? 'success' : 'info'">
              {{ getLocalStorageItem('admin_permissions') ? '存在' : '不存在' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="remember_username">
            <el-tag :type="getLocalStorageItem('remember_username') ? 'success' : 'info'">
              {{ getLocalStorageItem('remember_username') || '无' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="debug-section">
        <h3>操作按钮</h3>
        <el-space wrap>
          <el-button @click="refreshUserInfo" type="primary">刷新用户信息</el-button>
          <el-button @click="clearAuth" type="danger">清除认证状态</el-button>
          <el-button @click="testNavigation" type="warning">测试导航</el-button>
          <el-button @click="checkPermissions" type="info">检查权限</el-button>
        </el-space>
      </div>

      <div class="debug-section">
        <h3>调试日志</h3>
        <el-input
          v-model="debugLogs"
          type="textarea"
          :rows="10"
          readonly
          placeholder="调试日志将显示在这里..."
        />
        <div style="margin-top: 10px;">
          <el-button @click="clearLogs" size="small">清除日志</el-button>
          <el-button @click="exportLogs" size="small" type="primary">导出日志</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'

const authStore = useAuthStore()
const router = useRouter()
const debugLogs = ref('')

// localStorage访问方法
const getLocalStorageItem = (key: string) => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem(key)
  }
  return null
}

const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  debugLogs.value += `[${timestamp}] ${message}\n`
}

const refreshUserInfo = async () => {
  try {
    addLog('开始刷新用户信息...')
    await authStore.getUserInfo()
    addLog('用户信息刷新成功')
    ElMessage.success('用户信息刷新成功')
  } catch (error: any) {
    addLog(`用户信息刷新失败: ${error.message}`)
    ElMessage.error('用户信息刷新失败')
  }
}

const clearAuth = () => {
  addLog('清除认证状态...')
  authStore.clearAuthState()
  addLog('认证状态已清除')
  ElMessage.success('认证状态已清除')
}

const testNavigation = () => {
  addLog('测试导航到仪表盘...')
  router.push('/dashboard')
}

const checkPermissions = () => {
  addLog(`当前权限列表: ${authStore.permissions.join(', ')}`)
  ElMessage.info(`权限数量: ${authStore.permissions.length}`)
}

const clearLogs = () => {
  debugLogs.value = ''
}

const exportLogs = () => {
  const blob = new Blob([debugLogs.value], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `auth-debug-${new Date().toISOString().slice(0, 19)}.txt`
  a.click()
  URL.revokeObjectURL(url)
}

onMounted(() => {
  addLog('认证调试页面已加载')
  addLog(`当前路由: ${router.currentRoute.value.path}`)
})
</script>

<style scoped>
.auth-debug-container {
  padding: 20px;
}

.debug-section {
  margin-bottom: 20px;
}

.debug-section h3 {
  margin-bottom: 10px;
  color: #409eff;
}
</style>
