<template>
  <div class="auth-status-debug">
    <el-card>
      <template #header>
        <h3>认证状态调试</h3>
      </template>
      
      <div class="status-grid">
        <div class="status-item">
          <label>登录状态:</label>
          <el-tag :type="authStore.isLoggedIn ? 'success' : 'danger'">
            {{ authStore.isLoggedIn ? '已登录' : '未登录' }}
          </el-tag>
        </div>
        
        <div class="status-item">
          <label>Token存在:</label>
          <el-tag :type="hasToken ? 'success' : 'danger'">
            {{ hasToken ? '是' : '否' }}
          </el-tag>
        </div>
        
        <div class="status-item">
          <label>用户信息:</label>
          <el-tag :type="authStore.userInfo ? 'success' : 'warning'">
            {{ authStore.userInfo?.username || '无' }}
          </el-tag>
        </div>
        
        <div class="status-item">
          <label>权限数量:</label>
          <el-tag type="info">
            {{ authStore.permissions.length }}
          </el-tag>
        </div>
      </div>
      
      <div class="actions">
        <el-button @click="refreshStatus">刷新状态</el-button>
        <el-button @click="testTokenValidation" :loading="validating">验证Token</el-button>
        <el-button @click="clearAuth" type="danger">清除认证</el-button>
      </div>
      
      <div class="details">
        <h4>详细信息:</h4>
        <pre>{{ debugInfo }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import { computed, ref } from 'vue'

const authStore = useAuthStore()
const validating = ref(false)

const hasToken = computed(() => !!localStorage.getItem('admin_token'))

const debugInfo = computed(() => ({
  isLoggedIn: authStore.isLoggedIn,
  hasToken: hasToken.value,
  userInfo: authStore.userInfo,
  permissions: authStore.permissions,
  localStorage: {
    token: !!localStorage.getItem('admin_token'),
    userInfo: !!localStorage.getItem('admin_user_info'),
    permissions: !!localStorage.getItem('admin_permissions')
  }
}))

const refreshStatus = () => {
  authStore.restoreAuthFromStorage()
  ElMessage.success('状态已刷新')
}

const testTokenValidation = async () => {
  validating.value = true
  try {
    await authStore.getUserInfo()
    ElMessage.success('Token验证成功')
  } catch (error: any) {
    ElMessage.error('Token验证失败: ' + error.message)
  } finally {
    validating.value = false
  }
}

const clearAuth = () => {
  authStore.clearAuthState()
  ElMessage.warning('认证状态已清除')
}
</script>

<style scoped>
.auth-status-debug {
  padding: 20px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-item label {
  font-weight: bold;
  min-width: 80px;
}

.actions {
  margin: 20px 0;
  display: flex;
  gap: 12px;
}

.details {
  margin-top: 20px;
}

.details pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}
</style>
