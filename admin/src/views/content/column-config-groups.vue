<template>
  <div class="column-config-groups">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2>列配置组管理</h2>
          <p>管理可复用的列配置组，减少重复配置工作</p>
        </div>
        <el-button type="primary" @click="openCreateDialog">
          <i class="i-mdi-plus mr-1"></i>
          新建配置组
        </el-button>
      </div>
    </div>

    <!-- 配置组列表 -->
    <div class="config-groups-grid">
      <div 
        v-for="group in combinationTemplates" 
        :key="group.id"
        class="config-group-card"
        @click="selectGroup(group)"
        :class="{ active: selectedGroup?.id === group.id }"
      >
        <div class="card-header">
          <div class="group-info">
            <h3>{{ group.name }}</h3>
            <p>{{ group.description }}</p>
          </div>
          <div class="group-actions">
            <el-dropdown @command="handleGroupAction">
              <el-button type="text" size="small">
                <i class="i-mdi-dots-vertical"></i>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`edit-${group.id}`">编辑</el-dropdown-item>
                  <el-dropdown-item :command="`copy-${group.id}`">复制</el-dropdown-item>
                  <el-dropdown-item :command="`delete-${group.id}`" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
        
        <div class="card-content">
          <div class="group-meta">
            <el-tag size="small" type="info">{{ group.category }}</el-tag>
            <span class="column-count">{{ group.columns?.length || 0 }} 列</span>
          </div>
          
          <div class="columns-preview">
            <div 
              v-for="column in group.columns?.slice(0, 4)" 
              :key="column.id"
              class="column-tag"
            >
              {{ column.label }}
            </div>
            <div v-if="(group.columns?.length || 0) > 4" class="more-indicator">
              +{{ (group.columns?.length || 0) - 4 }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 配置组详情 -->
    <div v-if="selectedGroup" class="group-details">
      <div class="details-header">
        <h3>{{ selectedGroup.name }} - 列配置详情</h3>
        <div class="usage-info">
          <el-tag type="success" size="small">
            被 {{ getUsageCount(selectedGroup.id) }} 个表格使用
          </el-tag>
        </div>
      </div>
      
      <div class="columns-list">
        <div 
          v-for="(column, index) in selectedGroup.columns" 
          :key="column.id"
          class="column-item"
        >
          <div class="column-info">
            <div class="column-header">
              <span class="column-label">{{ column.label }}</span>
              <el-tag :type="getColumnTypeColor(column.type)" size="small">
                {{ column.type }}
              </el-tag>
            </div>
            <div class="column-meta">
              <span class="field-name">字段名: {{ column.name }}</span>
              <span v-if="column.is_required" class="required-mark">*必填</span>
            </div>
            <p v-if="column.description" class="column-description">
              {{ column.description }}
            </p>
          </div>
          <div class="column-actions">
            <span class="sort-order">#{{ index + 1 }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="editingGroup ? '编辑配置组' : '创建配置组'"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="groupForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="配置组名称" prop="name">
          <el-input v-model="groupForm.name" placeholder="请输入配置组名称" />
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="groupForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入配置组描述"
          />
        </el-form-item>
        
        <el-form-item label="分类" prop="category">
          <el-select v-model="groupForm.category" placeholder="请选择分类">
            <el-option label="液压设备" value="液压设备" />
            <el-option label="电机设备" value="电机设备" />
            <el-option label="过滤设备" value="过滤设备" />
            <el-option label="通用" value="通用" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="列配置">
          <ColumnTemplateSelector 
            v-model="groupForm.column_ids"
            :multiple="true"
            :show-preview="true"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveGroup" :loading="saveLoading">
          {{ editingGroup ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { columnTemplateApi } from '@/api/platform-config'
import ColumnTemplateSelector from '@/components/platform/ColumnTemplateSelector.vue'

// 响应式数据
const combinationTemplates = ref<any[]>([])
const selectedGroup = ref<any>(null)
const dialogVisible = ref(false)
const saveLoading = ref(false)
const editingGroup = ref<any>(null)

// 表单相关
const formRef = ref<FormInstance>()
const groupForm = reactive({
  name: '',
  description: '',
  category: '',
  column_ids: [] as number[]
})

const formRules: FormRules = {
  name: [{ required: true, message: '请输入配置组名称', trigger: 'blur' }],
  category: [{ required: true, message: '请选择分类', trigger: 'change' }]
}

// 计算属性
const getUsageCount = (groupId: number) => {
  // TODO: 实际应该从API获取使用统计
  return Math.floor(Math.random() * 10) + 1
}

const getColumnTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    text: '',
    number: 'warning',
    date: 'info',
    boolean: 'success',
    textarea: 'danger'
  }
  return colorMap[type] || ''
}

// 方法
const loadCombinationTemplates = async () => {
  try {
    const response = await columnTemplateApi.getCombinationTemplates()
    combinationTemplates.value = response.data
  } catch (error) {
    ElMessage.error('加载配置组失败')
  }
}

const selectGroup = (group: any) => {
  selectedGroup.value = group
}

const openCreateDialog = () => {
  editingGroup.value = null
  resetForm()
  dialogVisible.value = true
}

const handleGroupAction = (command: string) => {
  const [action, id] = command.split('-')
  const groupId = parseInt(id)
  const group = combinationTemplates.value.find(g => g.id === groupId)
  
  if (action === 'edit') {
    editGroup(group)
  } else if (action === 'copy') {
    copyGroup(group)
  } else if (action === 'delete') {
    deleteGroup(group)
  }
}

const editGroup = (group: any) => {
  editingGroup.value = group
  Object.assign(groupForm, {
    name: group.name,
    description: group.description,
    category: group.category,
    column_ids: group.columns?.map((col: any) => col.id) || []
  })
  dialogVisible.value = true
}

const copyGroup = (group: any) => {
  editingGroup.value = null
  Object.assign(groupForm, {
    name: `${group.name} - 副本`,
    description: group.description,
    category: group.category,
    column_ids: group.columns?.map((col: any) => col.id) || []
  })
  dialogVisible.value = true
}

const deleteGroup = async (group: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除配置组"${group.name}"吗？这将影响所有使用此配置组的表格。`,
      '确认删除',
      { type: 'warning' }
    )
    
    // TODO: 调用删除API
    // await columnTemplateApi.deleteCombinationTemplate(group.id)
    
    const index = combinationTemplates.value.findIndex(g => g.id === group.id)
    if (index > -1) {
      combinationTemplates.value.splice(index, 1)
    }
    
    if (selectedGroup.value?.id === group.id) {
      selectedGroup.value = null
    }
    
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const saveGroup = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    saveLoading.value = true
    
    if (editingGroup.value) {
      // TODO: 调用更新API
      // await columnTemplateApi.updateCombinationTemplate(editingGroup.value.id, groupForm)
      Object.assign(editingGroup.value, groupForm)
      ElMessage.success('更新成功')
    } else {
      // TODO: 调用创建API
      // const response = await columnTemplateApi.createCombinationTemplate(groupForm)
      const newGroup = {
        id: Date.now(),
        ...groupForm,
        is_system: false,
        columns: [] // TODO: 根据 column_ids 获取完整的列信息
      }
      combinationTemplates.value.push(newGroup)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

const resetForm = () => {
  Object.assign(groupForm, {
    name: '',
    description: '',
    category: '',
    column_ids: []
  })
  formRef.value?.clearValidate()
}

onMounted(() => {
  loadCombinationTemplates()
})
</script>

<style scoped>
.column-config-groups {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title-section h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.title-section p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.config-groups-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.config-group-card {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.config-group-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.config-group-card.active {
  border-color: #3b82f6;
  background: #f8faff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.group-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.group-info p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
}

.group-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.column-count {
  font-size: 12px;
  color: #6b7280;
}

.columns-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.column-tag {
  background: #f3f4f6;
  color: #374151;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.more-indicator {
  background: #e5e7eb;
  color: #6b7280;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.group-details {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.details-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.columns-list {
  display: grid;
  gap: 12px;
}

.column-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border-left: 4px solid #e5e7eb;
}

.column-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.column-label {
  font-weight: 500;
  color: #1f2937;
}

.column-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
}

.field-name {
  font-size: 12px;
  color: #6b7280;
}

.required-mark {
  color: #ef4444;
  font-size: 12px;
  font-weight: 500;
}

.column-description {
  margin: 0;
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
}

.sort-order {
  font-size: 12px;
  color: #9ca3af;
  font-weight: 500;
}
</style>
