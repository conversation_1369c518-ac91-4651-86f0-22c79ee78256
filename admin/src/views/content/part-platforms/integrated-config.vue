<template>
  <div class="p-6">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h2 class="text-2xl font-bold">平台配置管理</h2>
        <p class="text-gray-600 mt-1">为 "{{ platformName }}" 配置产品分类、表格结构和数据</p>
      </div>
      <div class="flex gap-2">
        <el-button @click="handleGoBack">
          <i class="i-mdi-arrow-left mr-2"></i>
          返回平台列表
        </el-button>
      </div>
    </div>

    <div class="flex gap-6 h-[calc(100vh-200px)]">
      <!-- 左侧导航区域 -->
      <div class="w-80 bg-white rounded-lg shadow-sm border p-4">
        <div class="flex justify-between items-center mb-4">
          <h3 class="font-semibold">配置结构</h3>
          <el-button size="small" type="primary" @click="handleAddCategory">
            <i class="i-mdi-plus mr-1"></i>
            新建分类
          </el-button>
        </div>

        <!-- 分类和表格树形结构 -->
        <div class="space-y-2">
          <div v-if="extensions.length === 0" class="text-center text-gray-500 py-8">
            <i class="i-mdi-folder-outline text-4xl mb-2"></i>
            <p>暂无分类</p>
            <p class="text-sm">点击"新建分类"开始配置</p>
          </div>

          <div v-for="extension in extensions" :key="extension.id" class="border rounded-lg">
            <!-- 分类节点 -->
            <div
              class="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50"
              :class="{ 'bg-blue-50 border-blue-200': selectedItem?.type === 'extension' && selectedItem?.id === extension.id }"
              @click="selectExtension(extension)"
            >
              <div class="flex items-center">
                <i class="i-mdi-folder text-blue-500 mr-2"></i>
                <span class="font-medium">{{ extension.title }}</span>
              </div>
              <el-dropdown @command="handleExtensionAction">
                <el-button size="small" text @click.stop>
                  <i class="i-mdi-dots-vertical"></i>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="`edit-${extension.id}`">编辑分类</el-dropdown-item>
                    <el-dropdown-item :command="`add-table-${extension.id}`">添加表格</el-dropdown-item>
                    <el-dropdown-item :command="`delete-${extension.id}`" divided>删除分类</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>

            <!-- 表格节点 -->
            <div v-if="extension.tables && extension.tables.length > 0" class="border-t bg-gray-50">
              <div
                v-for="table in extension.tables"
                :key="table.id"
                class="flex items-center justify-between p-3 pl-8 cursor-pointer hover:bg-gray-100"
                :class="{ 'bg-blue-100 border-blue-200': selectedItem?.type === 'table' && selectedItem?.id === table.id }"
                @click="selectTable(table, extension)"
              >
                <div class="flex items-center">
                  <i class="i-mdi-table text-green-500 mr-2"></i>
                  <span>{{ table.table_name }}</span>
                  <span class="text-xs text-gray-500 ml-2">({{ table.columns?.length || 0 }} 列)</span>
                </div>
                <el-dropdown @command="handleTableAction">
                  <el-button size="small" text @click.stop>
                    <i class="i-mdi-dots-vertical"></i>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="`edit-${table.id}`">编辑表格</el-dropdown-item>
                      <el-dropdown-item :command="`delete-${table.id}`" divided>删除表格</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧配置区域 -->
      <div class="flex-1 bg-white rounded-lg shadow-sm border">
        <!-- 未选择任何项目 -->
        <div v-if="!selectedItem" class="flex items-center justify-center h-full text-gray-500">
          <div class="text-center">
            <i class="i-mdi-cursor-default-click-outline text-6xl mb-4"></i>
            <h3 class="text-lg font-medium mb-2">选择配置项目</h3>
            <p>请在左侧选择分类或表格进行配置</p>
          </div>
        </div>

        <!-- 分类配置区域 -->
        <div v-else-if="selectedItem.type === 'extension'" class="p-6">
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-semibold">分类配置</h3>
            <el-button type="primary" @click="handleEditExtension">
              <i class="i-mdi-pencil mr-2"></i>
              编辑分类
            </el-button>
          </div>

          <div class="space-y-6">
            <!-- 分类信息展示 -->
            <el-card class="basic-info-card">
              <template #header>
                <div class="card-header">
                  <i class="i-mdi-information-outline text-lg mr-2 text-blue-600"></i>
                  <span class="card-title">基本信息</span>
                </div>
              </template>
              <div class="basic-info-content">
                <!-- 分类名称 -->
                <div class="info-item">
                  <div class="info-label">
                    <i class="i-mdi-tag text-gray-500 mr-1"></i>
                    分类名称
                  </div>
                  <div class="info-value primary">{{ selectedExtension?.title }}</div>
                </div>

                <!-- 分类描述 -->
                <div v-if="selectedExtension?.description" class="info-item">
                  <div class="info-label">
                    <i class="i-mdi-text-box-outline text-gray-500 mr-1"></i>
                    分类描述
                  </div>
                  <div class="info-value secondary">{{ selectedExtension.description }}</div>
                </div>

                <!-- 分类图片 -->
                <div v-if="selectedExtension?.image_url" class="info-item">
                  <div class="info-label">
                    <i class="i-mdi-image-outline text-gray-500 mr-1"></i>
                    分类图片
                  </div>
                  <div class="info-value">
                    <div class="image-container">
                      <el-image
                        :src="selectedExtension.image_url"
                        class="category-image"
                        fit="cover"
                        :preview-disabled="true"
                      >
                        <template #error>
                          <div class="image-error">
                            <i class="i-mdi-image-broken text-gray-400 text-2xl"></i>
                            <span class="text-gray-500 text-sm mt-1">图片加载失败</span>
                          </div>
                        </template>
                      </el-image>
                      <div class="image-overlay">
                        <el-button
                          type="primary"
                          size="small"
                          circle
                          @click="previewImage(selectedExtension.image_url)"
                          class="preview-btn"
                        >
                          <i class="i-mdi-eye text-sm"></i>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>

            <!-- 表格列表 -->
            <el-card>
              <template #header>
                <div class="flex justify-between items-center">
                  <span class="font-semibold">包含的表格</span>
                  <el-button size="small" type="success" @click="handleAddTable">
                    <i class="i-mdi-plus mr-1"></i>
                    添加表格
                  </el-button>
                </div>
              </template>
              <div v-if="selectedExtension?.tables && selectedExtension.tables.length > 0">
                <div
                  v-for="table in selectedExtension.tables"
                  :key="table.id"
                  class="flex items-center justify-between p-3 border rounded mb-2 last:mb-0"
                >
                  <div>
                    <h4 class="font-medium">{{ table.table_name }}</h4>
                    <p class="text-sm text-gray-500">{{ table.columns?.length || 0 }} 列配置</p>
                  </div>
                  <el-button size="small" @click="selectTable(table, selectedExtension)">
                    配置表格
                  </el-button>
                </div>
              </div>
              <div v-else class="text-center text-gray-500 py-8">
                <i class="i-mdi-table-plus text-4xl mb-2"></i>
                <p>暂无表格</p>
                <p class="text-sm">点击"添加表格"开始创建</p>
              </div>
            </el-card>
          </div>
        </div>

        <!-- 表格配置区域 -->
        <div v-else-if="selectedItem.type === 'table'" class="h-full flex flex-col">
          <div class="p-6 border-b">
            <div class="flex justify-between items-center">
              <div>
                <h3 class="text-xl font-semibold">{{ selectedTable?.table_name }}</h3>
                <p class="text-gray-600">配置表格结构和数据</p>
              </div>
              <el-button @click="handleEditTable">
                <i class="i-mdi-pencil mr-2"></i>
                编辑表格
              </el-button>
            </div>
          </div>

          <!-- 表格配置标签页 -->
          <div class="flex-1 p-6">
            <el-tabs v-model="activeTab" class="h-full">
              <el-tab-pane label="列配置" name="columns" class="h-full">
                <div class="h-full">
                  <TableColumnsConfig :table-id="selectedTable?.id" />
                </div>
              </el-tab-pane>
              <el-tab-pane label="数据管理" name="data" class="h-full">
                <div class="h-full">
                  <TableDataManagement
                    :table-id="selectedTable?.id"
                    :columns="selectedTable?.columns || []"
                  />
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>
    </div>

    <!-- 分类编辑对话框 -->
    <el-dialog
      v-model="extensionDialogVisible"
      :title="editingExtension ? '编辑分类' : '新建分类'"
      width="600px"
    >
      <el-form :model="extensionForm" label-width="100px">
        <el-form-item label="分类标题" required>
          <el-input v-model="extensionForm.title" placeholder="请输入分类标题" />
        </el-form-item>
        <el-form-item label="分类描述">
          <el-input
            v-model="extensionForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
          />
        </el-form-item>
        <el-form-item label="图片URL">
          <el-input v-model="extensionForm.image_url" placeholder="请输入图片URL" />
        </el-form-item>
        <el-form-item label="排序">
          <el-input-number v-model="extensionForm.sort_order" :min="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button @click="extensionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveExtension">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 表格编辑对话框 -->
    <el-dialog
      v-model="tableDialogVisible"
      :title="editingTable ? '编辑表格' : '新建表格'"
      width="500px"
    >
      <el-form :model="tableForm" label-width="100px">
        <el-form-item label="表格名称" required>
          <el-input v-model="tableForm.table_name" placeholder="请输入表格名称" />
        </el-form-item>
        <el-form-item label="排序">
          <el-input-number v-model="tableForm.sort_order" :min="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button @click="tableDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveTable">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 表格创建对话框 -->
    <el-dialog
      v-model="tableWizardVisible"
      title="创建新表格"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="newTableForm" :rules="newTableRules" ref="newTableFormRef" label-width="120px">
        <el-form-item label="表格名称" prop="table_name">
          <el-input
            v-model="newTableForm.table_name"
            placeholder="请输入表格名称，如：两位四通手控换向阀"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="表格描述">
          <el-input
            v-model="newTableForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入表格描述（可选）"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="选择模板">
          <el-select v-model="newTableForm.template" placeholder="选择表格模板">
            <el-option label="自定义表格" value="custom" />
            <el-option label="液压阀门" value="hydraulic_valve" />
            <el-option label="电子元件" value="electronic_component" />
            <el-option label="机械零件" value="mechanical_part" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序权重">
          <el-input-number
            v-model="newTableForm.sort_order"
            :min="0"
            :max="999"
            placeholder="数字越小排序越靠前"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button @click="tableWizardVisible = false">取消</el-button>
          <el-button type="primary" @click="createNewTable" :loading="creating">创建表格</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { platformConfigApi } from '@/api/platform-config'
import TableColumnsConfig from '@/components/platform/TableColumnsConfig.vue'
import TableDataManagement from '@/components/platform/TableDataManagement.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// 路由相关
const route = useRoute()
const router = useRouter()

// 响应式数据
const platformId = ref<number>(0)
const platformName = ref<string>('')
const extensions = ref<any[]>([])
const selectedItem = ref<{ type: 'extension' | 'table'; id: number } | null>(null)
const activeTab = ref('columns')

// 计算属性
const selectedExtension = computed(() => {
  if (selectedItem.value?.type === 'extension') {
    return extensions.value.find(ext => ext.id === selectedItem.value?.id)
  }
  return null
})

const selectedTable = computed(() => {
  if (selectedItem.value?.type === 'table') {
    for (const ext of extensions.value) {
      const table = ext.tables?.find((t: any) => t.id === selectedItem.value?.id)
      if (table) return table
    }
  }
  return null
})

// 方法
const handleGoBack = () => {
  router.push('/platform/list')
}

const selectExtension = (extension: any) => {
  selectedItem.value = { type: 'extension', id: extension.id }
}

const selectTable = (table: any, extension: any) => {
  selectedItem.value = { type: 'table', id: table.id }
}

// 对话框状态
const extensionDialogVisible = ref(false)
const tableDialogVisible = ref(false)
const tableWizardVisible = ref(false)
const creating = ref(false)
const editingExtension = ref<any>(null)
const editingTable = ref<any>(null)

// 表单数据
const extensionForm = ref({
  title: '',
  description: '',
  image_url: '',
  sort_order: 0
})

const tableForm = ref({
  table_name: '',
  sort_order: 0
})

// 新表格创建表单
const newTableForm = ref({
  table_name: '',
  description: '',
  template: 'custom',
  sort_order: 0
})

// 新表格表单引用
const newTableFormRef = ref<any>(null)

// 新表格验证规则
const newTableRules = {
  table_name: [
    { required: true, message: '请输入表格名称', trigger: 'blur' },
    { min: 2, max: 50, message: '表格名称长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

const handleAddCategory = () => {
  editingExtension.value = null
  extensionForm.value = {
    title: '',
    description: '',
    image_url: '',
    sort_order: extensions.value.length
  }
  extensionDialogVisible.value = true
}

const handleExtensionAction = (command: string) => {
  const parts = command.split('-')

  if (parts[0] === 'edit') {
    // edit-{id}
    const id = parts[1]
    const extension = extensions.value.find(ext => ext.id === parseInt(id))
    if (extension) {
      editingExtension.value = extension
      extensionForm.value = {
        title: extension.title,
        description: extension.description || '',
        image_url: extension.image_url || '',
        sort_order: extension.sort_order || 0
      }
      extensionDialogVisible.value = true
    }
  } else if (parts[0] === 'add' && parts[1] === 'table') {
    // add-table-{id}
    const id = parts[2]
    const extension = extensions.value.find(ext => ext.id === parseInt(id))
    if (extension) {
      // 设置当前选中的扩展（通过selectedItem）
      selectedItem.value = { type: 'extension', id: extension.id }
      // 调用添加表格方法
      handleAddTable()
    }
  } else if (parts[0] === 'delete') {
    // delete-{id}
    const id = parts[1]
    const extension = extensions.value.find(ext => ext.id === parseInt(id))
    if (extension) {
      ElMessageBox.confirm('确定要删除这个分类吗？', '确认删除', {
        type: 'warning'
      }).then(() => {
        // TODO: 调用删除API
        const index = extensions.value.findIndex(ext => ext.id === parseInt(id))
        if (index > -1) {
          extensions.value.splice(index, 1)
        }
        ElMessage.success('删除成功')
      }).catch(() => {})
    }
  }
}

const handleTableAction = (command: string) => {
  const [action, id] = command.split('-')
  const extension = selectedExtension.value
  const table = extension?.tables?.find((t: any) => t.id === parseInt(id))

  if (action === 'edit') {
    editingTable.value = table
    tableForm.value = {
      table_name: table.table_name,
      sort_order: table.sort_order || 0
    }
    tableDialogVisible.value = true
  } else if (action === 'delete') {
    ElMessageBox.confirm('确定要删除这个表格吗？', '确认删除', {
      type: 'warning'
    }).then(() => {
      // TODO: 调用删除API
      const index = extension.tables.findIndex((t: any) => t.id === parseInt(id))
      if (index > -1) {
        extension.tables.splice(index, 1)
      }
      ElMessage.success('删除成功')
    }).catch(() => {})
  }
}

const handleEditExtension = () => {
  if (!selectedExtension.value) return

  editingExtension.value = selectedExtension.value
  extensionForm.value = {
    title: selectedExtension.value.title,
    description: selectedExtension.value.description || '',
    image_url: selectedExtension.value.image_url || '',
    sort_order: selectedExtension.value.sort_order || 0
  }
  extensionDialogVisible.value = true
}

const handleAddTable = () => {
  if (!selectedExtension.value) {
    ElMessage.warning('请先选择一个分类')
    return
  }

  // 使用表格创建向导
  tableWizardVisible.value = true
}

const handleEditTable = () => {
  if (!selectedTable.value) return

  editingTable.value = selectedTable.value
  tableForm.value = {
    table_name: selectedTable.value.table_name,
    sort_order: selectedTable.value.sort_order || 0
  }
  tableDialogVisible.value = true
}

// 保存分类
const saveExtension = async () => {
  try {
    if (editingExtension.value) {
      // 更新分类
      const index = extensions.value.findIndex(ext => ext.id === editingExtension.value.id)
      if (index > -1) {
        Object.assign(extensions.value[index], extensionForm.value)
      }
      ElMessage.success('更新成功')
    } else {
      // 新建分类
      const newExtension = {
        id: Date.now(), // 临时ID
        part_platform_id: platformId.value,
        ...extensionForm.value,
        tables: []
      }
      extensions.value.push(newExtension)
      ElMessage.success('创建成功')
    }
    extensionDialogVisible.value = false
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

// 保存表格
const saveTable = async () => {
  try {
    if (editingTable.value) {
      // 更新表格
      Object.assign(editingTable.value, tableForm.value)
      ElMessage.success('更新成功')
    } else {
      // 新建表格
      const newTable = {
        id: Date.now(), // 临时ID
        extension_id: selectedExtension.value.id,
        ...tableForm.value,
        combination_template_id: tableForm.value.template_id, // 引用列配置组
        columns: [], // 不再直接存储列配置，而是通过模板引用
        rows: []
      }

      // 如果选择了模板，自动应用模板的列配置
      if (tableForm.value.template_id) {
        const template = combinationTemplates.value.find(t => t.id === tableForm.value.template_id)
        if (template && template.columns) {
          // 显示模板的列配置（用于UI展示）
          newTable.columns = template.columns.map((col, index) => ({
            id: Date.now() + index,
            table_id: newTable.id,
            column_name: col.name,
            column_label: col.label,
            column_type: col.type,
            is_required: col.is_required,
            sort_order: index,
            is_from_template: true // 标记来自模板
          }))
        }
      }

      const currentExtension = selectedExtension.value
      if (currentExtension) {
        if (!currentExtension.tables) {
          currentExtension.tables = []
        }
        currentExtension.tables.push(newTable)
      }
      ElMessage.success('表格创建成功')
    }
    tableDialogVisible.value = false
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

// 创建新表格
const createNewTable = async () => {
  if (!newTableFormRef.value || !selectedExtension.value) return

  try {
    await newTableFormRef.value.validate()
    creating.value = true

    // 获取模板列配置
    const getTemplateColumns = (template: string) => {
      const templates = {
        custom: [],
        hydraulic_valve: [
          { id: 1, column_name: 'model', column_label: '型号', column_type: 'text', is_required: true },
          { id: 2, column_name: 'flow_rate', column_label: '流量(L/min)', column_type: 'number', is_required: false },
          { id: 3, column_name: 'pressure', column_label: '压力(MPa)', column_type: 'number', is_required: false },
          { id: 4, column_name: 'material', column_label: '材质', column_type: 'text', is_required: false },
          { id: 5, column_name: 'price', column_label: '价格(元)', column_type: 'number', is_required: false }
        ],
        electronic_component: [
          { id: 1, column_name: 'model', column_label: '型号', column_type: 'text', is_required: true },
          { id: 2, column_name: 'voltage', column_label: '电压(V)', column_type: 'number', is_required: false },
          { id: 3, column_name: 'power', column_label: '功率(W)', column_type: 'number', is_required: false },
          { id: 4, column_name: 'package', column_label: '封装', column_type: 'text', is_required: false },
          { id: 5, column_name: 'price', column_label: '价格(元)', column_type: 'number', is_required: false }
        ],
        mechanical_part: [
          { id: 1, column_name: 'model', column_label: '型号', column_type: 'text', is_required: true },
          { id: 2, column_name: 'size', column_label: '尺寸', column_type: 'text', is_required: false },
          { id: 3, column_name: 'weight', column_label: '重量(kg)', column_type: 'number', is_required: false },
          { id: 4, column_name: 'material', column_label: '材质', column_type: 'text', is_required: false },
          { id: 5, column_name: 'price', column_label: '价格(元)', column_type: 'number', is_required: false }
        ]
      }
      return templates[template as keyof typeof templates] || []
    }

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    const newTable = {
      id: Date.now(),
      table_name: newTableForm.value.table_name,
      description: newTableForm.value.description,
      sort_order: newTableForm.value.sort_order,
      columns: getTemplateColumns(newTableForm.value.template),
      rows: []
    }

    // 添加到当前选中的扩展中
    const currentExtension = selectedExtension.value
    if (currentExtension) {
      if (!currentExtension.tables) {
        currentExtension.tables = []
      }
      currentExtension.tables.push(newTable)
    }

    // 自动选中新创建的表格
    selectedItem.value = { type: 'table', id: newTable.id }

    // 重置表单
    newTableForm.value = {
      table_name: '',
      description: '',
      template: 'custom',
      sort_order: 0
    }

    tableWizardVisible.value = false
    ElMessage.success('表格创建成功')
  } catch (error) {
    ElMessage.error('表格创建失败')
  } finally {
    creating.value = false
  }
}

// 预览图片
const previewImage = (imageUrl: string) => {
  // 创建图片预览器
  const imageViewer = document.createElement('div')
  imageViewer.className = 'image-preview-modal'
  imageViewer.innerHTML = `
    <div class="image-preview-backdrop" onclick="this.parentElement.remove()">
      <div class="image-preview-container" onclick="event.stopPropagation()">
        <img src="${imageUrl}" alt="预览图片" class="image-preview-img" />
        <button class="image-preview-close" onclick="this.closest('.image-preview-modal').remove()">
          <i class="i-mdi-close"></i>
        </button>
      </div>
    </div>
  `

  // 添加样式
  const style = document.createElement('style')
  style.textContent = `
    .image-preview-modal {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 9999;
    }
    .image-preview-backdrop {
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
    .image-preview-container {
      position: relative;
      max-width: 90vw;
      max-height: 90vh;
      cursor: default;
    }
    .image-preview-img {
      max-width: 100%;
      max-height: 100%;
      border-radius: 8px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }
    .image-preview-close {
      position: absolute;
      top: -40px;
      right: 0;
      background: rgba(255, 255, 255, 0.9);
      border: none;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 16px;
      color: #333;
    }
    .image-preview-close:hover {
      background: white;
    }
  `

  imageViewer.appendChild(style)
  document.body.appendChild(imageViewer)

  // ESC键关闭
  const handleEsc = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      imageViewer.remove()
      document.removeEventListener('keydown', handleEsc)
    }
  }
  document.addEventListener('keydown', handleEsc)
}

// 加载数据
const loadData = async () => {
  platformId.value = parseInt(route.params.id as string)

  try {
    // 从API加载真实数据
    const data = await platformConfigApi.getPlatformConfig(platformId.value)

    if (data && data.platform) {
      platformName.value = data.platform.name
      extensions.value = data.extensions || []
    } else {
      console.error('API返回数据格式不正确:', data)
      throw new Error('API返回数据格式不正确')
    }
  } catch (error) {
    console.error('加载平台配置失败:', error)
    ElMessage.error('加载平台配置失败')
    // 使用默认值
    platformName.value = '平台配置'
    extensions.value = []
  }
}

onMounted(async () => {
  await loadData()
})
</script>

<style scoped>
.h-full {
  height: 100%;
}

.flex-1 {
  flex: 1;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

/* 基本信息卡片样式 */
.basic-info-card {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.basic-info-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
  color: #1f2937;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.basic-info-content {
  padding: 4px 0;
}

.info-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border-left: 4px solid #e5e7eb;
  transition: all 0.2s ease;
}

.info-item:hover {
  background: #f3f4f6;
  border-left-color: #3b82f6;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  display: flex;
  align-items: center;
  font-size: 13px;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 15px;
  line-height: 1.5;
  color: #374151;
  font-weight: 400;
}

.info-value.primary {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.info-value.secondary {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.6;
}

.image-container {
  position: relative;
  display: inline-block;
}

.category-image {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  border: 2px solid #e5e7eb;
  transition: all 0.2s ease;
  display: block;
}

.image-container:hover .category-image {
  border-color: #3b82f6;
  transform: scale(1.02);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.image-container:hover .image-overlay {
  opacity: 1;
}

.preview-btn {
  background: rgba(255, 255, 255, 0.9) !important;
  border: none !important;
  color: #3b82f6 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.preview-btn:hover {
  background: white !important;
  transform: scale(1.1);
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  background: #f3f4f6;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-item {
    padding: 12px;
    margin-bottom: 16px;
  }

  .info-value.primary {
    font-size: 16px;
  }

  .category-image {
    width: 100px;
    height: 100px;
  }

  .image-error {
    width: 100px;
    height: 100px;
  }
}
</style>
