<template>
  <div class="p-6">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h2 class="text-2xl font-bold text-gray-800">管理员用户管理</h2>
        <p class="text-sm text-gray-500 mt-1">管理系统管理员账户信息</p>
      </div>
      <el-button 
        type="primary" 
        size="large"
        @click="handleAdd"
        class="shadow-lg hover:shadow-xl transition-shadow"
      >
        <i class="i-mdi-plus mr-2"></i>
        新建用户
      </el-button>
    </div>
    
    <!-- 搜索区域 -->
    <el-card class="mb-6 shadow-sm">
      <template #header>
        <div class="flex items-center">
          <i class="i-mdi-magnify text-lg text-blue-500 mr-2"></i>
          <span class="font-medium">搜索筛选</span>
        </div>
      </template>
      
      <el-form :model="searchParams" inline class="search-form">
        <el-form-item label="关键词">
          <el-input 
            v-model="searchParams.keyword" 
            placeholder="用户名/邮箱/姓名"
            clearable
            @keyup.enter="handleSearch"
            class="!w-48"
          />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="searchParams.status" placeholder="请选择状态" clearable class="!w-32">
            <el-option label="正常" value="active" />
            <el-option label="禁用" value="inactive" />
            <el-option label="封禁" value="banned" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="角色">
          <el-select v-model="searchParams.roleId" placeholder="请选择角色" clearable class="!w-32">
            <el-option 
              v-for="role in allRoles" 
              :key="role.id" 
              :label="role.name" 
              :value="role.id" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            @click="handleSearch"
            class="shadow-sm"
          >
            <i class="i-mdi-magnify mr-2"></i>
            搜索
          </el-button>
          <el-button 
            @click="handleReset"
            class="shadow-sm"
          >
            <i class="i-mdi-refresh mr-2"></i>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 表格区域 -->
    <el-card class="shadow-sm">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <i class="i-mdi-account-group text-lg text-green-500 mr-2"></i>
            <span class="font-medium">用户列表</span>
            <el-tag size="small" class="ml-2">共 {{ total }} 条</el-tag>
          </div>
        </div>
      </template>
      
      <el-table
        :data="users"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        class="modern-table"
        stripe
        border
      >
        <el-table-column type="selection" width="55" resizable />

        <el-table-column prop="id" label="ID" width="80" resizable />

        <el-table-column label="头像" width="80" resizable>
          <template #default="{ row }">
            <el-avatar :src="row.avatar" :size="40">
              {{ row.real_name?.charAt(0) }}
            </el-avatar>
          </template>
        </el-table-column>

        <el-table-column prop="username" label="用户名" min-width="120" resizable show-overflow-tooltip />
        <el-table-column prop="real_name" label="真实姓名" min-width="120" resizable show-overflow-tooltip />
        <el-table-column prop="email" label="邮箱" min-width="150" resizable show-overflow-tooltip />
        <el-table-column prop="phone" label="手机号" min-width="120" resizable show-overflow-tooltip />

        <el-table-column label="角色" width="120" resizable>
          <template #default="{ row }">
            <el-tag
              v-for="role in row.roles"
              :key="role.id"
              size="small"
              class="mr-1"
            >
              {{ role.name }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100" resizable>
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="lastLoginAt" label="最后登录" width="160" resizable>
          <template #default="{ row }">
            {{ row.lastLoginAt ? formatDate(row.lastLoginAt) : '从未登录' }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="380" fixed="right" resizable>
          <template #default="{ row }">
            <div class="flex items-center justify-start gap-2">
              <el-button 
                size="small" 
                type="primary"
                :icon="Edit"
                @click="handleEdit(row)"
                class="!min-w-16 !px-3"
              >
              编辑
            </el-button>
            <el-button 
              size="small" 
              type="warning"
                :icon="Key"
              @click="resetPassword(row.id, row.username)"
                class="!min-w-20 !px-3"
            >
              重置密码
            </el-button>
            <el-button 
              size="small" 
              :type="row.status === 'active' ? 'warning' : 'success'"
                :icon="row.status === 'active' ? Lock : Unlock"
              @click="toggleUserStatus(row.id)"
                class="!min-w-16 !px-3"
            >
              {{ row.status === 'active' ? '禁用' : '启用' }}
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
                :icon="Delete"
              @click="deleteUser(row.id, row.username)"
                class="!min-w-16 !px-3"
            >
              删除
            </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="flex justify-between items-center mt-6 px-2">
        <div>
          <el-button 
            v-if="selectedUsers.length > 0"
            type="danger"
            :icon="Delete"
            @click="handleBatchDelete"
            class="shadow-sm"
          >
            批量删除 ({{ selectedUsers.length }})
          </el-button>
        </div>
        
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="fetchUsers"
          @size-change="handleSizeChange"
          class="!justify-end"
        />
      </div>
    </el-card>
    
    <!-- 用户表单弹窗 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="dialogTitle"
      width="600px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="formData.username" placeholder="请输入用户名" />
        </el-form-item>
        
        <el-form-item label="真实姓名" prop="real_name">
          <el-input v-model="formData.real_name" placeholder="请输入真实姓名" />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="formData.email" placeholder="请输入邮箱" />
        </el-form-item>
        
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="formData.phone" placeholder="请输入手机号" />
        </el-form-item>
        
        <el-form-item v-if="!isEdit" label="密码" prop="password">
          <el-input 
            v-model="formData.password" 
            type="password" 
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="角色" prop="role_ids">
          <el-select 
            v-model="formData.role_ids" 
            multiple 
            placeholder="请选择角色"
            class="w-full"
          >
            <el-option 
              v-for="role in allRoles" 
              :key="role.id" 
              :label="role.name" 
              :value="role.id" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio label="active">正常</el-radio>
            <el-radio label="inactive">禁用</el-radio>
            <el-radio label="banned">封禁</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.search-form .el-form-item {
  margin-bottom: 16px;
}

.modern-table {
  border-radius: 8px;
  overflow: hidden;
}

.modern-table :deep(.el-table__header) {
  background-color: #f8fafc;
}

.modern-table :deep(.el-table__row:hover) {
  background-color: #f1f5f9;
}

.modern-table :deep(.el-table__cell) {
  border-bottom: 1px solid #e2e8f0;
}

.modern-table :deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
  height: 28px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.modern-table :deep(.el-button:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.modern-table :deep(.el-button .el-icon) {
  margin-right: 4px;
}

.modern-table :deep(.el-tag) {
  border-radius: 12px;
  font-weight: 500;
}

.modern-table :deep(.el-avatar) {
  border: 2px solid #e2e8f0;
  transition: all 0.2s ease;
}

.modern-table :deep(.el-avatar:hover) {
  border-color: #3b82f6;
  transform: scale(1.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form {
    display: block;
  }
  
  .search-form .el-form-item {
    display: block;
    margin-bottom: 12px;
  }
  
  .modern-table :deep(.el-button) {
    margin-bottom: 4px;
  }
}
</style>

<script setup lang="ts">
import { adminUsersApi, type AdminUser, type CreateAdminUserData, type UpdateAdminUserData } from '@/api'
import { useAdminUsers, useRoles } from '@/composables/useAdminApi'
import { Delete, Edit, Key, Lock, Unlock } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'

// 使用组合式函数
const {
  loading,
  users,
  total,
  pagination,
  searchParams,
  fetchUsers,
  deleteUser,
  resetPassword,
  toggleUserStatus
} = useAdminUsers()

const { fetchAllRoles } = useRoles()

// 表单相关
const dialogVisible = ref(false)
const submitLoading = ref(false)
const formRef = ref<FormInstance>()
const selectedUsers = ref<AdminUser[]>([])
const allRoles = ref<any[]>([])

const formData = reactive<CreateAdminUserData & { id?: number }>({
  username: '',
  email: '',
  password: '',
  real_name: '',
  phone: '',
  status: 'active',
  role_ids: []
})

// 计算属性
const isEdit = computed(() => !!formData.id)
const dialogTitle = computed(() => isEdit.value ? '编辑用户' : '新建用户')

// 表单验证规则
const formRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }
  ],
  real_name: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 方法
const handleSearch = () => {
  pagination.page = 1
  fetchUsers()
}

const handleReset = () => {
  Object.assign(searchParams, {
    keyword: '',
    status: undefined,
    roleId: undefined,
    startDate: '',
    endDate: ''
  })
  pagination.page = 1
  fetchUsers()
}

const handleSizeChange = () => {
  pagination.page = 1
  fetchUsers()
}

const handleSelectionChange = (selection: AdminUser[]) => {
  selectedUsers.value = selection
}

const handleAdd = () => {
  Object.assign(formData, {
    id: undefined,
    username: '',
    email: '',
    password: '',
    real_name: '',
    phone: '',
    status: 'active',
    role_ids: []
  })
  dialogVisible.value = true
}

const handleEdit = (row: AdminUser) => {
  Object.assign(formData, {
    id: row.id,
    username: row.username,
    email: row.email,
    real_name: row.real_name,
    phone: row.phone,
    status: row.status,
    role_ids: row.roles?.map(role => role.id) || []
  })
  dialogVisible.value = true
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    if (isEdit.value) {
      const updateData: UpdateAdminUserData = {
        username: formData.username,
        email: formData.email,
        real_name: formData.real_name,
        phone: formData.phone,
        status: formData.status,
        role_ids: formData.role_ids
      }
      await adminUsersApi.updateUser(formData.id!, updateData)
      ElMessage.success('更新成功')
    } else {
      await adminUsersApi.createUser(formData)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    fetchUsers()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    submitLoading.value = false
  }
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedUsers.value.length} 个用户吗？`,
      '确认删除',
      { type: 'warning' }
    )
    
    const ids = selectedUsers.value.map(user => user.id)
    await adminUsersApi.batchDeleteUsers(ids)
    ElMessage.success('批量删除成功')
    fetchUsers()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 工具函数
const getStatusType = (status: string) => {
  const typeMap: Record<string, 'success' | 'warning' | 'danger' | 'info'> = {
    active: 'success',
    inactive: 'warning',
    banned: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    active: '正常',
    inactive: '禁用',
    banned: '封禁'
  }
  return textMap[status] || status
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleString('zh-CN')
}

// 生命周期
onMounted(async () => {
  await Promise.all([
    fetchUsers(),
    fetchAllRoles().then(roles => {
      allRoles.value = roles
    })
  ])
})
</script> 