<template>
  <div class="p-6">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold">角色管理</h2>
      <el-button type="primary" @click="handleAdd">
        <i class="i-mdi-plus mr-2"></i>
        新建角色
      </el-button>
    </div>
    
    <!-- 搜索区域 -->
    <el-card class="mb-6">
      <el-form :model="searchParams" inline>
        <el-form-item label="关键词">
          <el-input 
            v-model="searchParams.keyword" 
            placeholder="角色名称/编码/描述"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="searchParams.status" placeholder="请选择状态" clearable>
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <i class="i-mdi-magnify mr-2"></i>
            搜索
          </el-button>
          <el-button @click="handleReset">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 表格区域 -->
    <el-card>
      <el-table
        :data="roles"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        border
        stripe
      >
        <el-table-column type="selection" width="55" resizable />
        <el-table-column prop="id" label="ID" width="80" resizable />
        <el-table-column prop="name" label="角色名称" min-width="120" resizable show-overflow-tooltip />
        <el-table-column prop="code" label="角色编码" min-width="120" resizable show-overflow-tooltip />
        <el-table-column prop="description" label="描述" min-width="150" resizable show-overflow-tooltip />

        <el-table-column label="状态" width="100" resizable>
          <template #default="{ row }">
            <el-tag
              :type="row.status === 'active' ? 'success' : 'danger'"
              size="small"
            >
              {{ row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="createdAt" label="创建时间" width="160" resizable>
          <template #default="{ row }">
            {{ formatDate(row.created_at || row.createdAt || row.CreateTime || row.create_time) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right" resizable>
          <template #default="{ row }">
            <el-button size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button size="small" type="info" @click="handlePermissions(row)">
              权限
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="deleteRole(row.id, row.name)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="flex justify-between items-center mt-4">
        <div>
          <el-button 
            v-if="selectedRoles.length > 0"
            type="danger"
            @click="handleBatchDelete"
          >
            批量删除 ({{ selectedRoles.length }})
          </el-button>
        </div>
        
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="fetchRoles"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
    
    <!-- 角色表单弹窗 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="dialogTitle"
      width="600px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入角色名称" />
        </el-form-item>
        
        <el-form-item label="角色编码" prop="code">
          <el-input v-model="formData.code" placeholder="请输入角色编码" />
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="formData.description" 
            type="textarea" 
            placeholder="请输入角色描述"
            :rows="3"
          />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio label="active">启用</el-radio>
            <el-radio label="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { rolesApi } from '@/api/roles'
import type { Role } from '@/types/api'
import { formatDate } from '@/utils/dateUtils'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const roles = ref<Role[]>([])
const selectedRoles = ref<Role[]>([])
const total = ref(0)

// 搜索参数
const searchParams = reactive({
  keyword: '',
  status: '' as 'active' | 'inactive' | ''
})

// 分页参数
const pagination = reactive({
  page: 1,
  size: 10
})

// 表单数据
const formData = reactive({
  id: null as number | null,
  name: '',
  code: '',
  description: '',
  status: 'active'
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入角色编码', trigger: 'blur' }
  ]
}

const formRef = ref<FormInstance>()

// 计算属性
const dialogTitle = computed(() => (isEdit.value && formData.id) ? '编辑角色' : '新建角色')

// 方法
const fetchRoles = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      keyword: searchParams.keyword || undefined,
      status: searchParams.status || undefined
    }
    
    const response = await rolesApi.getRoles(params)
// 调试代码已清理
    
    roles.value = response.list || []
    total.value = response.total || 0
  } catch (error) {
    console.error('获取角色列表失败:', error)
    ElMessage.error('获取角色列表失败')
    roles.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchRoles()
}

const handleReset = () => {
  Object.assign(searchParams, {
    keyword: '',
    status: ''
  })
  handleSearch()
}

const handleAdd = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row: any) => {
  isEdit.value = true
  // 处理Go后端字段名到前端的映射
  Object.assign(formData, {
    id: Number(row.id || row.ID),
    name: row.name || '',
    code: row.code || '',
    description: row.description || '',
    status: row.status || 'active'
  })
  // console.log('编辑角色数据:', formData)
  dialogVisible.value = true
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        const submitData = {
          name: formData.name.trim(),
          code: formData.code.trim(),
          description: formData.description || '',  // 确保有默认值
          status: formData.status as 'active' | 'inactive'
        }
        
        if (isEdit.value && formData.id) {
          // console.log('更新角色数据:', { id: formData.id, data: submitData })
          // 调用更新API
          await rolesApi.updateRole(formData.id, submitData)
        } else {
          // console.log('创建角色数据:', submitData)
          // 调用创建API
          await rolesApi.createRole(submitData)
        }
        ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
        dialogVisible.value = false
        fetchRoles()
      } catch (error: any) {
        console.error('操作失败:', error)
        const errorMessage = error?.response?.data?.message || error?.message || (isEdit.value ? '更新失败' : '创建失败')
        ElMessage.error(errorMessage)
      } finally {
        submitting.value = false
      }
    }
  })
}

const deleteRole = async (id: string, name: string) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除角色 "${name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 调用删除API
    console.log('删除角色', id)
    ElMessage.success('删除成功')
    fetchRoles()
  } catch (error) {
    // 用户取消删除
  }
}

const handlePermissions = (row: any) => {
  // TODO: 打开权限配置弹窗
  console.log('配置权限', row)
  ElMessage.info('权限配置功能开发中')
}

const handleSelectionChange = (selection: any[]) => {
  selectedRoles.value = selection
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRoles.value.length} 个角色吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 调用批量删除API
    console.log('批量删除角色', selectedRoles.value.map(r => r.id))
    ElMessage.success('批量删除成功')
    fetchRoles()
  } catch (error) {
    // 用户取消删除
  }
}

const handleSizeChange = () => {
  pagination.page = 1
  fetchRoles()
}

const resetForm = () => {
  Object.assign(formData, {
    id: null,
    name: '',
    code: '',
    description: '',
    status: 'active'
  })
  formRef.value?.clearValidate()
}



// 生命周期
onMounted(() => {
  fetchRoles()
})
</script> 