<template>
  <div class="p-6">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold">权限管理</h2>
      <el-button type="primary" @click="handleAdd">
        <i class="i-mdi-plus mr-2"></i>
        新建权限
      </el-button>
    </div>
    
    <!-- 搜索区域 -->
    <el-card class="mb-6">
      <el-form :model="searchParams" inline>
        <el-form-item label="关键词">
          <el-input 
            v-model="searchParams.keyword" 
            placeholder="权限名称/编码/路径"
            clearable
            @keyup.enter="handleSearch"
            class="w-200px"
          />
        </el-form-item>
        
        <el-form-item label="权限类型">
          <el-select v-model="searchParams.type" placeholder="请选择类型" clearable class="w-120px">
            <el-option label="菜单" value="menu" />
            <el-option label="按钮" value="button" />
            <el-option label="接口" value="api" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="searchParams.status" placeholder="请选择状态" clearable class="w-120px">
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="父权限ID">
          <el-input-number 
            v-model="searchParams.parentId" 
            placeholder="父权限ID"
            :min="0"
            clearable
            class="w-120px"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            搜索
          </el-button>
          <el-button @click="handleReset">
            重置
          </el-button>
          <el-button type="success" @click="handleCreate">
            新增权限
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 表格区域 -->
    <el-card>
      <el-table 
        :data="permissions" 
        v-loading="loading"
        border
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" resizable />
        <el-table-column prop="id" label="ID" width="80" resizable />
        <el-table-column prop="name" label="权限名称" min-width="120" resizable show-overflow-tooltip />
        <el-table-column prop="code" label="权限编码" min-width="150" resizable show-overflow-tooltip />
        <el-table-column prop="type" label="权限类型" width="120" resizable>
          <template #default="{ row }">
            <el-tag :type="row.type === 'menu' ? 'info' : row.type === 'button' ? 'success' : 'warning'">
              {{ row.type === 'menu' ? '菜单' : row.type === 'button' ? '按钮' : '接口' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="parentId" label="父权限" width="100" resizable>
          <template #default="{ row }">
            {{ row.parentId || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="path" label="路径" min-width="150" resizable show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.path || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="component" label="组件" min-width="150" resizable show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.component || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="icon" label="图标" width="120" resizable>
          <template #default="{ row }">
            <div v-if="row.icon" class="flex items-center gap-2">
              <i :class="`i-${row.icon}`" class="text-lg"></i>
              <span class="text-xs">{{ row.icon }}</span>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" width="80" resizable sortable>
          <template #default="{ row }">
            {{ row.sort }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" resizable>
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="createdAt" label="创建时间" width="160" resizable sortable>
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="160" fixed="right" resizable>
          <template #default="{ row }">
            <div class="flex flex-col gap-1">
              <!-- 如果是菜单类型且有4个按钮，使用2x2布局 -->
              <template v-if="row.type === 'menu'">
                <!-- 第一行：编辑 + 状态切换 -->
                <div class="flex gap-1">
                  <el-button size="small" class="w-12" @click="handleEdit(row)">
                    编辑
                  </el-button>
                  <el-button 
                    size="small" 
                    class="w-12"
                    :type="row.status === 'active' ? 'warning' : 'success'"
                    @click="handleToggleStatus(row)"
                  >
                    {{ row.status === 'active' ? '禁用' : '启用' }}
                  </el-button>
                </div>
                <!-- 第二行：子权限 + 删除 -->
                <div class="flex gap-1">
                  <el-button 
                    size="small" 
                    class="w-12"
                    type="info"
                    @click="handleViewChildren(row)"
                  >
                    子权限
                  </el-button>
                  <el-button 
                    size="small" 
                    class="w-12"
                    type="danger" 
                    @click="deletePermission(row.id, row.name)"
                  >
                    删除
                  </el-button>
                </div>
              </template>
              
              <!-- 如果是接口/按钮类型，3个按钮+1个隐藏按钮，保持2x2布局一致 -->
              <template v-else>
                <!-- 第一行：编辑 + 状态切换 -->
                <div class="flex gap-1">
                  <el-button size="small" class="w-12" @click="handleEdit(row)">
                    编辑
                  </el-button>
                  <el-button 
                    size="small" 
                    class="w-12"
                    :type="row.status === 'active' ? 'warning' : 'success'"
                    @click="handleToggleStatus(row)"
                  >
                    {{ row.status === 'active' ? '禁用' : '启用' }}
                  </el-button>
                </div>
                <!-- 第二行：删除 + 隐藏按钮 -->
                <div class="flex gap-1">
                  <el-button 
                    size="small" 
                    class="w-12"
                    type="danger" 
                    @click="deletePermission(row.id, row.name)"
                  >
                    删除
                  </el-button>
                  <!-- 隐藏的第4个按钮，保持布局一致 -->
                  <div class="w-12 h-6" style="visibility: hidden;"></div>
                </div>
              </template>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="flex justify-between items-center mt-4">
        <div>
          <el-button 
            v-if="selectedPermissions.length > 0"
            type="danger"
            @click="handleBatchDelete"
          >
            批量删除 ({{ selectedPermissions.length }})
          </el-button>
        </div>
        
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="fetchPermissions"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
    
    <!-- 权限表单弹窗 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="dialogTitle"
      width="700px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <div class="grid grid-cols-2 gap-4">
          <!-- 左列 -->
          <div>
            <el-form-item label="权限名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入权限名称" />
            </el-form-item>
            
            <el-form-item label="权限编码" prop="code">
              <el-input v-model="formData.code" placeholder="请输入权限编码" />
            </el-form-item>
            
            <el-form-item label="权限类型" prop="type">
              <el-select v-model="formData.type" placeholder="请选择权限类型" class="w-full">
                <el-option label="菜单" value="menu" />
                <el-option label="按钮" value="button" />
                <el-option label="接口" value="api" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="父权限ID" prop="parentId">
              <el-input-number 
                v-model="formData.parentId" 
                :min="0" 
                placeholder="父权限ID（可选）"
                class="w-full"
              />
            </el-form-item>
            
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="formData.status">
                <el-radio value="active">启用</el-radio>
                <el-radio value="inactive">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          
          <!-- 右列 -->
          <div>
            <el-form-item label="路径" prop="path">
              <el-input v-model="formData.path" placeholder="路由路径（菜单类型推荐填写）" />
            </el-form-item>
            
            <el-form-item label="组件" prop="component">
              <el-input v-model="formData.component" placeholder="组件路径（菜单类型推荐填写）" />
            </el-form-item>
            
            <el-form-item label="图标" prop="icon">
              <el-input v-model="formData.icon" placeholder="图标名称（如：mdi-home）">
                <template #prepend>
                  <i v-if="formData.icon" :class="`i-${formData.icon}`" class="text-lg"></i>
                  <i v-else class="i-mdi-help text-lg text-gray-400"></i>
                </template>
              </el-input>
            </el-form-item>
            
            <el-form-item label="排序" prop="sort">
              <el-input-number 
                v-model="formData.sort" 
                :min="0" 
                placeholder="排序值（数字越小越靠前）"
                class="w-full"
              />
            </el-form-item>
          </div>
        </div>
        
        <!-- 使用说明 -->
        <el-alert
          title="字段说明"
          type="info"
          :closable="false"
          show-icon
          class="mt-4"
        >
          <template #default>
            <ul class="text-sm space-y-1">
              <li><strong>菜单类型：</strong>需要填写路径和组件，用于生成路由</li>
              <li><strong>按钮类型：</strong>页面内的操作按钮权限</li>
              <li><strong>接口类型：</strong>API接口访问权限</li>
              <li><strong>图标格式：</strong>使用 UnoCSS 图标格式，如 mdi-home、carbon-user 等</li>
            </ul>
          </template>
        </el-alert>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { permissionsApi } from '@/api/permissions'
import type { CreatePermissionData, Permission, QueryPermissionParams } from '@/types/api'
import { formatDate } from '@/utils/dateUtils'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const permissions = ref<Permission[]>([])
const selectedPermissions = ref<Permission[]>([])
const total = ref(0)

// 搜索参数
const searchParams = reactive<QueryPermissionParams>({
  page: 1,
  size: 10,
  keyword: '',
  type: undefined,
  status: undefined,
  parentId: undefined
})

// 分页参数
const pagination = reactive({
  page: 1,
  size: 10
})

// 表单数据
const formData = reactive<CreatePermissionData & { id?: number }>({
  name: '',
  code: '',
  type: 'menu',
  parentId: undefined,
  path: '',
  component: '',
  icon: '',
  sort: 0,
  status: 'active'
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入权限编码', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择权限类型', trigger: 'change' }
  ]
}

const formRef = ref<FormInstance>()

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑权限' : '新建权限')

// 方法
const fetchPermissions = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      keyword: searchParams.keyword || undefined,
      type: searchParams.type || undefined,
      status: searchParams.status || undefined,
      parentId: searchParams.parentId || undefined
    }
    
    const response = await permissionsApi.getPermissions(params)
    permissions.value = response.list || []
    total.value = response.total || 0
  } catch (error) {
    ElMessage.error('获取权限列表失败')
    console.error('获取权限列表失败:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchPermissions()
}

const handleReset = () => {
  Object.assign(searchParams, {
    keyword: '',
    type: undefined,
    status: undefined,
    parentId: undefined
  })
  handleSearch()
}

const handleAdd = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row: Permission) => {
  isEdit.value = true
  Object.assign(formData, {
    id: row.id,
    name: row.name,
    code: row.code,
    type: row.type,
    parentId: row.parentId,
    path: row.path || '',
    component: row.component || '',
    icon: row.icon || '',
    sort: row.sort,
    status: row.status
  })
  dialogVisible.value = true
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        if (isEdit.value) {
          const { id, ...updateData } = formData
          await permissionsApi.updatePermission(id!, updateData)
          ElMessage.success('更新成功')
        } else {
          const { id, ...createData } = formData
          await permissionsApi.createPermission(createData)
          ElMessage.success('创建成功')
        }
        dialogVisible.value = false
        fetchPermissions()
      } catch (error) {
        ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
        console.error('提交权限失败:', error)
      } finally {
        submitting.value = false
      }
    }
  })
}

const deletePermission = async (id: number, name: string) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除权限 "${name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await permissionsApi.deletePermission(id)
    ElMessage.success('删除成功')
    fetchPermissions()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('删除权限失败:', error)
    }
  }
}

const handleSelectionChange = (selection: any[]) => {
  selectedPermissions.value = selection
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedPermissions.value.length} 个权限吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedPermissions.value.map(p => p.id)
    await permissionsApi.batchDeletePermissions(ids)
    ElMessage.success('批量删除成功')
    fetchPermissions()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
      console.error('批量删除权限失败:', error)
    }
  }
}

const handleSizeChange = () => {
  pagination.page = 1
  fetchPermissions()
}

const resetForm = () => {
  Object.assign(formData, {
    name: '',
    code: '',
    type: 'menu',
    parentId: undefined,
    path: '',
    component: '',
    icon: '',
    sort: 0,
    status: 'active'
  })
  formRef.value?.clearValidate()
}

const handleCreate = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

const handleToggleStatus = async (row: Permission) => {
  try {
    await ElMessageBox.confirm(
      `确定要${row.status === 'active' ? '禁用' : '启用'}权限 "${row.name}" 吗？`,
      '确认状态切换',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const updateData = {
      status: row.status === 'active' ? ('inactive' as const) : ('active' as const)
    }
    await permissionsApi.updatePermission(row.id!, updateData)
    ElMessage.success('状态切换成功')
    fetchPermissions()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('状态切换失败')
      console.error('状态切换失败:', error)
    }
  }
}

const handleViewChildren = (row: Permission) => {
  // 筛选显示该权限的子权限
  searchParams.parentId = row.id
  searchParams.keyword = ''
  searchParams.type = undefined
  searchParams.status = undefined
  handleSearch()
  ElMessage.info(`正在显示权限 "${row.name}" 的子权限`)
}

// 生命周期
onMounted(() => {
  fetchPermissions()
})
</script> 