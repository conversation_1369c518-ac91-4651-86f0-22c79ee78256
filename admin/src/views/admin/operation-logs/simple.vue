<template>
  <div class="logs-container">
    <div class="page-header">
      <h1>操作日志</h1>
      <p>系统操作日志记录</p>
    </div>
    
    <div class="logs-content">
      <el-card>
        <div class="coming-soon">
          <el-icon size="64" color="#409EFF">
            <Document />
          </el-icon>
          <h2>功能开发中</h2>
          <p>操作日志功能正在开发中，敬请期待...</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Document } from '@element-plus/icons-vue'

// 页面元信息
defineOptions({
  name: 'AdminLogs'
})
</script>

<style scoped>
.logs-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
}

.coming-soon h2 {
  margin: 20px 0 10px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 500;
}

.coming-soon p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}
</style>
