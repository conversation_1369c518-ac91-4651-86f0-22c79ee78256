<template>
  <div class="p-6">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold">操作日志</h2>
      <div>
        <el-button type="warning" @click="handleClearLogs">
          <i class="i-mdi-delete mr-2"></i>
          清空日志
        </el-button>
        <el-button type="primary" @click="handleExport">
          <i class="i-mdi-download mr-2"></i>
          导出日志
        </el-button>
      </div>
    </div>
    
    <!-- 搜索区域 -->
    <el-card class="mb-6">
      <el-form :model="searchParams" inline>
        <el-form-item label="关键词">
          <el-input 
            v-model="searchParams.keyword" 
            placeholder="操作内容/IP地址"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item label="操作模块">
          <el-select v-model="searchParams.module" placeholder="请选择模块" clearable>
            <el-option label="用户管理" value="user" />
            <el-option label="角色管理" value="role" />
            <el-option label="权限管理" value="permission" />
            <el-option label="日志管理" value="log" />
            <el-option label="系统设置" value="system" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="操作状态">
          <el-select v-model="searchParams.status" placeholder="请选择状态" clearable>
            <el-option label="成功" value="success" />
            <el-option label="失败" value="error" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="操作用户">
          <el-input 
            v-model="searchParams.adminUserId" 
            placeholder="用户ID"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="handleDateChange"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <i class="i-mdi-magnify mr-2"></i>
            搜索
          </el-button>
          <el-button @click="handleReset">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 统计卡片 -->
    <div class="grid grid-cols-4 gap-4 mb-6">
      <el-card>
        <div class="flex items-center">
          <div class="text-2xl text-blue-500 mr-4">
            <i class="i-mdi-chart-line"></i>
          </div>
          <div>
            <div class="text-sm text-gray-500">今日操作</div>
            <div class="text-xl font-bold">{{ statistics.todayCount || 0 }}</div>
          </div>
        </div>
      </el-card>
      
      <el-card>
        <div class="flex items-center">
          <div class="text-2xl text-green-500 mr-4">
            <i class="i-mdi-check-circle"></i>
          </div>
          <div>
            <div class="text-sm text-gray-500">成功操作</div>
            <div class="text-xl font-bold">{{ statistics.successCount || 0 }}</div>
          </div>
        </div>
      </el-card>
      
      <el-card>
        <div class="flex items-center">
          <div class="text-2xl text-red-500 mr-4">
            <i class="i-mdi-alert-circle"></i>
          </div>
          <div>
            <div class="text-sm text-gray-500">失败操作</div>
            <div class="text-xl font-bold">{{ statistics.errorCount || 0 }}</div>
          </div>
        </div>
      </el-card>
      
      <el-card>
        <div class="flex items-center">
          <div class="text-2xl text-purple-500 mr-4">
            <i class="i-mdi-account-multiple"></i>
          </div>
          <div>
            <div class="text-sm text-gray-500">活跃用户</div>
            <div class="text-xl font-bold">{{ statistics.activeUsers || 0 }}</div>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 表格区域 -->
    <el-card>
      <el-table 
        :data="logs" 
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column label="操作用户" width="120">
          <template #default="{ row }">
            <div>{{ row.adminUser?.realName || row.adminUser?.username }}</div>
            <div class="text-xs text-gray-500">ID: {{ row.adminUserId }}</div>
          </template>
        </el-table-column>
        
        <el-table-column prop="module" label="操作模块" width="100" />
        <el-table-column prop="action" label="操作动作" width="100" />
        <el-table-column prop="description" label="操作描述" />
        
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag 
              :type="row.status === 'success' ? 'success' : 'danger'"
              size="small"
            >
              {{ row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="ipAddress" label="IP地址" width="120" />
        <el-table-column prop="userAgent" label="用户代理" width="200" show-overflow-tooltip />
        
        <el-table-column prop="createdAt" label="操作时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleViewDetail(row)">
              详情
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="deleteLog(row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="flex justify-between items-center mt-4">
        <div>
          <el-button 
            v-if="selectedLogs.length > 0"
            type="danger"
            @click="handleBatchDelete"
          >
            批量删除 ({{ selectedLogs.length }})
          </el-button>
        </div>
        
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="fetchLogs"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
    
    <!-- 日志详情弹窗 -->
    <el-dialog 
      v-model="detailVisible" 
      title="操作日志详情"
      width="800px"
    >
      <div v-if="currentLog">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="日志ID">{{ currentLog.id }}</el-descriptions-item>
          <el-descriptions-item label="操作用户">
            {{ currentLog.adminUser?.realName || currentLog.adminUser?.username }}
          </el-descriptions-item>
          <el-descriptions-item label="操作模块">{{ currentLog.module }}</el-descriptions-item>
          <el-descriptions-item label="操作动作">{{ currentLog.action }}</el-descriptions-item>
          <el-descriptions-item label="操作状态">
            <el-tag :type="currentLog.status === 'success' ? 'success' : 'danger'">
              {{ currentLog.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="IP地址">{{ currentLog.ipAddress }}</el-descriptions-item>
          <el-descriptions-item label="操作时间" :span="2">
            {{ formatDate(currentLog.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="操作描述" :span="2">
            {{ currentLog.description }}
          </el-descriptions-item>
          <el-descriptions-item label="用户代理" :span="2">
            {{ currentLog.userAgent }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div v-if="currentLog.requestData" class="mt-4">
          <h4 class="mb-2">请求数据：</h4>
          <el-input
            type="textarea"
            :value="JSON.stringify(currentLog.requestData, null, 2)"
            readonly
            :rows="6"
          />
        </div>
        
        <div v-if="currentLog.responseData" class="mt-4">
          <h4 class="mb-2">响应数据：</h4>
          <el-input
            type="textarea"
            :value="JSON.stringify(currentLog.responseData, null, 2)"
            readonly
            :rows="6"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'

// 响应式数据
const loading = ref(false)
const detailVisible = ref(false)
const logs = ref<any[]>([])
const selectedLogs = ref<any[]>([])
const currentLog = ref<any>(null)
const total = ref(0)
const dateRange = ref<[string, string] | null>(null)

// 统计数据
const statistics = ref({
  todayCount: 0,
  successCount: 0,
  errorCount: 0,
  activeUsers: 0
})

// 搜索参数
const searchParams = reactive({
  keyword: '',
  module: '',
  status: '',
  adminUserId: '',
  startDate: '',
  endDate: ''
})

// 分页参数
const pagination = reactive({
  page: 1,
  size: 10
})

// 方法
const fetchLogs = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取日志列表
    console.log('获取日志列表', { ...searchParams, ...pagination })
    // 模拟数据
    logs.value = []
    total.value = 0
  } catch (error) {
    ElMessage.error('获取日志列表失败')
  } finally {
    loading.value = false
  }
}

const fetchStatistics = async () => {
  try {
    // TODO: 调用API获取统计数据
    console.log('获取统计数据')
    // 模拟数据
    statistics.value = {
      todayCount: 0,
      successCount: 0,
      errorCount: 0,
      activeUsers: 0
    }
  } catch (error) {
    console.error('获取统计数据失败', error)
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchLogs()
}

const handleReset = () => {
  Object.assign(searchParams, {
    keyword: '',
    module: '',
    status: '',
    adminUserId: '',
    startDate: '',
    endDate: ''
  })
  dateRange.value = null
  handleSearch()
}

const handleDateChange = (dates: [string, string] | null) => {
  if (dates) {
    searchParams.startDate = dates[0]
    searchParams.endDate = dates[1]
  } else {
    searchParams.startDate = ''
    searchParams.endDate = ''
  }
}

const handleViewDetail = (row: any) => {
  currentLog.value = row
  detailVisible.value = true
}

const deleteLog = async (id: string) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条日志吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 调用删除API
    console.log('删除日志', id)
    ElMessage.success('删除成功')
    fetchLogs()
  } catch (error) {
    // 用户取消删除
  }
}

const handleSelectionChange = (selection: any[]) => {
  selectedLogs.value = selection
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedLogs.value.length} 条日志吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 调用批量删除API
    console.log('批量删除日志', selectedLogs.value.map(l => l.id))
    ElMessage.success('批量删除成功')
    fetchLogs()
  } catch (error) {
    // 用户取消删除
  }
}

const handleClearLogs = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有日志吗？此操作不可恢复！',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 调用清空API
    console.log('清空日志')
    ElMessage.success('清空成功')
    fetchLogs()
    fetchStatistics()
  } catch (error) {
    // 用户取消清空
  }
}

const handleExport = () => {
  // TODO: 实现导出功能
  console.log('导出日志', searchParams)
  ElMessage.info('导出功能开发中')
}

const handleSizeChange = () => {
  pagination.page = 1
  fetchLogs()
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleString()
}

// 生命周期
onMounted(() => {
  fetchLogs()
  fetchStatistics()
})
</script> 