<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>仪表盘</h1>
      <p>欢迎回来，{{ userInfo.name }}！</p>
    </div>
    
    <!-- 统计卡片 - 使用 TSX 组件 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <StatusCard 
          title="总用户数" 
          :value="1234" 
          status="info" 
          icon="i-mdi-account-group"
        />
      </el-col>
      
      <el-col :span="6">
        <StatusCard 
          title="今日访问" 
          :value="2567" 
          status="success" 
          icon="i-mdi-eye"
        />
      </el-col>
      
      <el-col :span="6">
        <StatusCard 
          title="文章数量" 
          :value="456" 
          status="warning" 
          icon="i-mdi-document"
        />
      </el-col>
      
      <el-col :span="6">
        <StatusCard 
          title="系统消息" 
          :value="12" 
          status="error" 
          icon="i-mdi-bell"
        />
      </el-col>
    </el-row>
    
    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <h3>访问量趋势</h3>
          </template>
          <div class="chart-container">
            <div style="height: 300px; display: flex; align-items: center; justify-content: center; color: #999;">
              图表区域 - 将集成 ECharts
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <h3>用户分布</h3>
          </template>
          <div class="chart-container">
            <div style="height: 300px; display: flex; align-items: center; justify-content: center; color: #999;">
              图表区域 - 将集成 ECharts
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 最近活动 -->
    <el-row>
      <el-col :span="24">
        <el-card>
          <template #header>
            <h3>最近活动</h3>
          </template>
          <el-table :data="recentActivities" style="width: 100%">
            <el-table-column prop="time" label="时间" width="180" />
            <el-table-column prop="user" label="用户" width="120" />
            <el-table-column prop="action" label="操作" />
            <el-table-column prop="target" label="目标" />
            <el-table-column label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.status === 'success' ? 'success' : 'danger'">
                  {{ row.status === 'success' ? '成功' : '失败' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import StatusCard from '@/components/StatusCard.tsx'
import { reactive, ref } from 'vue'

const userInfo = ref({
  name: '管理员'
})

const stats = reactive([
  {
    title: '总用户数',
    value: '1,234',
    icon: 'User',
    color: '#409EFF'
  },
  {
    title: '今日访问',
    value: '2,567',
    icon: 'View',
    color: '#67C23A'
  },
  {
    title: '文章数量',
    value: '456',
    icon: 'Document',
    color: '#E6A23C'
  },
  {
    title: '系统消息',
    value: '12',
    icon: 'Bell',
    color: '#F56C6C'
  }
])

const recentActivities = reactive([
  {
    time: '2024-01-15 14:30:00',
    user: '管理员',
    action: '创建文章',
    target: '关于我们',
    status: 'success'
  },
  {
    time: '2024-01-15 13:45:00',
    user: '编辑者',
    action: '更新页面',
    target: '产品介绍',
    status: 'success'
  },
  {
    time: '2024-01-15 12:20:00',
    user: '管理员',
    action: '删除用户',
    target: '<EMAIL>',
    status: 'success'
  },
  {
    time: '2024-01-15 11:10:00',
    user: '系统',
    action: '备份数据',
    target: '数据库',
    status: 'success'
  }
])
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.dashboard-header {
  margin-bottom: 30px;
}

.dashboard-header h1 {
  font-size: 28px;
  color: #333;
  margin-bottom: 8px;
}

.dashboard-header p {
  color: #666;
  font-size: 16px;
}

.stats-row {
  margin-bottom: 30px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-title {
  font-size: 14px;
  color: #666;
}

.charts-row {
  margin-bottom: 30px;
}

.chart-container {
  height: 300px;
}

:deep(.el-card__header) {
  padding: 20px 20px 0;
  border-bottom: none;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style> 