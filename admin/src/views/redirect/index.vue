<template>
  <div class="redirect-loading">
    <el-loading-directive v-loading="true" text="页面刷新中..." />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

onMounted(() => {
  const { params, query } = route
  const redirectPath = Array.isArray(params.path) ? params.path.join('/') : params.path
  
  // 延迟一下再跳转，给用户一个刷新的感觉
  setTimeout(() => {
    router.replace({
      path: '/' + redirectPath,
      query
    })
  }, 100)
})
</script>

<style scoped>
.redirect-loading {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
