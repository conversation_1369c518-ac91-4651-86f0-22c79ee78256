// 基础实体接口
export interface BaseEntity {
  id: number
  created_at: string
  updated_at: string
}

// API响应接口
export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
  success: boolean
}

// 分页参数接口
export interface PaginationParams {
  page: number
  size: number
  [key: string]: any
}

// 分页结果接口
export interface PaginationResult<T> {
  list: T[]
  total: number
  page: number
  size: number
}

// 分页响应接口
export interface PaginatedResponse<T> {
  list: T[]
  total: number
  page: number
  page_size: number
}

// 排序参数接口
export interface SortParams {
  field: string
  order: 'asc' | 'desc'
}

// 搜索参数接口
export interface SearchParams extends PaginationParams {
  keyword?: string
  sort?: SortParams
}



// 管理员用户相关类型
export interface AdminUser extends BaseEntity {
  username: string
  email: string
  real_name: string
  avatar?: string
  phone?: string
  status: 'active' | 'inactive' | 'banned'
  lastLoginAt?: string
  lastLoginIp?: string
  roles?: Role[]
}

export interface CreateAdminUserData {
  username: string
  email: string
  password: string
  real_name: string
  phone?: string
  status?: 'active' | 'inactive' | 'banned'
  role_ids?: number[]
}

export interface UpdateAdminUserData {
  username?: string
  email?: string
  real_name?: string
  avatar?: string
  phone?: string
  status?: 'active' | 'inactive' | 'banned'
  role_ids?: number[]
}

export interface QueryAdminUserParams extends PaginationParams {
  keyword?: string
  status?: 'active' | 'inactive' | 'banned'
  role_id?: number
  start_date?: string
  end_date?: string
  page_size?: number
}

// 角色相关类型
export interface Role extends BaseEntity {
  name: string
  code: string
  description?: string
  status: 'active' | 'inactive'
  sort: number
  isSystem: boolean
  permissions?: Permission[]
}

export interface CreateRoleData {
  name: string
  code: string
  description?: string
  status?: 'active' | 'inactive'
  sort?: number
}

export interface UpdateRoleData extends Partial<CreateRoleData> {}

export interface QueryRoleParams extends PaginationParams {
  keyword?: string
  status?: 'active' | 'inactive'
}

export interface AssignPermissionsData {
  permissionIds: number[]
}

// 权限相关类型
export interface Permission extends BaseEntity {
  name: string
  code: string
  type: 'menu' | 'button' | 'api'
  parentId?: number
  path?: string
  component?: string
  icon?: string
  sort: number
  status: 'active' | 'inactive'
  children?: Permission[]
}

export interface CreatePermissionData {
  name: string
  code: string
  type: 'menu' | 'button' | 'api'
  parentId?: number
  path?: string
  component?: string
  icon?: string
  sort?: number
  status?: 'active' | 'inactive'
}

export interface UpdatePermissionData extends Partial<CreatePermissionData> {}

export interface QueryPermissionParams extends PaginationParams {
  keyword?: string
  type?: 'menu' | 'button' | 'api'
  status?: 'active' | 'inactive'
  parentId?: number
}

// 操作日志相关类型
export interface AdminLog extends BaseEntity {
  userId: number
  username: string
  action: string
  module: string
  description: string
  ip: string
  userAgent: string
  requestUrl: string
  requestMethod: string
  requestParams?: string
  responseData?: string
  duration: number
  status: 'success' | 'error'
}

export interface CreateAdminLogData {
  userId: number
  username: string
  action: string
  module: string
  description: string
  ip: string
  userAgent: string
  requestUrl: string
  requestMethod: string
  requestParams?: string
  responseData?: string
  duration: number
  status: 'success' | 'error'
}

export interface QueryAdminLogParams extends PaginationParams {
  keyword?: string
  userId?: number
  module?: string
  action?: string
  status?: 'success' | 'error'
  startDate?: string
  endDate?: string
}

export interface AdminLogStatistics {
  totalLogs: number
  todayLogs: number
  successRate: number
  topModules: Array<{ module: string; count: number }>
  topActions: Array<{ action: string; count: number }>
}

// 认证相关类型
export interface LoginData {
  username: string
  password: string
  captcha?: string
  captchaId?: string
}

export interface LoginResponse {
  token: string
  admin: AdminUser  // 后端返回的是admin字段，类型是AdminUser
  permissions?: string[]  // 用户权限列表
}

export interface UserInfoResponse {
  userInfo: AdminUser
  permissions: string[]
}

export interface UserInfo {
  id: number
  username: string
  nickname: string
  email: string
  avatar?: string
  role: string
}

// 文章相关类型
export interface Article extends BaseEntity {
  title: string
  content: string
  summary: string
  cover?: string
  status: 'draft' | 'published' | 'archived'
  tags: string[]
  categoryId: number
  authorId: number
  viewCount: number
  publishedAt?: string
}

export interface CreateArticleData {
  title: string
  content: string
  summary: string
  cover?: string
  status: 'draft' | 'published'
  tags: string[]
  categoryId: number
}

export interface UpdateArticleData extends Partial<CreateArticleData> {}

// 分类相关类型
export interface Category extends BaseEntity {
  name: string
  description?: string
  parentId?: number
  sort: number
  status: 'active' | 'inactive'
}

export interface CreateCategoryData {
  name: string
  description?: string
  parentId?: number
  sort?: number
  status?: 'active' | 'inactive'
}

export interface UpdateCategoryData extends Partial<CreateCategoryData> {}

// 文件上传相关类型
export interface UploadResponse {
  id: number
  originalName: string
  fileName: string
  filePath: string
  fileSize: number
  mimeType: string
  bucketName: string
  cosUrl: string
  uploadedBy: number
  module: string
  status: string
  createdAt: string
  updatedAt: string
}

export interface UploadConfigResponse {
  allowedTypes: string[]
  maxFileSize: number
  uploadPath: string
}

// 统计数据类型
export interface DashboardStats {
  userCount: number
  articleCount: number
  categoryCount: number
  todayVisits: number
}

// 系统设置类型
export interface SystemSettings {
  siteName: string
  siteDescription: string
  siteLogo?: string
  siteKeywords: string
  contactEmail: string
  icp?: string
}

export interface UpdateSystemSettingsData extends Partial<SystemSettings> {}

// 内容管理相关类型

// 轮播图相关类型
export interface Swiper extends BaseEntity {
  url: string
  title: string
  order: number
  status: 'active' | 'inactive'
}

export interface CreateSwiperData {
  url: string
  title: string
  order?: number
  status?: 'active' | 'inactive'
}

export interface UpdateSwiperData extends Partial<CreateSwiperData> {}

// 新闻相关类型
export interface News extends BaseEntity {
  title: string
  content: string
  image?: string
  isHomePage: boolean
}

export interface CreateNewsData {
  title: string
  content: string
  image?: string
  isHomePage?: boolean
}

export interface UpdateNewsData extends Partial<CreateNewsData> {}



// 项目案例相关类型
export interface ProjectCase extends BaseEntity {
  url: string
  title?: string
  description?: string
  sort: number
}

export interface CreateProjectCaseData {
  url: string
  title?: string
  description?: string
  sort?: number
}

export interface UpdateProjectCaseData extends Partial<CreateProjectCaseData> {}

// 合作伙伴相关类型
export interface Partner extends BaseEntity {
  name: string
  logo: string
}

export interface CreatePartnerData {
  name: string
  logo: string
}

export interface UpdatePartnerData extends Partial<CreatePartnerData> {}

// 友情链接相关类型
export interface FriendLink extends BaseEntity {
  name: string
  url: string
  order: number
}

export interface CreateFriendLinkData {
  name: string
  url: string
  order?: number
}

export interface UpdateFriendLinkData extends Partial<CreateFriendLinkData> {}

// 招聘信息相关类型
export interface Recruitment extends BaseEntity {
  name: string          // 姓名/职位名称
  location: string      // 工作地点
  content: string       // 招聘内容
  position: string      // 职位
  department: string    // 部门
  description: string   // 职位描述
  requirement?: string  // 职位要求（可选）
}

export interface CreateRecruitmentData {
  name: string
  location: string
  content: string
  position: string
  department: string
  description: string
  requirement?: string
}

export interface UpdateRecruitmentData extends Partial<CreateRecruitmentData> {}

// 平台相关类型
export interface PartPlatform extends BaseEntity {
  name: string
  url: string
  description: string
  parameters: string
  applications: string
}

export interface CreatePartPlatformData {
  name: string
  url: string
  description: string
  parameters: string
  applications: string
}

export interface UpdatePartPlatformData extends Partial<CreatePartPlatformData> {}

// 扩展数据相关类型
export interface PartPlatformExtension extends BaseEntity {
  part_platform_id: number
  title: string
  description?: string
  image_url?: string
  sort_order: number
  tables?: PartPlatformTable[]
}

export interface PartPlatformTable extends BaseEntity {
  extension_id: number
  table_name: string
  sort_order: number
  columns?: PartPlatformTableColumn[]
  rows?: PartPlatformTableRow[]
}

export interface PartPlatformTableColumn extends BaseEntity {
  table_id: number
  column_name: string
  column_label: string
  column_type: string
  sort_order: number
}

export interface PartPlatformTableRow extends BaseEntity {
  table_id: number
  row_data: Record<string, any>
  sort_order: number
}

// 创建和更新相关类型
export interface CreatePartPlatformExtensionData {
  part_platform_id: number
  title: string
  description?: string
  image_url?: string
  sort_order?: number
}

export interface UpdatePartPlatformExtensionData extends Partial<CreatePartPlatformExtensionData> {}

export interface CreatePartPlatformTableData {
  extension_id: number
  table_name: string
  sort_order?: number
}

export interface UpdatePartPlatformTableData extends Partial<CreatePartPlatformTableData> {}

export interface CreatePartPlatformTableColumnData {
  table_id: number
  column_name: string
  column_label: string
  column_type?: string
  sort_order?: number
}

export interface UpdatePartPlatformTableColumnData extends Partial<CreatePartPlatformTableColumnData> {}

export interface CreatePartPlatformTableRowData {
  table_id: number
  row_data: Record<string, any>
  sort_order?: number
}

export interface UpdatePartPlatformTableRowData extends Partial<CreatePartPlatformTableRowData> {}

// 详情页面响应类型
export interface PartPlatformDetailResponse {
  platform: PartPlatform
  extensions: PartPlatformExtension[]
}