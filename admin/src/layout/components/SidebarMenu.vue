<template>
  <el-menu
    :default-active="activeMenu"
    :default-openeds="defaultOpeneds"
    :collapse="isCollapse"
    background-color="#304156"
    text-color="#bfcbd9"
    active-text-color="#ffffff"
    router
    class="sidebar-menu"
  >
    <template v-for="route in menuRoutes" :key="route.path">
      <!-- 单级菜单（只有一个子路由的情况） -->
      <el-menu-item 
        v-if="route.children && route.children.length === 1" 
        :index="getMenuPath(route)"
        class="single-menu-item"
      >
        <el-icon v-if="route.children[0].meta?.icon">
          <component :is="route.children[0].meta.icon" />
        </el-icon>
        <template #title>{{ route.children[0].meta?.title }}</template>
      </el-menu-item>
      
      <!-- 多级菜单 -->
      <el-sub-menu 
        v-else-if="route.children && route.children.length > 1" 
        :index="route.path"
        class="multi-menu-item"
        :class="{ 'is-active-parent': isActiveParent(route.path) }"
      >
        <template #title>
          <el-icon v-if="route.meta?.icon">
            <component :is="route.meta.icon" />
          </el-icon>
          <span>{{ route.meta?.title }}</span>
        </template>
        <el-menu-item 
          v-for="child in route.children" 
          :key="child.path"
          :index="getChildMenuPath(route.path, child.path)"
          class="sub-menu-item"
        >
          <el-icon v-if="child.meta?.icon">
            <component :is="child.meta.icon" />
          </el-icon>
          <template #title>{{ child.meta?.title }}</template>
        </el-menu-item>
      </el-sub-menu>
    </template>
  </el-menu>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

interface Props {
  isCollapse: boolean
}

defineProps<Props>()

const router = useRouter()
const route = useRoute()

// 获取菜单路由（只获取顶级Layout路由）
const menuRoutes = computed(() => {
  return router.getRoutes().filter(route =>
    !route.meta?.hideInMenu &&
    route.path !== '/login' &&
    route.path !== '/:pathMatch(.*)*' && // 过滤404页面
    route.path !== '/' && // 过滤根路径重定向
    route.children && route.children.length > 0 // 只显示有子路由的路由
  ).map(route => {
    // 过滤掉子路由中设置了 hideInMenu 的路由
    const visibleChildren = route.children?.filter(child => !child.meta?.hideInMenu) || []
    return {
      ...route,
      children: visibleChildren
    }
  }).filter(route => route.children.length > 0) // 过滤掉没有可见子路由的父路由
})

// 当前激活的菜单
const activeMenu = computed(() => {
  return route.path
})

// 默认展开的菜单
const defaultOpeneds = computed(() => {
  const currentPath = route.path
  const openeds: string[] = []
  
  // 找到当前路径对应的父菜单并展开
  for (const menuRoute of menuRoutes.value) {
    if (currentPath.startsWith(menuRoute.path) && menuRoute.children && menuRoute.children.length > 1) {
      openeds.push(menuRoute.path)
    }
  }
  
  return openeds
})

// 判断是否为激活的父菜单
const isActiveParent = (parentPath: string) => {
  return route.path.startsWith(parentPath)
}

// 获取单级菜单路径
const getMenuPath = (routeItem: any) => {
  if (routeItem.children && routeItem.children.length === 1) {
    const child = routeItem.children[0]
    return child.path === '' ? routeItem.path : `${routeItem.path}/${child.path}`
  }
  return routeItem.path
}

// 获取子菜单路径
const getChildMenuPath = (parentPath: string, childPath: string) => {
  return childPath === '' ? parentPath : `${parentPath}/${childPath}`
}
</script>

<style scoped>
.sidebar-menu {
  border-right: none;
  height: calc(100vh - 60px);
  overflow-y: auto;
}

.sidebar-menu:not(.el-menu--collapse) {
  width: 200px;
}

/* 基础菜单项样式 */
:deep(.el-menu-item),
:deep(.el-sub-menu__title) {
  height: 50px;
  line-height: 50px;
  transition: all 0.3s ease;
}

/* 单级菜单项样式 */
:deep(.single-menu-item) {
  border-left: 3px solid transparent;
}

:deep(.single-menu-item:hover) {
  background-color: #263445 !important;
  border-left-color: #409EFF;
}

:deep(.single-menu-item.is-active) {
  background-color: #409EFF !important;
  border-left-color: #ffffff;
  color: #ffffff !important;
}

/* 多级菜单父项样式 */
:deep(.multi-menu-item .el-sub-menu__title) {
  border-left: 3px solid transparent;
}

:deep(.multi-menu-item .el-sub-menu__title:hover) {
  background-color: #263445 !important;
  border-left-color: #409EFF;
}

:deep(.multi-menu-item.is-active-parent .el-sub-menu__title) {
  background-color: #263445 !important;
  border-left-color: #409EFF;
  color: #409EFF !important;
}

/* 子菜单项样式 */
:deep(.sub-menu-item) {
  background-color: #1f2d3d !important;
  border-left: 3px solid transparent;
  margin-left: 0;
  padding-left: 50px !important;
}

:deep(.sub-menu-item:hover) {
  background-color: #263445 !important;
  border-left-color: #409EFF;
}

:deep(.sub-menu-item.is-active) {
  background-color: #409EFF !important;
  border-left-color: #ffffff;
  color: #ffffff !important;
}

/* 图标样式 */
:deep(.el-menu-item .el-icon),
:deep(.el-sub-menu__title .el-icon) {
  margin-right: 8px;
  font-size: 16px;
}

/* 折叠状态下的样式 */
:deep(.el-menu--collapse .el-menu-item),
:deep(.el-menu--collapse .el-sub-menu__title) {
  padding: 0 20px !important;
}

/* 滚动条样式 */
.sidebar-menu::-webkit-scrollbar {
  width: 4px;
}

.sidebar-menu::-webkit-scrollbar-track {
  background: #1f2d3d;
}

.sidebar-menu::-webkit-scrollbar-thumb {
  background: #409EFF;
  border-radius: 2px;
}

.sidebar-menu::-webkit-scrollbar-thumb:hover {
  background: #66b1ff;
}
</style> 