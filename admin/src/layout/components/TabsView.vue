<template>
  <div class="tabs-view" v-if="tabsStore.tabs.length > 0">
    <div class="tabs-container">
      <div class="tabs-wrapper">
        <div 
          v-for="tab in tabsStore.tabs" 
          :key="tab.path"
          :class="[
            'tab-item',
            { 'is-active': tab.path === tabsStore.activeTab }
          ]"
          @click="handleTabClick(tab)"
          @contextmenu.prevent="handleContextMenu($event, tab)"
        >
          <span class="tab-title">{{ tab.title }}</span>
          <el-icon 
            v-if="tab.closable" 
            class="tab-close"
            @click.stop="handleTabClose(tab)"
          >
            <Close />
          </el-icon>
        </div>
      </div>
      
      <!-- 右侧操作按钮 -->
      <div class="tabs-actions">
        <el-dropdown @command="handleCommand" trigger="click">
          <el-button size="small" text>
            <el-icon><MoreFilled /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="closeOthers" :disabled="tabsStore.tabs.length <= 1">
                <el-icon><Close /></el-icon>
                关闭其他
              </el-dropdown-item>
              <el-dropdown-item command="closeAll" :disabled="!hasClosableTabs">
                <el-icon><CircleClose /></el-icon>
                关闭所有
              </el-dropdown-item>
              <el-dropdown-item command="refresh" divided>
                <el-icon><Refresh /></el-icon>
                刷新当前页
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 右键菜单 -->
    <div 
      v-if="contextMenu.visible"
      class="context-menu"
      :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }"
      @click="hideContextMenu"
    >
      <div class="context-menu-item" @click="handleRefresh">
        <el-icon><Refresh /></el-icon>
        刷新
      </div>
      <div 
        v-if="contextMenu.tab?.closable"
        class="context-menu-item" 
        @click="handleTabClose(contextMenu.tab!)"
      >
        <el-icon><Close /></el-icon>
        关闭
      </div>
      <div 
        class="context-menu-item" 
        @click="handleCloseOthers"
        :class="{ disabled: tabsStore.tabs.length <= 1 }"
      >
        <el-icon><Close /></el-icon>
        关闭其他
      </div>
      <div 
        class="context-menu-item" 
        @click="handleCloseAll"
        :class="{ disabled: !hasClosableTabs }"
      >
        <el-icon><CircleClose /></el-icon>
        关闭所有
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useTabsStore, type TabItem } from '@/stores/tabs'
import { CircleClose, Close, MoreFilled, Refresh } from '@element-plus/icons-vue'
import { computed, onMounted, onUnmounted, reactive } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const tabsStore = useTabsStore()

// 右键菜单状态
const contextMenu = reactive({
  visible: false,
  x: 0,
  y: 0,
  tab: null as TabItem | null
})

// 计算属性
const hasClosableTabs = computed(() => {
  return tabsStore.tabs.some(tab => tab.closable)
})

// 点击tab
const handleTabClick = (tab: TabItem) => {
  if (tab.path !== tabsStore.activeTab) {
    router.push({
      path: tab.path,
      params: tab.params || {},
      query: tab.query || {}
    })
  }
}

// 关闭tab
const handleTabClose = (tab: TabItem) => {
  // 检查是否关闭的是当前激活的tab
  const isClosingActiveTab = tab.path === tabsStore.activeTab

  const nextTab = tabsStore.closeTab(tab.path)

  // 如果关闭的是当前激活的tab，需要跳转到下一个tab
  if (isClosingActiveTab && nextTab) {
    router.push({
      path: nextTab.path,
      params: nextTab.params || {},
      query: nextTab.query || {}
    })
  }

  hideContextMenu()
}

// 右键菜单
const handleContextMenu = (event: MouseEvent, tab: TabItem) => {
  event.preventDefault()
  contextMenu.visible = true
  contextMenu.x = event.clientX
  contextMenu.y = event.clientY
  contextMenu.tab = tab
}

// 隐藏右键菜单
const hideContextMenu = () => {
  contextMenu.visible = false
  contextMenu.tab = null
}

// 下拉菜单命令
const handleCommand = (command: string) => {
  switch (command) {
    case 'closeOthers':
      handleCloseOthers()
      break
    case 'closeAll':
      handleCloseAll()
      break
    case 'refresh':
      handleRefresh()
      break
  }
}

// 关闭其他
const handleCloseOthers = () => {
  const keepPath = contextMenu.tab?.path || tabsStore.activeTab
  const currentActivePath = tabsStore.activeTab

  tabsStore.closeOtherTabs(keepPath)

  // 如果保留的tab不是当前激活的tab，需要跳转
  if (keepPath !== currentActivePath) {
    const targetTab = tabsStore.getTab(keepPath)
    if (targetTab) {
      router.push({
        path: targetTab.path,
        params: targetTab.params || {},
        query: targetTab.query || {}
      })
    }
  }

  hideContextMenu()
}

// 关闭所有
const handleCloseAll = () => {
  tabsStore.closeAllTabs()
  const firstTab = tabsStore.tabs[0]
  if (firstTab) {
    router.push({
      path: firstTab.path,
      params: firstTab.params,
      query: firstTab.query
    })
  }
  hideContextMenu()
}

// 刷新页面
const handleRefresh = () => {
  const currentPath = contextMenu.tab?.path || tabsStore.activeTab
  const currentTab = tabsStore.getTab(currentPath)
  if (currentTab) {
    // 先跳转到一个空路由，再跳转回来实现刷新
    router.replace({ path: '/redirect' + currentPath })
  }
  hideContextMenu()
}

// 点击其他地方隐藏右键菜单
const handleClickOutside = (event: MouseEvent) => {
  if (contextMenu.visible) {
    hideContextMenu()
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.tabs-view {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 1px 2px 0 rgba(0, 0, 0, 0.24);
}

.tabs-container {
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 16px;
}

.tabs-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  overflow-x: auto;
  overflow-y: hidden;
}

.tabs-wrapper::-webkit-scrollbar {
  height: 4px;
}

.tabs-wrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.tab-item {
  display: flex;
  align-items: center;
  height: 32px;
  padding: 0 12px;
  margin-right: 4px;
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap;
  user-select: none;
}

.tab-item:hover {
  background: #ecf5ff;
  border-color: #b3d8ff;
}

.tab-item.is-active {
  background: #409eff;
  border-color: #409eff;
  color: #fff;
}

.tab-title {
  font-size: 12px;
  line-height: 1;
}

.tab-close {
  margin-left: 8px;
  font-size: 12px;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.3s;
}

.tab-close:hover {
  background: rgba(0, 0, 0, 0.12);
}

.tab-item.is-active .tab-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.tabs-actions {
  margin-left: 8px;
}

/* 右键菜单 */
.context-menu {
  position: fixed;
  z-index: 9999;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 4px 0;
  min-width: 120px;
}

.context-menu-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.context-menu-item:hover:not(.disabled) {
  background: #f5f7fa;
}

.context-menu-item.disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}

.context-menu-item .el-icon {
  margin-right: 8px;
  font-size: 14px;
}
</style>
