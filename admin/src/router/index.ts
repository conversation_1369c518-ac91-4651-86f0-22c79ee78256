import Layout from '@/layout/index.vue'
import { useAuthStore, useTabsStore } from '@/stores'
import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHistory } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录',
      hideInMenu: true,
      requiresAuth: false,
    },
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: {
          title: '仪表盘',
          icon: 'Dashboard',
          requiresAuth: true,
        },
      },
      {
        path: 'debug/auth',
        name: 'AuthDebug',
        component: () => import('@/views/debug/auth-debug.vue'),
        meta: {
          title: '认证调试',
          icon: 'Tools',
          requiresAuth: true,
        },
      },
      {
        path: 'debug/auth-status',
        name: 'AuthStatus',
        component: () => import('@/views/debug/auth-status.vue'),
        meta: {
          title: '认证状态',
          icon: 'Monitor',
          requiresAuth: true,
        },
      },
    ],
  },
  {
    path: '/admin',
    component: Layout,
    redirect: '/admin/users',
    meta: {
      title: '管理员管理',
      icon: 'UserFilled',
      requiresAuth: true,
    },
    children: [
      {
        path: 'users',
        name: 'AdminUsers',
        component: () => import('@/views/admin/users/index.vue'),
        meta: {
          title: '管理员用户',
          icon: 'User',
          requiresAuth: true,
          permissions: ['admin:user:list'],
        },
      },
      {
        path: 'roles',
        name: 'AdminRoles',
        component: () => import('@/views/admin/roles/index.vue'),
        meta: {
          title: '角色管理',
          icon: 'Avatar',
          requiresAuth: true,
          permissions: ['admin:role:list'],
        },
      },
      {
        path: 'permissions',
        name: 'AdminPermissions',
        component: () => import('@/views/admin/permissions/index.vue'),
        meta: {
          title: '权限管理',
          icon: 'Key',
          requiresAuth: true,
          permissions: ['admin:permission:list'],
        },
      },
      {
        path: 'logs',
        name: 'AdminLogs',
        component: () => import('@/views/admin/operation-logs/simple.vue'),
        meta: {
          title: '操作日志',
          icon: 'Document',
          requiresAuth: true,
          permissions: ['admin:log:list'],
        },
      },
    ],
  },

  {
    path: '/content',
    component: Layout,
    meta: {
      title: '内容管理',
      icon: 'Document',
      requiresAuth: true,
    },
    children: [
      {
        path: 'swipers',
        name: 'ContentSwipers',
        component: () => import('@/views/content/swipers/index.vue'),
        meta: {
          title: '轮播图管理',
          icon: 'Picture',
          requiresAuth: true,
          permissions: ['content:swiper:list'],
        },
      },
      {
        path: 'news',
        name: 'ContentNews',
        component: () => import('@/views/content/news/index.vue'),
        meta: {
          title: '新闻管理',
          icon: 'ChatLineRound',
          requiresAuth: true,
          permissions: ['content:news:list'],
        },
      },

      {
        path: 'project-cases',
        name: 'ContentProjectCases',
        component: () => import('@/views/content/project-cases/index.vue'),
        meta: {
          title: '项目案例',
          icon: 'Folder',
          requiresAuth: true,
          permissions: ['content:project:list'],
        },
      },
              {
          path: 'partners',
          name: 'ContentPartners',
          component: () => import('@/views/content/partners/index.vue'),
          meta: {
            title: '合作伙伴',
            icon: 'UserFilled',
            requiresAuth: true,
            permissions: ['content:partner:list'],
          },
        },
        {
          path: 'friend-links',
          name: 'ContentFriendLinks',
          component: () => import('@/views/content/friend-links/index.vue'),
          meta: {
            title: '友情链接',
            icon: 'Link',
            requiresAuth: true,
            permissions: ['content:link:list'],
          },
        },
        {
          path: 'recruitments',
          name: 'ContentRecruitments',
          component: () => import('@/views/content/recruitments/index.vue'),
          meta: {
            title: '招聘信息',
            icon: 'Briefcase',
            requiresAuth: true,
            permissions: ['content:recruitment:list'],
          },
        },

    ],
  },

  // 平台管理
  {
    path: '/platform',
    component: Layout,
    meta: {
      title: '平台管理',
      icon: 'Monitor',
      requiresAuth: true,
    },
    children: [
      {
        path: 'list',
        name: 'PlatformList',
        component: () => import('@/views/content/part-platforms/index.vue'),
        meta: {
          title: '平台列表',
          icon: 'Menu',
          requiresAuth: true,
          permissions: ['content:platform:list'],
        },
      },
      // 旧页面重定向到一体化配置页面
      {
        path: 'extensions/:id',
        redirect: to => `/platform/config/${to.params.id}`,
        meta: {
          hideInMenu: true,
        },
      },
      {
        path: 'table-columns/:tableId',
        redirect: '/platform/list', // 由于需要平台ID，重定向到列表页
        meta: {
          hideInMenu: true,
        },
      },
      {
        path: 'table-data/:tableId',
        redirect: '/platform/list', // 由于需要平台ID，重定向到列表页
        meta: {
          hideInMenu: true,
        },
      },
      {
        path: 'config/:id',
        name: 'PlatformIntegratedConfig',
        component: () => import('@/views/content/part-platforms/integrated-config.vue'),
        meta: {
          title: '平台配置',
          icon: 'Setting',
          requiresAuth: true,
          permissions: ['content:platform:list'],
          hideInMenu: true,
        },
      },

    ],
  },

  {
    path: '/settings',
    component: Layout,
    meta: {
      title: '系统设置',
      icon: 'Setting',
      requiresAuth: true,
    },
    children: [
      {
        path: '',
        name: 'Settings',
        component: () => import('@/views/settings/index.vue'),
        meta: {
          title: '基本设置',
          icon: 'Tools',
          requiresAuth: true,
        },
      },
    ],
  },
  {
    path: '/redirect',
    component: Layout,
    meta: {
      hideInMenu: true,
      requiresAuth: true,
    },
    children: [
      {
        path: ':path(.*)',
        name: 'Redirect',
        component: () => import('@/views/redirect/index.vue'),
        meta: {
          title: '重定向',
          hideInMenu: true,
          requiresAuth: true,
        },
      },
    ],
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '页面不存在',
      hideInMenu: true,
      requiresAuth: false,
    },
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '页面不存在',
      hideInMenu: true,
      requiresAuth: false,
    },
  },
]

const router = createRouter({
  history: createWebHistory('/admin/'),
  routes,
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  console.log('🚀 [路由守卫] 导航开始:', {
    from: from.path,
    to: to.path,
    isLoggedIn: authStore.isLoggedIn,
    hasToken: !!localStorage.getItem('admin_token'),
    hasUserInfo: !!authStore.userInfo,
    requiresAuth: to.meta.requiresAuth
  })

  // 如果有Token但没有用户信息，说明认证状态可能还在初始化中
  const hasToken = !!localStorage.getItem('admin_token')
  if (hasToken && !authStore.userInfo && to.meta.requiresAuth !== false) {
    console.log('⏳ [路由守卫] 检测到Token但无用户信息，等待认证状态初始化')
    try {
      // 尝试获取用户信息来完成认证状态初始化
      await authStore.getUserInfo()
      console.log('✅ [路由守卫] 认证状态初始化完成')
    } catch (error) {
      console.error('❌ [路由守卫] 认证状态初始化失败:', error)
      // 如果获取失败，清除状态并继续正常流程
      authStore.clearAuthState()
    }
  }

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 蔚之领域管理后台`
  }

  // 如果是登录页面
  if (to.path === '/login') {
    console.log('📝 [路由守卫] 访问登录页面, 当前登录状态:', authStore.isLoggedIn)
    // 如果已经登录，重定向到首页
    if (authStore.isLoggedIn) {
      console.log('✅ [路由守卫] 已登录，重定向到首页')
      next('/')
    } else {
      console.log('👤 [路由守卫] 未登录，允许访问登录页面')
      next()
    }
    return
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth !== false) {
    console.log('🔐 [路由守卫] 页面需要认证')

    // 检查token是否存在
    const token = localStorage.getItem('admin_token')

    if (!token || !authStore.isLoggedIn) {
      console.log('❌ [路由守卫] 无token或未登录，跳转到登录页面:', {
        hasToken: !!token,
        isLoggedIn: authStore.isLoggedIn
      })
      // 清除可能残留的认证状态
      authStore.clearAuthState()
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // 如果store中没有用户信息，尝试获取（但不要每次都验证）
    if (!authStore.userInfo) {
      console.log('👤 [路由守卫] 无用户信息，尝试获取用户信息')
      try {
        await authStore.getUserInfo()
        console.log('✅ [路由守卫] 获取用户信息成功')
      } catch (error) {
        // 获取用户信息失败，说明token无效
        console.error('❌ [路由守卫] 获取用户信息失败:', error)
        authStore.clearAuthState()
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }
    } else {
      // 如果已有用户信息，只是简单检查token是否存在，不进行网络验证
      console.log('✅ [路由守卫] 已有用户信息，跳过验证')
    }

    // 检查权限
    if (to.meta.permissions && Array.isArray(to.meta.permissions)) {
      const hasPermission = authStore.checkPermission(to.meta.permissions)
      console.log('🔑 [路由守卫] 权限检查:', {
        requiredPermissions: to.meta.permissions,
        hasPermission
      })
      if (!hasPermission) {
        console.log('🚫 [路由守卫] 权限不足，跳转到首页')
        // 没有权限，跳转到403页面或首页
        next('/')
        return
      }
    }
  } else {
    console.log('🔓 [路由守卫] 页面无需认证')
  }

  console.log('✅ [路由守卫] 导航允许通过')
  next()
})

// 路由后置守卫 - 处理tab
router.afterEach((to) => {
  const tabsStore = useTabsStore()

  // 跳过不需要添加到tab的页面
  if (
    to.meta.hideInMenu ||
    to.path === '/login' ||
    to.path === '/404' ||
    to.path.startsWith('/redirect')
  ) {
    return
  }

  // 初始化首页tab（如果还没有tab）
  if (tabsStore.tabs.length === 0) {
    tabsStore.initHomePage()
  }

  // 添加当前页面到tab
  tabsStore.addTab(to)
})

// 路由后置守卫 - 处理tab
router.afterEach((to) => {
  const tabsStore = useTabsStore()

  // 跳过不需要添加到tab的页面
  if (
    to.meta.hideInMenu ||
    to.path === '/login' ||
    to.path === '/404' ||
    to.path.startsWith('/redirect')
  ) {
    return
  }

  // 初始化首页tab（如果还没有tab）
  if (tabsStore.tabs.length === 0) {
    tabsStore.initHomePage()
  }

  // 添加当前页面到tab
  tabsStore.addTab(to)
})

export default router 