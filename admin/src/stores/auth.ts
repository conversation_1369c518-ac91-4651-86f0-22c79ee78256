import { authApi } from '@/api/auth'
import { ElMessage } from 'element-plus'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export interface AdminUser {
  id: number
  username: string
  email: string
  real_name: string
  avatar?: string
  phone?: string
  status: string
  last_login_at?: string
  created_at: string
  updated_at: string
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(null)
  const userInfo = ref<AdminUser | null>(null)
  const permissions = ref<string[]>([])
  const loginAttempts = ref(0)
  const maxLoginAttempts = 5
  const tokenRefreshTimer = ref<number | null>(null)

  // 同步恢复本地存储的认证状态（不进行网络验证）
  const restoreAuthFromStorage = () => {
    console.log('🔄 [AuthStore] 同步恢复本地存储的认证状态')

    const savedToken = localStorage.getItem('admin_token')
    const savedUserInfo = localStorage.getItem('admin_user_info')
    const savedPermissions = localStorage.getItem('admin_permissions')

    if (savedToken && savedUserInfo && savedPermissions) {
      try {
        token.value = savedToken
        userInfo.value = JSON.parse(savedUserInfo)
        permissions.value = JSON.parse(savedPermissions)

        console.log('✅ [AuthStore] 本地认证状态恢复成功:', userInfo.value?.username)
        return true
      } catch (error) {
        console.error('❌ [AuthStore] 解析本地存储数据失败:', error)
        clearAuthState()
        return false
      }
    }

    console.log('📝 [AuthStore] 无有效的本地存储数据')
    return false
  }

  // 在store创建时立即尝试恢复状态
  restoreAuthFromStorage()

  // 计算属性
  const isLoggedIn = computed(() => {
    return !!(token.value && userInfo.value)
  })

  // 登录
  const login = async (credentials: { username: string; password: string }) => {
    console.log('🚀 [AuthStore] 开始登录')
    
    try {
      const response = await authApi.login(credentials)
      console.log('✅ [AuthStore] 登录成功')
      
      // 设置认证信息
      token.value = response.token
      userInfo.value = response.admin
      
      // 从后端获取用户权限
      const userPermissions = response.permissions || []
      permissions.value = userPermissions
      
      // 保存到本地存储
      localStorage.setItem('admin_token', response.token)
      localStorage.setItem('admin_user_info', JSON.stringify(userInfo.value))
      localStorage.setItem('admin_permissions', JSON.stringify(userPermissions))
      
      // 重置登录尝试次数
      loginAttempts.value = 0
      localStorage.removeItem('login_attempts')

      // 启动Token自动刷新
      startTokenRefresh()

      return response
    } catch (error: any) {
      console.error('❌ [AuthStore] 登录失败:', error)
      
      // 处理登录失败
      loginAttempts.value++
      localStorage.setItem('login_attempts', loginAttempts.value.toString())
      
      if (loginAttempts.value >= maxLoginAttempts) {
        throw new Error(`登录失败次数过多，请${maxLoginAttempts}分钟后再试`)
      }
      
      if (error?.response?.status === 401) {
        throw new Error('用户名或密码错误')
      } else if (error?.response?.data?.message) {
        throw new Error(error.response.data.message)
      } else {
        throw new Error('登录失败，请检查网络连接')
      }
    }
  }

  // 获取用户信息
  const getUserInfo = async () => {
    console.log('👤 [AuthStore] 开始获取用户信息')
    
    // 如果没有 token，先尝试从 localStorage 获取
    if (!token.value) {
      const savedToken = localStorage.getItem('admin_token')
      console.log('🔑 [AuthStore] Store中无token，从localStorage获取:', !!savedToken)
      if (savedToken) {
        token.value = savedToken
      } else {
        console.log('❌ [AuthStore] localStorage中也无token')
        throw new Error('未登录')
      }
    }
    
    try {
      console.log('📡 [AuthStore] 调用API获取用户信息')
      const response = await authApi.getUserInfo()
      
      // 处理响应数据
      userInfo.value = response.userInfo
      const userPermissions = response.permissions || []
      permissions.value = userPermissions

      console.log('✅ [AuthStore] 获取用户信息成功:', userInfo.value?.username)
      
      // 更新本地存储
      localStorage.setItem('admin_user_info', JSON.stringify(userInfo.value))
      localStorage.setItem('admin_permissions', JSON.stringify(userPermissions))
      
      return { userInfo: userInfo.value, permissions: userPermissions }
    } catch (error: any) {
      console.error('❌ [AuthStore] 获取用户信息失败:', error)
      
      // 如果是认证错误，清除登录状态
      if (error?.response?.status === 401) {
        console.log('🔐 [AuthStore] 401错误，清除认证状态')
        clearAuthState()
        throw new Error('登录已过期，请重新登录')
      }
      
      throw error
    }
  }

  // 登出
  const logout = async (showMessage = true) => {
    try {
      await authApi.logout()
    } catch (error) {
      console.error('登出API调用失败:', error)
    } finally {
      clearAuthState()
      if (showMessage) {
        ElMessage.success('已安全退出')
      }
    }
  }

  // 启动Token自动刷新
  const startTokenRefresh = () => {
    // 清除现有的定时器
    if (tokenRefreshTimer.value) {
      clearInterval(tokenRefreshTimer.value)
    }

    // 每6小时刷新一次Token（Token有效期8小时，提前2小时刷新）
    const refreshInterval = 6 * 60 * 60 * 1000 // 6小时

    tokenRefreshTimer.value = window.setInterval(async () => {
      console.log('🔄 [AuthStore] 自动刷新Token')
      try {
        await getUserInfo() // 通过获取用户信息来验证Token是否仍然有效
        console.log('✅ [AuthStore] Token自动刷新成功')
      } catch (error) {
        console.error('❌ [AuthStore] Token自动刷新失败:', error)
        // Token已过期，清除状态
        clearAuthState()
      }
    }, refreshInterval)

    console.log('⏰ [AuthStore] Token自动刷新已启动，间隔:', refreshInterval / 1000 / 60, '分钟')
  }

  // 停止Token自动刷新
  const stopTokenRefresh = () => {
    if (tokenRefreshTimer.value) {
      clearInterval(tokenRefreshTimer.value)
      tokenRefreshTimer.value = null
      console.log('⏹️ [AuthStore] Token自动刷新已停止')
    }
  }

  // 清除认证状态
  const clearAuthState = () => {
    console.log('🧹 [AuthStore] 清除认证状态')

    // 停止Token刷新
    stopTokenRefresh()

    token.value = null
    userInfo.value = null
    permissions.value = []

    // 清除本地存储
    localStorage.removeItem('admin_token')
    localStorage.removeItem('admin_user_info')
    localStorage.removeItem('admin_permissions')
    console.log('✅ [AuthStore] 认证状态已清除')
  }

  // 初始化认证状态
  const initAuth = async () => {
    console.log('🚀 [AuthStore] 开始初始化认证状态')
    try {
      // 从本地存储恢复状态
      const savedToken = localStorage.getItem('admin_token')
      const savedUserInfo = localStorage.getItem('admin_user_info')
      const savedPermissions = localStorage.getItem('admin_permissions')
      
      console.log('💾 [AuthStore] 本地存储状态:', {
        hasToken: !!savedToken,
        hasUserInfo: !!savedUserInfo,
        hasPermissions: !!savedPermissions
      })
      
      if (savedToken && savedUserInfo && savedPermissions) {
        console.log('✅ [AuthStore] 发现完整的本地存储数据，先恢复状态')

        // 先恢复本地状态，确保用户界面能正常显示
        try {
          token.value = savedToken
          userInfo.value = JSON.parse(savedUserInfo)
          permissions.value = JSON.parse(savedPermissions)
          console.log('✅ [AuthStore] 本地状态恢复成功:', userInfo.value?.username)
        } catch (error) {
          console.error('❌ [AuthStore] 解析本地数据失败:', error)
          clearAuthState()
          return
        }

        // 然后在后台验证token有效性
        try {
          console.log('🔍 [AuthStore] 后台验证token有效性')
          await getUserInfo()
          console.log('✅ [AuthStore] Token验证成功')

          // 启动Token自动刷新
          startTokenRefresh()
        } catch (error) {
          console.error('❌ [AuthStore] Token验证失败，清除认证状态:', error)
          // Token无效，立即清除状态
          clearAuthState()

          // 如果当前不在登录页面，跳转到登录页面
          if (typeof window !== 'undefined' && window.location.pathname !== '/admin/login') {
            console.log('🔄 [AuthStore] Token无效，跳转到登录页面')
            // 动态导入router避免循环依赖
            import('@/router').then(({ default: router }) => {
              router.push({
                path: '/login',
                query: { redirect: window.location.pathname + window.location.search }
              })
            })
          }
        }
      } else {
        console.log('📝 [AuthStore] 本地存储数据不完整，需要重新登录')
        clearAuthState()
      }
    } catch (error) {
      console.error('❌ [AuthStore] 初始化认证状态失败:', error)
      clearAuthState()
    }
  }

  // 权限检查
  const hasPermission = (permission: string | string[]): boolean => {
    if (!permissions.value.length) return false
    
    // 超级管理员拥有所有权限
    if (permissions.value.includes('super:admin')) return true
    
    if (Array.isArray(permission)) {
      return permission.some(p => permissions.value.includes(p))
    }
    
    return permissions.value.includes(permission)
  }

  // 角色检查
  const hasRole = (role: string | string[]): boolean => {
    // 这里可以根据实际需求实现角色检查逻辑
    return hasPermission(role)
  }

  // 检查权限（别名）
  const checkPermission = hasPermission

  return {
    // 状态
    token,
    userInfo,
    permissions,
    loginAttempts,
    
    // 计算属性
    isLoggedIn,
    
    // 方法
    login,
    logout,
    getUserInfo,
    initAuth,
    clearAuthState,
    restoreAuthFromStorage,
    startTokenRefresh,
    stopTokenRefresh,
    hasPermission,
    hasRole,
    checkPermission
  }
})
