import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import type { RouteLocationNormalized } from 'vue-router'

export interface TabItem {
  path: string
  name: string
  title: string
  closable: boolean
  params?: Record<string, any>
  query?: Record<string, any>
}

export const useTabsStore = defineStore('tabs', () => {
  // 状态
  const tabs = ref<TabItem[]>([])
  const activeTab = ref<string>('')
  const maxTabs = ref(10) // 最大tab数量

  // 计算属性
  const tabsCount = computed(() => tabs.value.length)
  const hasMaxTabs = computed(() => tabs.value.length >= maxTabs.value)

  // 初始化首页tab
  const initHomePage = () => {
    const homeTab: TabItem = {
      path: '/dashboard',
      name: 'Dashboard',
      title: '仪表盘',
      closable: false
    }
    
    if (!tabs.value.find(tab => tab.path === homeTab.path)) {
      tabs.value.push(homeTab)
      activeTab.value = homeTab.path
    }
  }

  // 添加tab
  const addTab = (route: RouteLocationNormalized) => {
    const { path, name, meta, params, query } = route
    
    // 检查是否已存在
    const existingTab = tabs.value.find(tab => tab.path === path)
    if (existingTab) {
      activeTab.value = path
      return
    }

    // 检查是否达到最大数量
    if (hasMaxTabs.value) {
      // 移除最旧的可关闭tab
      const closableIndex = tabs.value.findIndex(tab => tab.closable)
      if (closableIndex !== -1) {
        tabs.value.splice(closableIndex, 1)
      } else {
        // 如果没有可关闭的tab，不添加新tab
        return
      }
    }

    // 创建新tab
    const newTab: TabItem = {
      path,
      name: name as string,
      title: meta?.title as string || '未命名页面',
      closable: path !== '/dashboard', // 首页不可关闭
      params: params && Object.keys(params).length > 0 ? params : undefined,
      query: query && Object.keys(query).length > 0 ? query : undefined
    }

    tabs.value.push(newTab)
    activeTab.value = path
  }

  // 关闭tab
  const closeTab = (targetPath: string) => {
    const targetIndex = tabs.value.findIndex(tab => tab.path === targetPath)
    if (targetIndex === -1) return null

    const targetTab = tabs.value[targetIndex]
    
    // 不能关闭不可关闭的tab
    if (!targetTab.closable) return null

    // 移除tab
    tabs.value.splice(targetIndex, 1)

    // 如果关闭的是当前激活的tab，需要切换到其他tab
    if (activeTab.value === targetPath) {
      if (tabs.value.length > 0) {
        // 优先切换到右边的tab，如果没有则切换到左边的tab
        const newActiveTab = tabs.value[targetIndex] || tabs.value[targetIndex - 1]
        activeTab.value = newActiveTab.path
        return newActiveTab
      } else {
        // 如果没有其他tab了，回到首页
        initHomePage()
        return tabs.value[0]
      }
    }

    return null
  }

  // 关闭其他tab
  const closeOtherTabs = (keepPath: string) => {
    tabs.value = tabs.value.filter(tab => tab.path === keepPath || !tab.closable)
    activeTab.value = keepPath
  }

  // 关闭所有tab
  const closeAllTabs = () => {
    tabs.value = tabs.value.filter(tab => !tab.closable)
    if (tabs.value.length > 0) {
      activeTab.value = tabs.value[0].path
    } else {
      initHomePage()
    }
  }

  // 设置激活tab
  const setActiveTab = (path: string) => {
    if (tabs.value.find(tab => tab.path === path)) {
      activeTab.value = path
    }
  }

  // 获取tab
  const getTab = (path: string) => {
    return tabs.value.find(tab => tab.path === path)
  }

  // 更新tab标题
  const updateTabTitle = (path: string, title: string) => {
    const tab = tabs.value.find(tab => tab.path === path)
    if (tab) {
      tab.title = title
    }
  }

  // 清空所有tab
  const clearTabs = () => {
    tabs.value = []
    activeTab.value = ''
  }

  return {
    // 状态
    tabs,
    activeTab,
    maxTabs,
    
    // 计算属性
    tabsCount,
    hasMaxTabs,
    
    // 方法
    initHomePage,
    addTab,
    closeTab,
    closeOtherTabs,
    closeAllTabs,
    setActiveTab,
    getTab,
    updateTabTitle,
    clearTabs
  }
})
