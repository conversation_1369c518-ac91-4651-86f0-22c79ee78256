import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 状态
  const sidebarCollapsed = ref(false)
  const theme = ref<'light' | 'dark'>('light')
  const language = ref('zh-CN')
  const loading = ref(false)
  const breadcrumbs = ref<Array<{ title: string; path: string }>>([])

  // 计算属性
  const sidebarWidth = computed(() => sidebarCollapsed.value ? '64px' : '200px')
  
  const isDarkTheme = computed(() => theme.value === 'dark')

  // 方法
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
    // 保存到本地存储
    localStorage.setItem('sidebar_collapsed', String(sidebarCollapsed.value))
  }

  const setSidebarCollapsed = (collapsed: boolean) => {
    sidebarCollapsed.value = collapsed
    localStorage.setItem('sidebar_collapsed', String(collapsed))
  }

  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
    localStorage.setItem('theme', theme.value)
    // 更新HTML类名
    document.documentElement.className = theme.value
  }

  const setTheme = (newTheme: 'light' | 'dark') => {
    theme.value = newTheme
    localStorage.setItem('theme', newTheme)
    document.documentElement.className = newTheme
  }

  const setLanguage = (lang: string) => {
    language.value = lang
    localStorage.setItem('language', lang)
  }

  const setLoading = (isLoading: boolean) => {
    loading.value = isLoading
  }

  const setBreadcrumbs = (crumbs: Array<{ title: string; path: string }>) => {
    breadcrumbs.value = crumbs
  }

  const addBreadcrumb = (crumb: { title: string; path: string }) => {
    const exists = breadcrumbs.value.find(item => item.path === crumb.path)
    if (!exists) {
      breadcrumbs.value.push(crumb)
    }
  }

  const removeBreadcrumb = (path: string) => {
    const index = breadcrumbs.value.findIndex(item => item.path === path)
    if (index > -1) {
      breadcrumbs.value.splice(index, 1)
    }
  }

  // 初始化应用状态
  const initializeApp = () => {
    // 从本地存储恢复状态
    const savedCollapsed = localStorage.getItem('sidebar_collapsed')
    if (savedCollapsed !== null) {
      sidebarCollapsed.value = savedCollapsed === 'true'
    }

    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark'
    if (savedTheme) {
      theme.value = savedTheme
      document.documentElement.className = savedTheme
    }

    const savedLanguage = localStorage.getItem('language')
    if (savedLanguage) {
      language.value = savedLanguage
    }
  }

  // 重置状态
  const $reset = () => {
    sidebarCollapsed.value = false
    theme.value = 'light'
    language.value = 'zh-CN'
    loading.value = false
    breadcrumbs.value = []
    
    // 清除本地存储
    localStorage.removeItem('sidebar_collapsed')
    localStorage.removeItem('theme')
    localStorage.removeItem('language')
  }

  return {
    // 状态
    sidebarCollapsed,
    theme,
    language,
    loading,
    breadcrumbs,
    
    // 计算属性
    sidebarWidth,
    isDarkTheme,
    
    // 方法
    toggleSidebar,
    setSidebarCollapsed,
    toggleTheme,
    setTheme,
    setLanguage,
    setLoading,
    setBreadcrumbs,
    addBreadcrumb,
    removeBreadcrumb,
    initializeApp,
    $reset
  }
}) 