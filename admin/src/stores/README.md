# Pinia 状态管理使用指南

## 概述

admin项目使用Pinia作为状态管理工具，提供了响应式的全局状态管理。

## Store结构

```
src/stores/
├── auth.ts        # 认证状态管理
├── user.ts        # 用户管理状态
├── app.ts         # 应用全局状态
├── index.ts       # 统一导出
└── README.md      # 使用指南
```

## 已创建的Store

### 1. 认证Store (`auth.ts`)

管理用户登录状态、用户信息和权限：

```typescript
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// 状态
authStore.token          // 用户token
authStore.userInfo       // 用户信息
authStore.permissions    // 用户权限

// 计算属性
authStore.isLoggedIn     // 是否已登录
authStore.hasPermission('user:create')  // 检查权限

// 方法
await authStore.login({ username, password })
await authStore.logout()
await authStore.getUserInfo()
```

### 2. 用户Store (`user.ts`)

管理用户列表和用户相关操作：

```typescript
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 状态
userStore.users          // 用户列表
userStore.currentUser    // 当前选中用户
userStore.loading        // 加载状态
userStore.total          // 总数
userStore.roles          // 角色列表

// 计算属性
userStore.activeUsers    // 活跃用户
userStore.getUserById(1) // 根据ID获取用户

// 方法
await userStore.fetchUsers({ page: 1, size: 10 })
await userStore.createUser(userData)
await userStore.updateUser(id, userData)
await userStore.deleteUser(id)
```

### 3. 应用Store (`app.ts`)

管理应用全局状态：

```typescript
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()

// 状态
appStore.sidebarCollapsed  // 侧边栏折叠状态
appStore.theme            // 主题
appStore.language         // 语言
appStore.loading          // 全局加载状态
appStore.breadcrumbs      // 面包屑导航

// 计算属性
appStore.sidebarWidth     // 侧边栏宽度
appStore.isDarkTheme      // 是否暗色主题

// 方法
appStore.toggleSidebar()
appStore.toggleTheme()
appStore.setLanguage('en')
```

## 使用方式

### 1. 在组件中使用

```vue
<template>
  <div>
    <h1>{{ authStore.userInfo?.nickname }}</h1>
    <button @click="toggleSidebar">切换侧边栏</button>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore, useAppStore } from '@/stores'

const authStore = useAuthStore()
const appStore = useAppStore()

const toggleSidebar = () => {
  appStore.toggleSidebar()
}
</script>
```

### 2. 在组合式函数中使用

```typescript
// composables/useAuth.ts
import { useAuthStore } from '@/stores/auth'

export function useAuth() {
  const authStore = useAuthStore()
  
  const login = async (credentials: LoginData) => {
    try {
      await authStore.login(credentials)
      return true
    } catch (error) {
      return false
    }
  }
  
  return {
    login,
    logout: authStore.logout,
    isLoggedIn: authStore.isLoggedIn,
    userInfo: authStore.userInfo
  }
}
```

### 3. 在路由守卫中使用

```typescript
// router/index.ts
import { useAuthStore } from '@/stores/auth'

router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  if (to.path === '/login') {
    next()
  } else if (!authStore.isLoggedIn) {
    next('/login')
  } else {
    next()
  }
})
```

## Store设计模式

### 1. 使用Composition API语法

```typescript
export const useExampleStore = defineStore('example', () => {
  // 状态 (相当于 state)
  const count = ref(0)
  const items = ref<Item[]>([])
  
  // 计算属性 (相当于 getters)
  const doubleCount = computed(() => count.value * 2)
  
  // 方法 (相当于 actions)
  const increment = () => {
    count.value++
  }
  
  const fetchItems = async () => {
    // API调用
  }
  
  return {
    count,
    items,
    doubleCount,
    increment,
    fetchItems
  }
})
```

### 2. 状态持久化

```typescript
// 在Store中实现本地存储
const saveToStorage = () => {
  localStorage.setItem('key', JSON.stringify(state.value))
}

const loadFromStorage = () => {
  const saved = localStorage.getItem('key')
  if (saved) {
    state.value = JSON.parse(saved)
  }
}
```

### 3. 错误处理

```typescript
const fetchData = async () => {
  loading.value = true
  error.value = null
  
  try {
    const result = await api.getData()
    data.value = result
  } catch (err) {
    error.value = err.message
    console.error('获取数据失败:', err)
  } finally {
    loading.value = false
  }
}
```

## 最佳实践

### 1. Store命名规范

- Store文件名使用小写，如 `user.ts`
- Store名称使用camelCase，如 `useUserStore`
- 状态变量使用描述性名称

### 2. 状态结构设计

```typescript
// ✅ 好的设计
const users = ref<User[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

// ❌ 避免的设计
const state = ref({
  users: [],
  loading: false,
  error: null
})
```

### 3. 异步操作处理

```typescript
// ✅ 统一的异步处理模式
const fetchUsers = async (params?: any) => {
  loading.value = true
  try {
    const result = await userApi.getList(params)
    users.value = result.list
    total.value = result.total
    return result
  } catch (error) {
    console.error('获取用户失败:', error)
    throw error
  } finally {
    loading.value = false
  }
}
```

### 4. 计算属性使用

```typescript
// ✅ 使用计算属性进行数据派生
const activeUsers = computed(() => 
  users.value.filter(user => user.status === 'active')
)

const getUserById = computed(() => 
  (id: number) => users.value.find(user => user.id === id)
)
```

### 5. Store之间的通信

```typescript
// 在一个Store中使用另一个Store
export const useUserStore = defineStore('user', () => {
  const authStore = useAuthStore()
  
  const createUser = async (userData: CreateUserData) => {
    // 检查权限
    if (!authStore.hasPermission('user:create')) {
      throw new Error('没有创建用户的权限')
    }
    
    // 执行创建操作
    const user = await userApi.create(userData)
    users.value.push(user)
    return user
  }
  
  return { createUser }
})
```

## 调试和开发工具

### 1. Vue DevTools

安装Vue DevTools浏览器扩展，可以查看和调试Pinia状态。

### 2. 状态重置

```typescript
// 重置单个Store
const userStore = useUserStore()
userStore.$reset()

// 重置所有Store
import { getActivePinia } from 'pinia'
getActivePinia()._s.forEach(store => store.$reset())
```

### 3. 状态订阅

```typescript
// 监听状态变化
const userStore = useUserStore()
userStore.$subscribe((mutation, state) => {
  console.log('状态变化:', mutation, state)
})
```

## 与API集成

Store与API的集成遵循以下模式：

```typescript
export const useDataStore = defineStore('data', () => {
  const items = ref<Item[]>([])
  const loading = ref(false)
  
  const fetchItems = async () => {
    loading.value = true
    try {
      const result = await api.getItems()
      items.value = result.list
    } catch (error) {
      // 错误处理
    } finally {
      loading.value = false
    }
  }
  
  const createItem = async (data: CreateItemData) => {
    const item = await api.createItem(data)
    items.value.push(item)
    return item
  }
  
  return { items, loading, fetchItems, createItem }
})
```

这样的设计确保了状态管理的一致性和可维护性。 