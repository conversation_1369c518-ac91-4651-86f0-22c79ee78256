# 蔚之领域智能科技 - 管理端环境变量配置示例
# 复制此文件为 .env.development 或 .env.production 使用

# API 配置
# 开发环境: http://localhost:3001/api
# 生产环境: https://your-api-domain.com/api
VITE_API_BASE_URL=http://localhost:3001/api

# 应用配置
VITE_APP_TITLE=蔚之领域智能科技 - 管理系统
VITE_APP_VERSION=1.0.0

# 环境标识 (development/production)
VITE_APP_ENV=development

# 调试模式 (true/false)
VITE_DEBUG=true

# 开发服务器配置
VITE_DEV_SERVER_PORT=5173
VITE_DEV_SERVER_HOST=localhost

# 后端服务配置
VITE_BACKEND_URL=http://localhost:3001

# 上传配置
VITE_UPLOAD_URL=/api/upload
VITE_MAX_FILE_SIZE=10485760

# 图片预览域名
VITE_IMAGE_BASE_URL=http://localhost:3001

# WebSocket 配置
VITE_WS_URL=ws://localhost:3001/ws

# 分页配置
VITE_DEFAULT_PAGE_SIZE=10
VITE_MAX_PAGE_SIZE=100 