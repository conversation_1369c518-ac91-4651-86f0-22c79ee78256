import { defineConfig, presetAttributify, presetIcons, presetUno } from 'unocss'

export default defineConfig({
  presets: [
    presetUno(), // 启用默认预设
    presetAttributify(), // 启用属性化预设
    presetIcons({
      collections: {
        carbon: () => import('@iconify-json/carbon/icons.json').then(i => i.default),
        mdi: () => import('@iconify-json/mdi/icons.json').then(i => i.default),
        tabler: () => import('@iconify-json/tabler/icons.json').then(i => i.default),
      }
    }), // 启用图标预设
  ],
  shortcuts: [
    // 自定义快捷类
    ['btn', 'px-4 py-1 rounded inline-block bg-teal-600 text-white cursor-pointer hover:bg-teal-700 disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50'],
    ['btn-primary', 'btn bg-blue-600 hover:bg-blue-700'],
    ['btn-secondary', 'btn bg-gray-600 hover:bg-gray-700'],
    ['icon-btn', 'text-[0.9em] inline-block cursor-pointer select-none opacity-75 transition duration-200 ease-in-out hover:opacity-100 hover:text-teal-600'],
    // 页面布局
    ['page-container', 'min-h-screen bg-gray-50'],
    ['content-wrapper', 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'],
    // 卡片样式
    ['card', 'bg-white rounded-lg shadow-sm border border-gray-200'],
    ['card-header', 'px-6 py-4 border-b border-gray-200'],
    ['card-body', 'px-6 py-4'],
    // 表单样式
    ['form-input', 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'],
    ['form-label', 'block text-sm font-medium text-gray-700 mb-2'],
  ],
  theme: {
    colors: {
      primary: {
        50: '#eff6ff',
        100: '#dbeafe',
        200: '#bfdbfe',
        300: '#93c5fd',
        400: '#60a5fa',
        500: '#3b82f6',
        600: '#2563eb',
        700: '#1d4ed8',
        800: '#1e40af',
        900: '#1e3a8a',
      }
    }
  }
}) 