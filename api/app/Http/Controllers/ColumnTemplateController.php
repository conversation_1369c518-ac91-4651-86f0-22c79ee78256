<?php

namespace App\Http\Controllers;

use App\Models\ColumnTemplate;
use App\Models\CombinationTemplate;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ColumnTemplateController extends Controller
{
    /**
     * 获取所有列模板
     */
    public function getColumnTemplates(Request $request): JsonResponse
    {
        $query = ColumnTemplate::query();
        
        // 按分类筛选
        if ($request->has('category') && $request->category !== 'all') {
            $query->where('category', $request->category);
        }
        
        // 只获取常用列
        if ($request->boolean('common_only')) {
            $query->where('is_common', true);
        }
        
        // 搜索
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('label', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }
        
        // 排除已存在的列
        if ($request->has('exclude_columns')) {
            $excludeColumns = is_array($request->exclude_columns) 
                ? $request->exclude_columns 
                : explode(',', $request->exclude_columns);
            $query->whereNotIn('name', $excludeColumns);
        }
        
        $templates = $query->orderBy('category')
                          ->orderBy('sort_order')
                          ->orderBy('name')
                          ->get();
        
        return response()->json([
            'code' => 200,
            'message' => 'success',
            'data' => $templates
        ]);
    }
    
    /**
     * 获取所有组合模板
     */
    public function getCombinationTemplates(Request $request): JsonResponse
    {
        $query = CombinationTemplate::with(['columns' => function ($query) {
            $query->orderBy('combination_template_columns.sort_order');
        }]);
        
        // 搜索
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereJsonContains('scenarios', $search);
            });
        }
        
        $templates = $query->orderBy('sort_order')
                          ->orderBy('name')
                          ->get();
        
        return response()->json([
            'code' => 200,
            'message' => 'success',
            'data' => $templates
        ]);
    }
    
    /**
     * 获取列模板分类
     */
    public function getColumnCategories(): JsonResponse
    {
        $categories = ColumnTemplate::select('category')
                                   ->distinct()
                                   ->orderBy('category')
                                   ->pluck('category');
        
        return response()->json([
            'code' => 200,
            'message' => 'success',
            'data' => $categories
        ]);
    }
    
    /**
     * 创建自定义列模板
     */
    public function createColumnTemplate(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:100|unique:column_templates,name',
            'label' => 'required|string|max:100',
            'type' => 'required|in:text,number,date,boolean,textarea',
            'category' => 'required|string|max:50',
            'description' => 'nullable|string',
            'is_required' => 'boolean',
            'is_common' => 'boolean',
            'default_value' => 'nullable',
            'options' => 'nullable|array',
            'validation_rules' => 'nullable|array',
            'sort_order' => 'integer|min:0'
        ]);
        
        $template = ColumnTemplate::create([
            ...$request->only([
                'name', 'label', 'type', 'category', 'description',
                'is_required', 'is_common', 'default_value', 'options',
                'validation_rules', 'sort_order'
            ]),
            'is_system' => false
        ]);
        
        return response()->json([
            'code' => 200,
            'message' => '列模板创建成功',
            'data' => $template
        ]);
    }
    
    /**
     * 创建自定义组合模板
     */
    public function createCombinationTemplate(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:100',
            'description' => 'nullable|string',
            'category' => 'nullable|string|max:50',
            'scenarios' => 'nullable|array',
            'column_ids' => 'required|array|min:1',
            'column_ids.*' => 'exists:column_templates,id'
        ]);
        
        $combination = CombinationTemplate::create([
            'name' => $request->name,
            'description' => $request->description,
            'category' => $request->category,
            'scenarios' => $request->scenarios,
            'is_system' => false,
            'created_by' => auth()->id()
        ]);
        
        // 关联列模板
        $columnData = [];
        foreach ($request->column_ids as $index => $columnId) {
            $columnData[] = [
                'combination_template_id' => $combination->id,
                'column_template_id' => $columnId,
                'sort_order' => $index + 1,
                'created_at' => now()
            ];
        }
        
        \DB::table('combination_template_columns')->insert($columnData);
        
        // 重新加载关联数据
        $combination->load(['columns' => function ($query) {
            $query->orderBy('combination_template_columns.sort_order');
        }]);
        
        return response()->json([
            'code' => 200,
            'message' => '组合模板创建成功',
            'data' => $combination
        ]);
    }
    
    /**
     * 删除自定义模板
     */
    public function deleteColumnTemplate(ColumnTemplate $template): JsonResponse
    {
        if ($template->is_system) {
            return response()->json([
                'code' => 400,
                'message' => '系统预设模板不能删除'
            ], 400);
        }
        
        $template->delete();
        
        return response()->json([
            'code' => 200,
            'message' => '模板删除成功'
        ]);
    }
    
    /**
     * 删除自定义组合模板
     */
    public function deleteCombinationTemplate(CombinationTemplate $template): JsonResponse
    {
        if ($template->is_system) {
            return response()->json([
                'code' => 400,
                'message' => '系统预设模板不能删除'
            ], 400);
        }
        
        $template->delete();
        
        return response()->json([
            'code' => 200,
            'message' => '组合模板删除成功'
        ]);
    }
}
