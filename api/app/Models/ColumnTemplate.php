<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class ColumnTemplate extends Model
{
    protected $fillable = [
        'name',
        'label',
        'type',
        'category',
        'description',
        'is_required',
        'is_common',
        'default_value',
        'options',
        'validation_rules',
        'sort_order',
        'is_system'
    ];

    protected $casts = [
        'is_required' => 'boolean',
        'is_common' => 'boolean',
        'is_system' => 'boolean',
        'options' => 'array',
        'validation_rules' => 'array',
        'sort_order' => 'integer'
    ];

    /**
     * 关联的组合模板
     */
    public function combinationTemplates(): BelongsToMany
    {
        return $this->belongsToMany(
            CombinationTemplate::class,
            'combination_template_columns',
            'column_template_id',
            'combination_template_id'
        )->withPivot('sort_order')
         ->orderBy('combination_template_columns.sort_order');
    }

    /**
     * 获取常用列模板
     */
    public function scopeCommon($query)
    {
        return $query->where('is_common', true);
    }

    /**
     * 按分类筛选
     */
    public function scopeByCategory($query, $category)
    {
        if ($category && $category !== 'all') {
            return $query->where('category', $category);
        }
        return $query;
    }

    /**
     * 搜索
     */
    public function scopeSearch($query, $search)
    {
        if ($search) {
            return $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('label', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }
        return $query;
    }

    /**
     * 排除指定列名
     */
    public function scopeExcludeColumns($query, $columns)
    {
        if ($columns && is_array($columns) && count($columns) > 0) {
            return $query->whereNotIn('name', $columns);
        }
        return $query;
    }
}
