<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CombinationTemplate extends Model
{
    protected $fillable = [
        'name',
        'description',
        'category',
        'scenarios',
        'is_system',
        'created_by',
        'sort_order'
    ];

    protected $casts = [
        'scenarios' => 'array',
        'is_system' => 'boolean',
        'sort_order' => 'integer'
    ];

    /**
     * 关联的列模板
     */
    public function columns(): BelongsToMany
    {
        return $this->belongsToMany(
            ColumnTemplate::class,
            'combination_template_columns',
            'combination_template_id',
            'column_template_id'
        )->withPivot('sort_order')
         ->orderBy('combination_template_columns.sort_order');
    }

    /**
     * 创建者
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 搜索
     */
    public function scopeSearch($query, $search)
    {
        if ($search) {
            return $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereJsonContains('scenarios', $search);
            });
        }
        return $query;
    }

    /**
     * 按分类筛选
     */
    public function scopeByCategory($query, $category)
    {
        if ($category && $category !== 'all') {
            return $query->where('category', $category);
        }
        return $query;
    }

    /**
     * 获取系统预设模板
     */
    public function scopeSystem($query)
    {
        return $query->where('is_system', true);
    }

    /**
     * 获取用户自定义模板
     */
    public function scopeCustom($query)
    {
        return $query->where('is_system', false);
    }
}
